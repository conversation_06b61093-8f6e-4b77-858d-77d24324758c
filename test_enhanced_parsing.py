#!/usr/bin/env python3
"""
Test enhanced bulk modification parsing
"""

import json
import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_parsing():
    """Test enhanced parsing functionality"""
    logger.info("=== Enhanced Parsing Test ===")
    
    # Import the class directly
    from utils.import_export_manager import BulkModificationManager
    
    # Initialize the manager
    manager = BulkModificationManager('app/test_cases')
    
    # Test rules that should work with the enhanced parsing
    test_rules = [
        # Rule from frontend with proper format
        "Add step 'tapIfLocatorExists' with locator_value '//XCUIElementTypeButton[@name=\"Checkout\"]' locator_type 'xpath' after step that contains \"locator_value\":\"//XCUIElementTypeButton[contains(@name,\\\"Tab 4 of 5\\\")]\"",
        
        # Simpler rule for testing
        "Add step 'tap' with locator_value '//XCUIElementTypeButton[@name=\"Checkout\"]' locator_type 'xpath' after step that contains \"type\":\"tap\"",
        
        # Rule with complex XPath
        "Add step 'waitTill' with locator_value '//XCUIElementTypeStaticText[contains(@name,\"Success\") and @visible=\"true\"]' timeout '10' after step that contains \"locator_value\":\"//XCUIElementTypeButton[contains(@name,\\\"Tab 4 of 5\\\")]\"",
    ]
    
    for i, rule in enumerate(test_rules, 1):
        logger.info(f"Testing rule {i}: {rule}")
        
        # Test parsing
        parsed = manager._parse_modification_rule(rule)
        print(f"\nRule {i}:")
        print(f"Input: {rule}")
        print(f"Parsed: {json.dumps(parsed, indent=2)}")
        
        if parsed:
            # Test the parameters parsing specifically
            if 'parameters' in parsed:
                print(f"Parameters: {json.dumps(parsed['parameters'], indent=2)}")
            
            # Test the condition parsing
            if 'condition' in parsed:
                print(f"Condition: {json.dumps(parsed['condition'], indent=2)}")
        
        print("-" * 80)

def test_full_preview():
    """Test the full preview with enhanced rules"""
    logger.info("=== Full Preview Test ===")
    
    from utils.import_export_manager import BulkModificationManager
    
    # Initialize the manager
    manager = BulkModificationManager('app/test_cases')
    
    # Test with a rule that should match our test case
    test_rules = [
        "Add step 'tapIfLocatorExists' with locator_value '//XCUIElementTypeButton[@name=\"Checkout\"]' locator_type 'xpath' after step that contains \"locator_value\":\"//XCUIElementTypeButton[contains(@name,\\\"Tab 4 of 5\\\")]\"",
    ]
    
    logger.info(f"Testing preview with enhanced rule")
    result = manager.preview_modifications(test_rules)
    
    print("Enhanced Preview Result:")
    print(json.dumps(result, indent=2))

def test_parameter_parsing():
    """Test parameter parsing specifically"""
    logger.info("=== Parameter Parsing Test ===")
    
    from utils.import_export_manager import BulkModificationManager
    
    manager = BulkModificationManager('app/test_cases')
    
    # Test different parameter formats
    test_params = [
        'locator_value \'//XCUIElementTypeButton[@name="Checkout"]\' locator_type \'xpath\'',
        'locator_value "//XCUIElementTypeButton[contains(@name,\\"Tab 4 of 5\\")]" locator_type "xpath"',
        'timeout \'10\' locator_value \'//XCUIElementTypeStaticText[contains(@name,"Success")]\' locator_type \'xpath\'',
    ]
    
    for i, param_str in enumerate(test_params, 1):
        logger.info(f"Testing parameter string {i}: {param_str}")
        
        # Test both parsing methods
        legacy_result = manager._parse_parameters(param_str)
        enhanced_result = manager._parse_parameters_enhanced(param_str)
        
        print(f"\nParameter String {i}: {param_str}")
        print(f"Legacy Result: {json.dumps(legacy_result, indent=2)}")
        print(f"Enhanced Result: {json.dumps(enhanced_result, indent=2)}")
        print("-" * 60)

if __name__ == "__main__":
    print("Enhanced Bulk Modification Parsing Test")
    print("=" * 50)
    
    try:
        # Test 1: Enhanced parsing
        test_enhanced_parsing()
        print()
        
        # Test 2: Parameter parsing specifically
        test_parameter_parsing()
        print()
        
        # Test 3: Full preview
        test_full_preview()
        
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
