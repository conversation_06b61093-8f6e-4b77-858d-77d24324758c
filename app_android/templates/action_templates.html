<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Action Templates - Mobile App Automation Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .required-field {
            color: #dc3545 !important;
            font-weight: bold;
        }
        .template-card {
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
        }
        .template-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
        }
        .template-body {
            padding: 1rem;
        }
        .json-template {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', Consolas, monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            margin-bottom: 0.5rem;
        }
        .copy-btn {
            margin-top: 0.5rem;
        }
        .category-header {
            background-color: #e9ecef;
            padding: 1rem;
            margin: 1.5rem 0 1rem 0;
            border-radius: 0.375rem;
            font-weight: bold;
            font-size: 1.1rem;
        }
        .search-container {
            position: sticky;
            top: 0;
            background-color: white;
            z-index: 100;
            padding: 1rem 0;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 1rem;
        }
        .template-description {
            color: #6c757d;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="search-container">
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-file-earmark-code"></i> Action Templates</h2>
                    <p class="text-muted">Copy and paste these templates into your test case JSON. <span class="required-field">Red fields</span> are required.</p>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" id="searchInput" class="form-control" placeholder="Search action templates...">
                        <button type="button" id="clearSearch" class="btn btn-outline-secondary">Clear</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="templatesContainer">
            <!-- Basic Actions -->
            <div class="category-header">
                <i class="bi bi-hand-index"></i> Basic Interaction Actions
            </div>

            <!-- Tap Action -->
            <div class="template-card" data-action="tap">
                <div class="template-header">
                    <h5 class="mb-0">Tap</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Tap at specific coordinates or on an element using locator or image.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "tap"</span>,
  "timestamp": "",
  
  // For coordinate-based tap
  "x": 100,
  "y": 200,
  
  // For locator-based tap
  "locator_type": "xpath",
  "locator_value": "//button[@text='Submit']",
  "timeout": 30,
  
  // For image-based tap
  "method": "image",
  "image_filename": "button.png"
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Input Text Action -->
            <div class="template-card" data-action="inputText">
                <div class="template-header">
                    <h5 class="mb-0">Input Text</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Input text into an element identified by locator.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "inputText"</span>,
  "timestamp": "",
  <span class="required-field">"locator_type": "xpath"</span>,
  <span class="required-field">"locator_value": "//input[@id='username']"</span>,
  <span class="required-field">"text": "Hello World"</span>,
  "clear_first": true,
  "enter": false,
  "timeout": 30
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Wait Action -->
            <div class="template-card" data-action="wait">
                <div class="template-header">
                    <h5 class="mb-0">Wait</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Wait for a specified duration.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "wait"</span>,
  "timestamp": "",
  "duration": 2.0,
  "time": 2000
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Swipe Action -->
            <div class="template-card" data-action="swipe">
                <div class="template-header">
                    <h5 class="mb-0">Swipe</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Perform swipe gesture between two points.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "swipe"</span>,
  "timestamp": "",
  "vector_start": [0.5, 0.3],
  "vector_end": [0.5, 0.7],
  "duration": 300,
  "count": 1,
  "interval": 0.5
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- App Management Actions -->
            <div class="category-header">
                <i class="bi bi-app"></i> App Management Actions
            </div>

            <!-- Restart App Action -->
            <div class="template-card" data-action="restartApp">
                <div class="template-header">
                    <h5 class="mb-0">Restart App</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Restart an application by package ID.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "restartApp"</span>,
  "timestamp": "",
  <span class="required-field">"package_id": "com.example.app"</span>
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Take Screenshot Action -->
            <div class="template-card" data-action="takeScreenshot">
                <div class="template-header">
                    <h5 class="mb-0">Take Screenshot</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Take a screenshot with a custom name.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "takeScreenshot"</span>,
  "timestamp": "",
  <span class="required-field">"screenshot_name": "login_screen"</span>
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Validation Actions -->
            <div class="category-header">
                <i class="bi bi-check-circle"></i> Validation Actions
            </div>

            <!-- Wait Till Action -->
            <div class="template-card" data-action="waitTill">
                <div class="template-header">
                    <h5 class="mb-0">Wait Till</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Wait until an element appears or disappears.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "waitTill"</span>,
  "timestamp": "",
  <span class="required-field">"locator_type": "xpath"</span>,
  <span class="required-field">"locator_value": "//button[@text='Submit']"</span>,
  "method": "locator",
  "timeout": 30,
  "interval": 0.5
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Info Action -->
            <div class="template-card" data-action="info">
                <div class="template-header">
                    <h5 class="mb-0">Info</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Log information with environment variable parsing.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "info"</span>,
  "timestamp": "",
  <span class="required-field">"text": "Starting test execution"</span>
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Advanced Actions -->
            <div class="category-header">
                <i class="bi bi-gear"></i> Advanced Actions
            </div>

            <!-- Multi Step Action -->
            <div class="template-card" data-action="multistep">
                <div class="template-header">
                    <h5 class="mb-0">Multi Step</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Execute another test case as a multi-step action.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "multistep"</span>,
  "timestamp": "",
  <span class="required-field">"test_case_id": "login_flow"</span>,
  "test_case_steps": []
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Repeat Steps Action -->
            <div class="template-card" data-action="repeatSteps">
                <div class="template-header">
                    <h5 class="mb-0">Repeat Steps</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Repeat a set of actions multiple times.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "repeatSteps"</span>,
  "timestamp": "",
  <span class="required-field">"repeat_count": 3</span>,
  <span class="required-field">"steps": []</span>
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Cleanup Steps Action -->
            <div class="template-card" data-action="cleanupSteps">
                <div class="template-header">
                    <h5 class="mb-0">Cleanup Steps</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Execute cleanup actions only when there are failed test steps.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "cleanupSteps"</span>,
  "timestamp": "",
  <span class="required-field">"steps": []</span>
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Platform-Specific Actions -->
            <div class="category-header">
                <i class="bi bi-phone"></i> Platform-Specific Actions
            </div>

            <!-- Android Functions Action -->
            <div class="template-card" data-action="androidFunctions">
                <div class="template-header">
                    <h5 class="mb-0">Android Functions</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Execute Android-specific system functions.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "androidFunctions"</span>,
  "timestamp": "",
  <span class="required-field">"function_type": "back"</span>
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>

            <!-- Tap on Element (UISelector) - Android -->
            <div class="template-card" data-action="tapOnElementUISelector">
                <div class="template-header">
                    <h5 class="mb-0">Tap on Element (UISelector)</h5>
                </div>
                <div class="template-body">
                    <div class="template-description">
                        Tap on element using Android UISelector syntax.
                    </div>
                    <div class="json-template">{
  "action_id": "",
  "executionTime": "",
  <span class="required-field">"type": "tapOnElementUISelector"</span>,
  "timestamp": "",
  <span class="required-field">"uiSelector": "new UiSelector().text(\"Submit\")"</span>,
  "timeout": 30
}</div>
                    <button class="btn btn-sm btn-primary copy-btn" onclick="copyTemplate(this)">
                        <i class="bi bi-clipboard"></i> Copy Template
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyTemplate(button) {
            const templateDiv = button.previousElementSibling;
            const templateText = templateDiv.textContent;
            
            // Clean up the template text (remove HTML and extra whitespace)
            const cleanTemplate = templateText
                .replace(/\s+/g, ' ')
                .replace(/{\s+/g, '{\n  ')
                .replace(/,\s+/g, ',\n  ')
                .replace(/\s+}/g, '\n}')
                .replace(/\/\/[^\n]*/g, '') // Remove comments
                .trim();
            
            navigator.clipboard.writeText(cleanTemplate).then(() => {
                // Show success feedback
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="bi bi-check"></i> Copied!';
                button.classList.remove('btn-primary');
                button.classList.add('btn-success');
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-primary');
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
                alert('Failed to copy template. Please copy manually.');
            });
        }

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const templates = document.querySelectorAll('.template-card');
            
            templates.forEach(template => {
                const actionName = template.querySelector('h5').textContent.toLowerCase();
                const description = template.querySelector('.template-description').textContent.toLowerCase();
                const jsonContent = template.querySelector('.json-template').textContent.toLowerCase();
                
                if (actionName.includes(searchTerm) || 
                    description.includes(searchTerm) || 
                    jsonContent.includes(searchTerm)) {
                    template.style.display = 'block';
                } else {
                    template.style.display = 'none';
                }
            });
        });

        document.getElementById('clearSearch').addEventListener('click', function() {
            document.getElementById('searchInput').value = '';
            document.querySelectorAll('.template-card').forEach(template => {
                template.style.display = 'block';
            });
        });
    </script>
</body>
</html>
