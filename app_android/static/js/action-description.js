/**
 * Helper function to get a description of an action for display
 * @param {Object} actionData - The action data object
 * @returns {string} - Human-readable description of the action
 */
window.getActionDescription = function(actionData) {
    // Helper method to get a text description of an action
    switch (actionData.type) {
        case 'tap':
            let description = '';

            // Primary method description
            if (actionData.method === 'locator' || actionData.locator_type) {
                description = `Tap on element with ${actionData.locator_type}: ${actionData.locator_value}`;
            } else if (actionData.method === 'image' || actionData.image_filename) {
                description = `Tap on image: ${actionData.image_filename}`;
            } else {
                description = `Tap at (${actionData.x}, ${actionData.y})`;
            }

            // Add fallback description if available
            if (actionData.fallback_type) {
                description += ' with fallback: ';

                switch (actionData.fallback_type) {
                    case 'coordinates':
                        description += `Coordinates (${actionData.fallback_x}, ${actionData.fallback_y})`;
                        break;

                    case 'image':
                        description += `Image ${actionData.fallback_image_filename}`;
                        break;

                    case 'text':
                        description += `Text "${actionData.fallback_text}"`;
                        break;

                    case 'locator':
                        description += `${actionData.fallback_locator_type}: ${actionData.fallback_locator_value}`;
                        break;
                }
            }

            return description;

        case 'doubleTap':
            if (actionData.method === 'coordinates') {
                return `Double tap at (${actionData.x}, ${actionData.y})`;
            } else if (actionData.method === 'image' || actionData.image_filename) {
                return `Double tap on image: ${actionData.image_filename}`;
            } else {
                // Check if we have fallback locators
                const hasFallbacks = actionData.fallback_locators && Array.isArray(actionData.fallback_locators) && actionData.fallback_locators.length > 0;

                if (hasFallbacks) {
                    let description = '';

                    // If we have a primary locator
                    if (actionData.locator_type && actionData.locator_value) {
                        description = `Double tap on ${actionData.locator_type}: ${actionData.locator_value}`;
                    } else {
                        description = 'Double tap using fallback locators';
                    }

                    // Add fallback count
                    description += ` (with ${actionData.fallback_locators.length} fallback${actionData.fallback_locators.length > 1 ? 's' : ''})`;
                    return description;
                } else {
                    return `Double tap on ${actionData.locator_type}: ${actionData.locator_value}`;
                }
            }

        case 'swipe':
            // Use vector_start and vector_end coordinates if available (these are in 0-1 range)
            if (actionData.vector_start && actionData.vector_end) {
                // Format coordinates to display with 2 decimal places
                const startX = (actionData.vector_start[0] * 100).toFixed(0);
                const startY = (actionData.vector_start[1] * 100).toFixed(0);
                const endX = (actionData.vector_end[0] * 100).toFixed(0);
                const endY = (actionData.vector_end[1] * 100).toFixed(0);

                return `Swipe from (${startX}%, ${startY}%) to (${endX}%, ${endY}%)`;
            }
            // Fallback to the older start_x, start_y format if vector format not available
            return `Swipe from (${actionData.start_x}, ${actionData.start_y}) to (${actionData.end_x}, ${actionData.end_y})`;

        case 'text':
            return `Input text: "${actionData.text}"`;

        case 'exists':
            if (actionData.locator_type === 'image') {
                return `Check if image "${actionData.locator_value}" exists on screen`;
            } else {
                return `Check if element with ${actionData.locator_type}="${actionData.locator_value}" exists`;
            }

        case 'ifElseSteps':
            let conditionDesc = '';
            let thenActionDesc = '';
            const condition = actionData.condition || {};
            
            // Detailed condition description based on condition type
            switch (actionData.condition_type) {
                case 'exists':
                    conditionDesc = `If exists: ${condition.locator_type}="${condition.locator_value}" (timeout: ${condition.timeout || 10}s)`;
                    break;
                case 'not_exists':
                    conditionDesc = `If NOT exists: ${condition.locator_type}="${condition.locator_value}" (timeout: ${condition.timeout || 10}s)`;
                    break;
                case 'visible':
                    conditionDesc = `If visible: ${condition.locator_type}="${condition.locator_value}" (timeout: ${condition.timeout || 10}s)`;
                    break;
                case 'contains_text':
                    conditionDesc = `If element ${condition.locator_type}="${condition.locator_value}" contains text: "${condition.text}" (timeout: ${condition.timeout || 10}s)`;
                    break;
                case 'value_equals':
                    conditionDesc = `If element ${condition.locator_type}="${condition.locator_value}" value equals: "${condition.expected_value}" (timeout: ${condition.timeout || 10}s)`;
                    break;
                case 'value_contains':
                    conditionDesc = `If element ${condition.locator_type}="${condition.locator_value}" value contains: "${condition.expected_value}" (timeout: ${condition.timeout || 10}s)`;
                    break;
                case 'has_attribute':
                    conditionDesc = `If element ${condition.locator_type}="${condition.locator_value}" has attribute: "${condition.attribute_name}"${condition.attribute_value ? ` with value: "${condition.attribute_value}"` : ''} (timeout: ${condition.timeout || 10}s)`;
                    break;
                case 'screen_contains':
                    conditionDesc = `If screen contains image: "${condition.image}" (threshold: ${condition.threshold || 0.7}, timeout: ${condition.timeout || 10}s)`;
                    break;
                default:
                    conditionDesc = `If ${actionData.condition_type} condition`;
                    break;
            }

            // Then action description
            const thenAction = actionData.then_action || {};
            switch (thenAction.type) {
                case 'tap':
                    if (thenAction.method === 'locator' || thenAction.locator_type) {
                        thenActionDesc = `Then tap on element with ${thenAction.locator_type}: ${thenAction.locator_value}`;
                    } else if (thenAction.method === 'image' || thenAction.image_filename) {
                        thenActionDesc = `Then tap on image: ${thenAction.image_filename}`;
                    } else if (typeof thenAction.x !== 'undefined' && typeof thenAction.y !== 'undefined') {
                        thenActionDesc = `Then tap at (${thenAction.x}, ${thenAction.y})`;
                    } else {
                        // Handle the case where coordinates might be undefined
                        const x = thenAction.x !== undefined ? thenAction.x : 'unspecified';
                        const y = thenAction.y !== undefined ? thenAction.y : 'unspecified';
                        thenActionDesc = `Then tap ${thenAction.element_description || `at location (${x}, ${y})`}`;
                    }
                    break;
                case 'doubleTap':
                    if (thenAction.method === 'locator' || thenAction.locator_type) {
                        thenActionDesc = `Then double tap on element with ${thenAction.locator_type}: ${thenAction.locator_value}`;
                    } else if (thenAction.method === 'image' || thenAction.image_filename) {
                        thenActionDesc = `Then double tap on image: ${thenAction.image_filename}`;
                    } else if (typeof thenAction.x !== 'undefined' && typeof thenAction.y !== 'undefined') {
                        thenActionDesc = `Then double tap at (${thenAction.x}, ${thenAction.y})`;
                    } else {
                        // Handle the case where coordinates might be undefined
                        thenActionDesc = `Then double tap at location`;
                    }
                    break;
                case 'swipe':
                    // Check if all coordinates are available
                    if (typeof thenAction.start_x !== 'undefined' && typeof thenAction.start_y !== 'undefined' &&
                        typeof thenAction.end_x !== 'undefined' && typeof thenAction.end_y !== 'undefined') {
                        thenActionDesc = `Then swipe from (${thenAction.start_x}, ${thenAction.start_y}) to (${thenAction.end_x}, ${thenAction.end_y})`;
                    } else {
                        thenActionDesc = `Then swipe`;
                    }
                    break;
                case 'text':
                    thenActionDesc = `Then input text: "${thenAction.text || ''}"`;
                    break;
                case 'key':
                    thenActionDesc = `Then press key: ${thenAction.key || 'unspecified'}`;
                    break;
                case 'wait':
                    thenActionDesc = `Then wait for ${thenAction.time || 'unspecified'}ms`;
                    break;
                case 'hideKeyboard':
                    thenActionDesc = `Then hide keyboard`;
                    break;
                case 'clickElement':
                    thenActionDesc = `Then click element: ${thenAction.locator_type || 'unspecified'}="${thenAction.locator_value || 'unspecified'}"`;
                    break;
                case 'waitTill':
                    thenActionDesc = `Then wait till: ${thenAction.locator_type || 'unspecified'}="${thenAction.locator_value || 'unspecified'}"`;
                    break;
                case 'swipeTillVisible':
                    thenActionDesc = `Then swipe ${thenAction.direction || 'up'} till visible (max ${thenAction.count || 5} swipes)`;
                    break;
                case 'textClear':
                    thenActionDesc = `Then clear and input text: "${thenAction.text || ''}"`;
                    break;
                case 'iosFunctions':
                    thenActionDesc = `Then execute iOS function: ${thenAction.function_name || 'unspecified'}`;
                    break;
                case 'androidFunctions':
                    thenActionDesc = `Then execute Android function: ${thenAction.function_name || 'unspecified'}`;
                    break;
                default:
                    thenActionDesc = thenAction.type ? `Then perform action: ${thenAction.type}` : "No action";
                    break;
            }

            return `${conditionDesc} → ${thenActionDesc}`;

        case 'multiStep':
            const testCaseName = actionData.test_case_name || 'Unknown Test Case';
            const stepsCount = actionData.test_case_steps_count ||
                              (actionData.test_case_steps ? actionData.test_case_steps.length : 0);
            return `Execute Test Case: ${testCaseName} (${stepsCount} steps)`;
            
        case 'repeatSteps':
            const repeatTestCaseName = actionData.test_case_name || 'Unknown Test Case';
            const repeatStepsCount = actionData.test_case_steps_count ||
                                   (actionData.test_case_steps ? actionData.test_case_steps.length : 0);
            const repeatCount = actionData.repeat_count || 1;
            return `Repeat Test Case: ${repeatTestCaseName} (${repeatStepsCount} steps) ${repeatCount} times`;

        case 'hookAction':
            const hookType = actionData.hook_type || 'Unknown';
            let hookDescription = `Hook Action: ${hookType}`;

            // Add more details based on the hook type
            switch (hookType) {
                case 'tap':
                    if (actionData.hook_data.method === 'locator') {
                        hookDescription += ` on element with ${actionData.hook_data.locator_type}: ${actionData.hook_data.locator_value}`;
                    } else if (actionData.hook_data.method === 'image') {
                        hookDescription += ` on image: ${actionData.hook_data.image_filename}`;
                    } else {
                        hookDescription += ` at (${actionData.hook_data.x}, ${actionData.hook_data.y})`;
                    }
                    break;
                case 'wait':
                    hookDescription += ` for ${actionData.hook_data.duration}s`;
                    break;
                case 'text':
                    hookDescription += `: "${actionData.hook_data.text}"`;
                    break;
                case 'launchApp':
                case 'restartApp':
                case 'terminateApp':
                    hookDescription += `: ${actionData.hook_data.package_id}`;
                    break;
                case 'iosFunctions':
                    hookDescription += `: ${actionData.hook_data.function_name}`;
                    break;
                case 'androidFunctions':
                    hookDescription += `: ${actionData.hook_data.function_name}`;
                    break;
            }

            return hookDescription + ' (Recovery)';

        case 'takeScreenshot':
            return `Take Screenshot: "${actionData.screenshot_name || 'unnamed'}"`;

        case 'swipeTillVisible':
            let swipeTillDesc = `Swipe ${actionData.direction || 'up'} till `;
            if (actionData.image_filename) {
                swipeTillDesc += `image "${actionData.image_filename}" is visible`;
            } else if (actionData.locator_type === 'image') {
                swipeTillDesc += `image "${actionData.locator_value}" is visible`;
            } else if (actionData.locator_type && actionData.locator_value) {
                swipeTillDesc += `element ${actionData.locator_type}: "${actionData.locator_value}" is visible`;
            } else {
                swipeTillDesc += `element is visible`;
            }
            if (actionData.max_swipes) {
                swipeTillDesc += ` (max ${actionData.max_swipes} swipes)`;
            }
            return swipeTillDesc;

        case 'iosFunctions':
            let iosDesc = `iOS Function: ${actionData.function_name || 'unspecified'}`;
            if (actionData.text) {
                iosDesc += ` - Text: "${actionData.text}"`;
            }
            if (actionData.locator_type && actionData.locator_value) {
                iosDesc += ` - ${actionData.locator_type}: "${actionData.locator_value}"`;
            }
            if (actionData.x !== undefined && actionData.y !== undefined) {
                iosDesc += ` - Coordinates: (${actionData.x}, ${actionData.y})`;
            }
            return iosDesc;

        case 'androidFunctions':
            let androidDesc = `Android Function: ${actionData.function_name || 'unspecified'}`;
            if (actionData.key_event) {
                androidDesc += ` - Key Event: ${actionData.key_event}`;
            }
            if (actionData.context) {
                androidDesc += ` - Context: ${actionData.context}`;
            }
            if (actionData.package_name) {
                androidDesc += ` - Package: ${actionData.package_name}`;
            }
            if (actionData.connectivity_type) {
                androidDesc += ` - ${actionData.connectivity_type}: ${actionData.state ? 'Enable' : 'Disable'}`;
            }
            if (actionData.content) {
                androidDesc += ` - Content: "${actionData.content}"`;
            }
            if (actionData.image_path) {
                androidDesc += ` - Image: ${actionData.image_path}`;
            }
            return androidDesc;

        case 'wait':
            return `Wait for ${actionData.time || actionData.duration || 'unspecified'} ms`;

        case 'clickElement':
            return `Click element: ${actionData.locator_type}="${actionData.locator_value}" (timeout: ${actionData.timeout || 10}s)`;

        case 'waitTill':
            if (actionData.locator_type === 'image') {
                return `Wait till image appears: ${actionData.image_filename || actionData.locator_value} (timeout: ${actionData.timeout || 10}s)`;
            } else if (actionData.locator_type === 'text') {
                return `Wait till text appears: "${actionData.locator_value}" (timeout: ${actionData.timeout || 10}s)`;
            } else {
                return `Wait till ${actionData.locator_type}="${actionData.locator_value}" (timeout: ${actionData.timeout || 10}s)`;
            }

        case 'launchApp':
            return `Launch app: ${actionData.package || actionData.package_id || 'unspecified'}`;

        case 'terminateApp':
            return `Terminate app: ${actionData.package || actionData.package_id || 'unspecified'}`;

        case 'restartApp':
            return `Restart app: ${actionData.package || actionData.package_id || 'unspecified'}`;

        case 'tapOnText':
            return `Tap on Text: "${actionData.text_to_find || actionData.text}"${actionData.double_tap ? ' (Double Tap)' : ''}`;

        case 'textClear':
            return `Clear and input text: "${actionData.text || actionData.input_text || 'unspecified'}"`;

        case 'key':
            return `Press key: ${actionData.key || 'unspecified'}`;

        case 'hideKeyboard':
            return `Hide keyboard`;

        case 'INFO':
            // Use parsed text from execution result if available, otherwise use raw text
            const infoText = actionData.info_text || actionData.text || 'No information text';
            return `INFO: ${infoText}`;

        default:
            return `Action: ${actionData.type}`;
    }
}