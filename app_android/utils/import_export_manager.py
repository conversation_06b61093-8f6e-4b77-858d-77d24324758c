"""
Import/Export Manager for Test Cases and Test Suites (Android)
Handles ZIP file creation/extraction and database synchronization
"""

import os
import json
import zipfile
import tempfile
import shutil
import uuid
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

from .database import (
    save_test_suite, get_db_path, init_db
)
from .test_case_manager import TestCaseManager
from test_suites_manager import TestSuitesManager

logger = logging.getLogger(__name__)

class ImportExportManager:
    """Manages import and export operations for test cases and test suites"""
    
    def __init__(self, test_cases_dir: str, test_suites_dir: str):
        self.test_cases_dir = Path(test_cases_dir)
        self.test_suites_dir = Path(test_suites_dir)
        self.test_case_manager = TestCaseManager(test_cases_dir)
        self.test_suites_manager = TestSuitesManager()
        
        # Ensure directories exist
        self.test_cases_dir.mkdir(parents=True, exist_ok=True)
        self.test_suites_dir.mkdir(parents=True, exist_ok=True)
    
    def export_test_cases(self, output_path: str) -> bool:
        """
        Export all test cases to a ZIP file
        
        Args:
            output_path: Path where the ZIP file should be created
            
        Returns:
            bool: True if export was successful, False otherwise
        """
        try:
            logger.info(f"Starting test cases export to {output_path}")
            
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add all JSON test case files
                for json_file in self.test_cases_dir.glob('*.json'):
                    if not json_file.name.endswith('.bak'):
                        zipf.write(json_file, f"test_cases/{json_file.name}")
                        logger.debug(f"Added {json_file.name} to export")
                
                # Create metadata file with export info
                metadata = {
                    "export_type": "test_cases",
                    "export_timestamp": datetime.now().isoformat(),
                    "total_files": len(list(self.test_cases_dir.glob('*.json'))),
                    "version": "1.0"
                }
                
                # Add metadata to ZIP
                metadata_json = json.dumps(metadata, indent=2)
                zipf.writestr("export_metadata.json", metadata_json)
            
            logger.info(f"Successfully exported test cases to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting test cases: {str(e)}")
            return False
    
    def import_test_cases(self, zip_path: str, conflict_resolution: str = "skip") -> Dict[str, Any]:
        """
        Import test cases from a ZIP file
        
        Args:
            zip_path: Path to the ZIP file containing test cases
            conflict_resolution: How to handle conflicts ("skip", "overwrite", "rename")
            
        Returns:
            Dict containing import results and statistics
        """
        try:
            logger.info(f"Starting test cases import from {zip_path}")
            
            results = {
                "success": True,
                "imported": 0,
                "skipped": 0,
                "errors": 0,
                "conflicts": [],
                "error_details": []
            }
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extract ZIP file
                with zipfile.ZipFile(zip_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                
                # Look for test cases in the extracted files
                test_cases_path = Path(temp_dir) / "test_cases"
                if not test_cases_path.exists():
                    # Try root directory if no test_cases folder
                    test_cases_path = Path(temp_dir)
                
                # Process each JSON file
                for json_file in test_cases_path.glob('*.json'):
                    if json_file.name == "export_metadata.json":
                        continue
                    
                    try:
                        # Load test case data
                        with open(json_file, 'r') as f:
                            test_case_data = json.load(f)
                        
                        # Check for conflicts
                        target_path = self.test_cases_dir / json_file.name
                        if target_path.exists():
                            results["conflicts"].append(json_file.name)
                            
                            if conflict_resolution == "skip":
                                results["skipped"] += 1
                                logger.info(f"Skipped existing test case: {json_file.name}")
                                continue
                            elif conflict_resolution == "rename":
                                # Generate new filename with timestamp
                                timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                                name_parts = json_file.stem.split('_')
                                if len(name_parts) > 1:
                                    new_name = f"{name_parts[0]}_imported_{timestamp}.json"
                                else:
                                    new_name = f"{json_file.stem}_imported_{timestamp}.json"
                                target_path = self.test_cases_dir / new_name
                        
                        # Copy file to test cases directory
                        shutil.copy2(json_file, target_path)
                        
                        # Update database
                        self._sync_test_case_to_db(test_case_data, target_path.name)
                        
                        results["imported"] += 1
                        logger.info(f"Imported test case: {target_path.name}")
                        
                    except Exception as e:
                        results["errors"] += 1
                        results["error_details"].append(f"{json_file.name}: {str(e)}")
                        logger.error(f"Error importing {json_file.name}: {str(e)}")
            
            logger.info(f"Import completed. Imported: {results['imported']}, Skipped: {results['skipped']}, Errors: {results['errors']}")
            return results
            
        except Exception as e:
            logger.error(f"Error during test cases import: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "imported": 0,
                "skipped": 0,
                "errors": 1,
                "conflicts": [],
                "error_details": [str(e)]
            }
    
    def export_test_suites(self, output_path: str) -> bool:
        """
        Export all test suites to a ZIP file
        
        Args:
            output_path: Path where the ZIP file should be created
            
        Returns:
            bool: True if export was successful, False otherwise
        """
        try:
            logger.info(f"Starting test suites export to {output_path}")
            
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add all JSON test suite files
                for json_file in self.test_suites_dir.glob('*.json'):
                    zipf.write(json_file, f"test_suites/{json_file.name}")
                    logger.debug(f"Added {json_file.name} to export")
                
                # Create metadata file with export info
                metadata = {
                    "export_type": "test_suites",
                    "export_timestamp": datetime.now().isoformat(),
                    "total_files": len(list(self.test_suites_dir.glob('*.json'))),
                    "version": "1.0"
                }
                
                # Add metadata to ZIP
                metadata_json = json.dumps(metadata, indent=2)
                zipf.writestr("export_metadata.json", metadata_json)
            
            logger.info(f"Successfully exported test suites to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting test suites: {str(e)}")
            return False
    
    def import_test_suites(self, zip_path: str, conflict_resolution: str = "skip") -> Dict[str, Any]:
        """
        Import test suites from a ZIP file
        
        Args:
            zip_path: Path to the ZIP file containing test suites
            conflict_resolution: How to handle conflicts ("skip", "overwrite", "rename")
            
        Returns:
            Dict containing import results and statistics
        """
        try:
            logger.info(f"Starting test suites import from {zip_path}")
            
            results = {
                "success": True,
                "imported": 0,
                "skipped": 0,
                "errors": 0,
                "conflicts": [],
                "error_details": []
            }
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extract ZIP file
                with zipfile.ZipFile(zip_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                
                # Look for test suites in the extracted files
                test_suites_path = Path(temp_dir) / "test_suites"
                if not test_suites_path.exists():
                    # Try root directory if no test_suites folder
                    test_suites_path = Path(temp_dir)
                
                # Process each JSON file
                for json_file in test_suites_path.glob('*.json'):
                    if json_file.name == "export_metadata.json":
                        continue
                    
                    try:
                        # Load test suite data
                        with open(json_file, 'r') as f:
                            test_suite_data = json.load(f)
                        
                        # Check for conflicts
                        target_path = self.test_suites_dir / json_file.name
                        if target_path.exists():
                            results["conflicts"].append(json_file.name)
                            
                            if conflict_resolution == "skip":
                                results["skipped"] += 1
                                logger.info(f"Skipped existing test suite: {json_file.name}")
                                continue
                            elif conflict_resolution == "rename":
                                # Generate new UUID for renamed suite
                                new_uuid = str(uuid.uuid4())
                                target_path = self.test_suites_dir / f"{new_uuid}.json"
                                test_suite_data["id"] = new_uuid
                                test_suite_data["name"] = f"{test_suite_data.get('name', 'Imported Suite')} (Imported)"
                        
                        # Copy file to test suites directory
                        shutil.copy2(json_file, target_path)
                        
                        # Update database
                        self._sync_test_suite_to_db(test_suite_data)
                        
                        results["imported"] += 1
                        logger.info(f"Imported test suite: {target_path.name}")
                        
                    except Exception as e:
                        results["errors"] += 1
                        results["error_details"].append(f"{json_file.name}: {str(e)}")
                        logger.error(f"Error importing {json_file.name}: {str(e)}")
            
            logger.info(f"Import completed. Imported: {results['imported']}, Skipped: {results['skipped']}, Errors: {results['errors']}")
            return results
            
        except Exception as e:
            logger.error(f"Error during test suites import: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "imported": 0,
                "skipped": 0,
                "errors": 1,
                "conflicts": [],
                "error_details": [str(e)]
            }

    def _sync_test_case_to_db(self, test_case_data: Dict[str, Any], filename: str):
        """Synchronize imported test case to database"""
        try:
            import sqlite3
            from .database import get_db_path

            # Generate a unique suite_id for the imported test case
            import uuid
            suite_id = str(uuid.uuid4())

            # Connect to database
            db_path = get_db_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Insert test case into test_cases table
            test_case_name = test_case_data.get('name', 'Imported Test Case')
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute('''
                INSERT OR REPLACE INTO test_cases
                (suite_id, test_idx, name, status, duration, timestamp, retry_count, max_retries, error)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (suite_id, 0, test_case_name, 'imported', '0ms', timestamp, 0, 0, None))

            # Insert test steps into test_steps table
            actions = test_case_data.get('actions', [])
            for step_idx, action in enumerate(actions):
                action_id = action.get('action_id', f'imported_{step_idx}')
                action_type = action.get('type', 'unknown')
                action_name = f"{action_type} - {action_id}"

                cursor.execute('''
                    INSERT OR REPLACE INTO test_steps
                    (suite_id, test_idx, step_idx, name, action_type, action_id, status, duration, timestamp, screenshot_path, error, enabled)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (suite_id, 0, step_idx, action_name, action_type, action_id, 'imported', '0ms', timestamp, None, None, 1))

            conn.commit()
            conn.close()

            logger.info(f"Successfully synced test case {filename} to database with suite_id {suite_id}")

        except Exception as e:
            logger.error(f"Error syncing test case {filename} to database: {str(e)}")

    def _sync_test_suite_to_db(self, test_suite_data: Dict[str, Any]):
        """Synchronize imported test suite to database"""
        try:
            import sqlite3
            from .database import get_db_path

            # Connect to database
            db_path = get_db_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Insert test suite into test_suites table
            suite_id = test_suite_data.get('id')
            suite_name = test_suite_data.get('name', 'Imported Test Suite')
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute('''
                INSERT OR REPLACE INTO test_suites
                (suite_id, name, status, passed, failed, skipped, timestamp, report_dir, error)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (suite_id, suite_name, 'imported', 0, 0, 0, timestamp, None, None))

            # Insert test cases referenced by the suite
            test_cases = test_suite_data.get('test_cases', [])
            for test_idx, test_case_filename in enumerate(test_cases):
                # Try to load the test case to get its name
                test_case_name = test_case_filename
                try:
                    test_case_path = self.test_cases_dir / test_case_filename
                    if test_case_path.exists():
                        with open(test_case_path, 'r') as f:
                            test_case_data = json.load(f)
                            test_case_name = test_case_data.get('name', test_case_filename)
                except Exception:
                    pass  # Use filename if we can't load the test case

                cursor.execute('''
                    INSERT OR REPLACE INTO test_cases
                    (suite_id, test_idx, name, status, duration, timestamp, retry_count, max_retries, error)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (suite_id, test_idx, test_case_name, 'imported', '0ms', timestamp, 0, 0, None))

            conn.commit()
            conn.close()

            logger.info(f"Successfully synced test suite {suite_id} to database with {len(test_cases)} test cases")

        except Exception as e:
            logger.error(f"Error syncing test suite to database: {str(e)}")


class BulkModificationManager:
    """Manages bulk modification operations for test cases"""

    def __init__(self, test_cases_dir: str):
        self.test_cases_dir = Path(test_cases_dir)
        self.test_case_manager = TestCaseManager(test_cases_dir)

    def preview_modifications(self, modification_rules: List[str]) -> Dict[str, Any]:
        """
        Preview the changes that would be made by bulk modification rules

        Args:
            modification_rules: List of modification rule strings

        Returns:
            Dict containing preview results
        """
        try:
            preview_results = {
                "success": True,
                "total_test_cases": 0,
                "affected_test_cases": 0,
                "modifications": [],
                "errors": []
            }

            # Parse modification rules
            parsed_rules = []
            for rule in modification_rules:
                try:
                    parsed_rule = self._parse_modification_rule(rule)
                    if parsed_rule:
                        parsed_rules.append(parsed_rule)
                except Exception as e:
                    preview_results["errors"].append(f"Rule parsing error: {rule} - {str(e)}")

            # Process each test case
            for json_file in self.test_cases_dir.glob('*.json'):
                if json_file.name.endswith('.bak'):
                    continue

                try:
                    with open(json_file, 'r') as f:
                        test_case_data = json.load(f)

                    preview_results["total_test_cases"] += 1

                    # Apply rules to this test case
                    modifications_for_case = []
                    for rule in parsed_rules:
                        modifications = self._apply_rule_preview(test_case_data, rule)
                        if modifications:
                            modifications_for_case.extend(modifications)

                    if modifications_for_case:
                        preview_results["affected_test_cases"] += 1
                        preview_results["modifications"].append({
                            "test_case": json_file.name,
                            "test_case_name": test_case_data.get('name', 'Unnamed'),
                            "changes": modifications_for_case
                        })

                except Exception as e:
                    preview_results["errors"].append(f"Error processing {json_file.name}: {str(e)}")

            return preview_results

        except Exception as e:
            logger.error(f"Error during modification preview: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "total_test_cases": 0,
                "affected_test_cases": 0,
                "modifications": [],
                "errors": [str(e)]
            }

    def apply_modifications(self, modification_rules: List[str], create_backup: bool = True) -> Dict[str, Any]:
        """
        Apply bulk modification rules to test cases

        Args:
            modification_rules: List of modification rule strings
            create_backup: Whether to create backup files before modification

        Returns:
            Dict containing application results
        """
        try:
            results = {
                "success": True,
                "total_test_cases": 0,
                "modified_test_cases": 0,
                "modified_files": [],
                "backup_files": [],
                "errors": []
            }

            # Parse modification rules
            parsed_rules = []
            for rule in modification_rules:
                try:
                    parsed_rule = self._parse_modification_rule(rule)
                    if parsed_rule:
                        parsed_rules.append(parsed_rule)
                except Exception as e:
                    results["errors"].append(f"Rule parsing error: {rule} - {str(e)}")

            if not parsed_rules:
                results["errors"].append("No valid modification rules found")
                return results

            # Process each test case
            for json_file in self.test_cases_dir.glob('*.json'):
                if json_file.name.endswith('.bak'):
                    continue

                try:
                    with open(json_file, 'r') as f:
                        original_data = json.load(f)

                    results["total_test_cases"] += 1
                    modified_data = original_data.copy()

                    # Apply rules to this test case
                    case_modified = False
                    for rule in parsed_rules:
                        if self._apply_rule_to_case(modified_data, rule):
                            case_modified = True

                    if case_modified:
                        # Create backup if requested
                        if create_backup:
                            backup_path = json_file.with_suffix('.bak')
                            shutil.copy2(json_file, backup_path)
                            results["backup_files"].append(str(backup_path))

                        # Save modified test case
                        with open(json_file, 'w') as f:
                            json.dump(modified_data, f, indent=2)

                        results["modified_test_cases"] += 1
                        results["modified_files"].append(json_file.name)
                        logger.info(f"Modified test case: {json_file.name}")

                except Exception as e:
                    results["errors"].append(f"Error modifying {json_file.name}: {str(e)}")

            # Update database for modified files
            if results["modified_files"]:
                self.update_database_after_modifications(results["modified_files"])

            return results

        except Exception as e:
            logger.error(f"Error during bulk modification: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "total_test_cases": 0,
                "modified_test_cases": 0,
                "backup_files": [],
                "errors": [str(e)]
            }

    def _parse_modification_rule(self, rule: str) -> Optional[Dict[str, Any]]:
        """Parse a modification rule string into a structured format"""
        try:
            rule = rule.strip()

            # Parse "Add step" rules
            if rule.startswith("Add step"):
                return self._parse_add_step_rule(rule)

            # Parse "Replace" rules
            elif rule.startswith("Replace"):
                return self._parse_replace_rule(rule)

            else:
                logger.warning(f"Unsupported rule format: {rule}")
                return None

        except Exception as e:
            logger.error(f"Error parsing rule '{rule}': {str(e)}")
            return None

    def _parse_add_step_rule(self, rule: str) -> Optional[Dict[str, Any]]:
        """Parse 'Add step' type rules"""
        import re

        # Pattern: Add step 'ACTION_TYPE' with PARAMETERS when/after CONDITION
        pattern = r"Add step ['\"]([^'\"]+)['\"] with (.+?) (?:when|after) (.+)"
        match = re.match(pattern, rule, re.IGNORECASE)

        if not match:
            return None

        action_type = match.group(1)
        parameters_str = match.group(2)
        condition_str = match.group(3)

        # Parse parameters
        parameters = self._parse_parameters(parameters_str)

        # Parse condition
        condition = self._parse_condition(condition_str)

        return {
            "type": "add_step",
            "action_type": action_type,
            "parameters": parameters,
            "condition": condition,
            "position": "after" if "after" in rule.lower() else "when"
        }

    def _parse_replace_rule(self, rule: str) -> Optional[Dict[str, Any]]:
        """Parse 'Replace' type rules"""
        import re

        # Pattern: Replace 'OLD_VALUE' with 'NEW_VALUE' in FIELD
        pattern = r"Replace ['\"]([^'\"]+)['\"] with ['\"]([^'\"]+)['\"] in (.+)"
        match = re.match(pattern, rule, re.IGNORECASE)

        if not match:
            return None

        old_value = match.group(1)
        new_value = match.group(2)
        field = match.group(3).strip()

        return {
            "type": "replace",
            "old_value": old_value,
            "new_value": new_value,
            "field": field
        }

    def _parse_parameters(self, parameters_str: str) -> Dict[str, Any]:
        """Parse parameter string into dictionary"""
        import re

        parameters = {}

        # Handle different parameter formats
        # Format: key 'value', key2 'value2'
        param_pattern = r"(\w+)\s+['\"]([^'\"]+)['\"]"
        matches = re.findall(param_pattern, parameters_str)

        for key, value in matches:
            parameters[key] = value

        return parameters

    def _parse_condition(self, condition_str: str) -> Dict[str, Any]:
        """Parse condition string into structured format"""
        import re

        condition_str = condition_str.strip()

        # Handle different condition formats
        if "test case contains" in condition_str.lower():
            # Extract what the test case should contain
            pattern = r"test case contains ['\"]([^'\"]+)['\"]"
            match = re.search(pattern, condition_str, re.IGNORECASE)
            if match:
                return {
                    "type": "contains",
                    "value": match.group(1)
                }

        elif "step that" in condition_str.lower():
            # Extract step condition
            pattern = r"step that (.+)"
            match = re.search(pattern, condition_str, re.IGNORECASE)
            if match:
                step_condition = match.group(1).strip()
                return {
                    "type": "step_condition",
                    "value": step_condition
                }

        return {
            "type": "raw",
            "value": condition_str
        }

    def create_backups(self, files: List[str]) -> Dict[str, Any]:
        """
        Create backup files for specified test cases

        Args:
            files: List of test case filenames to backup

        Returns:
            Dict containing backup results
        """
        try:
            results = {
                "backup_count": 0,
                "backup_files": [],
                "errors": []
            }

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            for filename in files:
                try:
                    source_path = self.test_cases_dir / filename
                    if not source_path.exists():
                        results["errors"].append(f"File not found: {filename}")
                        continue

                    # Create backup with timestamp
                    backup_filename = f"{source_path.stem}_backup_{timestamp}.json"
                    backup_path = self.test_cases_dir / backup_filename

                    shutil.copy2(source_path, backup_path)

                    results["backup_count"] += 1
                    results["backup_files"].append(backup_filename)
                    logger.info(f"Created backup: {backup_filename}")

                except Exception as e:
                    results["errors"].append(f"Error backing up {filename}: {str(e)}")

            return results

        except Exception as e:
            logger.error(f"Error creating backups: {str(e)}")
            return {
                "backup_count": 0,
                "backup_files": [],
                "errors": [str(e)]
            }

    def revert_from_backups(self, files: List[str]) -> Dict[str, Any]:
        """
        Revert test cases from their backup files

        Args:
            files: List of test case filenames to revert

        Returns:
            Dict containing revert results
        """
        try:
            results = {
                "reverted_count": 0,
                "reverted_files": [],
                "errors": []
            }

            for filename in files:
                try:
                    # Find the most recent backup file for this test case
                    base_name = Path(filename).stem
                    backup_pattern = f"{base_name}_backup_*.json"
                    backup_files = list(self.test_cases_dir.glob(backup_pattern))

                    if not backup_files:
                        results["errors"].append(f"No backup found for: {filename}")
                        continue

                    # Get the most recent backup (sort by filename which includes timestamp)
                    most_recent_backup = sorted(backup_files)[-1]

                    # Restore from backup
                    target_path = self.test_cases_dir / filename
                    shutil.copy2(most_recent_backup, target_path)

                    results["reverted_count"] += 1
                    results["reverted_files"].append(filename)
                    logger.info(f"Reverted {filename} from backup: {most_recent_backup.name}")

                except Exception as e:
                    results["errors"].append(f"Error reverting {filename}: {str(e)}")

            return results

        except Exception as e:
            logger.error(f"Error reverting from backups: {str(e)}")
            return {
                "reverted_count": 0,
                "reverted_files": [],
                "errors": [str(e)]
            }
