"""
Device management routes
"""
import os
import json
import subprocess
import sys
from pathlib import Path

# Add the app directory to the path
app_dir = str(Path(__file__).resolve().parent.parent)
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

from flask import Blueprint, jsonify, request
from app_android.utils.appium_device_controller import AppiumDeviceController

devices_bp = Blueprint('devices', __name__, url_prefix='/api/devices')

# Device controller will be initialized lazily
device_controller = None

def get_device_controller():
    """Get or create device controller with configured ports"""
    global device_controller
    if device_controller is None:
        try:
            # Add parent directory to path to import config
            parent_dir = str(Path(__file__).resolve().parent.parent.parent)
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
            import config
            appium_port = getattr(config, 'APPIUM_PORT', 4723)
            wda_port = getattr(config, 'WDA_PORT', 8100)
            device_controller = AppiumDeviceController(appium_port=appium_port, wda_port=wda_port)
        except ImportError:
            device_controller = AppiumDeviceController()
    return device_controller

@devices_bp.route('', methods=['GET'])
def get_devices():
    """Get all connected Android devices"""
    try:
        # Get Android devices using adb devices
        devices = []
        try:
            adb_output = subprocess.run(
                ['adb', 'devices', '-l'],
                capture_output=True,
                text=True,
                check=True
            ).stdout

            # Parse adb devices output
            lines = adb_output.strip().split('\n')[1:]  # Skip header line
            for line in lines:
                if line.strip() and 'device' in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        device_id = parts[0]
                        status = parts[1]

                        if status == 'device':  # Only include connected devices
                            # Get device name and properties
                            try:
                                # Get device model
                                device_model = subprocess.run(
                                    ['adb', '-s', device_id, 'shell', 'getprop', 'ro.product.model'],
                                    capture_output=True,
                                    text=True
                                ).stdout.strip()

                                # Get Android version
                                android_version = subprocess.run(
                                    ['adb', '-s', device_id, 'shell', 'getprop', 'ro.build.version.release'],
                                    capture_output=True,
                                    text=True
                                ).stdout.strip()

                                # Get device manufacturer
                                manufacturer = subprocess.run(
                                    ['adb', '-s', device_id, 'shell', 'getprop', 'ro.product.manufacturer'],
                                    capture_output=True,
                                    text=True
                                ).stdout.strip()

                                device_name = f"{manufacturer} {device_model}".strip()

                                devices.append({
                                    'id': device_id,
                                    'udid': device_id,
                                    'name': device_name or f"Android Device ({device_id})",
                                    'platform': 'Android',
                                    'osVersion': android_version,
                                    'model': device_model,
                                    'manufacturer': manufacturer,
                                    'status': 'Online',
                                    'type': 'real'
                                })
                            except Exception as e:
                                # Add with limited info
                                devices.append({
                                    'id': device_id,
                                    'udid': device_id,
                                    'name': f"Android Device ({device_id})",
                                    'platform': 'Android',
                                    'osVersion': 'Unknown',
                                    'model': 'Unknown',
                                    'manufacturer': 'Unknown',
                                    'status': 'Online',
                                    'type': 'real'
                                })
        except Exception as e:
            # Log the error but continue
            print(f"Error getting Android devices: {e}")

        return jsonify({"devices": devices})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@devices_bp.route('/<device_id>', methods=['GET'])
def get_device(device_id):
    """Get a specific Android device by ID"""
    try:
        # Try to get device info
        device_info = {
            'id': device_id,
            'udid': device_id,
            'platform': 'Android',
            'status': 'Unknown'
        }

        try:
            # Get device model
            device_model = subprocess.run(
                ['adb', '-s', device_id, 'shell', 'getprop', 'ro.product.model'],
                capture_output=True,
                text=True
            ).stdout.strip()
            device_info['model'] = device_model

            # Get Android version
            android_version = subprocess.run(
                ['adb', '-s', device_id, 'shell', 'getprop', 'ro.build.version.release'],
                capture_output=True,
                text=True
            ).stdout.strip()
            device_info['osVersion'] = android_version

            # Get device manufacturer
            manufacturer = subprocess.run(
                ['adb', '-s', device_id, 'shell', 'getprop', 'ro.product.manufacturer'],
                capture_output=True,
                text=True
            ).stdout.strip()
            device_info['manufacturer'] = manufacturer

            device_name = f"{manufacturer} {device_model}".strip()
            device_info['name'] = device_name or f"Android Device ({device_id})"

            # Check if device is online
            device_info['status'] = 'Online'
        except Exception as e:
            print(f"Error getting detailed info for Android device {device_id}: {e}")

        return jsonify(device_info)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@devices_bp.route('/scan', methods=['POST'])
def scan_devices():
    """Scan for available devices"""
    try:
        # Trigger a rescan of devices
        return get_devices()
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@devices_bp.route('/connect', methods=['POST'])
def connect_device():
    """Connect to a device"""
    try:
        device_info = request.json
        # In a real implementation, you would connect to the device here
        return jsonify({"success": True, "message": "Device connected successfully"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@devices_bp.route('/<device_id>/disconnect', methods=['POST'])
def disconnect_device(device_id):
    """Disconnect a device"""
    try:
        # In a real implementation, you would disconnect the device here
        return jsonify({"success": True, "message": f"Device {device_id} disconnected successfully"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@devices_bp.route('/<device_id>/restart', methods=['POST'])
def restart_device(device_id):
    """Restart a device"""
    try:
        controller = get_device_controller()

        # Validate device_id
        if not device_id or device_id.strip() == '':
            return jsonify({
                "success": False,
                "error": "Invalid device ID provided"
            }), 400

        # Check if this is the currently connected device
        if controller.device_id == device_id:
            # Restart the session for the connected device
            print(f"Restarting session for connected device: {device_id}")
            success = controller.restart_device_session()
            if success:
                return jsonify({
                    "success": True,
                    "message": f"Device {device_id} session restarted successfully"
                })
            else:
                return jsonify({
                    "success": False,
                    "error": f"Failed to restart session for device {device_id}"
                }), 500
        else:
            # For non-connected devices, perform a physical restart
            try:
                print(f"Performing physical restart for device: {device_id}")

                # First check if device is available via ADB
                check_result = subprocess.run(
                    ['adb', 'devices'],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if device_id not in check_result.stdout:
                    return jsonify({
                        "success": False,
                        "error": f"Device {device_id} not found in ADB devices list"
                    }), 404

                # For Android devices, use ADB to restart
                print(f"Sending reboot command to device: {device_id}")
                result = subprocess.run(
                    ['adb', '-s', device_id, 'reboot'],
                    capture_output=True,
                    text=True,
                    timeout=15
                )

                if result.returncode == 0:
                    return jsonify({
                        "success": True,
                        "message": f"Device {device_id} restart command sent successfully. Device will reboot and may take 1-2 minutes to come back online."
                    })
                else:
                    error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                    print(f"ADB reboot failed for {device_id}: {error_msg}")
                    return jsonify({
                        "success": False,
                        "error": f"Failed to restart device {device_id}: {error_msg}"
                    }), 500

            except subprocess.TimeoutExpired:
                print(f"Restart command timed out for device: {device_id}")
                return jsonify({
                    "success": False,
                    "error": f"Restart command for device {device_id} timed out"
                }), 500
            except FileNotFoundError:
                return jsonify({
                    "success": False,
                    "error": "ADB command not found. Please ensure Android SDK is installed and ADB is in PATH."
                }), 500
            except Exception as restart_error:
                print(f"Error restarting device {device_id}: {str(restart_error)}")
                return jsonify({
                    "success": False,
                    "error": f"Error restarting device {device_id}: {str(restart_error)}"
                }), 500

    except Exception as e:
        print(f"Unexpected error in restart_device: {str(e)}")
        return jsonify({"error": str(e)}), 500