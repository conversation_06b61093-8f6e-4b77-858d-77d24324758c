#!/usr/bin/env python3
"""
Test script to verify coordinate calculation accuracy for the "Tap on Coordinates" functionality.
This script tests the coordinate mapping between the web interface and actual device coordinates.
"""

import unittest
import json
import requests
import time
from unittest.mock import Mock, patch

class TestCoordinateCalculation(unittest.TestCase):
    """Test cases for coordinate calculation accuracy"""
    
    def setUp(self):
        """Set up test environment"""
        self.base_url = "http://localhost:8081"  # Android app port
        self.test_device_dimensions = {
            "width": 1080,
            "height": 2340
        }
        self.test_image_dimensions = {
            "width": 540,  # Half size for testing scaling
            "height": 1170
        }
        
    def test_coordinate_scaling_calculation(self):
        """Test coordinate scaling calculation logic"""
        # Test data: click at center of image should map to center of device
        image_center_x = self.test_image_dimensions["width"] / 2  # 270
        image_center_y = self.test_image_dimensions["height"] / 2  # 585
        
        # Expected device coordinates (center of device)
        expected_device_x = self.test_device_dimensions["width"] / 2  # 540
        expected_device_y = self.test_device_dimensions["height"] / 2  # 1170
        
        # Calculate scaling factors
        scale_x = self.test_device_dimensions["width"] / self.test_image_dimensions["width"]  # 2.0
        scale_y = self.test_device_dimensions["height"] / self.test_image_dimensions["height"]  # 2.0
        
        # Calculate device coordinates
        calculated_x = round(image_center_x * scale_x)
        calculated_y = round(image_center_y * scale_y)
        
        # Verify calculations
        self.assertEqual(calculated_x, expected_device_x, "X coordinate calculation incorrect")
        self.assertEqual(calculated_y, expected_device_y, "Y coordinate calculation incorrect")
        
        print(f"✓ Center coordinate test passed: ({calculated_x}, {calculated_y})")
        
    def test_corner_coordinates(self):
        """Test coordinate calculation for corner cases"""
        test_cases = [
            # (image_x, image_y, expected_device_x, expected_device_y, description)
            (0, 0, 0, 0, "Top-left corner"),
            (self.test_image_dimensions["width"] - 1, 0, self.test_device_dimensions["width"] - 2, 0, "Top-right corner"),
            (0, self.test_image_dimensions["height"] - 1, 0, self.test_device_dimensions["height"] - 2, "Bottom-left corner"),
            (self.test_image_dimensions["width"] - 1, self.test_image_dimensions["height"] - 1, 
             self.test_device_dimensions["width"] - 2, self.test_device_dimensions["height"] - 2, "Bottom-right corner")
        ]
        
        scale_x = self.test_device_dimensions["width"] / self.test_image_dimensions["width"]
        scale_y = self.test_device_dimensions["height"] / self.test_image_dimensions["height"]
        
        for image_x, image_y, expected_x, expected_y, description in test_cases:
            calculated_x = round(image_x * scale_x)
            calculated_y = round(image_y * scale_y)
            
            # Clamp to device bounds
            calculated_x = max(0, min(calculated_x, self.test_device_dimensions["width"] - 1))
            calculated_y = max(0, min(calculated_y, self.test_device_dimensions["height"] - 1))
            
            self.assertEqual(calculated_x, expected_x, f"{description} X coordinate incorrect")
            self.assertEqual(calculated_y, expected_y, f"{description} Y coordinate incorrect")
            
            print(f"✓ {description} test passed: ({calculated_x}, {calculated_y})")
            
    def test_coordinate_bounds_validation(self):
        """Test coordinate bounds validation and clamping"""
        scale_x = self.test_device_dimensions["width"] / self.test_image_dimensions["width"]
        scale_y = self.test_device_dimensions["height"] / self.test_image_dimensions["height"]
        
        # Test coordinates outside bounds
        test_cases = [
            (-10, -10, "Negative coordinates"),
            (self.test_image_dimensions["width"] + 10, self.test_image_dimensions["height"] + 10, "Coordinates beyond image"),
            (self.test_image_dimensions["width"] * 2, self.test_image_dimensions["height"] * 2, "Far beyond image")
        ]
        
        for image_x, image_y, description in test_cases:
            calculated_x = round(image_x * scale_x)
            calculated_y = round(image_y * scale_y)
            
            # Apply clamping
            clamped_x = max(0, min(calculated_x, self.test_device_dimensions["width"] - 1))
            clamped_y = max(0, min(calculated_y, self.test_device_dimensions["height"] - 1))
            
            # Verify clamping worked
            self.assertGreaterEqual(clamped_x, 0, f"{description}: X coordinate not clamped to minimum")
            self.assertLessEqual(clamped_x, self.test_device_dimensions["width"] - 1, f"{description}: X coordinate not clamped to maximum")
            self.assertGreaterEqual(clamped_y, 0, f"{description}: Y coordinate not clamped to minimum")
            self.assertLessEqual(clamped_y, self.test_device_dimensions["height"] - 1, f"{description}: Y coordinate not clamped to maximum")
            
            print(f"✓ {description} bounds test passed: ({clamped_x}, {clamped_y})")
            
    def test_different_aspect_ratios(self):
        """Test coordinate calculation with different aspect ratios"""
        # Test with different image display sizes
        test_scenarios = [
            {"image_width": 360, "image_height": 780, "description": "Small display"},
            {"image_width": 720, "image_height": 1560, "description": "Medium display"},
            {"image_width": 1080, "image_height": 2340, "description": "Full size display"},
        ]
        
        for scenario in test_scenarios:
            image_width = scenario["image_width"]
            image_height = scenario["image_height"]
            description = scenario["description"]
            
            # Test center coordinates
            image_center_x = image_width / 2
            image_center_y = image_height / 2
            
            scale_x = self.test_device_dimensions["width"] / image_width
            scale_y = self.test_device_dimensions["height"] / image_height
            
            calculated_x = round(image_center_x * scale_x)
            calculated_y = round(image_center_y * scale_y)
            
            expected_x = self.test_device_dimensions["width"] / 2
            expected_y = self.test_device_dimensions["height"] / 2
            
            # Allow small rounding differences
            self.assertAlmostEqual(calculated_x, expected_x, delta=1, 
                                 msg=f"{description}: X coordinate calculation incorrect")
            self.assertAlmostEqual(calculated_y, expected_y, delta=1, 
                                 msg=f"{description}: Y coordinate calculation incorrect")
            
            print(f"✓ {description} aspect ratio test passed: ({calculated_x}, {calculated_y})")
            
    def test_precision_and_rounding(self):
        """Test coordinate calculation precision and rounding"""
        # Test fractional coordinates
        test_cases = [
            (100.3, 200.7, "Fractional coordinates"),
            (50.5, 100.5, "Half-pixel coordinates"),
            (75.1, 150.9, "Small fractional parts"),
        ]
        
        scale_x = self.test_device_dimensions["width"] / self.test_image_dimensions["width"]
        scale_y = self.test_device_dimensions["height"] / self.test_image_dimensions["height"]
        
        for image_x, image_y, description in test_cases:
            calculated_x = round(image_x * scale_x)
            calculated_y = round(image_y * scale_y)
            
            # Verify results are integers
            self.assertIsInstance(calculated_x, int, f"{description}: X coordinate not properly rounded to integer")
            self.assertIsInstance(calculated_y, int, f"{description}: Y coordinate not properly rounded to integer")
            
            # Verify coordinates are reasonable
            self.assertGreaterEqual(calculated_x, 0, f"{description}: X coordinate negative")
            self.assertGreaterEqual(calculated_y, 0, f"{description}: Y coordinate negative")
            
            print(f"✓ {description} precision test passed: ({calculated_x}, {calculated_y})")

def run_coordinate_tests():
    """Run all coordinate calculation tests"""
    print("🧪 Running Coordinate Calculation Tests...")
    print("=" * 50)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCoordinateCalculation)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 50)
    if result.wasSuccessful():
        print("✅ All coordinate calculation tests passed!")
        return True
    else:
        print("❌ Some coordinate calculation tests failed!")
        return False

if __name__ == "__main__":
    success = run_coordinate_tests()
    exit(0 if success else 1)
