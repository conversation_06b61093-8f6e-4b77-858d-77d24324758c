from .base_action import BaseAction
import time

class SwipeAction(BaseAction):
    """Handler for swipe actions using Airtest API"""
    
    def execute(self, params):
        """
        Execute swipe action
        
        Args:
            params: Dictionary containing:
                - vector_start: Starting vector as percentage [x, y] (0-1 range)
                - vector_end: Ending vector as percentage [x, y] (0-1 range)
                - duration: Duration of swipe in milliseconds
                - count: Number of swipes to perform
                - interval: Time interval between swipes in seconds
                
        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Use method selection strategy for swipe actions
        preferred_method = self.get_preferred_automation_method('swipe', 'gesture')
        self.log_method_selection('swipe', 'gesture', preferred_method, 'Swipe gesture - avoiding AirTest to prevent unwanted keyboards')

        # Get parameters with default values
        vector_start = params.get('vector_start', [0.5, 0.5])
        vector_end = params.get('vector_end', [0.5, 0.7])
        duration = params.get('duration', 300)  # Default 300ms
        count = params.get('count', 1)  # Default 1 swipe
        interval = params.get('interval', 0.5)  # Default 0.5s between swipes
        
        if not vector_start or not vector_end:
            return {"status": "error", "message": "Missing swipe vectors"}
        
        # Ensure vectors are in 0-1 range (convert if they're percentages)
        if vector_start[0] > 1 or vector_start[1] > 1:
            self.logger.info(f"Converting vector_start from percentage to relative: {vector_start}")
            vector_start = [val/100 if val <= 100 else val for val in vector_start]
            
        if vector_end[0] > 1 or vector_end[1] > 1:
            self.logger.info(f"Converting vector_end from percentage to relative: {vector_end}")
            vector_end = [val/100 if val <= 100 else val for val in vector_end]
        
        try:
            result = {"status": "success", "message": ""}
            
            # Convert duration from milliseconds to seconds for Airtest
            duration_sec = duration / 1000.0
            
            for i in range(count):
                # Execute the swipe using UIAutomator/Appium (avoiding AirTest per method selection strategy)
                # Use regular device controller with absolute coordinates
                screen_size = self.controller.get_device_dimensions()
                if not screen_size:
                    screen_size = (1080, 1920)  # Default fallback size
                        
                    # Convert relative coordinates to absolute
                    start_x = int(vector_start[0] * screen_size[0])
                    start_y = int(vector_start[1] * screen_size[1])
                    end_x = int(vector_end[0] * screen_size[0])
                    end_y = int(vector_end[1] * screen_size[1])
                    
                    result = self.controller.swipe(start_x, start_y, end_x, end_y, duration)
                
                # Wait for the specified interval between swipes (except after the last one)
                if i < count - 1:
                    time.sleep(interval)
            
            # Create a descriptive message about the swipe operation
            message = f"Performed {count} swipe(s) from {vector_start} to {vector_end}"
            
            # Check if the result is a dict or a boolean
            if isinstance(result, dict):
                # Only override status if it's an error
                if result.get("status") == "error":
                    return result
                return {"status": "success", "message": message}
            elif result is True:
                return {"status": "success", "message": message}
            else:
                return {"status": "error", "message": f"Failed to swipe from {vector_start} to {vector_end}"}
                
        except Exception as e:
            self.logger.error(f"Error executing swipe action: {e}")
            return {"status": "error", "message": f"Swipe action failed: {str(e)}"} 