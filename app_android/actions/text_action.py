from .base_action import BaseAction
# Import parameter_utils utilities with fallback
try:
    from app_android.utils.parameter_utils import substitute_parameters
except ImportError:
    try:
        from ..utils.parameter_utils import substitute_parameters
    except ImportError:
        # Fallback function if import fails
        def substitute_parameters(*args, **kwargs):
            return args[0] if args else None

class TextAction(BaseAction):
    """Handler for text input actions"""

    def execute(self, params):
        """
        Execute text input action

        Args:
            params: Dictionary containing:
                - text: The text to input
                - enter: (Optional) Whether to press Enter after text input

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get the text and apply parameter substitution
        original_text = params.get('text')
        if not original_text:
            return {"status": "error", "message": "Missing text parameter"}

        # Apply parameter substitution
        text = substitute_parameters(original_text)

        # Get the enter parameter
        enter = params.get('enter', False)

        # Log if parameter substitution occurred
        if text != original_text:
            self.logger.info(f"Parameter substitution applied: '{original_text}' -> '{text}'")

        try:
            # Use method selection strategy for text input
            preferred_method = self.get_preferred_automation_method('text', 'input_text')
            self.log_method_selection('text', 'input_text', preferred_method, 'Text input - avoiding AirTest to prevent unwanted keyboards')

            # Execute the text input using the controller
            result = self.controller.input_text(text, enter=enter)

            # Check if the result is a dict or a boolean
            if isinstance(result, dict):
                # Controller returned a dict with status and message
                # Make sure the message shows the substituted text if parameter substitution occurred
                if text != original_text and result.get("status") == "success":
                    result["message"] = f"Input text: {text} (substituted from {original_text})"
                return result
            elif result is True:
                # Controller returned a boolean success
                # Include both original and substituted text in the message if parameter substitution occurred
                if text != original_text:
                    return {"status": "success", "message": f"Input text: {text} (substituted from {original_text})"}
                else:
                    return {"status": "success", "message": f"Input text: {text}"}
            else:
                # Controller returned a boolean failure
                return {"status": "error", "message": f"Failed to input text: {text}"}

        except Exception as e:
            self.logger.error(f"Error executing text input action: {e}")
            return {"status": "error", "message": f"Text input action failed: {str(e)}"}