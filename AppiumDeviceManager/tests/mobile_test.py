import os
import sys
import time
from appium import webdriver
from appium.options.ios import XCUITestOptions
from appium.options.android import UiAutomator2Options
from appium.webdriver.common.appiumby import AppiumBy
from dotenv import load_dotenv

def run_mobile_test(device_name, os_version, platform_name):
    """
    Run a mobile test on BrowserStack
    
    Args:
        device_name (str): Name of the device to test on
        os_version (str): OS version of the device
        platform_name (str): 'ios' or 'android'
    """
    # Load environment variables
    load_dotenv()
    
    # BrowserStack credentials
    browserstack_username = os.getenv('BROWSERSTACK_USERNAME')
    browserstack_access_key = os.getenv('BROWSERSTACK_ACCESS_KEY')
    
    if not all([browserstack_username, browserstack_access_key]):
        raise ValueError("BrowserStack credentials not found in environment variables")
    
    # Common capabilities
    common_caps = {
        'browserstack.user': browserstack_username,
        'browserstack.key': browserstack_access_key,
        'project': 'Appium Python Project',
        'build': 'Python iOS' if platform_name.lower() == 'ios' else 'Python Android',
        'name': f'{platform_name.capitalize()} Test',
        'device': device_name,
        'os_version': os_version,
        'app': 'bs://0c500dbd289bb2cb508a00eca4456be2d8d556de',  # Replace with your app URL
        'autoGrantPermissions': True,
        'browserstack.debug': True,
        'browserstack.networkLogs': True,
        'browserstack.deviceLogs': True
    }
    
    try:
        # Platform-specific capabilities
        if platform_name.lower() == 'ios':
            options = XCUITestOptions()
            options.platform_name = 'iOS'
            options.device_name = device_name
            options.platform_version = os_version
            options.automation_name = 'XCUITest'
            options.app = common_caps['app']
            options.set_capability('browserstack.user', browserstack_username)
            options.set_capability('browserstack.key', browserstack_access_key)
            options.set_capability('project', common_caps['project'])
            options.set_capability('build', common_caps['build'])
            options.set_capability('name', common_caps['name'])
            options.set_capability('autoGrantPermissions', True)
            options.set_capability('browserstack.debug', True)
            options.set_capability('browserstack.networkLogs', True)
            options.set_capability('browserstack.deviceLogs', True)
            
            driver = webdriver.Remote(
                command_executor=f'https://{browserstack_username}:{browserstack_access_key}@hub-cloud.browserstack.com/wd/hub',
                options=options
            )
            
            # iOS specific test steps
            print("Running iOS test...")
            time.sleep(5)  # Wait for app to load
            
            # Example: Find and click an element
            try:
                # Replace with actual element locators for your app
                element = driver.find_element(AppiumBy.ACCESSIBILITY_ID, 'some_element')
                element.click()
                print("Clicked on element")
            except Exception as e:
                print(f"Could not find/click element: {e}")
            
        elif platform_name.lower() == 'android':
            options = UiAutomator2Options()
            options.platform_name = 'Android'
            options.device_name = device_name
            options.platform_version = os_version
            options.automation_name = 'UiAutomator2'
            options.app = common_caps['app']
            options.set_capability('browserstack.user', browserstack_username)
            options.set_capability('browserstack.key', browserstack_access_key)
            options.set_capability('project', common_caps['project'])
            options.set_capability('build', common_caps['build'])
            options.set_capability('name', common_caps['name'])
            options.set_capability('autoGrantPermissions', True)
            options.set_capability('browserstack.debug', True)
            options.set_capability('browserstack.networkLogs', True)
            options.set_capability('browserstack.deviceLogs', True)
            
            driver = webdriver.Remote(
                command_executor=f'https://{browserstack_username}:{browserstack_access_key}@hub-cloud.browserstack.com/wd/hub',
                options=options
            )
            
            # Android specific test steps
            print("Running Android test...")
            time.sleep(5)  # Wait for app to load
            
            # Example: Find and click an element
            try:
                # Replace with actual element locators for your app
                element = driver.find_element(AppiumBy.ID, 'com.example.someapp:id/some_element')
                element.click()
                print("Clicked on element")
            except Exception as e:
                print(f"Could not find/click element: {e}")
        else:
            raise ValueError(f"Unsupported platform: {platform_name}")
        
        # Mark test as passed
        driver.execute_script('browserstack_executor: {"action": "setSessionStatus", "arguments": {"status":"passed"}}')
        
    except Exception as e:
        print(f"Test failed: {str(e)}")
        # Mark test as failed
        if 'driver' in locals():
            driver.execute_script('browserstack_executor: {"action": "setSessionStatus", "arguments": {"status":"failed"}}')
        raise
    
    finally:
        # Quit the driver
        if 'driver' in locals():
            driver.quit()

if __name__ == "__main__":
    if len(sys.argv) < 4:
        print("Usage: python mobile_test.py <device_name> <os_version> <ios|android>")
        sys.exit(1)
        
    device_name = sys.argv[1]
    os_version = sys.argv[2]
    platform_name = sys.argv[3].lower()
    
    if platform_name not in ['ios', 'android']:
        print("Error: platform must be either 'ios' or 'android'")
        sys.exit(1)
    
    run_mobile_test(device_name, os_version, platform_name)
