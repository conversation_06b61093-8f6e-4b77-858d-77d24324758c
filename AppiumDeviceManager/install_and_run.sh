#!/bin/bash

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Appium Device Manager Installation ===${NC}"

# Check if Node.js is installed
if ! [ -x "$(command -v node)" ]; then
  echo -e "${RED}Error: Node.js is not installed.${NC}" >&2
  echo -e "Please install Node.js from https://nodejs.org/ (version 14 or later recommended)"
  exit 1
fi

# Check node version
NODE_VERSION=$(node -v | cut -d 'v' -f 2)
NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d '.' -f 1)

if [ "$NODE_MAJOR_VERSION" -lt 14 ]; then
  echo -e "${YELLOW}Warning: You are using an older version of Node.js (v$NODE_VERSION).${NC}"
  echo -e "${YELLOW}It's recommended to use Node.js v14 or later.${NC}"
  echo ""
  read -p "Do you want to continue anyway? (y/n) " -n 1 -r
  echo ""
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${RED}Installation aborted.${NC}"
    exit 1
  fi
fi

# Check if npm is installed
if ! [ -x "$(command -v npm)" ]; then
  echo -e "${RED}Error: npm is not installed.${NC}" >&2
  echo -e "Please install npm (it should come with Node.js)"
  exit 1
fi

echo -e "${GREEN}Node.js v$NODE_VERSION and npm are installed.${NC}"

# Check if Appium is installed
if ! [ -x "$(command -v appium)" ]; then
  echo -e "${YELLOW}Warning: Appium is not installed or not in PATH.${NC}"
  echo -e "${YELLOW}For full functionality, Appium 2.0+ is required.${NC}"
  echo -e "You can install it with: npm install -g appium@next"
fi

# Install dependencies
echo -e "${BLUE}Installing dependencies...${NC}"
npm install

# Check if installation was successful
if [ $? -ne 0 ]; then
  echo -e "${RED}Error: Failed to install dependencies.${NC}" >&2
  exit 1
fi

echo -e "${GREEN}Dependencies installed successfully!${NC}"

# Run the application
echo -e "${BLUE}Starting Appium Device Manager...${NC}"
echo -e "${YELLOW}The application will be available at http://localhost:1234${NC}"
npm start

# This line won't be reached during normal operation
# as npm start will keep running
exit 0 