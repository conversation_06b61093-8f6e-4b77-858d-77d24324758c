// devserver.js
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const Bundler = require('parcel-bundler');
const path = require('path');
const { spawn } = require('child_process');

const app = express();
const PORT = 1234;
const BACKEND_PORT = 3001;
const BACKEND_URL = `http://localhost:${BACKEND_PORT}`;

// Start backend server as a child process
const backend = spawn('node', ['server.js'], { stdio: 'inherit' });

// Proxy /api requests to backend
app.use('/api', createProxyMiddleware({
  target: BACKEND_URL,
  changeOrigin: true,
  logLevel: 'debug',
}));

// Serve frontend using Parcel
const entryFiles = [path.join(__dirname, 'src', 'index.html'), path.join(__dirname, 'src', 'test-session.html')];
const bundler = new Bundler(entryFiles, { hmr: true });
app.use(bundler.middleware());

app.listen(PORT, () => {
  console.log(`Dev server running at http://localhost:${PORT}`);
  console.log(`Proxying /api to ${BACKEND_URL}`);
});

// Cleanup on exit
process.on('SIGINT', () => {
  backend.kill('SIGINT');
  process.exit();
});
process.on('SIGTERM', () => {
  backend.kill('SIGTERM');
  process.exit();
});
