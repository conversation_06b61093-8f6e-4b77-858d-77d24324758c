* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Roboto', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

button {
  cursor: pointer;
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #45a049;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

input, select {
  padding: 8px;
  margin: 5px 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.error {
  color: #f44336;
  margin: 10px 0;
}

.success {
  color: #4CAF50;
  margin: 10px 0;
}

.input-field {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
}

.input-field label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.input-field input,
.input-field select {
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.input-field input:focus,
.input-field select:focus {
  outline: none;
  border-color: #3498db;
}

.run-button {
  background-color: #2ecc71;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.run-button:hover {
  background-color: #27ae60;
}

.run-button:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.status-message {
  padding: 0.75rem;
  margin-top: 1rem;
  border-radius: 4px;
  text-align: center;
}

.status-message.success {
  background-color: #e6f7ee;
  color: #27ae60;
}

.status-message.error {
  background-color: #fbedeb;
  color: #e74c3c;
} 