<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Appium Test Session</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f5f5f5;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }
    
    header {
      background-color: #2c3e50;
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .logo {
      font-size: 1.5rem;
      font-weight: bold;
      display: flex;
      align-items: center;
    }
    
    .logo span {
      margin-left: 10px;
    }
    
    main {
      flex: 1;
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
      width: 100%;
    }
    
    .session-info {
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
    }
    
    .device-info {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .info-card {
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .info-card h3 {
      margin-top: 0;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    
    .test-controls {
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }
    
    button {
      background-color: #3498db;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 10px 15px;
      font-size: 1rem;
      cursor: pointer;
    }
    
    button.success {
      background-color: #2ecc71;
    }
    
    button.danger {
      background-color: #e74c3c;
    }
    
    button:hover {
      opacity: 0.9;
    }
    
    .device-screen {
      width: 100%;
      height: 500px;
      background-color: #ddd;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
    }
    
    .detail-row {
      display: flex;
      margin-bottom: 8px;
    }
    
    .detail-label {
      flex: 1;
      color: #7f8c8d;
      font-weight: 500;
    }
    
    .detail-value {
      flex: 2;
    }

    .error-message {
      background-color: #fee;
      color: #e74c3c;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
      text-align: center;
    }
  </style>
</head>
<body>
  <header>
    <div class="logo">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M17 2H7C4.23858 2 2 4.23858 2 7V17C2 19.7614 4.23858 22 7 22H17C19.7614 22 22 19.7614 22 17V7C22 4.23858 19.7614 2 17 2Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M12 9V15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M9 12H15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span>Appium Test Session</span>
    </div>
    
    <div id="session-id">Session: <span>Loading...</span></div>
  </header>
  
  <main>
    <div id="error-container"></div>
    
    <section class="session-info">
      <h2>Active Test Session</h2>
      <p>This is a test session for your connected device. You can interact with the device or run automated tests.</p>
    </section>
    
    <div class="device-info">
      <div class="info-card">
        <h3>Device Information</h3>
        <div class="detail-row">
          <div class="detail-label">Name:</div>
          <div class="detail-value" id="device-name">Loading...</div>
        </div>
        <div class="detail-row">
          <div class="detail-label">UDID:</div>
          <div class="detail-value" id="device-udid">Loading...</div>
        </div>
        <div class="detail-row">
          <div class="detail-label">Platform:</div>
          <div class="detail-value" id="device-platform">Loading...</div>
        </div>
        <div class="detail-row">
          <div class="detail-label">OS Version:</div>
          <div class="detail-value" id="device-os">Loading...</div>
        </div>
      </div>
      
      <div class="info-card">
        <h3>Session Information</h3>
        <div class="detail-row">
          <div class="detail-label">Status:</div>
          <div class="detail-value" id="session-status">Connected</div>
        </div>
        <div class="detail-row">
          <div class="detail-label">Start Time:</div>
          <div class="detail-value" id="session-start-time">Loading...</div>
        </div>
        <div class="detail-row">
          <div class="detail-label">Duration:</div>
          <div class="detail-value" id="session-duration">0:00</div>
        </div>
      </div>
    </div>
    
    <div class="test-controls">
      <h3>Test Controls</h3>
      
      <div class="button-group">
        <button class="success" id="start-test-btn">Start Test</button>
        <button id="screenshot-btn">Take Screenshot</button>
        <button id="record-btn">Record Screen</button>
        <button class="danger" id="end-session-btn">End Session</button>
      </div>
      
      <div class="device-screen">
        <p>Device screen will display here</p>
      </div>
    </div>
  </main>

  <script>
    // Global variable to store device information
    window.deviceInfo = window.deviceInfo || {};
    
    // Simulate receiving device information
    window.onload = function() {
      try {
        // Get query parameters
        const urlParams = new URLSearchParams(window.location.search);
        
        // Create device info object from URL parameters or use the global one if it exists
        const deviceInfo = {
          name: urlParams.get('name') || window.deviceInfo.name || 'Unknown Device',
          udid: urlParams.get('udid') || window.deviceInfo.udid || 'Unknown',
          platform: urlParams.get('platform') || window.deviceInfo.platform || 'Unknown',
          os: urlParams.get('os') || window.deviceInfo.os || 'Unknown'
        };
        
        // Update device information
        document.getElementById('device-name').textContent = deviceInfo.name;
        document.getElementById('device-udid').textContent = deviceInfo.udid;
        document.getElementById('device-platform').textContent = deviceInfo.platform;
        document.getElementById('device-os').textContent = deviceInfo.os;
        
        // Update session information
        const sessionId = `session-${Date.now()}`;
        document.querySelector('#session-id span').textContent = sessionId;
        
        const startTime = new Date();
        document.getElementById('session-start-time').textContent = startTime.toLocaleTimeString();
        
        // Update session duration
        setInterval(() => {
          const now = new Date();
          const diff = now - startTime;
          const minutes = Math.floor(diff / 60000);
          const seconds = Math.floor((diff % 60000) / 1000);
          document.getElementById('session-duration').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
        
        // Set up button event listeners
        document.getElementById('start-test-btn').addEventListener('click', function() {
          alert('Starting test for device: ' + deviceInfo.name);
        });
        
        document.getElementById('screenshot-btn').addEventListener('click', function() {
          alert('Taking screenshot of device: ' + deviceInfo.name);
        });
        
        document.getElementById('record-btn').addEventListener('click', function() {
          alert('Recording screen of device: ' + deviceInfo.name);
        });
        
        document.getElementById('end-session-btn').addEventListener('click', function() {
          if (confirm('Are you sure you want to end this session?')) {
            window.close();
          }
        });
      } catch (error) {
        console.error('Error initializing test session:', error);
        const errorContainer = document.getElementById('error-container');
        errorContainer.innerHTML = `
          <div class="error-message">
            <strong>Error initializing test session:</strong> ${error.message}
          </div>
        `;
      }
    };
  </script>
</body>
</html> 