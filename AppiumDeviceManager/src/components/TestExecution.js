import React, { useState } from 'react';
import styled from 'styled-components';

const TestExecutionContainer = styled.div`
  margin-bottom: 30px;
`;

const SectionTitle = styled.h2`
  margin-bottom: 20px;
  font-size: 1.5rem;
  color: #2c3e50;
`;

const Card = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
`;

const ActionButton = styled.button`
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
`;

function TestExecution() {
  const [executing, setExecuting] = useState(false);
  
  const handleStartExecution = () => {
    setExecuting(true);
    
    // Simulate test execution
    setTimeout(() => {
      setExecuting(false);
    }, 3000);
  };
  
  return (
    <TestExecutionContainer>
      <SectionTitle>Test Execution</SectionTitle>
      
      <Card>
        <FormGroup>
          <label>Test Execution Page</label>
          <p>This page will allow you to configure and start test executions.</p>
        </FormGroup>
        
        <ActionButton 
          onClick={handleStartExecution}
          disabled={executing}
        >
          {executing ? 'Running...' : 'Start Test Execution'}
        </ActionButton>
      </Card>
    </TestExecutionContainer>
  );
}

export default TestExecution; 