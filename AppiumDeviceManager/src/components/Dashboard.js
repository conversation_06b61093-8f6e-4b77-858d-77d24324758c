import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { deviceService } from '../services/deviceService';
import ErrorBoundary from './ErrorBoundary';

const DashboardContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
`;

const GridSection = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f8f9fa;
`;

const SectionTitle = styled.h2`
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const DeviceCount = styled.span`
  background-color: ${props => props.$type === 'connected' ? '#e8f5e8' : '#f0f8ff'};
  color: ${props => props.$type === 'connected' ? '#27ae60' : '#3498db'};
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
`;

const DeviceGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
`;

const DeviceCard = styled.div`
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  border-left: 4px solid ${props => props.$connected ? '#27ae60' : '#3498db'};

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    border-left-color: ${props => props.$connected ? '#2ecc71' : '#2980b9'};
  }
`;

const DeviceHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const DeviceName = styled.h3`
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const StatusBadge = styled.div`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  background-color: ${props =>
    props.$status === 'In Use' ? '#fff3cd' :
    props.$status === 'Online' ? '#d4edda' : '#f8d7da'};
  color: ${props =>
    props.$status === 'In Use' ? '#856404' :
    props.$status === 'Online' ? '#155724' : '#721c24'};
`;

const DeviceInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
`;

const InfoLabel = styled.span`
  color: #6c757d;
  font-weight: 500;
`;

const InfoValue = styled.span`
  color: #495057;
  font-weight: 600;
`;

const ActionButton = styled.button`
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 12px;
  width: 100%;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
`;

const EmptyIcon = styled.div`
  margin-bottom: 16px;

  svg {
    width: 48px;
    height: 48px;
    color: #adb5bd;
  }
`;

const EmptyText = styled.p`
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
`;

function Dashboard() {
  const [devices, setDevices] = useState([]);
  const [sessions, setSessions] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchDevices = async () => {
    try {
      setError(null);
      const fetchedDevices = await deviceService.getDevices();
      setDevices(Array.isArray(fetchedDevices) ? fetchedDevices : []);
    } catch (error) {
      console.error('Error fetching devices:', error);
      setError(`Failed to fetch devices: ${error.message}`);
      setDevices([]);
    }
  };

  const fetchSessions = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/sessions');
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.sessions)) {
          const sessionMap = {};
          data.sessions.forEach(session => {
            if (session && session.deviceId) {
              sessionMap[session.deviceId] = session;
            }
          });
          setSessions(sessionMap);
        }
      }
    } catch (error) {
      console.error('Error fetching sessions:', error);
    }
  };

  const handleLaunchApp = async (device) => {
    try {
      const response = await fetch('http://localhost:3001/api/devices/launch-app', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device: device,
          platform: device.platform
        }),
      });

      const result = await response.json();

      if (response.ok && result.url) {
        window.open(result.url, '_blank');
        // Refresh sessions to get updated state
        setTimeout(fetchSessions, 1000);
      } else {
        console.error('Failed to launch app:', result.error || result.details);
      }
    } catch (error) {
      console.error('Error launching app:', error);
    }
  };

  const handleOpenApp = (session) => {
    if (session && session.port) {
      window.open(`http://localhost:${session.port}`, '_blank');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchDevices(), fetchSessions()]);
      setLoading(false);
    };

    loadData();

    // Poll for updates every 10 seconds
    const interval = setInterval(() => {
      fetchSessions();
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  // Separate devices into connected and available
  const connectedDevices = devices.filter(device => {
    const deviceId = device.udid || device.id;
    return sessions[deviceId];
  });

  const availableDevices = devices.filter(device => {
    const deviceId = device.udid || device.id;
    return !sessions[deviceId];
  });

  if (loading) {
    return (
      <div style={{ padding: '40px', textAlign: 'center' }}>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '40px', textAlign: 'center', color: '#e74c3c' }}>
        <p>{error}</p>
        <ActionButton onClick={() => window.location.reload()}>
          Retry
        </ActionButton>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <DashboardContainer>
        {/* Connected Devices Grid */}
        <GridSection>
          <SectionHeader>
            <SectionTitle>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="#27ae60" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Connected Devices
            </SectionTitle>
            <DeviceCount $type="connected">{connectedDevices.length}</DeviceCount>
          </SectionHeader>

          {connectedDevices.length > 0 ? (
            <DeviceGrid>
              {connectedDevices.map((device) => {
                const deviceId = device.udid || device.id;
                const session = sessions[deviceId];

                return (
                  <DeviceCard key={deviceId} $connected={true}>
                    <DeviceHeader>
                      <DeviceName>{device.name}</DeviceName>
                      <StatusBadge $status="In Use">In Use</StatusBadge>
                    </DeviceHeader>
                    <DeviceInfo>
                      <InfoRow>
                        <InfoLabel>Platform:</InfoLabel>
                        <InfoValue>{device.platform}</InfoValue>
                      </InfoRow>
                      <InfoRow>
                        <InfoLabel>Model:</InfoLabel>
                        <InfoValue>{device.model || 'N/A'}</InfoValue>
                      </InfoRow>
                      <InfoRow>
                        <InfoLabel>Port:</InfoLabel>
                        <InfoValue>{session?.port || 'N/A'}</InfoValue>
                      </InfoRow>
                      <InfoRow>
                        <InfoLabel>Started:</InfoLabel>
                        <InfoValue>
                          {session?.startTime ? new Date(session.startTime).toLocaleTimeString() : 'N/A'}
                        </InfoValue>
                      </InfoRow>
                    </DeviceInfo>
                    <ActionButton onClick={() => handleOpenApp(session)}>
                      Open Automation App
                    </ActionButton>
                  </DeviceCard>
                );
              })}
            </DeviceGrid>
          ) : (
            <EmptyState>
              <EmptyIcon>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </EmptyIcon>
              <EmptyText>No devices currently connected</EmptyText>
            </EmptyState>
          )}
        </GridSection>

        {/* Available Devices Grid */}
        <GridSection>
          <SectionHeader>
            <SectionTitle>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17 2H7C5.89543 2 5 2.89543 5 4V20C5 21.1046 5.89543 22 7 22H17C18.1046 22 19 21.1046 19 20V4C19 2.89543 18.1046 2 17 2Z" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 18H12.01" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Available Devices
            </SectionTitle>
            <DeviceCount $type="available">{availableDevices.length}</DeviceCount>
          </SectionHeader>

          {availableDevices.length > 0 ? (
            <DeviceGrid>
              {availableDevices.map((device) => {
                const deviceId = device.udid || device.id;
                const status = device.status || device.state || 'Online';

                return (
                  <DeviceCard key={deviceId} $connected={false}>
                    <DeviceHeader>
                      <DeviceName>{device.name}</DeviceName>
                      <StatusBadge $status={status}>{status}</StatusBadge>
                    </DeviceHeader>
                    <DeviceInfo>
                      <InfoRow>
                        <InfoLabel>Platform:</InfoLabel>
                        <InfoValue>{device.platform}</InfoValue>
                      </InfoRow>
                      <InfoRow>
                        <InfoLabel>Model:</InfoLabel>
                        <InfoValue>{device.model || 'N/A'}</InfoValue>
                      </InfoRow>
                      <InfoRow>
                        <InfoLabel>OS Version:</InfoLabel>
                        <InfoValue>{device.osVersion || device.version || 'N/A'}</InfoValue>
                      </InfoRow>
                      <InfoRow>
                        <InfoLabel>UDID:</InfoLabel>
                        <InfoValue style={{ fontSize: '0.75rem', wordBreak: 'break-all' }}>
                          {deviceId}
                        </InfoValue>
                      </InfoRow>
                    </DeviceInfo>
                    <ActionButton onClick={() => handleLaunchApp(device)}>
                      Launch Automation App
                    </ActionButton>
                  </DeviceCard>
                );
              })}
            </DeviceGrid>
          ) : (
            <EmptyState>
              <EmptyIcon>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </EmptyIcon>
              <EmptyText>No available devices found</EmptyText>
            </EmptyState>
          )}
        </GridSection>
      </DashboardContainer>
    </ErrorBoundary>
  );
}

export default Dashboard; 