import React from 'react';
import styled from 'styled-components';

// Styled components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  width: 90%;
  max-width: 500px;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
`;

const ModalHeader = styled.div`
  margin-bottom: 20px;
`;

const ModalTitle = styled.h3`
  margin: 0;
  color: #333;
`;

const ProgressContainer = styled.div`
  margin-bottom: 20px;
`;

const ProgressBarOuter = styled.div`
  width: 100%;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
  margin-top: 10px;
`;

const ProgressBarInner = styled.div`
  height: 100%;
  background-color: ${props => props.$isError ? '#dc3545' : '#28a745'};
  width: ${props => props.$progress}%;
  transition: width 0.3s ease;
`;

const StatusMessage = styled.p`
  margin-top: 10px;
  font-size: 14px;
  color: ${props => props.$isError ? '#dc3545' : '#333'};
`;

/**
 * Progress Modal Component
 * @param {Object} props - Component props
 * @param {boolean} props.isVisible - Whether the modal is visible
 * @param {number} props.progress - Current progress (0-100)
 * @param {string} props.title - Modal title
 * @param {string} props.message - Current status message
 * @param {boolean} props.error - Whether there is an error
 * @returns {JSX.Element} Progress modal component
 */
const ProgressModal = ({ isVisible, progress, title, message, error }) => {
  if (!isVisible) return null;

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>{title}</ModalTitle>
        </ModalHeader>
        <ProgressContainer>
          <ProgressBarOuter>
            <ProgressBarInner $progress={progress} $isError={error} />
          </ProgressBarOuter>
          <StatusMessage $isError={error}>{message}</StatusMessage>
        </ProgressContainer>
      </ModalContent>
    </ModalOverlay>
  );
};

export default ProgressModal; 