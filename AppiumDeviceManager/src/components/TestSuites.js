import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { testService } from '../services/testService';

const TestSuitesContainer = styled.div`
  margin-bottom: 30px;
`;

const SectionTitle = styled.h2`
  margin-bottom: 20px;
  font-size: 1.5rem;
  color: #2c3e50;
`;

const AddButton = styled.button`
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    background-color: #2980b9;
  }
`;

const TestSuiteCard = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
`;

const TestSuiteHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
`;

const TestSuiteName = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  cursor: pointer;
  
  &:hover {
    color: #3498db;
  }
`;

const TestCount = styled.div`
  font-size: 0.9rem;
  color: #7f8c8d;
`;

const TagContainer = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
`;

const Tag = styled.span`
  background-color: #f1f5f9;
  color: #64748b;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
`;

const TestList = styled.div`
  margin-top: 15px;
  border-top: 1px solid #ecf0f1;
  padding-top: 15px;
  display: ${props => props.expanded ? 'block' : 'none'};
`;

const TestItem = styled.div`
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #f8f9fa;
  
  &:hover {
    background-color: #f1f5f9;
  }
`;

const ActionBar = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 15px;
`;

const ActionButton = styled.button`
  background-color: ${props => props.variant === 'danger' ? '#e74c3c' : 
                              props.variant === 'success' ? '#27ae60' : '#3498db'};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9rem;
  cursor: pointer;
  
  &:hover {
    background-color: ${props => props.variant === 'danger' ? '#c0392b' : 
                                props.variant === 'success' ? '#219653' : '#2980b9'};
  }
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const ModalTitle = styled.h3`
  margin: 0;
  font-size: 1.5rem;
`;

const CloseButton = styled.button`
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
  
  &:hover {
    color: #34495e;
  }
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  input, textarea, select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
  }
  
  textarea {
    min-height: 100px;
    resize: vertical;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
`;

function TestSuites() {
  const [testSuites, setTestSuites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedSuites, setExpandedSuites] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [currentSuite, setCurrentSuite] = useState(null);
  
  useEffect(() => {
    const fetchTestSuites = async () => {
      try {
        const suites = await testService.getTestSuites();
        setTestSuites(suites);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching test suites:', error);
        setLoading(false);
      }
    };
    
    fetchTestSuites();
  }, []);
  
  const toggleSuiteExpansion = (suiteId) => {
    setExpandedSuites(prev => ({
      ...prev,
      [suiteId]: !prev[suiteId]
    }));
  };
  
  const handleAddSuite = () => {
    setCurrentSuite(null);
    setShowModal(true);
  };
  
  const handleEditSuite = (suite) => {
    setCurrentSuite(suite);
    setShowModal(true);
  };
  
  const handleCloseModal = () => {
    setShowModal(false);
  };
  
  const handleSaveSuite = (event) => {
    event.preventDefault();
    // In a real app, this would save the test suite to your backend
    setShowModal(false);
  };
  
  const handleDeleteSuite = (suiteId) => {
    // In a real app, this would delete the test suite from your backend
    setTestSuites(testSuites.filter(suite => suite.id !== suiteId));
  };
  
  return (
    <TestSuitesContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <SectionTitle>Test Suites</SectionTitle>
        <AddButton onClick={handleAddSuite}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          Add Test Suite
        </AddButton>
      </div>
      
      {loading ? (
        <p>Loading test suites...</p>
      ) : testSuites.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px', backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
          <h3>No test suites found</h3>
          <p style={{ marginBottom: '20px' }}>Create your first test suite to get started</p>
          <AddButton onClick={handleAddSuite}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 5V19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Add Test Suite
          </AddButton>
        </div>
      ) : (
        testSuites.map(suite => (
          <TestSuiteCard key={suite.id}>
            <TestSuiteHeader>
              <TestSuiteName onClick={() => toggleSuiteExpansion(suite.id)}>
                {suite.name}
              </TestSuiteName>
              <TestCount>{suite.tests.length} Tests</TestCount>
            </TestSuiteHeader>
            
            <div>{suite.description}</div>
            
            <TagContainer>
              {suite.tags.map(tag => (
                <Tag key={tag}>{tag}</Tag>
              ))}
            </TagContainer>
            
            <TestList expanded={expandedSuites[suite.id]}>
              {suite.tests.map(test => (
                <TestItem key={test.id}>
                  {test.name}
                </TestItem>
              ))}
            </TestList>
            
            <ActionBar>
              <ActionButton 
                variant="success"
                onClick={() => window.location.href = `/execution?suiteId=${suite.id}`}
              >
                Run Tests
              </ActionButton>
              <ActionButton onClick={() => handleEditSuite(suite)}>
                Edit
              </ActionButton>
              <ActionButton 
                variant="danger" 
                onClick={() => handleDeleteSuite(suite.id)}
              >
                Delete
              </ActionButton>
            </ActionBar>
          </TestSuiteCard>
        ))
      )}
      
      {showModal && (
        <ModalOverlay>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>{currentSuite ? 'Edit Test Suite' : 'Add Test Suite'}</ModalTitle>
              <CloseButton onClick={handleCloseModal}>&times;</CloseButton>
            </ModalHeader>
            
            <form onSubmit={handleSaveSuite}>
              <FormGroup>
                <label htmlFor="name">Name</label>
                <input 
                  type="text" 
                  id="name" 
                  defaultValue={currentSuite?.name || ''} 
                  required 
                />
              </FormGroup>
              
              <FormGroup>
                <label htmlFor="description">Description</label>
                <textarea 
                  id="description" 
                  defaultValue={currentSuite?.description || ''} 
                />
              </FormGroup>
              
              <FormGroup>
                <label htmlFor="tags">Tags (comma separated)</label>
                <input 
                  type="text" 
                  id="tags" 
                  defaultValue={currentSuite?.tags.join(', ') || ''} 
                />
              </FormGroup>
              
              <ButtonGroup>
                <ActionButton type="button" onClick={handleCloseModal}>
                  Cancel
                </ActionButton>
                <ActionButton type="submit" variant="success">
                  Save
                </ActionButton>
              </ButtonGroup>
            </form>
          </ModalContent>
        </ModalOverlay>
      )}
    </TestSuitesContainer>
  );
}

export default TestSuites; 