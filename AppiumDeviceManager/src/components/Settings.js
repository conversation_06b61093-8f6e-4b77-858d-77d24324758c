import React from 'react';
import styled from 'styled-components';

const SettingsContainer = styled.div`
  margin-bottom: 30px;
`;

const SectionTitle = styled.h2`
  margin-bottom: 20px;
  font-size: 1.5rem;
  color: #2c3e50;
`;

const Card = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  input, select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
  }
`;

const ActionButton = styled.button`
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  
  &:hover {
    background-color: #2980b9;
  }
`;

function Settings() {
  const handleSaveSettings = (e) => {
    e.preventDefault();
    // This would save the settings in a real app
    alert('Settings saved successfully!');
  };

  return (
    <SettingsContainer>
      <SectionTitle>Settings</SectionTitle>
      
      <Card>
        <form onSubmit={handleSaveSettings}>
          <FormGroup>
            <label htmlFor="appiumPath">Appium Server Path</label>
            <input 
              type="text" 
              id="appiumPath" 
              defaultValue="/usr/local/bin/appium" 
            />
          </FormGroup>
          
          <FormGroup>
            <label htmlFor="serverPort">Appium Server Port</label>
            <input 
              type="number" 
              id="serverPort" 
              defaultValue="4723" 
            />
          </FormGroup>
          
          <FormGroup>
            <label htmlFor="screenshotPath">Screenshot Storage Path</label>
            <input 
              type="text" 
              id="screenshotPath" 
              defaultValue="./screenshots" 
            />
          </FormGroup>
          
          <FormGroup>
            <label htmlFor="reportFormat">Report Format</label>
            <select id="reportFormat" defaultValue="html">
              <option value="html">HTML</option>
              <option value="json">JSON</option>
              <option value="xml">XML</option>
              <option value="all">All Formats</option>
            </select>
          </FormGroup>
          
          <ActionButton type="submit">Save Settings</ActionButton>
        </form>
      </Card>
    </SettingsContainer>
  );
}

export default Settings; 