import { createServer } from 'net';

/**
 * Finds a free port starting from the provided port
 * @param {number} startPort - The port to start checking from
 * @param {number} maxAttempts - Maximum number of attempts to find a free port
 * @returns {Promise<number>} - A free port
 */
export const findFreePort = (startPort = 8081, maxAttempts = 10) => {
  return new Promise((resolve, reject) => {
    let currentPort = startPort;
    let attempts = 0;

    function tryPort() {
      if (attempts >= maxAttempts) {
        // If we've tried too many times, just use a random port in a safe range
        const fallbackPort = Math.floor(Math.random() * (65535 - 49152)) + 49152;
        console.log(`Max attempts reached, using fallback port ${fallbackPort}`);
        resolve(fallbackPort);
        return;
      }

      attempts++;
      const server = createServer();
      
      server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          // Port is in use, try the next port
          currentPort++;
          console.log(`Port ${currentPort - 1} in use, trying ${currentPort}`);
          tryPort();
        } else {
          console.error('Error finding free port:', err);
          reject(err);
        }
      });
      
      server.listen(currentPort, () => {
        const { port } = server.address();
        server.close(() => {
          console.log(`Found free port: ${port}`);
          resolve(port);
        });
      });
    }

    tryPort();
  });
};

export default {
  findFreePort
}; 