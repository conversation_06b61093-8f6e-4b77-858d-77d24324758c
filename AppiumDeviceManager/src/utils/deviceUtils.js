// This file contains utility functions for device detection and management
// In a real implementation, these would interact with ADB, ios-deploy, etc.

/**
 * Parses device information from ADB output
 * @param {string} adbOutput - Raw output from ADB devices command
 * @returns {Array} Array of Android device objects
 */
export const parseAndroidDevices = (adbOutput) => {
  const devices = [];
  const lines = adbOutput.trim().split('\n');
  
  // Skip the first line (header)
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    const [id, status] = line.split('\t');
    
    devices.push({
      id,
      udid: id,
      platform: 'Android',
      status: status === 'device' ? 'Online' : 
              status === 'offline' ? 'Offline' : 'Unknown',
      name: `Android Device (${id})`,
      model: 'Unknown', // Would be fetched with additional ADB commands
      osVersion: 'Unknown', // Would be fetched with additional ADB commands
    });
  }
  
  return devices;
};

/**
 * Parses device information from Xcode instruments output
 * @param {string} instrumentsOutput - Raw output from instruments command
 * @returns {Array} Array of iOS device objects
 */
export const parseIOSDevices = (instrumentsOutput) => {
  const devices = [];
  const lines = instrumentsOutput.trim().split('\n');
  
  for (const line of lines) {
    if (!line.trim()) continue;
    
    // Try to get device details if available
    const match = line.match(/^(.*?)\s\(([A-Za-z0-9-]+)\)\s\((\d+\.\d+)\)$/);
    if (match) {
      const [_, name, udid, version] = match;
      
      devices.push({
        id: udid,
        udid,
        platform: 'iOS',
        status: 'Online', // Would need more info to determine actual status
        name,
        model: name, // Basic model info from name
        osVersion: version,
      });
    } else {
      // Simple format with just UDIDs (from idevice_id -l)
      const udid = line.trim();
      devices.push({
        id: udid,
        udid,
        platform: 'iOS',
        status: 'Online',
        name: `iOS Device (${udid})`,
        model: 'Unknown',
        osVersion: 'Unknown',
      });
    }
  }
  
  return devices;
};

/**
 * Detects connected Android and iOS devices using the backend API
 * @returns {Promise<Array>} Promise resolving to array of device objects
 */
export const detectConnectedDevices = async () => {
  try {
    console.log('Detecting connected devices via API...');
    const response = await fetch('/api/devices/scan');
    if (!response.ok) {
      throw new Error(`Failed to fetch devices: ${response.status}`);
    }

    const data = await response.json();
    if (data.success) {
      return data.devices || [];
    } else {
      throw new Error(data.error || 'Failed to detect devices');
    }
  } catch (error) {
    console.error('Error detecting connected devices:', error);
    // Return empty array instead of mock data
    return [];
  }
};

/**
 * Gets detailed information about a specific device
 * @param {string} udid - Device UDID
 * @param {string} platform - Device platform ('Android' or 'iOS')
 * @returns {Promise<Object>} Promise resolving to device details
 */
export const getDeviceDetails = async (udid, platform) => {
  try {
    // Try to get device details from the API
    const response = await fetch(`/api/devices/${udid}`);
    if (response.ok) {
      return await response.json();
    }
    
    // Fallback to simulated data
    if (platform === 'Android') {
      // In a real implementation, this would execute ADB commands
      return {
        id: udid,
        udid,
        platform: 'Android',
        status: 'Online',
        name: `Android Device (${udid})`,
        model: 'Google Pixel 6',
        osVersion: '13',
        manufacturer: 'Google',
        screenSize: '1080x2400',
        ram: '8 GB',
        cpu: 'Octa-core',
        batteryLevel: '87%',
      };
    } else if (platform === 'iOS') {
      // In a real implementation, this would execute iOS commands
      return {
        id: udid,
        udid,
        platform: 'iOS',
        status: 'Online',
        name: 'iPhone',
        model: 'iPhone',
        osVersion: '16.0',
        manufacturer: 'Apple',
        screenSize: '1170x2532',
        ram: '4 GB',
        cpu: 'Hexa-core',
        batteryLevel: '92%',
      };
    }
    
    throw new Error(`Unsupported platform: ${platform}`);
  } catch (error) {
    console.error(`Error getting details for device ${udid}:`, error);
    throw error;
  }
};

/**
 * Restarts a device
 * @param {string} udid - Device UDID
 * @param {string} platform - Device platform ('Android' or 'iOS')
 * @returns {Promise<Object>} Promise resolving to restart status
 */
export const restartDevice = async (udid, platform) => {
  try {
    // In a real implementation, this would execute platform-specific commands
    return { success: true, message: `Device ${udid} restarted successfully` };
  } catch (error) {
    console.error(`Error restarting device ${udid}:`, error);
    throw error;
  }
};

/**
 * Takes a screenshot of a device
 * @param {string} udid - Device UDID
 * @param {string} platform - Device platform ('Android' or 'iOS')
 * @param {string} outputPath - Path to save the screenshot
 * @returns {Promise<Object>} Promise resolving to screenshot details
 */
export const takeDeviceScreenshot = async (udid, platform, outputPath) => {
  try {
    // In a real implementation, this would execute platform-specific commands
    return { 
      success: true, 
      message: `Screenshot saved to ${outputPath}`,
      path: outputPath
    };
  } catch (error) {
    console.error(`Error taking screenshot of device ${udid}:`, error);
    throw error;
  }
};

export default {
  parseAndroidDevices,
  parseIOSDevices,
  detectConnectedDevices,
  getDeviceDetails,
  restartDevice,
  takeDeviceScreenshot
}; 