/**
 * URL Helper Utility
 * Provides functions for formatting URLs for the main app
 */

// Main app URL configuration - ensure full URL with protocol
const MAIN_APP_URL = 'http://localhost:8080';

/**
 * Format the URL for the main app with device ID parameter
 * @param {string} deviceId - The device ID to include in the URL
 * @param {string} platform - The device platform (iOS/Android)
 * @returns {string} - Formatted URL for the main app
 */
const formatMainAppUrl = (deviceId, platform = '') => {
  if (!deviceId) {
    return MAIN_APP_URL;
  }
  
  // Create URL with device ID and platform parameters
  let url = `${MAIN_APP_URL}?deviceId=${encodeURIComponent(deviceId)}`;
  
  // Add platform if provided
  if (platform) {
    url += `&platform=${encodeURIComponent(platform)}`;
  }
  
  // Add unique session ID to prevent cached connections to the same endpoint
  const sessionId = Date.now() + Math.random().toString(36).substring(2, 8);
  url += `&sessionId=${sessionId}`;
  
  console.log(`Opening main app URL: ${url}`);
  return url;
};

export default {
  formatMainAppUrl,
  MAIN_APP_URL
}; 