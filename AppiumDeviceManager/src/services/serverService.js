/**
 * Server Service
 * Handles starting and checking the main app server
 */
import urlHelper from '../utils/urlHelper';

// Main app server details
const SERVER_DETAILS = {
  startupTime: 5000, // milliseconds to wait for server startup
  port: 8080
};

// Start the main app server
const startMainAppServer = async (progressCallback) => {
  try {
    // Update progress
    if (progressCallback) progressCallback(10, 'Checking if main app is already running...');
    
    // Check if server is already running
    const isRunning = await checkServerRunning();
    if (isRunning) {
      if (progressCallback) progressCallback(100, 'Main app is already running');
      return { success: true, message: 'Main app is already running' };
    }
    
    // Update progress
    if (progressCallback) progressCallback(20, 'Starting main app server...');
    
    // In a browser environment, we can't directly start the server process
    // Instead, we need to inform the user to start the server manually
    if (progressCallback) progressCallback(100, 'Please start the server manually with "python run.py"', true);
    
    return { 
      success: false, 
      message: 'Cannot start server from browser. Please run "python run.py" in a terminal.'
    };
  } catch (error) {
    console.error('Error starting server:', error);
    if (progressCallback) progressCallback(100, `Error: ${error.message}`, true);
    return { success: false, message: `Error starting server: ${error.message}` };
  }
};

// Check if the server is running
const checkServerRunning = async () => {
  try {
    const url = urlHelper.MAIN_APP_URL;
    console.log(`Checking if server is running at: ${url}`);
    
    const response = await fetch(url, { 
      method: 'HEAD',
      mode: 'no-cors',
      cache: 'no-cache',
      timeout: 2000
    });
    
    console.log('Server check response:', response);
    return true;
  } catch (error) {
    console.log('Server is not running:', error.message);
    return false;
  }
};

export const serverService = {
  startMainAppServer,
  checkServerRunning
};

export default serverService; 