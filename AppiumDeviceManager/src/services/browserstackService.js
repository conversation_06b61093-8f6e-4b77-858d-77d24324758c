const API_URL = 'https://api-cloud.browserstack.com/app-automate';

const browserstackService = {
  async getDevices(username, accessKey) {
    try {
      const response = await fetch('http://localhost:3001/api/devices', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-BS-User': username,
          'X-BS-Key': accessKey
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        try {
          // Try to parse it as JSON, as our server should send JSON errors
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.error);
        } catch (e) {
          // If the error response isn't JSON, throw the raw text.
          // This handles cases where the proxy fails completely and sends HTML or plain text.
          throw new Error(errorText || `An unknown server error occurred (status: ${response.status}).`);
        }
      }
      
      const devices = await response.json();
      return devices
        .filter(device => device && (device.os || device.osName) && (device.os_version || device.osVersion) && (device.device || device.deviceName))
        .map(device => {
          // Normalize device properties
          const normalized = {
            device: device.device || device.deviceName,
            os: (device.os || device.osName || '').toLowerCase(),
            os_version: device.os_version || device.osVersion || '',
            // Include all original properties for backward compatibility
            ...device
          };
          
          // Create a consistent display name and ID
          return {
            ...normalized,
            name: `${normalized.device} - ${normalized.os_version} (${normalized.os})`,
            id: `${normalized.device}-${normalized.os_version}-${normalized.os}`.toLowerCase().replace(/\s+/g, '-')
          };
        });
    } catch (error) {
      console.error('BrowserStack API error:', error);
      throw error;
    }
  }
};

export default browserstackService; 