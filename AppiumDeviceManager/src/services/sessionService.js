import portUtils from '../utils/portUtils';
import urlHelper from '../utils/urlHelper';
import serverService from './serverService';

// Function to check if the main app is reachable
const checkMainAppAvailability = async () => {
  // Mock implementation to avoid errors
  console.log('Using mock implementation of checkMainAppAvailability');
  return true; // Always return true in mock mode
};

// This service will handle launching the automation app for a device
export const sessionService = {
  // Start a test session for a device - this launches the automation app and opens it
  startTestSession: async (device, progressCallback = null) => {
    try {
      console.log('Starting automation app for device:', device);

      // Update progress
      if (progressCallback) progressCallback(20, 'Launching automation app...');

      // Call the backend API to launch the automation app
      const response = await fetch('/api/devices/launch-app', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device: device,
          platform: device.platform
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to launch automation app: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to launch automation app');
      }

      if (progressCallback) progressCallback(60, 'App starting up...');

      // Wait a bit for the app to start up
      await new Promise(resolve => setTimeout(resolve, 3000));

      if (progressCallback) progressCallback(80, 'Opening app in browser...');

      // Create URL with the device ID and platform parameters
      const sessionUrl = `${result.url}?deviceId=${encodeURIComponent(device.udid || device.id)}&platform=${encodeURIComponent(device.platform)}`;

      // Open the automation app in a new window
      console.log('Opening automation app URL:', sessionUrl);
      const newWindow = window.open(sessionUrl, '_blank');

      if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
        throw new Error('Could not open the automation app. Please check your popup blocker settings.');
      }

      if (progressCallback) progressCallback(100, 'Automation app ready!');

      console.log('Automation app opened at:', sessionUrl);

      return {
        success: true,
        message: result.alreadyRunning ? 'Automation app was already running' : 'Automation app launched successfully',
        device,
        sessionUrl,
        alreadyRunning: result.alreadyRunning
      };
    } catch (error) {
      console.error('Error in session service:', error);
      if (progressCallback) progressCallback(100, `Error: ${error.message}`, true);
      throw new Error(`Failed to start session: ${error.message || 'Unknown error'}`);
    }
  },
  
  // Close a test session
  closeSession: async (deviceId) => {
    try {
      // Mock implementation
      return { success: true, message: 'Session closed' };
    } catch (error) {
      console.error(`Error closing session for device ${deviceId}:`, error);
      throw new Error(`Failed to close session: ${error.message || 'Unknown error'}`);
    }
  }
};

export default sessionService; 