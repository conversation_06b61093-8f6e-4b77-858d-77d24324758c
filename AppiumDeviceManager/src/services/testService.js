// This is a mock service for test suites and test cases.
// In a real implementation, this would interact with your backend API.

// Mock data for testing UI
const MOCK_TEST_SUITES = [
  {
    id: '1',
    name: 'Login Flow Tests',
    description: 'Test suite for verifying login functionality across the app',
    tags: ['authentication', 'critical-path', 'regression'],
    tests: [
      { id: '101', name: 'Valid login credentials' },
      { id: '102', name: 'Invalid login credentials' },
      { id: '103', name: 'Password reset flow' },
      { id: '104', name: 'Remember me functionality' }
    ]
  },
  {
    id: '2',
    name: 'Profile Management Tests',
    description: 'Test suite for user profile management features',
    tags: ['profile', 'user-data', 'regression'],
    tests: [
      { id: '201', name: 'Update profile information' },
      { id: '202', name: 'Change password' },
      { id: '203', name: 'Update notification preferences' }
    ]
  },
  {
    id: '3',
    name: 'Payment Flow Tests',
    description: 'Test suite for payment processing and billing',
    tags: ['payment', 'critical-path', 'security'],
    tests: [
      { id: '301', name: 'Credit card payment' },
      { id: '302', name: 'PayPal integration' },
      { id: '303', name: 'Billing address validation' },
      { id: '304', name: 'Order summary verification' },
      { id: '305', name: 'Payment confirmation' }
    ]
  }
];

// In a real app, this would make API calls to your backend
export const testService = {
  // Get all test suites
  getTestSuites: async () => {
    // Simulate API delay
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(MOCK_TEST_SUITES);
      }, 1000);
    });
  },
  
  // Get a single test suite by ID
  getTestSuiteById: async (id) => {
    // Simulate API delay
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const suite = MOCK_TEST_SUITES.find(s => s.id === id);
        if (suite) {
          resolve(suite);
        } else {
          reject(new Error('Test suite not found'));
        }
      }, 500);
    });
  },
  
  // Create a new test suite
  createTestSuite: async (suiteData) => {
    // Simulate API delay
    return new Promise((resolve) => {
      setTimeout(() => {
        const newSuite = {
          id: `${Date.now()}`,
          ...suiteData,
          tests: []
        };
        resolve(newSuite);
      }, 1000);
    });
  },
  
  // Update an existing test suite
  updateTestSuite: async (id, suiteData) => {
    // Simulate API delay
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const suiteIndex = MOCK_TEST_SUITES.findIndex(s => s.id === id);
        if (suiteIndex !== -1) {
          const updatedSuite = {
            ...MOCK_TEST_SUITES[suiteIndex],
            ...suiteData
          };
          resolve(updatedSuite);
        } else {
          reject(new Error('Test suite not found'));
        }
      }, 1000);
    });
  },
  
  // Delete a test suite
  deleteTestSuite: async (id) => {
    // Simulate API delay
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true });
      }, 1000);
    });
  },
  
  // Add a test case to a suite
  addTestCase: async (suiteId, testCase) => {
    // Simulate API delay
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const suite = MOCK_TEST_SUITES.find(s => s.id === suiteId);
        if (suite) {
          const newTest = {
            id: `${Date.now()}`,
            ...testCase
          };
          resolve(newTest);
        } else {
          reject(new Error('Test suite not found'));
        }
      }, 1000);
    });
  },
  
  // Run a test suite on specified devices
  runTestSuite: async (suiteId, deviceIds, config = {}) => {
    // Simulate API delay
    return new Promise((resolve) => {
      setTimeout(() => {
        // In a real app, this would trigger test execution on the backend
        resolve({
          executionId: `exec-${Date.now()}`,
          status: 'started',
          message: 'Test execution started successfully'
        });
      }, 2000);
    });
  },
  
  // Get test execution status
  getExecutionStatus: async (executionId) => {
    // Simulate API delay
    return new Promise((resolve) => {
      setTimeout(() => {
        // In a real app, this would get the status from the backend
        resolve({
          executionId,
          status: 'in_progress',
          progress: 45,
          startTime: new Date(Date.now() - 120000).toISOString(),
          devices: [
            { id: '1', name: 'iPhone 13', status: 'running' },
            { id: '2', name: 'Google Pixel 6', status: 'pending' }
          ]
        });
      }, 500);
    });
  },
  
  // Get test results
  getTestResults: async (executionId) => {
    // Simulate API delay
    return new Promise((resolve) => {
      setTimeout(() => {
        // In a real app, this would get the results from the backend
        resolve({
          executionId,
          status: 'completed',
          startTime: new Date(Date.now() - 300000).toISOString(),
          endTime: new Date().toISOString(),
          summary: {
            total: 12,
            passed: 10,
            failed: 1,
            skipped: 1
          },
          devices: [
            { 
              id: '1', 
              name: 'iPhone 13', 
              status: 'completed',
              results: {
                total: 6,
                passed: 5,
                failed: 0,
                skipped: 1
              }
            },
            { 
              id: '2', 
              name: 'Google Pixel 6', 
              status: 'completed',
              results: {
                total: 6,
                passed: 5,
                failed: 1,
                skipped: 0
              }
            }
          ]
        });
      }, 1000);
    });
  }
}; 