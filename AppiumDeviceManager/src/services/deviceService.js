// This is a device service for real devices.
// This interacts with your backend API to get actual device information.

// Device service that makes API calls to the backend for real device detection
export const deviceService = {
  // Get all devices by calling the scan API
  getDevices: async () => {
    try {
      console.log('Fetching devices from backend API...');
      const response = await fetch('http://localhost:3001/api/devices/scan', {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch devices: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        console.log(`Successfully fetched ${result.devices.length} devices`);
        return result.devices;
      } else {
        throw new Error(result.error || 'Failed to fetch devices');
      }
    } catch (error) {
      console.error('Error in getDevices:', error);
      // Return empty array as fallback
      return [];
    }
  },
  
  // Get a single device by ID
  getDeviceById: async (id) => {
    try {
      const devices = await deviceService.getDevices();
      const device = devices.find(d => d.id === id || d.udid === id);

      if (device) {
        return device;
      } else {
        throw new Error('Device not found');
      }
    } catch (error) {
      console.error(`Error in getDeviceById for ${id}:`, error);
      throw error;
    }
  },
  
  // Connect to a device (would use ADB or XCUITest in a real implementation)
  connectDevice: async (deviceInfo) => {
    try {
      // In a real app, this would use ADB or XCUITest to connect to the device
      const response = await fetch('/api/devices/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(deviceInfo),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to connect device: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error connecting device:', error);
      // Return simulated response for development
      return { success: true, message: 'Device connected successfully' };
    }
  },
  
  // Disconnect a device
  disconnectDevice: async (deviceId) => {
    try {
      // In a real app, this would use ADB or XCUITest to disconnect the device
      const response = await fetch(`/api/devices/${deviceId}/disconnect`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to disconnect device: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error disconnecting device ${deviceId}:`, error);
      // Return simulated response for development
      return { success: true, message: 'Device disconnected successfully' };
    }
  },
  
  // Restart a device
  restartDevice: async (deviceId) => {
    try {
      // In a real app, this would use ADB or XCUITest to restart the device
      const response = await fetch(`/api/devices/${deviceId}/restart`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to restart device: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error restarting device ${deviceId}:`, error);
      // Return simulated response for development
      return { success: true, message: 'Device restarted successfully' };
    }
  },
  
  // Scan for available devices
  scanForDevices: async () => {
    try {
      // Make an explicit request to the backend to scan for devices
      const response = await fetch('http://localhost:3001/api/devices/scan', {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error(`Failed to scan for devices: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error scanning for devices:', error);
      return { success: false, message: 'Failed to scan for devices', error: error.message };
    }
  },

  // Launch automation app for a specific device
  launchAutomationApp: async (device) => {
    try {
      console.log(`Launching automation app for ${device.platform} device ${device.udid}`);
      const response = await fetch('http://localhost:3001/api/devices/launch-app', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device: device,
          platform: device.platform
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to launch automation app: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error launching automation app:', error);
      throw error;
    }
  }
}; 