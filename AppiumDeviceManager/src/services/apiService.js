import axios from 'axios';

// This service will act as a bridge to the AppiumTestDistribution backend API
// For now, it's just a mock, but in a real implementation, you would connect to ATD

const API_BASE_URL = 'http://localhost:4723/wd/hub'; // Default Appium server address

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for API calls
apiClient.interceptors.request.use(
  async (config) => {
    // You could add authentication headers here
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for API calls
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    // Handle specific error cases
    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      // Handle auth refresh if needed
      return apiClient(originalRequest);
    }
    
    return Promise.reject(error);
  }
);

export const apiService = {
  // Configure API base URL
  setBaseUrl: (url) => {
    apiClient.defaults.baseURL = url;
  },
  
  // Get API status
  getStatus: async () => {
    try {
      // In a real implementation, this would call the ATD status endpoint
      // For now, just simulate a response
      return { status: 'online', version: '1.0.0' };
    } catch (error) {
      console.error('Error getting API status:', error);
      throw error;
    }
  },
  
  // Start Appium server
  startServer: async (config = {}) => {
    try {
      // In a real implementation, this would start the Appium server
      // For now, just simulate a response
      return { success: true, message: 'Appium server started' };
    } catch (error) {
      console.error('Error starting Appium server:', error);
      throw error;
    }
  },
  
  // Stop Appium server
  stopServer: async () => {
    try {
      // In a real implementation, this would stop the Appium server
      // For now, just simulate a response
      return { success: true, message: 'Appium server stopped' };
    } catch (error) {
      console.error('Error stopping Appium server:', error);
      throw error;
    }
  },
  
  // Execute a raw API request
  request: async (method, endpoint, data = null) => {
    try {
      const response = await apiClient({
        method,
        url: endpoint,
        data,
      });
      
      return response.data;
    } catch (error) {
      console.error(`Error making ${method} request to ${endpoint}:`, error);
      throw error;
    }
  },
};

export default apiService; 