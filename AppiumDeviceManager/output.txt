
> appium-device-manager@1.0.0 prestart
> npm run clean-ports


> appium-device-manager@1.0.0 clean-ports
> PID1=$(lsof -t -i:1234); if [ -n "$PID1" ]; then kill -9 $PID1; fi; PID2=$(lsof -t -i:3001); if [ -n "$PID2" ]; then kill -9 $PID2; fi


> appium-device-manager@1.0.0 start
> concurrently --kill-others "npm run server" "parcel src/index.html src/test-session.html --port 1234"

[0] 
[0] > appium-device-manager@1.0.0 server
[0] > node server.js
[0] 
[0] Backend server running on http://localhost:3001
[0] Session management system initialized
[0] iOS base port: 8080, Android base port: 8081
[1] Server running at http://localhost:1234
[1] ✨ Built in 9ms
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/devices/scan
[0] Detecting Android devices...
[0] Found 0 Android devices
[0] Detecting iOS devices...
[0] Found iOS device: 00008030-00020C123E60402E
[0] iOS device details: {"id":"00008030-00020C123E60402E","udid":"00008030-00020C123E60402E","platform":"iOS","status":"Online","name":"iPhone","model":"iPhone12,8","osVersion":"18.4.1","manufacturer":"Apple"}
[0] Found iOS device: 00008120-00186C801E13C01E
[0] iOS device details: {"id":"00008120-00186C801E13C01E","udid":"00008120-00186C801E13C01E","platform":"iOS","status":"Online","name":"iPhone","model":"iPhone15,2","osVersion":"18.5","manufacturer":"Apple"}
[0] Found 2 iOS devices
[0] Total devices found: 2
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received POST request for /api/devices/launch-app
[0] Launching run.py for iOS device 00008120-00186C801E13C01E on port 8086 with session session_1751283351609_jlpn1bp0l
[0] Session created: session_1751283351611_32oq3jrzs for device 00008120-00186C801E13C01E on port 8086
[0] Automation app launched with PID: 32806, Session: session_1751283351609_jlpn1bp0l
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,067 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,068 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,069 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/test_cases
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,070 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,070 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,071 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,071 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/test_suites
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,072 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,072 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,073 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,073 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,073 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8086
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,074 - __main__ - INFO - Device-specific session isolation enabled for device: 00008120-00186C801E13C01E
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,074 - __main__ - INFO - Using device-specific database suffix: _device_00008120_00186C801E13C01E
[0] 2025-06-30 21:35:52,074 - __main__ - INFO - Platform: iOS
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,074 - __main__ - INFO - Using custom ports (Flask: 8086, Appium: 4723, WDA: 8100) - preserving existing processes for multi-instance support
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:52,074 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
[0] 
[0] [API] Received GET request for /api/sessions
[0] Cleaned up dead session: session_1751283351609_jlpn1bp0l for device 00008120-00186C801E13C01E
[0] [API] Received GET request for /api/sessions
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:53,716 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_device_00008120_00186C801E13C01E.db
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:53,716 - utils.global_values_db - INFO - Global values database initialized successfully
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:53,717 - utils.global_values_db - INFO - Using global values from config.py
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:53,717 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:53,720 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:53,721 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:53,771 - AppiumDeviceController - INFO - Successfully imported Airtest library.
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:54,283 - session_isolation_manager - INFO - Session Isolation Manager initialized with base dir: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 2025-06-30 21:35:54,285 - enhanced_device_manager - INFO - Enhanced Device Manager initialized
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: Traceback (most recent call last):
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 88, in register_current_session
[0]     logger.info(f"Registering session {session_id} for device {device_id} with device manager")
[0]     ^^^^^^
[0] NameError: name 'logger' is not defined
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr: 
[0] During handling of the above exception, another exception occurred:
[0] 
[0] Traceback (most recent call last):
[0] 
[0] [session_1751283351609_jlpn1bp0l] stderr:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/run.py", line 272, in <module>
[0]     import app as flask_app
[0]   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 123, in <module>
[0]     register_current_session()
[0]     ~~~~~~~~~~~~~~~~~~~~~~~~^^
[0]   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 120, in register_current_session
[0]     logger.error(f"Error in session registration: {e}")
[0]     ^^^^^^
[0] NameError: name 'logger' is not defined
[0] 
[0] [session_1751283351609_jlpn1bp0l] Process exited with code: 1
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
