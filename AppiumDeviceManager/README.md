# Appium Device Manager

A user-friendly interface for managing mobile devices and running automated tests with Appium and AppiumTestDistribution.

## Features

- 📱 Device management - View and control connected Android and iOS devices
- 🧪 Test suite management - Organize and configure your test suites
- ▶️ Test execution - Run tests across multiple devices in parallel
- 📊 Results visualization - View and analyze test results
- ⚙️ Settings management - Configure Appium and test settings

## Screenshots

_Screenshots will be added after initial implementation_

## Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Appium 2.0 or later installed with appium-device-farm plugin
- Connected Android/iOS devices or emulators/simulators
- AppiumTestDistribution configured

## Installation

1. Clone this repository:
   ```
   git clone <repository-url>
   cd AppiumDeviceManager
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm start
   ```

4. Open [http://localhost:1234](http://localhost:1234) in your browser.

## Project Structure

```
AppiumDeviceManager/
  ├── src/                  # Source files
  │   ├── components/       # React components
  │   ├── services/         # API service layer
  │   ├── utils/            # Utility functions
  │   ├── styles/           # Global styles
  │   ├── index.html        # HTML entry point
  │   └── index.js          # JavaScript entry point
  ├── package.json          # Project dependencies
  └── README.md             # This file
```

## Integration with AppiumTestDistribution

This UI is designed to work seamlessly with [AppiumTestDistribution](https://github.com/AppiumTestDistribution/AppiumTestDistribution) for parallel test execution.

In a real implementation, the service layer would connect to ATD's API endpoints to:
- Detect and manage connected devices
- Run test suites in parallel across devices
- Collect and display test results

## Development

This project uses:
- React for the UI framework
- Styled Components for styling
- Parcel for bundling

To build for production:
```
npm run build
```

## Future Enhancements

- Real-time device monitoring
- Test recording and playback
- Test script generation from UI interactions
- Integration with CI/CD pipelines
- Advanced reporting and analytics

## License

MIT 