const { createProxyMiddleware } = require('http-proxy-middleware');

const API_TARGET = 'http://localhost:3001';

console.log(`[PROXY] Middleware loaded. Forwarding all /api requests to ${API_TARGET}`);

module.exports = function (app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: API_TARGET,
      changeOrigin: true,
      logLevel: 'debug',
      onProxyReq: (proxyReq, req, res) => {
        console.log(`[HPM Proxy] ==> ${req.method} ${req.originalUrl} -> ${API_TARGET}${proxyReq.path}`);
      },
      onError: (err, req, res) => {
        console.error('[HPM Proxy] Error:', err);
      },
    })
  );

  console.log('✅ Simple, unified proxy for /api is now active with diagnostics.');
};