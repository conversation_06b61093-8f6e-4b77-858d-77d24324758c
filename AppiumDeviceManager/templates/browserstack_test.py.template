from appium import webdriver
from appium.webdriver.common.appiumby import Appium<PERSON>y
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import os

def main():
    # BrowserStack credentials from environment variables
    username = os.environ.get('BROWSERSTACK_USERNAME')
    access_key = os.environ.get('BROWSERSTACK_ACCESS_KEY')
    
    if not username or not access_key:
        print("Error: Missing BrowserStack credentials")
        return 1

    # Desired capabilities
    desired_caps = {
        'platformName': '{{os}}',
        'platformVersion': '{{os_version}}',
        'deviceName': '{{device}}',
        'app': 'bs://c700ce60cf13ae8ed97705a55b8e13f5c982202c',  # Sample app
        'bstack:options': {
            'userName': username,
            'accessKey': access_key,
            'projectName': 'Appium Device Manager',
            'buildName': 'Automated Test',
            'sessionName': 'Test Session'
        }
    }

    driver = None
    try:
        # Initialize the WebDriver
        driver = webdriver.Remote("https://hub-cloud.browserstack.com/wd/hub", desired_caps)
        
        # Simple test case - wait for an element and click it
        search_element = WebDriverWait(driver, 30).until(
            EC.element_to_be_clickable((AppiumBy.ACCESSIBILITY_ID, "Search Wikipedia"))
        )
        search_element.click()
        
        # Add a small delay to see the action
        import time
        time.sleep(2)
        
        print("Test completed successfully!")
        return 0
        
    except Exception as e:
        print(f"Test failed: {str(e)}")
        return 1
        
    finally:
        # Quit the driver
        if driver:
            driver.quit()

if __name__ == "__main__":
    exit(main())
