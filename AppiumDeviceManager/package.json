{"name": "appium-device-manager", "version": "1.0.0", "description": "UI interface for managing devices and test sessions with AppiumTestDistribution", "source": "src/index.html", "scripts": {"prestart": "npm run clean-ports", "start": "concurrently --kill-others \"npm run server\" \"parcel src/index.html src/test-session.html --port 1234\"", "build": "parcel build src/index.html src/test-session.html", "test": "echo \"Error: no test specified\" && exit 1", "clean-ports": "PID1=$(lsof -t -i:1234); if [ -n \"$PID1\" ]; then kill -9 $PID1; fi; PID2=$(lsof -t -i:3001); if [ -n \"$PID2\" ]; then kill -9 $PID2; fi", "server": "node server.js"}, "staticFiles": {"staticPath": "src/assets"}, "browserslist": "> 0.5%, last 2 versions, not dead", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.2", "axios": "^1.6.2", "cors": "^2.8.5", "express": "^4.18.2", "http-proxy-middleware": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "styled-components": "^6.1.1", "ws": "^8.13.0"}, "devDependencies": {"@parcel/transformer-sass": "^2.10.3", "buffer": "^6.0.3", "concurrently": "^9.1.2", "parcel": "^2.15.4", "process": "^0.11.10"}}