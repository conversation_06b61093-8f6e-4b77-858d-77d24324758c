const express = require('express');
const cors = require('cors');
const path = require('path');
const { spawn, exec } = require('child_process');
const fs = require('fs');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Session management system
class SessionManager {
  constructor() {
    this.sessions = new Map(); // deviceId -> session info
    this.usedPorts = new Set();
    this.basePortIOS = 8080;
    this.basePortAndroid = 8081;
  }

  // Get next available port for platform
  async getNextAvailablePort(platform) {
    const basePort = platform.toLowerCase() === 'ios' ? this.basePortIOS : this.basePortAndroid;
    let port = basePort;

    while (this.usedPorts.has(port) || !(await this.isPortAvailable(port))) {
      port++;
    }

    return port;
  }

  // Create new session
  createSession(deviceId, platform, port, processId) {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const session = {
      sessionId,
      deviceId,
      platform,
      port,
      processId,
      status: 'active',
      startTime: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };

    this.sessions.set(deviceId, session);
    this.usedPorts.add(port);

    console.log(`Session created: ${sessionId} for device ${deviceId} on port ${port}`);
    return session;
  }

  // Get session by device ID
  getSession(deviceId) {
    return this.sessions.get(deviceId);
  }

  // Get all active sessions
  getAllSessions() {
    return Array.from(this.sessions.values());
  }

  // Update session activity
  updateActivity(deviceId) {
    const session = this.sessions.get(deviceId);
    if (session) {
      session.lastActivity = new Date().toISOString();
    }
  }

  // Terminate session
  async terminateSession(deviceId) {
    const session = this.sessions.get(deviceId);
    if (!session) {
      throw new Error(`No session found for device ${deviceId}`);
    }

    try {
      // Kill the process if it's still running
      if (session.processId) {
        try {
          process.kill(session.processId, 'SIGTERM');
          console.log(`Terminated process ${session.processId} for session ${session.sessionId}`);
        } catch (killError) {
          console.warn(`Failed to kill process ${session.processId}:`, killError.message);
        }
      }

      // Free up the port
      this.usedPorts.delete(session.port);

      // Remove session
      this.sessions.delete(deviceId);

      console.log(`Session terminated: ${session.sessionId} for device ${deviceId}`);
      return session;
    } catch (error) {
      console.error(`Error terminating session for device ${deviceId}:`, error);
      throw error;
    }
  }

  // Check if port is available
  async isPortAvailable(port) {
    try {
      const { stdout } = await execAsync(`lsof -i :${port}`, { timeout: 5000 });
      return !stdout.trim();
    } catch (error) {
      // If lsof fails, assume port is available
      return true;
    }
  }

  // Cleanup dead sessions
  async cleanupDeadSessions() {
    const sessionsToRemove = [];

    for (const [deviceId, session] of this.sessions.entries()) {
      try {
        // Check if process is still running
        if (session.processId) {
          process.kill(session.processId, 0); // Signal 0 just checks if process exists
        }

        // Check if port is still in use
        const portInUse = !(await this.isPortAvailable(session.port));
        if (!portInUse) {
          sessionsToRemove.push(deviceId);
        }
      } catch (error) {
        // Process doesn't exist
        sessionsToRemove.push(deviceId);
      }
    }

    // Remove dead sessions
    for (const deviceId of sessionsToRemove) {
      const session = this.sessions.get(deviceId);
      if (session) {
        this.usedPorts.delete(session.port);
        this.sessions.delete(deviceId);
        console.log(`Cleaned up dead session: ${session.sessionId} for device ${deviceId}`);
      }
    }

    return sessionsToRemove.length;
  }
}

// Global session manager instance
const sessionManager = new SessionManager();

// Device detection utility functions
async function detectAndroidDevices() {
  try {
    console.log('Detecting Android devices...');
    const { stdout } = await execAsync('adb devices', { timeout: 10000 });
    const devices = [];
    const lines = stdout.trim().split('\n');

    // Skip the first line (header: "List of devices attached")
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      const [deviceId, status] = line.split('\t');
      if (!deviceId || !status) continue;

      console.log(`Found Android device: ${deviceId} (${status})`);

      // Get detailed device information
      let deviceInfo = {
        id: deviceId,
        udid: deviceId,
        platform: 'Android',
        status: status === 'device' ? 'Online' :
                status === 'offline' ? 'Offline' :
                status === 'unauthorized' ? 'Unauthorized' : 'Unknown',
        name: `Android Device (${deviceId})`,
        model: 'Unknown',
        osVersion: 'Unknown',
        manufacturer: 'Unknown'
      };

      // Only get detailed info for online devices
      if (status === 'device') {
        try {
          // Get device model
          const modelResult = await execAsync(`adb -s ${deviceId} shell getprop ro.product.model`, { timeout: 5000 });
          if (modelResult.stdout.trim()) {
            deviceInfo.model = modelResult.stdout.trim();
            deviceInfo.name = deviceInfo.model;
          }

          // Get Android version
          const versionResult = await execAsync(`adb -s ${deviceId} shell getprop ro.build.version.release`, { timeout: 5000 });
          if (versionResult.stdout.trim()) {
            deviceInfo.osVersion = versionResult.stdout.trim();
          }

          // Get manufacturer
          const manufacturerResult = await execAsync(`adb -s ${deviceId} shell getprop ro.product.manufacturer`, { timeout: 5000 });
          if (manufacturerResult.stdout.trim()) {
            deviceInfo.manufacturer = manufacturerResult.stdout.trim();
          }

          console.log(`Android device details: ${JSON.stringify(deviceInfo)}`);
        } catch (detailError) {
          console.warn(`Failed to get details for Android device ${deviceId}:`, detailError.message);
        }
      }

      devices.push(deviceInfo);
    }

    return devices;
  } catch (error) {
    console.error('Error detecting Android devices:', error.message);
    if (error.message.includes('command not found') || error.message.includes('not recognized')) {
      throw new Error('ADB not found. Please install Android SDK and add ADB to your PATH.');
    }
    throw error;
  }
}

async function detectIOSDevices() {
  try {
    console.log('Detecting iOS devices...');
    const { stdout } = await execAsync('idevice_id -l', { timeout: 10000 });
    const devices = [];
    const lines = stdout.trim().split('\n');

    for (const line of lines) {
      const udid = line.trim();
      if (!udid) continue;

      console.log(`Found iOS device: ${udid}`);

      let deviceInfo = {
        id: udid,
        udid: udid,
        platform: 'iOS',
        status: 'Online',
        name: `iOS Device (${udid})`,
        model: 'Unknown',
        osVersion: 'Unknown',
        manufacturer: 'Apple'
      };

      try {
        // Get device name
        const nameResult = await execAsync(`ideviceinfo -u ${udid} -k DeviceName`, { timeout: 5000 });
        if (nameResult.stdout.trim()) {
          deviceInfo.name = nameResult.stdout.trim();
        }

        // Get device model
        const modelResult = await execAsync(`ideviceinfo -u ${udid} -k ProductType`, { timeout: 5000 });
        if (modelResult.stdout.trim()) {
          deviceInfo.model = modelResult.stdout.trim();
        }

        // Get iOS version
        const versionResult = await execAsync(`ideviceinfo -u ${udid} -k ProductVersion`, { timeout: 5000 });
        if (versionResult.stdout.trim()) {
          deviceInfo.osVersion = versionResult.stdout.trim();
        }

        console.log(`iOS device details: ${JSON.stringify(deviceInfo)}`);
      } catch (detailError) {
        console.warn(`Failed to get details for iOS device ${udid}:`, detailError.message);
      }

      devices.push(deviceInfo);
    }

    return devices;
  } catch (error) {
    console.error('Error detecting iOS devices:', error.message);
    if (error.message.includes('command not found') || error.message.includes('not recognized')) {
      throw new Error('iOS device tools not found. Please install libimobiledevice (brew install libimobiledevice).');
    }
    throw error;
  }
}

const app = express();
app.use(cors());
app.use(express.json());

// Explicit CORS preflight handler for custom headers
app.options('/api/devices', (req, res) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, X-BS-User, X-BS-Key');
  res.sendStatus(204);
});

// API endpoint for BrowserStack devices
app.get('/api/devices', async (req, res) => {
  const { 'x-bs-user': username, 'x-bs-key': accessKey } = req.headers;
  console.log('[API] Received GET request for /api/devices');

  if (!username || !accessKey) {
    console.error('[API] Missing username or accessKey in headers.');
    return res.status(400).json({ error: 'Username and Access Key are required.' });
  }

  const credentials = Buffer.from(`${username}:${accessKey}`).toString('base64');
  const url = 'https://api-cloud.browserstack.com/app-automate/devices.json';

  try {
    console.log(`[API] Forwarding request to BrowserStack: ${url}`);
    const bsResponse = await fetch(url, {
      method: 'GET',
      headers: { 'Authorization': `Basic ${credentials}` },
    });

    const responseBodyText = await bsResponse.text();
    console.log(`[API] BrowserStack response status: ${bsResponse.status}`);

    if (!bsResponse.ok) {
      console.error(`[API] BrowserStack request failed: ${responseBodyText}`);
      return res.status(bsResponse.status).json({ error: `BrowserStack API Error: ${responseBodyText}` });
    }

    res.setHeader('Content-Type', 'application/json');
    res.send(responseBodyText);

  } catch (error) {
    console.error('[API] Internal server error:', error);
    res.status(500).json({ error: `Internal Server Error: ${error.message}` });
  }
});

// API endpoint for scanning local devices
app.get('/api/devices/scan', async (req, res) => {
  console.log('[API] Received GET request for /api/devices/scan');

  try {
    const allDevices = [];

    // Detect Android devices
    try {
      const androidDevices = await detectAndroidDevices();
      allDevices.push(...androidDevices);
      console.log(`Found ${androidDevices.length} Android devices`);
    } catch (androidError) {
      console.warn('Android device detection failed:', androidError.message);
    }

    // Detect iOS devices
    try {
      const iosDevices = await detectIOSDevices();
      allDevices.push(...iosDevices);
      console.log(`Found ${iosDevices.length} iOS devices`);
    } catch (iosError) {
      console.warn('iOS device detection failed:', iosError.message);
    }

    console.log(`Total devices found: ${allDevices.length}`);
    res.json({ success: true, devices: allDevices });

  } catch (error) {
    console.error('[API] Error scanning for devices:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to scan for devices',
      details: error.message
    });
  }
});

// API endpoint for Android devices only
app.get('/api/devices/android', async (req, res) => {
  console.log('[API] Received GET request for /api/devices/android');

  try {
    const androidDevices = await detectAndroidDevices();
    console.log(`Found ${androidDevices.length} Android devices`);
    res.json({ success: true, devices: androidDevices });

  } catch (error) {
    console.error('[API] Error detecting Android devices:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to detect Android devices',
      details: error.message
    });
  }
});

// API endpoint for iOS devices only
app.get('/api/devices/ios', async (req, res) => {
  console.log('[API] Received GET request for /api/devices/ios');

  try {
    const iosDevices = await detectIOSDevices();
    console.log(`Found ${iosDevices.length} iOS devices`);
    res.json({ success: true, devices: iosDevices });

  } catch (error) {
    console.error('[API] Error detecting iOS devices:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to detect iOS devices',
      details: error.message
    });
  }
});

// Session management API endpoints

// Get all active sessions
app.get('/api/sessions', async (req, res) => {
  console.log('[API] Received GET request for /api/sessions');

  try {
    // Cleanup dead sessions first
    await sessionManager.cleanupDeadSessions();

    const sessions = sessionManager.getAllSessions();
    res.json({ success: true, sessions });
  } catch (error) {
    console.error('[API] Error getting sessions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get sessions',
      details: error.message
    });
  }
});

// Get session status for specific device
app.get('/api/sessions/status/:deviceId', async (req, res) => {
  console.log(`[API] Received GET request for /api/sessions/status/${req.params.deviceId}`);

  try {
    const { deviceId } = req.params;
    const session = sessionManager.getSession(deviceId);

    if (!session) {
      return res.json({ success: true, hasSession: false, session: null });
    }

    // Update activity
    sessionManager.updateActivity(deviceId);

    res.json({ success: true, hasSession: true, session });
  } catch (error) {
    console.error('[API] Error getting session status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get session status',
      details: error.message
    });
  }
});

// Terminate session
app.post('/api/sessions/terminate', async (req, res) => {
  console.log('[API] Received POST request for /api/sessions/terminate');

  try {
    const { deviceId } = req.body;

    if (!deviceId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameter',
        details: 'Device ID is required'
      });
    }

    const terminatedSession = await sessionManager.terminateSession(deviceId);

    res.json({
      success: true,
      message: 'Session terminated successfully',
      session: terminatedSession
    });
  } catch (error) {
    console.error('[API] Error terminating session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to terminate session',
      details: error.message
    });
  }
});

// Cleanup dead sessions
app.post('/api/sessions/cleanup', async (req, res) => {
  console.log('[API] Received POST request for /api/sessions/cleanup');

  try {
    const cleanedCount = await sessionManager.cleanupDeadSessions();

    res.json({
      success: true,
      message: `Cleaned up ${cleanedCount} dead sessions`,
      cleanedCount
    });
  } catch (error) {
    console.error('[API] Error cleaning up sessions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup sessions',
      details: error.message
    });
  }
});

// API endpoint for launching automation app
app.post('/api/devices/launch-app', async (req, res) => {
  console.log('[API] Received POST request for /api/devices/launch-app');
  const { device, platform } = req.body;

  if (!device || !platform) {
    return res.status(400).json({
      success: false,
      error: 'Missing required parameters',
      details: 'Device UDID and platform are required'
    });
  }

  try {
    const deviceId = device.udid || device.id;

    // Check if device already has an active session
    const existingSession = sessionManager.getSession(deviceId);
    if (existingSession) {
      console.log(`Device ${deviceId} already has an active session on port ${existingSession.port}`);
      
      // Generate URL with pre-connected device parameters
      const sessionUrl = `http://localhost:${existingSession.port}?deviceId=${deviceId}&platform=${platform}&sessionId=${existingSession.sessionId}&autoConnect=true&hideDeviceList=true`;
      
      return res.json({
        success: true,
        message: 'Automation app is already running for this device',
        url: sessionUrl,
        port: existingSession.port,
        session: existingSession,
        alreadyRunning: true
      });
    }

    // Determine the script to run based on platform
    let scriptName;
    if (platform.toLowerCase() === 'ios') {
      scriptName = 'run.py';
    } else if (platform.toLowerCase() === 'android') {
      scriptName = 'run_android.py';
    } else {
      return res.status(400).json({
        success: false,
        error: 'Invalid platform',
        details: 'Platform must be either iOS or Android'
      });
    }

    // Get next available port for this platform
    const port = await sessionManager.getNextAvailablePort(platform);
    
    // Get the project root directory (parent of AppiumDeviceManager, then into MobileApp-AutoTest)
    const projectRoot = path.join(__dirname, '..', 'MobileApp-AutoTest');
    const scriptPath = path.join(projectRoot, scriptName);

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      return res.status(404).json({
        success: false,
        error: 'Automation script not found',
        details: `The script ${scriptName} does not exist at ${scriptPath}`
      });
    }

    // Double-check port availability
    const portAvailable = await sessionManager.isPortAvailable(port);
    if (!portAvailable) {
      return res.status(409).json({
        success: false,
        error: 'Port conflict',
        details: `Port ${port} is already in use`
      });
    }

    // Generate unique session ID
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Enhanced environment variables for complete session isolation
    const env = {
      ...process.env,
      // Original variables (maintain compatibility)
      SELECTED_DEVICE_UDID: deviceId,
      SELECTED_DEVICE_PLATFORM: platform,
      
      // Enhanced session isolation variables
      SELECTED_DEVICE_ID: deviceId,
      SELECTED_PLATFORM: platform,
      SESSION_ID: sessionId,
      INSTANCE_DB_SUFFIX: `_device_${deviceId.replace(/-/g, '_')}`,
      SESSION_ISOLATION_MODE: 'true'
    };

    // Launch the automation app with enhanced environment
    console.log(`Launching ${scriptName} for ${platform} device ${deviceId} on port ${port} with session ${sessionId}`);
    const automationProcess = spawn('bash', ['-c', `source venv/bin/activate && python3 ${scriptName} --port ${port}`], {
      cwd: projectRoot,
      env: env,
      detached: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });

    // Create enhanced session with session ID
    const session = sessionManager.createSession(deviceId, platform, port, automationProcess.pid);
    session.sessionId = sessionId; // Add session ID to the session object

    // Log output for debugging
    automationProcess.stdout.on('data', (data) => {
      console.log(`[${sessionId}] stdout: ${data}`);
      sessionManager.updateActivity(deviceId);
    });

    automationProcess.stderr.on('data', (data) => {
      console.error(`[${sessionId}] stderr: ${data}`);
      sessionManager.updateActivity(deviceId);
    });

    automationProcess.on('exit', (code) => {
      console.log(`[${sessionId}] Process exited with code: ${code}`);
      // Clean up session when process exits
      try {
        const existingSession = sessionManager.getSession(deviceId);
        if (existingSession) {
          sessionManager.terminateSession(deviceId);
        }
      } catch (error) {
        console.warn(`Failed to cleanup session for device ${deviceId}:`, error.message);
      }
    });

    automationProcess.on('error', (error) => {
      console.error(`[${sessionId}] Process error:`, error);
      // Clean up session on error
      try {
        const existingSession = sessionManager.getSession(deviceId);
        if (existingSession) {
          sessionManager.terminateSession(deviceId);
        }
      } catch (cleanupError) {
        console.warn(`Failed to cleanup session for device ${deviceId}:`, cleanupError.message);
      }
    });

    // Detach the process so it continues running independently
    automationProcess.unref();

    console.log(`Automation app launched with PID: ${automationProcess.pid}, Session: ${sessionId}`);

    // Generate URL with pre-connected device parameters for simplified UI
    const sessionUrl = `http://localhost:${port}?deviceId=${deviceId}&platform=${platform}&sessionId=${sessionId}&autoConnect=true&hideDeviceList=true`;

    // Give the app a moment to start up
    setTimeout(() => {
      res.json({
        success: true,
        message: 'Automation app launched successfully',
        url: sessionUrl,
        port: port,
        pid: automationProcess.pid,
        session: session,
        sessionId: sessionId,
        alreadyRunning: false
      });
    }, 2000);

  } catch (error) {
    console.error('[API] Error launching automation app:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to launch automation app',
      details: error.message
    });
  }
});;

// Endpoint to get the list of test scripts
app.get(['/api/tests', '/api/tests/'], (req, res) => {
  const testsDirectory = path.join(__dirname, 'tests');
  fs.readdir(testsDirectory, (err, files) => {
    if (err) {
      console.error('Could not list the directory.', err);
      return res.status(500).send('Unable to read test scripts.');
    }
    const testFiles = files.filter(file => file.endsWith('.py'));
    res.json(testFiles);
  });
});

// Endpoint to run a test using BrowserStack SDK
app.post('/api/run-test', async (req, res) => {
  const { device, os_version, os, testScript } = req.body;
  const { 'x-bs-user': username, 'x-bs-key': accessKey } = req.headers;

  if (!device || !os_version || !os) {
    return res.status(400).json({
      error: 'Missing required parameters',
      details: 'Device, OS version, and OS are required'
    });
  }

  if (!username || !accessKey) {
    return res.status(401).json({
      error: 'Unauthorized',
      details: 'BrowserStack credentials are required'
    });
  }

  let testFile;
  let pythonProcess;
  let timeout;

  const cleanup = () => {
    // Clean up the timeout
    if (timeout) clearTimeout(timeout);
    
    // Commented out test file deletion to preserve the file after test run
    /*
    if (testFile && fs.existsSync(testFile)) {
      try {
        fs.unlinkSync(testFile);
        console.log('Test file cleaned up:', testFile);
      } catch (e) {
        console.error('Error cleaning up test file:', e);
      }
    }
    */
  };

  try {
    // Create a test name based on the device and timestamp
    const testName = `Test on ${device} (${os} ${os_version}) - ${new Date().toISOString()}`;
    const buildName = `Build ${new Date().toISOString().split('T')[0]}`;
    
    // Use the selected test script
    const testDir = path.join(__dirname, 'tests');
    testFile = path.join(testDir, testScript || 'mobile_test.py');
    
    // Ensure test file exists
    if (!fs.existsSync(testFile)) {
      return res.status(404).json({
        error: 'Test script not found',
        details: `The test script ${testScript} does not exist`
      });
    }
    
    // Ensure tests directory exists
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }

    // Set environment variables for the test
    const env = {
      ...process.env,
      BROWSERSTACK_USERNAME: username,
      BROWSERSTACK_ACCESS_KEY: accessKey,
      BROWSERSTACK_DEVICE: device,
      BROWSERSTACK_OS: os,
      BROWSERSTACK_OS_VERSION: os_version
    };
    
    // Prepare command arguments
    const args = [testFile, device, os_version, os];
    
    // Log the command being executed
    console.log('Executing test with arguments:', { args, env: { ...env, BROWSERSTACK_ACCESS_KEY: '***' } });
    
    // Execute the test script with arguments
    pythonProcess = spawn('python3', args, {
      env,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    console.log(`Test process started with PID: ${pythonProcess.pid}`);
    
    let stdout = '';
    let stderr = '';
    
    // Set up stdout/stderr handlers
    pythonProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[TEST OUTPUT] ${output}`);
      stdout += output;
    });
    
    pythonProcess.stderr.on('data', (data) => {
      const error = data.toString();
      console.error(`[TEST ERROR] ${error}`);
      stderr += error;
    });
    
    // Handle process exit
    const handleExit = (code, signal) => {
      console.log(`Test process exited with code ${code}, signal: ${signal}`);
      cleanup();
      
      if (code !== 0) {
        console.error(`Test execution failed with code ${code}: ${stderr}`);
        if (!res.headersSent) {
          return res.status(500).json({
            error: 'Test execution failed',
            details: stderr || 'Unknown error occurred',
            stdout: stdout,
            stderr: stderr,
            exitCode: code
          });
        }
      } else if (!res.headersSent) {
        // Return success response
        res.status(200).json({
          message: 'Test executed successfully',
          output: stdout,
          error: stderr || null,
          exitCode: code
        });
      }
    };
    
    pythonProcess.on('close', handleExit);
    
    // Handle process errors
    pythonProcess.on('error', (error) => {
      console.error('Failed to start test process:', error);
      cleanup();
      
      if (!res.headersSent) {
        res.status(500).json({
          error: 'Failed to start test process',
          details: error.message
        });
      }
    });
    
    // Set a timeout for the test execution
    timeout = setTimeout(() => {
      if (pythonProcess && !pythonProcess.killed) {
        console.error('Test execution timed out - terminating process');
        pythonProcess.kill('SIGTERM');
        
        if (!res.headersSent) {
          res.status(500).json({
            error: 'Test execution timed out',
            details: 'The test took too long to complete (5 minute timeout)'
          });
        }
      }
    }, 300000); // 5 minutes timeout
    
  } catch (error) {
    console.error('Error in test execution:', error);
    cleanup();
    
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to execute test',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }
});

// Serve static files from the 'dist' directory
app.use(express.static(path.join(__dirname, 'dist')));

// For any other request, serve the index.html file
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Periodic cleanup of dead sessions (every 30 seconds)
setInterval(async () => {
  try {
    const cleanedCount = await sessionManager.cleanupDeadSessions();
    if (cleanedCount > 0) {
      console.log(`Periodic cleanup: removed ${cleanedCount} dead sessions`);
    }
  } catch (error) {
    console.error('Error during periodic session cleanup:', error);
  }
}, 30000);

const PORT = 3001;

// Enhanced session management endpoints for device manager integration

// Get session status for a device
app.get('/api/sessions/:deviceId/status', (req, res) => {
  try {
    const { deviceId } = req.params;
    const session = sessionManager.getSession(deviceId);
    
    if (session) {
      const sessionUrl = `http://localhost:${session.port}?deviceId=${deviceId}&sessionId=${session.sessionId}&autoConnect=true&hideDeviceList=true`;
      res.json({
        success: true,
        session: session,
        status: 'active',
        url: sessionUrl
      });
    } else {
      res.json({
        success: true,
        session: null,
        status: 'inactive'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Update device status
app.post('/api/devices/:deviceId/status', (req, res) => {
  try {
    const { deviceId } = req.params;
    const { status } = req.body;
    
    // Update device status in session manager
    const session = sessionManager.getSession(deviceId);
    if (session) {
      session.deviceStatus = status;
      res.json({
        success: true,
        message: `Device ${deviceId} status updated to ${status}`
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'No active session found for device'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

app.listen(PORT, () => {
  console.log(`Backend server running on http://localhost:${PORT}`);
  console.log('Session management system initialized');
  console.log(`iOS base port: ${sessionManager.basePortIOS}, Android base port: ${sessionManager.basePortAndroid}`);
});