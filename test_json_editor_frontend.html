<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Editor Frontend Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .code-block { background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>JSON Editor Frontend Test Suite</h1>
        <p class="text-muted">Testing the new JSON editor functionality for test case modification</p>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body" id="testResults">
                        <div class="test-info">
                            <strong>Starting tests...</strong>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Manual Test Instructions</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>Search Functionality:</strong>
                                <ul>
                                    <li>Go to Test Case Modification tab</li>
                                    <li>Search for test cases using locator value, image files, or text values</li>
                                    <li>Click "Edit" button on any result</li>
                                    <li>Verify JSON editor modal opens with test case data</li>
                                </ul>
                            </li>
                            <li><strong>JSON Editor Features:</strong>
                                <ul>
                                    <li>Verify line numbers are displayed</li>
                                    <li>Test search functionality within JSON editor</li>
                                    <li>Try case-sensitive and case-insensitive search</li>
                                    <li>Use next/previous buttons to navigate search results</li>
                                </ul>
                            </li>
                            <li><strong>Validation Testing:</strong>
                                <ul>
                                    <li>Modify JSON to have invalid syntax and click "Validate"</li>
                                    <li>Remove required fields and test validation</li>
                                    <li>Add invalid action types and test validation</li>
                                    <li>Test action ID validation (should be 10 chars or 'al_' + 7 chars)</li>
                                </ul>
                            </li>
                            <li><strong>Save/Revert Testing:</strong>
                                <ul>
                                    <li>Make changes to JSON and save</li>
                                    <li>Verify changes are persisted</li>
                                    <li>Test revert functionality</li>
                                    <li>Verify UI updates after save</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Sample JSON</h5>
                    </div>
                    <div class="card-body">
                        <div class="code-block" id="sampleJson">
                            Loading sample JSON...
                        </div>
                        <button class="btn btn-sm btn-primary mt-2" onclick="copySampleJson()">Copy to Clipboard</button>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Action ID Generator Test</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testActionIdGeneration()">Generate Test IDs</button>
                        <div id="actionIdResults" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test results container
        const testResults = document.getElementById('testResults');
        
        // Sample test case JSON
        const sampleTestCase = {
            "name": "Sample Test Case",
            "description": "Test case for JSON editor validation",
            "created": "2025-01-06T12:00:00",
            "actions": [
                {
                    "type": "tap",
                    "action_id": "test123456",
                    "locator_type": "accessibility_id",
                    "locator_value": "login_button",
                    "timeout": 10
                },
                {
                    "type": "inputText",
                    "action_id": "input12345",
                    "locator_type": "xpath",
                    "locator_value": "//input[@id='username']",
                    "text": "testuser",
                    "timeout": 5
                },
                {
                    "type": "addLog",
                    "action_id": "al_log1234",
                    "text": "Login process started"
                },
                {
                    "type": "wait",
                    "action_id": "wait123456",
                    "duration": 2
                }
            ]
        };
        
        function addTestResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result test-${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            testResults.appendChild(div);
            testResults.scrollTop = testResults.scrollHeight;
        }
        
        function testFrontendComponents() {
            addTestResult('Testing frontend components...', 'info');
            
            // Test 1: Check if JSON editor modal exists
            const modal = document.getElementById('jsonEditorModal');
            if (modal) {
                addTestResult('✓ JSON editor modal found in DOM', 'pass');
            } else {
                addTestResult('✗ JSON editor modal not found in DOM', 'fail');
            }
            
            // Test 2: Check search components
            const searchInput = document.getElementById('jsonSearchInput');
            if (searchInput) {
                addTestResult('✓ JSON search input found', 'pass');
            } else {
                addTestResult('✗ JSON search input not found', 'fail');
            }
            
            // Test 3: Check validation components
            const validateBtn = document.getElementById('validateJsonBtn');
            if (validateBtn) {
                addTestResult('✓ Validate button found', 'pass');
            } else {
                addTestResult('✗ Validate button not found', 'fail');
            }
            
            // Test 4: Check if legacy editor is removed
            const legacyEditor = document.getElementById('testCaseEditorCard');
            if (!legacyEditor) {
                addTestResult('✓ Legacy test case editor successfully removed', 'pass');
            } else {
                addTestResult('✗ Legacy test case editor still present', 'fail');
            }
        }
        
        function testActionIdGeneration() {
            const resultsDiv = document.getElementById('actionIdResults');
            resultsDiv.innerHTML = '';
            
            // Generate test action IDs
            const generateActionId = (actionType) => {
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                let result = '';
                
                if (actionType === 'addLog') {
                    result = 'al_';
                }
                
                const remainingLength = 10 - result.length;
                for (let i = 0; i < remainingLength; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                
                return result;
            };
            
            // Test different action types
            const actionTypes = ['tap', 'addLog', 'inputText', 'wait'];
            
            actionTypes.forEach(actionType => {
                const actionId = generateActionId(actionType);
                const isValid = validateActionId(actionId, actionType);
                
                const div = document.createElement('div');
                div.className = `alert ${isValid ? 'alert-success' : 'alert-danger'} py-1 my-1`;
                div.innerHTML = `<small><strong>${actionType}:</strong> ${actionId} ${isValid ? '✓' : '✗'}</small>`;
                resultsDiv.appendChild(div);
            });
        }
        
        function validateActionId(actionId, actionType) {
            if (actionType === 'addLog') {
                return /^al_[a-zA-Z0-9]{7}$/.test(actionId);
            } else {
                return /^[a-zA-Z0-9_]{10}$/.test(actionId);
            }
        }
        
        function copySampleJson() {
            const jsonString = JSON.stringify(sampleTestCase, null, 2);
            navigator.clipboard.writeText(jsonString).then(() => {
                addTestResult('✓ Sample JSON copied to clipboard', 'pass');
            }).catch(() => {
                addTestResult('✗ Failed to copy sample JSON', 'fail');
            });
        }
        
        // Initialize tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Display sample JSON
            document.getElementById('sampleJson').textContent = JSON.stringify(sampleTestCase, null, 2);
            
            // Run frontend tests
            setTimeout(() => {
                testFrontendComponents();
                addTestResult('Frontend component tests completed', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
