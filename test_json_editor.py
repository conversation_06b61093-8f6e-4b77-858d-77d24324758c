#!/usr/bin/env python3
"""
Test script for JSON Editor functionality
Tests the new test case modification features including JSON editor, validation, and search
"""

import requests
import json
import time
import sys
import os

# Configuration
IOS_BASE_URL = "http://localhost:8080"
ANDROID_BASE_URL = "http://localhost:8081"

def test_json_editor_apis(base_url, platform):
    """Test JSON Editor API endpoints"""
    print(f"\n=== Testing {platform} JSON Editor APIs ===")
    
    # Test data
    test_case_data = {
        "name": "Test JSON Editor",
        "description": "Testing JSON editor functionality",
        "created": "2025-01-06T12:00:00",
        "actions": [
            {
                "type": "tap",
                "action_id": "test123456",
                "locator_type": "accessibility_id",
                "locator_value": "test_button",
                "timeout": 10
            },
            {
                "type": "addLog",
                "action_id": "al_test123",
                "text": "Test log message"
            }
        ]
    }
    
    # Test 1: JSON Backup API
    print("1. Testing JSON backup API...")
    try:
        response = requests.post(f"{base_url}/api/test_cases/json_backup", 
                               json={
                                   "filename": "test_json_editor.json",
                                   "json_data": test_case_data
                               })
        if response.status_code == 200:
            print("   ✓ JSON backup API working")
        else:
            print(f"   ✗ JSON backup API failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ JSON backup API error: {e}")
    
    # Test 2: JSON Validation API
    print("2. Testing JSON validation API...")
    try:
        response = requests.post(f"{base_url}/api/test_cases/validate_json",
                               json={
                                   "test_case": test_case_data,
                                   "filename": "test_json_editor.json"
                               })
        if response.status_code == 200:
            print("   ✓ JSON validation API working")
        else:
            print(f"   ✗ JSON validation API failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   ✗ JSON validation API error: {e}")
    
    # Test 3: Invalid JSON validation
    print("3. Testing invalid JSON validation...")
    try:
        invalid_data = {"name": "", "actions": "not_an_array"}
        response = requests.post(f"{base_url}/api/test_cases/validate_json",
                               json={
                                   "test_case": invalid_data,
                                   "filename": "test_invalid.json"
                               })
        if response.status_code == 400:
            print("   ✓ Invalid JSON properly rejected")
        else:
            print(f"   ✗ Invalid JSON validation unexpected result: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Invalid JSON validation error: {e}")
    
    # Test 4: JSON Save API
    print("4. Testing JSON save API...")
    try:
        response = requests.post(f"{base_url}/api/test_cases/save_json",
                               json={
                                   "filename": "test_json_editor.json",
                                   "test_case": test_case_data
                               })
        if response.status_code == 200:
            print("   ✓ JSON save API working")
        else:
            print(f"   ✗ JSON save API failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   ✗ JSON save API error: {e}")

def test_database_schema():
    """Test database schema for JSON editor backup table"""
    print("\n=== Testing Database Schema ===")
    
    try:
        import sqlite3
        from app.utils.database import get_db_path
        
        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if backup table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_case_json_backups'")
        if cursor.fetchone():
            print("   ✓ test_case_json_backups table exists")
            
            # Check table structure
            cursor.execute("PRAGMA table_info(test_case_json_backups)")
            columns = cursor.fetchall()
            expected_columns = ['id', 'test_case_filename', 'test_case_id', 'json_data', 'backup_timestamp', 'session_id', 'created_by']
            
            actual_columns = [col[1] for col in columns]
            missing_columns = [col for col in expected_columns if col not in actual_columns]
            
            if not missing_columns:
                print("   ✓ All required columns present")
            else:
                print(f"   ✗ Missing columns: {missing_columns}")
        else:
            print("   ✗ test_case_json_backups table missing")
        
        conn.close()
        
    except Exception as e:
        print(f"   ✗ Database schema test error: {e}")

def test_action_id_validation():
    """Test action ID validation logic"""
    print("\n=== Testing Action ID Validation ===")
    
    test_cases = [
        ("valid_10_char", "abcd123456", True),
        ("valid_addlog", "al_1234567", True),
        ("invalid_short", "abc123", False),
        ("invalid_long", "abcd1234567890", False),
        ("invalid_addlog", "al_12345678", False),
        ("invalid_chars", "abc@123456", False)
    ]
    
    for test_name, action_id, should_be_valid in test_cases:
        # Test 10-character pattern
        is_valid_10 = len(action_id) == 10 and action_id.replace('_', '').isalnum()
        # Test addLog pattern
        is_valid_addlog = action_id.startswith('al_') and len(action_id) == 10 and action_id[3:].isalnum()
        
        is_valid = is_valid_10 or is_valid_addlog
        
        if is_valid == should_be_valid:
            print(f"   ✓ {test_name}: {action_id}")
        else:
            print(f"   ✗ {test_name}: {action_id} (expected {should_be_valid}, got {is_valid})")

def main():
    """Run all tests"""
    print("JSON Editor Test Suite")
    print("=" * 50)
    
    # Test database schema
    test_database_schema()
    
    # Test action ID validation
    test_action_id_validation()
    
    # Test iOS APIs if available
    try:
        response = requests.get(f"{IOS_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            test_json_editor_apis(IOS_BASE_URL, "iOS")
        else:
            print(f"\n⚠ iOS server not responding (status: {response.status_code})")
    except Exception as e:
        print(f"\n⚠ iOS server not available: {e}")
    
    # Test Android APIs if available
    try:
        response = requests.get(f"{ANDROID_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            test_json_editor_apis(ANDROID_BASE_URL, "Android")
        else:
            print(f"\n⚠ Android server not responding (status: {response.status_code})")
    except Exception as e:
        print(f"\n⚠ Android server not available: {e}")
    
    print("\n" + "=" * 50)
    print("Test suite completed!")

if __name__ == "__main__":
    main()
