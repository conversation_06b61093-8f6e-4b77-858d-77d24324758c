#!/usr/bin/env python3
"""
Comprehensive Integration Test for Test Case Modification Feature
Tests all aspects of the new search, filter, and editing functionality
"""

import os
import sys
import json
import requests
import time
import logging
from pathlib import Path

# Add the parent directory to the path so we can import app modules
parent_dir = Path(__file__).resolve().parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestCaseModificationTester:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.test_results = []
        
    def log_test_result(self, test_name, success, message=""):
        """Log a test result"""
        status = "PASS" if success else "FAIL"
        logger.info(f"{test_name}: {status} - {message}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
    
    def test_api_endpoints(self):
        """Test all new API endpoints"""
        logger.info("=== Testing API Endpoints ===")
        
        # Test action types endpoint
        try:
            response = requests.get(f"{self.base_url}/api/test_cases/action_types")
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success' and 'action_types' in data:
                    self.log_test_result("Action Types API", True, f"Found {len(data['action_types'])} action types")
                else:
                    self.log_test_result("Action Types API", False, "Invalid response format")
            else:
                self.log_test_result("Action Types API", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test_result("Action Types API", False, str(e))
        
        # Test locator types endpoint
        try:
            response = requests.get(f"{self.base_url}/api/test_cases/locator_types")
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success' and 'locator_types' in data:
                    self.log_test_result("Locator Types API", True, f"Found {len(data['locator_types'])} locator types")
                else:
                    self.log_test_result("Locator Types API", False, "Invalid response format")
            else:
                self.log_test_result("Locator Types API", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test_result("Locator Types API", False, str(e))
        
        # Test search endpoint with empty criteria
        try:
            response = requests.post(f"{self.base_url}/api/test_cases/search", 
                                   json={}, 
                                   headers={'Content-Type': 'application/json'})
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success' and 'results' in data:
                    self.log_test_result("Search API (empty)", True, f"Found {len(data['results'])} test cases")
                else:
                    self.log_test_result("Search API (empty)", False, "Invalid response format")
            else:
                self.log_test_result("Search API (empty)", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test_result("Search API (empty)", False, str(e))
        
        # Test search with specific criteria
        try:
            search_criteria = {
                "action_types": ["tap", "clickElement"],
                "test_case_name": "test"
            }
            response = requests.post(f"{self.base_url}/api/test_cases/search", 
                                   json=search_criteria, 
                                   headers={'Content-Type': 'application/json'})
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.log_test_result("Search API (filtered)", True, f"Found {len(data['results'])} matching test cases")
                else:
                    self.log_test_result("Search API (filtered)", False, "Invalid response format")
            else:
                self.log_test_result("Search API (filtered)", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test_result("Search API (filtered)", False, str(e))
    
    def test_test_case_loading(self):
        """Test test case loading functionality"""
        logger.info("=== Testing Test Case Loading ===")
        
        # First, get a list of test cases
        try:
            response = requests.post(f"{self.base_url}/api/test_cases/search", 
                                   json={}, 
                                   headers={'Content-Type': 'application/json'})
            if response.status_code == 200:
                data = response.json()
                test_cases = data.get('results', [])
                
                if test_cases:
                    # Test loading the first test case
                    filename = test_cases[0]['filename']
                    load_response = requests.get(f"{self.base_url}/api/test_cases/load/{filename}")
                    
                    if load_response.status_code == 200:
                        load_data = load_response.json()
                        if load_data.get('status') == 'success' and 'test_case' in load_data:
                            self.log_test_result("Test Case Loading", True, f"Successfully loaded {filename}")
                        else:
                            self.log_test_result("Test Case Loading", False, "Invalid load response format")
                    else:
                        self.log_test_result("Test Case Loading", False, f"HTTP {load_response.status_code}")
                else:
                    self.log_test_result("Test Case Loading", False, "No test cases found to load")
            else:
                self.log_test_result("Test Case Loading", False, "Failed to get test case list")
        except Exception as e:
            self.log_test_result("Test Case Loading", False, str(e))
    
    def test_validation(self):
        """Test validation functionality"""
        logger.info("=== Testing Validation ===")
        
        # Test valid test case
        valid_test_case = {
            "name": "Test Validation Case",
            "description": "A test case for validation testing",
            "actions": [
                {
                    "action_id": "test123abc",
                    "type": "tap",
                    "locator_type": "xpath",
                    "locator_value": "//button[@id='test']",
                    "timestamp": int(time.time() * 1000)
                }
            ]
        }
        
        try:
            response = requests.post(f"{self.base_url}/api/test_cases/save_modified",
                                   json={
                                       "filename": "validation_test.json",
                                       "test_case": valid_test_case,
                                       "validate_only": True
                                   },
                                   headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.log_test_result("Validation (valid case)", True, "Valid test case passed validation")
                else:
                    self.log_test_result("Validation (valid case)", False, "Valid test case failed validation")
            else:
                self.log_test_result("Validation (valid case)", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test_result("Validation (valid case)", False, str(e))
        
        # Test invalid test case
        invalid_test_case = {
            "name": "",  # Missing name
            "actions": [
                {
                    "type": "tap",
                    # Missing required fields
                }
            ]
        }
        
        try:
            response = requests.post(f"{self.base_url}/api/test_cases/save_modified",
                                   json={
                                       "filename": "invalid_test.json",
                                       "test_case": invalid_test_case,
                                       "validate_only": True
                                   },
                                   headers={'Content-Type': 'application/json'})
            
            if response.status_code == 400:
                data = response.json()
                if 'validation_errors' in data:
                    self.log_test_result("Validation (invalid case)", True, f"Invalid test case properly rejected with {len(data['validation_errors'])} errors")
                else:
                    self.log_test_result("Validation (invalid case)", False, "Invalid test case rejected but no validation errors provided")
            else:
                self.log_test_result("Validation (invalid case)", False, f"Invalid test case not properly rejected (HTTP {response.status_code})")
        except Exception as e:
            self.log_test_result("Validation (invalid case)", False, str(e))
    
    def test_backup_functionality(self):
        """Test backup functionality"""
        logger.info("=== Testing Backup Functionality ===")
        
        # Get a test case to backup
        try:
            response = requests.post(f"{self.base_url}/api/test_cases/search", 
                                   json={}, 
                                   headers={'Content-Type': 'application/json'})
            if response.status_code == 200:
                data = response.json()
                test_cases = data.get('results', [])
                
                if test_cases:
                    filename = test_cases[0]['filename']
                    backup_response = requests.post(f"{self.base_url}/api/test_cases/backup/{filename}",
                                                  headers={'Content-Type': 'application/json'})
                    
                    if backup_response.status_code == 200:
                        backup_data = backup_response.json()
                        if backup_data.get('status') == 'success':
                            self.log_test_result("Backup Functionality", True, f"Successfully created backup for {filename}")
                        else:
                            self.log_test_result("Backup Functionality", False, "Backup API returned error")
                    else:
                        self.log_test_result("Backup Functionality", False, f"HTTP {backup_response.status_code}")
                else:
                    self.log_test_result("Backup Functionality", False, "No test cases found to backup")
            else:
                self.log_test_result("Backup Functionality", False, "Failed to get test case list")
        except Exception as e:
            self.log_test_result("Backup Functionality", False, str(e))
    
    def run_all_tests(self):
        """Run all tests"""
        logger.info("Starting comprehensive test case modification integration tests...")
        
        self.test_api_endpoints()
        self.test_test_case_loading()
        self.test_validation()
        self.test_backup_functionality()
        
        # Print summary
        logger.info("\n=== TEST SUMMARY ===")
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        logger.info(f"Tests passed: {passed}/{total}")
        
        if passed == total:
            logger.info("🎉 ALL TESTS PASSED!")
        else:
            logger.info("❌ Some tests failed:")
            for result in self.test_results:
                if not result['success']:
                    logger.info(f"  - {result['test']}: {result['message']}")
        
        return passed == total

if __name__ == "__main__":
    tester = TestCaseModificationTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
