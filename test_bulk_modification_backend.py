#!/usr/bin/env python3
"""
Test script for bulk modification backend functionality
"""

import json
import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.import_export_manager import ImportExportManager
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_case():
    """Create a test case with the specific XPath we're looking for"""
    test_case = {
        "name": "Test Case with Tab 4 of 5",
        "description": "Test case for bulk modification testing",
        "actions": [
            {
                "action_id": "test123abc",
                "type": "tap",
                "locator_type": "xpath",
                "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]",
                "timestamp": 1704067200000
            },
            {
                "action_id": "test456def",
                "type": "waitTill",
                "locator_type": "xpath",
                "locator_value": "//XCUIElementTypeStaticText[contains(@name,\"Content\")]",
                "timeout": 5,
                "timestamp": 1704067210000
            }
        ]
    }
    
    # Save test case to file
    test_cases_dir = Path('app/test_cases')
    test_cases_dir.mkdir(exist_ok=True)
    
    test_file = test_cases_dir / 'bulk_modification_test.json'
    with open(test_file, 'w') as f:
        json.dump(test_case, f, indent=2)
    
    logger.info(f"Created test case: {test_file}")
    return test_file

def test_rule_parsing():
    """Test rule parsing functionality"""
    logger.info("=== Testing Rule Parsing ===")
    
    # Initialize the manager
    manager = ImportExportManager(Path('app/test_cases'))
    
    # Test rules generated by the frontend
    test_rules = [
        "Add step 'tapIfLocatorExists' with locator_value '//XCUIElementTypeButton[@name=\"Checkout\"]' locator_type 'xpath' after step that contains 'contains \"locator_value\":\"//XCUIElementTypeButton[contains(@name,\\\"Tab 4 of 5\\\")]\""
    ]
    
    for rule in test_rules:
        logger.info(f"Testing rule: {rule}")
        parsed = manager._parse_modification_rule(rule)
        logger.info(f"Parsed result: {parsed}")
        print(f"Rule: {rule}")
        print(f"Parsed: {json.dumps(parsed, indent=2)}")
        print("-" * 80)

def test_condition_matching():
    """Test condition matching against test case"""
    logger.info("=== Testing Condition Matching ===")
    
    # Create test case
    test_file = create_test_case()
    
    # Load test case
    with open(test_file, 'r') as f:
        test_case_data = json.load(f)
    
    # Initialize the manager
    manager = ImportExportManager(Path('app/test_cases'))
    
    # Test different condition formats
    test_conditions = [
        {
            "type": "step_condition",
            "value": "contains \"locator_value\":\"//XCUIElementTypeButton[contains(@name,\\\"Tab 4 of 5\\\")]\""
        },
        {
            "type": "step_condition", 
            "value": "contains \"type\":\"tap\""
        },
        {
            "type": "contains",
            "value": "Tab 4 of 5"
        }
    ]
    
    for condition in test_conditions:
        logger.info(f"Testing condition: {condition}")
        result = manager._check_condition(test_case_data, condition)
        logger.info(f"Condition result: {result}")
        print(f"Condition: {json.dumps(condition, indent=2)}")
        print(f"Result: {result}")
        print("-" * 80)

def test_full_preview():
    """Test the full preview functionality"""
    logger.info("=== Testing Full Preview ===")
    
    # Create test case
    test_file = create_test_case()
    
    # Initialize the manager
    manager = ImportExportManager(Path('app/test_cases'))
    
    # Test rules that should match
    test_rules = [
        "Add step 'tapIfLocatorExists' with locator_value '//XCUIElementTypeButton[@name=\"Checkout\"]' locator_type 'xpath' after step that contains 'contains \"locator_value\":\"//XCUIElementTypeButton[contains(@name,\\\"Tab 4 of 5\\\")]\"'"
    ]
    
    logger.info(f"Testing preview with rules: {test_rules}")
    result = manager.preview_modifications(test_rules)
    
    print("Preview Result:")
    print(json.dumps(result, indent=2))
    
    # Clean up
    test_file.unlink()

def test_frontend_rule_conversion():
    """Test the frontend rule conversion logic"""
    logger.info("=== Testing Frontend Rule Conversion ===")
    
    # Simulate the frontend rule pairs
    rule_pairs = [
        {
            'before': 'Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]',
            'after': 'Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]\nTap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"'
        }
    ]
    
    # Simulate the frontend conversion logic
    def convert_rule_pairs_to_api_format(rule_pairs):
        rules = []
        
        for pair in rule_pairs:
            if pair['before'] and pair['after']:
                try:
                    # Parse the before condition to extract trigger information
                    before_lines = pair['before'].split('\n')
                    after_lines = pair['after'].split('\n')
                    
                    if len(before_lines) == 0 or len(after_lines) == 0:
                        continue
                    
                    # Extract the trigger condition from the "before" text
                    trigger_line = before_lines[0]
                    
                    # Find what needs to be added from the "after" text
                    new_steps = after_lines[1:]  # Skip the first line which should match "before"
                    
                    if len(new_steps) > 0:
                        # For each new step, create a rule
                        for new_step in new_steps:
                            # Parse the new step to extract action type and parameters
                            if 'Tap if locator exists: xpath=' in new_step:
                                import re
                                xpath_match = re.search(r'xpath="([^"]+)"', new_step)
                                if xpath_match:
                                    xpath_value = xpath_match.group(1)
                                    
                                    # Extract trigger condition
                                    trigger_xpath_match = re.search(r'xpath:\s*(.+)', trigger_line)
                                    if trigger_xpath_match:
                                        trigger_xpath = trigger_xpath_match.group(1).strip()
                                        condition = f'contains "locator_value":"{trigger_xpath}"'
                                        
                                        rule = f"Add step 'tapIfLocatorExists' with locator_value '{xpath_value}' locator_type 'xpath' after step that {condition}"
                                        rules.append(rule)
                                        
                except Exception as e:
                    print(f'Error converting rule pair: {e}')
        
        return rules
    
    converted_rules = convert_rule_pairs_to_api_format(rule_pairs)
    
    print("Original rule pairs:")
    for pair in rule_pairs:
        print(f"Before: {pair['before']}")
        print(f"After: {pair['after']}")
        print()
    
    print("Converted rules:")
    for rule in converted_rules:
        print(f"Rule: {rule}")
    
    return converted_rules

if __name__ == "__main__":
    print("Testing Bulk Modification Backend Functionality")
    print("=" * 60)
    
    try:
        # Test 1: Rule parsing
        test_rule_parsing()
        print()
        
        # Test 2: Condition matching
        test_condition_matching()
        print()
        
        # Test 3: Frontend rule conversion
        converted_rules = test_frontend_rule_conversion()
        print()
        
        # Test 4: Full preview with converted rules
        if converted_rules:
            logger.info("=== Testing Full Preview with Converted Rules ===")
            test_file = create_test_case()
            manager = ImportExportManager(Path('app/test_cases'))
            result = manager.preview_modifications(converted_rules)
            print("Preview Result with Converted Rules:")
            print(json.dumps(result, indent=2))
            test_file.unlink()
        
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
