#!/usr/bin/env python3
"""
Direct test for bulk modification
"""

import json
import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_direct():
    """Direct test"""
    logger.info("=== Direct Test ===")
    
    # Import the class directly
    from utils.import_export_manager import BulkModificationManager
    
    # Initialize the manager
    manager = BulkModificationManager('app/test_cases')
    
    # Test a simple rule
    test_rules = [
        "Add step 'tap' with locator_value '//XCUIElementTypeButton[@name=\"Checkout\"]' locator_type 'xpath' after step that contains 'Tab 4 of 5'"
    ]
    
    logger.info(f"Testing rule: {test_rules[0]}")
    
    # Test the preview method directly
    result = manager.preview_modifications(test_rules)
    
    print(f"Rule: {test_rules[0]}")
    print(f"Preview result: {json.dumps(result, indent=2)}")

if __name__ == "__main__":
    print("Direct Bulk Modification Test")
    print("=" * 40)
    
    try:
        test_direct()
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
