Action Log - 2025-07-02 19:03:09
================================================================================

[[19:03:09]] [INFO] Generating execution report...
[[19:03:09]] [SUCCESS] All tests passed successfully!
[[19:03:09]] [SUCCESS] Screenshot refreshed
[[19:03:09]] [INFO] Refreshing screenshot...
[[19:03:09]] [INFO] xyHVihJMBi=pass
[[19:03:06]] [SUCCESS] Screenshot refreshed successfully
[[19:03:06]] [SUCCESS] Screenshot refreshed successfully
[[19:03:05]] [INFO] xyHVihJMBi=running
[[19:03:05]] [INFO] Executing action 50/50: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[19:03:05]] [SUCCESS] Screenshot refreshed
[[19:03:05]] [INFO] Refreshing screenshot...
[[19:03:05]] [INFO] mWeLQtXiL6=pass
[[19:03:00]] [SUCCESS] Screenshot refreshed successfully
[[19:03:00]] [SUCCESS] Screenshot refreshed successfully
[[19:03:00]] [INFO] mWeLQtXiL6=running
[[19:03:00]] [INFO] Executing action 49/50: Swipe from (50%, 70%) to (50%, 30%)
[[19:02:59]] [SUCCESS] Screenshot refreshed
[[19:02:59]] [INFO] Refreshing screenshot...
[[19:02:59]] [INFO] rkwVoJGZG4=pass
[[19:02:58]] [SUCCESS] Screenshot refreshed successfully
[[19:02:58]] [SUCCESS] Screenshot refreshed successfully
[[19:02:57]] [INFO] rkwVoJGZG4=running
[[19:02:57]] [INFO] Executing action 48/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[19:02:57]] [SUCCESS] Screenshot refreshed
[[19:02:57]] [INFO] Refreshing screenshot...
[[19:02:57]] [INFO] 0f2FSZYjWq=pass
[[19:02:12]] [SUCCESS] Screenshot refreshed successfully
[[19:02:12]] [SUCCESS] Screenshot refreshed successfully
[[19:02:08]] [INFO] 0f2FSZYjWq=running
[[19:02:08]] [INFO] Executing action 47/50: Check if element with text="3000" exists
[[19:02:08]] [SUCCESS] Screenshot refreshed
[[19:02:08]] [INFO] Refreshing screenshot...
[[19:02:08]] [INFO] Tebej51pT2=pass
[[19:02:06]] [SUCCESS] Screenshot refreshed successfully
[[19:02:06]] [SUCCESS] Screenshot refreshed successfully
[[19:02:06]] [INFO] Tebej51pT2=running
[[19:02:06]] [INFO] Executing action 46/50: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[19:02:05]] [SUCCESS] Screenshot refreshed
[[19:02:05]] [INFO] Refreshing screenshot...
[[19:02:05]] [INFO] eVytJrry9x=pass
[[19:02:00]] [SUCCESS] Screenshot refreshed successfully
[[19:02:00]] [SUCCESS] Screenshot refreshed successfully
[[19:01:59]] [INFO] eVytJrry9x=running
[[19:01:59]] [INFO] Executing action 45/50: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[19:01:59]] [SUCCESS] Screenshot refreshed
[[19:01:59]] [INFO] Refreshing screenshot...
[[19:01:59]] [INFO] s8h8VDUIOC=pass
[[19:01:56]] [SUCCESS] Screenshot refreshed successfully
[[19:01:56]] [SUCCESS] Screenshot refreshed successfully
[[19:01:56]] [INFO] s8h8VDUIOC=running
[[19:01:56]] [INFO] Executing action 44/50: Swipe from (50%, 70%) to (50%, 30%)
[[19:01:55]] [SUCCESS] Screenshot refreshed
[[19:01:55]] [INFO] Refreshing screenshot...
[[19:01:55]] [INFO] ZWpYNcpbFA=pass
[[19:01:46]] [SUCCESS] Screenshot refreshed successfully
[[19:01:46]] [SUCCESS] Screenshot refreshed successfully
[[19:01:45]] [INFO] ZWpYNcpbFA=running
[[19:01:45]] [INFO] Executing action 43/50: Tap on Text: "VIC"
[[19:01:45]] [SUCCESS] Screenshot refreshed
[[19:01:45]] [INFO] Refreshing screenshot...
[[19:01:45]] [INFO] QpBLC6BStn=pass
[[19:01:43]] [SUCCESS] Screenshot refreshed successfully
[[19:01:43]] [SUCCESS] Screenshot refreshed successfully
[[19:01:42]] [INFO] QpBLC6BStn=running
[[19:01:42]] [INFO] Executing action 42/50: textClear action
[[19:01:42]] [SUCCESS] Screenshot refreshed
[[19:01:42]] [INFO] Refreshing screenshot...
[[19:01:42]] [INFO] G4A3KBlXHq=pass
[[19:01:24]] [INFO] G4A3KBlXHq=running
[[19:01:24]] [INFO] Executing action 41/50: Tap on Text: "Nearby"
[[19:01:24]] [INFO] 3gJsiap2Ds=fail
[[19:01:24]] [ERROR] Action 40 failed: Text 'Collect' not found within timeout (30s)
[[19:00:50]] [SUCCESS] Screenshot refreshed successfully
[[19:00:50]] [SUCCESS] Screenshot refreshed successfully
[[19:00:49]] [INFO] 3gJsiap2Ds=running
[[19:00:49]] [INFO] Executing action 40/50: Tap on Text: "Collect"
[[19:00:49]] [SUCCESS] Screenshot refreshed
[[19:00:49]] [INFO] Refreshing screenshot...
[[19:00:49]] [INFO] sevnilsRXy=pass
[[19:00:21]] [SUCCESS] Screenshot refreshed successfully
[[19:00:21]] [SUCCESS] Screenshot refreshed successfully
[[19:00:21]] [INFO] sevnilsRXy=running
[[19:00:21]] [INFO] Executing action 39/50: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[19:00:20]] [SUCCESS] Screenshot refreshed
[[19:00:20]] [INFO] Refreshing screenshot...
[[19:00:20]] [INFO] rkwVoJGZG4=pass
[[19:00:19]] [SUCCESS] Screenshot refreshed successfully
[[19:00:19]] [SUCCESS] Screenshot refreshed successfully
[[19:00:18]] [INFO] rkwVoJGZG4=running
[[19:00:18]] [INFO] Executing action 38/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[19:00:18]] [SUCCESS] Screenshot refreshed
[[19:00:18]] [INFO] Refreshing screenshot...
[[19:00:18]] [INFO] 94ikwhIEE2=pass
[[19:00:14]] [SUCCESS] Screenshot refreshed successfully
[[19:00:14]] [SUCCESS] Screenshot refreshed successfully
[[19:00:10]] [INFO] 94ikwhIEE2=running
[[19:00:10]] [INFO] Executing action 37/50: Tap on Text: "bag"
[[19:00:10]] [SUCCESS] Screenshot refreshed
[[19:00:10]] [INFO] Refreshing screenshot...
[[19:00:10]] [INFO] DfwaiVZ8Z9=pass
[[19:00:07]] [SUCCESS] Screenshot refreshed successfully
[[19:00:07]] [SUCCESS] Screenshot refreshed successfully
[[19:00:06]] [INFO] DfwaiVZ8Z9=running
[[19:00:06]] [INFO] Executing action 36/50: Swipe from (50%, 70%) to (50%, 50%)
[[19:00:06]] [SUCCESS] Screenshot refreshed
[[19:00:06]] [INFO] Refreshing screenshot...
[[19:00:06]] [INFO] eRCmRhc3re=pass
[[19:00:03]] [SUCCESS] Screenshot refreshed successfully
[[19:00:03]] [SUCCESS] Screenshot refreshed successfully
[[19:00:03]] [INFO] eRCmRhc3re=running
[[19:00:03]] [INFO] Executing action 35/50: Check if element with text="Broadway" exists
[[19:00:02]] [SUCCESS] Screenshot refreshed
[[19:00:02]] [INFO] Refreshing screenshot...
[[19:00:02]] [INFO] E2jpN7BioW=pass
[[19:00:00]] [SUCCESS] Screenshot refreshed successfully
[[19:00:00]] [SUCCESS] Screenshot refreshed successfully
[[18:59:59]] [INFO] E2jpN7BioW=running
[[18:59:59]] [INFO] Executing action 34/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[18:59:59]] [SUCCESS] Screenshot refreshed
[[18:59:59]] [INFO] Refreshing screenshot...
[[18:59:59]] [INFO] IOc0IwmLPQ=pass
[[18:59:57]] [SUCCESS] Screenshot refreshed successfully
[[18:59:57]] [SUCCESS] Screenshot refreshed successfully
[[18:59:57]] [INFO] IOc0IwmLPQ=running
[[18:59:57]] [INFO] Executing action 33/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[18:59:56]] [SUCCESS] Screenshot refreshed
[[18:59:56]] [INFO] Refreshing screenshot...
[[18:59:56]] [INFO] H0ODFz7sWJ=pass
[[18:59:00]] [SUCCESS] Screenshot refreshed successfully
[[18:59:00]] [SUCCESS] Screenshot refreshed successfully
[[18:59:00]] [INFO] H0ODFz7sWJ=running
[[18:59:00]] [INFO] Executing action 32/50: Tap on Text: "2000"
[[18:58:59]] [SUCCESS] Screenshot refreshed
[[18:58:59]] [INFO] Refreshing screenshot...
[[18:58:59]] [INFO] pldheRUBVi=pass
[[18:58:57]] [SUCCESS] Screenshot refreshed successfully
[[18:58:57]] [SUCCESS] Screenshot refreshed successfully
[[18:58:56]] [INFO] pldheRUBVi=running
[[18:58:56]] [INFO] Executing action 31/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[18:58:56]] [SUCCESS] Screenshot refreshed
[[18:58:56]] [INFO] Refreshing screenshot...
[[18:58:56]] [INFO] uZHvvAzVfx=pass
[[18:58:03]] [SUCCESS] Screenshot refreshed successfully
[[18:58:03]] [SUCCESS] Screenshot refreshed successfully
[[18:58:02]] [INFO] uZHvvAzVfx=running
[[18:58:02]] [INFO] Executing action 30/50: textClear action
[[18:58:02]] [SUCCESS] Screenshot refreshed
[[18:58:02]] [INFO] Refreshing screenshot...
[[18:58:02]] [INFO] WmNWcsWVHv=pass
[[18:57:58]] [SUCCESS] Screenshot refreshed successfully
[[18:57:58]] [SUCCESS] Screenshot refreshed successfully
[[18:57:58]] [INFO] WmNWcsWVHv=running
[[18:57:58]] [INFO] Executing action 29/50: Tap on Text: "4000"
[[18:57:58]] [SUCCESS] Screenshot refreshed
[[18:57:58]] [INFO] Refreshing screenshot...
[[18:57:58]] [INFO] lnjoz8hHUU=pass
[[18:57:51]] [SUCCESS] Screenshot refreshed successfully
[[18:57:51]] [SUCCESS] Screenshot refreshed successfully
[[18:57:51]] [INFO] lnjoz8hHUU=running
[[18:57:51]] [INFO] Executing action 28/50: Tap on Text: "Edit"
[[18:57:50]] [SUCCESS] Screenshot refreshed
[[18:57:50]] [INFO] Refreshing screenshot...
[[18:57:50]] [INFO] BQ7Cxm53HQ=pass
[[18:57:48]] [SUCCESS] Screenshot refreshed successfully
[[18:57:48]] [SUCCESS] Screenshot refreshed successfully
[[18:57:48]] [INFO] BQ7Cxm53HQ=running
[[18:57:48]] [INFO] Executing action 27/50: Wait till text appears: "UNO"
[[18:57:47]] [SUCCESS] Screenshot refreshed
[[18:57:47]] [INFO] Refreshing screenshot...
[[18:57:47]] [INFO] VkUKQbf1Qt=pass
[[18:57:39]] [SUCCESS] Screenshot refreshed successfully
[[18:57:39]] [SUCCESS] Screenshot refreshed successfully
[[18:57:39]] [INFO] VkUKQbf1Qt=running
[[18:57:39]] [INFO] Executing action 26/50: Tap on Text: "UNO"
[[18:57:39]] [SUCCESS] Screenshot refreshed
[[18:57:39]] [INFO] Refreshing screenshot...
[[18:57:39]] [INFO] 73NABkfWyY=pass
[[18:57:35]] [INFO] 73NABkfWyY=running
[[18:57:35]] [INFO] Executing action 25/50: Check if element with text="Toowong" exists
[[18:57:35]] [INFO] E2jpN7BioW=fail
[[18:57:35]] [ERROR] Action 24 failed: Error tapping element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[18:57:03]] [SUCCESS] Screenshot refreshed successfully
[[18:57:03]] [SUCCESS] Screenshot refreshed successfully
[[18:57:03]] [INFO] E2jpN7BioW=running
[[18:57:03]] [INFO] Executing action 24/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[18:57:03]] [SUCCESS] Screenshot refreshed
[[18:57:03]] [INFO] Refreshing screenshot...
[[18:57:03]] [INFO] IOc0IwmLPQ=pass
[[18:57:01]] [SUCCESS] Screenshot refreshed successfully
[[18:57:01]] [SUCCESS] Screenshot refreshed successfully
[[18:57:00]] [INFO] IOc0IwmLPQ=running
[[18:57:00]] [INFO] Executing action 23/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[18:57:00]] [SUCCESS] Screenshot refreshed
[[18:57:00]] [INFO] Refreshing screenshot...
[[18:57:00]] [INFO] VkUKQbf1Qt=pass
[[18:56:56]] [SUCCESS] Screenshot refreshed successfully
[[18:56:56]] [SUCCESS] Screenshot refreshed successfully
[[18:56:00]] [INFO] VkUKQbf1Qt=running
[[18:56:00]] [INFO] Executing action 22/50: Tap on Text: "CITY"
[[18:56:00]] [SUCCESS] Screenshot refreshed
[[18:56:00]] [INFO] Refreshing screenshot...
[[18:56:00]] [INFO] pldheRUBVi=pass
[[18:55:57]] [SUCCESS] Screenshot refreshed successfully
[[18:55:57]] [SUCCESS] Screenshot refreshed successfully
[[18:55:57]] [INFO] pldheRUBVi=running
[[18:55:57]] [INFO] Executing action 21/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[18:55:56]] [SUCCESS] Screenshot refreshed
[[18:55:56]] [INFO] Refreshing screenshot...
[[18:55:56]] [INFO] kbdEPCPYod=pass
[[18:55:32]] [SUCCESS] Screenshot refreshed successfully
[[18:55:32]] [SUCCESS] Screenshot refreshed successfully
[[18:55:31]] [INFO] kbdEPCPYod=running
[[18:55:31]] [INFO] Executing action 20/50: textClear action
[[18:55:31]] [SUCCESS] Screenshot refreshed
[[18:55:31]] [INFO] Refreshing screenshot...
[[18:55:31]] [INFO] GYRHQr7TWx=pass
[[18:55:27]] [SUCCESS] Screenshot refreshed successfully
[[18:55:27]] [SUCCESS] Screenshot refreshed successfully
[[18:55:25]] [INFO] GYRHQr7TWx=running
[[18:55:25]] [INFO] Executing action 19/50: Tap on Text: "3000"
[[18:55:24]] [SUCCESS] Screenshot refreshed
[[18:55:24]] [INFO] Refreshing screenshot...
[[18:55:24]] [INFO] 8WCusTZ8q9=pass
[[18:55:14]] [SUCCESS] Screenshot refreshed successfully
[[18:55:14]] [SUCCESS] Screenshot refreshed successfully
[[18:55:14]] [INFO] 8WCusTZ8q9=running
[[18:55:14]] [INFO] Executing action 18/50: Tap on Text: "Search"
[[18:55:13]] [SUCCESS] Screenshot refreshed
[[18:55:13]] [INFO] Refreshing screenshot...
[[18:55:13]] [INFO] VkUKQbf1Qt=pass
[[18:55:08]] [SUCCESS] Screenshot refreshed successfully
[[18:55:08]] [SUCCESS] Screenshot refreshed successfully
[[18:55:07]] [INFO] VkUKQbf1Qt=running
[[18:55:07]] [INFO] Executing action 17/50: Tap on Text: "Edit"
[[18:55:07]] [SUCCESS] Screenshot refreshed
[[18:55:07]] [INFO] Refreshing screenshot...
[[18:55:07]] [INFO] BQ7Cxm53HQ=pass
[[18:55:03]] [SUCCESS] Screenshot refreshed successfully
[[18:55:03]] [SUCCESS] Screenshot refreshed successfully
[[18:55:02]] [INFO] BQ7Cxm53HQ=running
[[18:55:02]] [INFO] Executing action 16/50: Wait till text appears: "UNO"
[[18:55:02]] [SUCCESS] Screenshot refreshed
[[18:55:02]] [INFO] Refreshing screenshot...
[[18:55:02]] [INFO] IupxLP2Jsr=pass
[[18:55:00]] [SUCCESS] Screenshot refreshed successfully
[[18:55:00]] [SUCCESS] Screenshot refreshed successfully
[[18:55:00]] [INFO] IupxLP2Jsr=running
[[18:55:00]] [INFO] Executing action 15/50: Input text: "P_6225544"
[[18:54:59]] [SUCCESS] Screenshot refreshed
[[18:54:59]] [INFO] Refreshing screenshot...
[[18:54:59]] [INFO] 70iOOakiG7=pass
[[18:54:55]] [SUCCESS] Screenshot refreshed successfully
[[18:54:55]] [SUCCESS] Screenshot refreshed successfully
[[18:54:04]] [INFO] 70iOOakiG7=running
[[18:54:04]] [INFO] Executing action 14/50: Tap on Text: "Find"
[[18:54:04]] [SUCCESS] Screenshot refreshed
[[18:54:04]] [INFO] Refreshing screenshot...
[[18:54:04]] [INFO] Xqj9EIVEfg=pass
[[18:53:52]] [SUCCESS] Screenshot refreshed successfully
[[18:53:52]] [SUCCESS] Screenshot refreshed successfully
[[18:53:51]] [INFO] Xqj9EIVEfg=running
[[18:53:51]] [INFO] Executing action 13/50: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[18:53:51]] [SUCCESS] Screenshot refreshed
[[18:53:51]] [INFO] Refreshing screenshot...
[[18:53:51]] [INFO] E2jpN7BioW=pass
[[18:53:48]] [SUCCESS] Screenshot refreshed successfully
[[18:53:48]] [SUCCESS] Screenshot refreshed successfully
[[18:53:48]] [INFO] E2jpN7BioW=running
[[18:53:48]] [INFO] Executing action 12/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[18:53:47]] [SUCCESS] Screenshot refreshed
[[18:53:47]] [INFO] Refreshing screenshot...
[[18:53:47]] [INFO] kDnmoQJG4o=pass
[[18:53:46]] [SUCCESS] Screenshot refreshed successfully
[[18:53:46]] [SUCCESS] Screenshot refreshed successfully
[[18:53:45]] [INFO] kDnmoQJG4o=running
[[18:53:45]] [INFO] Executing action 11/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[18:53:45]] [SUCCESS] Screenshot refreshed
[[18:53:45]] [INFO] Refreshing screenshot...
[[18:53:45]] [INFO] mw9GQ4mzRE=pass
[[18:53:41]] [SUCCESS] Screenshot refreshed successfully
[[18:53:41]] [SUCCESS] Screenshot refreshed successfully
[[18:53:38]] [INFO] mw9GQ4mzRE=running
[[18:53:38]] [INFO] Executing action 10/50: Tap on Text: "VIC"
[[18:53:38]] [SUCCESS] Screenshot refreshed
[[18:53:38]] [INFO] Refreshing screenshot...
[[18:53:38]] [INFO] pldheRUBVi=pass
[[18:53:35]] [SUCCESS] Screenshot refreshed successfully
[[18:53:35]] [SUCCESS] Screenshot refreshed successfully
[[18:53:35]] [INFO] pldheRUBVi=running
[[18:53:35]] [INFO] Executing action 9/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[18:53:34]] [SUCCESS] Screenshot refreshed
[[18:53:34]] [INFO] Refreshing screenshot...
[[18:53:34]] [INFO] kbdEPCPYod=pass
[[18:53:31]] [SUCCESS] Screenshot refreshed successfully
[[18:53:31]] [SUCCESS] Screenshot refreshed successfully
[[18:53:31]] [INFO] kbdEPCPYod=running
[[18:53:31]] [INFO] Executing action 8/50: textClear action
[[18:53:30]] [SUCCESS] Screenshot refreshed
[[18:53:30]] [INFO] Refreshing screenshot...
[[18:53:30]] [INFO] 8WCusTZ8q9=pass
[[18:53:27]] [SUCCESS] Screenshot refreshed successfully
[[18:53:27]] [SUCCESS] Screenshot refreshed successfully
[[18:53:27]] [INFO] 8WCusTZ8q9=running
[[18:53:27]] [INFO] Executing action 7/50: Tap on Text: "3000"
[[18:53:26]] [SUCCESS] Screenshot refreshed
[[18:53:26]] [INFO] Refreshing screenshot...
[[18:53:26]] [INFO] QMXBlswP6H=pass
[[18:53:22]] [SUCCESS] Screenshot refreshed successfully
[[18:53:22]] [SUCCESS] Screenshot refreshed successfully
[[18:53:14]] [INFO] QMXBlswP6H=running
[[18:53:14]] [INFO] Executing action 6/50: Tap on Text: "Edit"
[[18:53:14]] [SUCCESS] Screenshot refreshed
[[18:53:14]] [INFO] Refreshing screenshot...
[[18:53:14]] [INFO] RLz6vQo3ag=pass
[[18:53:09]] [INFO] RLz6vQo3ag=running
[[18:53:09]] [INFO] Executing action 5/50: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[18:53:09]] [SUCCESS] Screenshot refreshed
[[18:53:09]] [INFO] Refreshing screenshot...
[[18:53:09]] [SUCCESS] Screenshot refreshed
[[18:53:09]] [INFO] Refreshing screenshot...
[[18:53:07]] [SUCCESS] Screenshot refreshed successfully
[[18:53:07]] [SUCCESS] Screenshot refreshed successfully
[[18:53:07]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[18:53:06]] [SUCCESS] Screenshot refreshed
[[18:53:06]] [INFO] Refreshing screenshot...
[[18:53:04]] [SUCCESS] Screenshot refreshed successfully
[[18:53:04]] [SUCCESS] Screenshot refreshed successfully
[[18:53:04]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[18:53:03]] [SUCCESS] Screenshot refreshed
[[18:53:03]] [INFO] Refreshing screenshot...
[[18:53:01]] [SUCCESS] Screenshot refreshed successfully
[[18:53:01]] [SUCCESS] Screenshot refreshed successfully
[[18:53:01]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[18:53:00]] [SUCCESS] Screenshot refreshed
[[18:53:00]] [INFO] Refreshing screenshot...
[[18:52:58]] [SUCCESS] Screenshot refreshed successfully
[[18:52:58]] [SUCCESS] Screenshot refreshed successfully
[[18:52:58]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[18:52:57]] [SUCCESS] Screenshot refreshed
[[18:52:57]] [INFO] Refreshing screenshot...
[[18:52:55]] [SUCCESS] Screenshot refreshed successfully
[[18:52:55]] [SUCCESS] Screenshot refreshed successfully
[[18:52:55]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[18:52:55]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[18:52:55]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[18:52:55]] [INFO] xz8njynjpZ=running
[[18:52:55]] [INFO] Executing action 4/50: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[18:52:55]] [SUCCESS] Screenshot refreshed
[[18:52:55]] [INFO] Refreshing screenshot...
[[18:52:55]] [INFO] J9loj6Zl5K=pass
[[18:52:34]] [SUCCESS] Screenshot refreshed successfully
[[18:52:34]] [SUCCESS] Screenshot refreshed successfully
[[18:52:34]] [INFO] J9loj6Zl5K=running
[[18:52:34]] [INFO] Executing action 3/50: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[18:52:34]] [SUCCESS] Screenshot refreshed
[[18:52:34]] [INFO] Refreshing screenshot...
[[18:52:34]] [INFO] Y8vz7AJD1i=pass
[[18:52:27]] [SUCCESS] Screenshot refreshed successfully
[[18:52:27]] [SUCCESS] Screenshot refreshed successfully
[[18:52:27]] [INFO] Y8vz7AJD1i=running
[[18:52:27]] [INFO] Executing action 2/50: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:52:26]] [SUCCESS] Screenshot refreshed
[[18:52:26]] [INFO] Refreshing screenshot...
[[18:52:26]] [INFO] H9fy9qcFbZ=pass
[[18:52:23]] [INFO] H9fy9qcFbZ=running
[[18:52:23]] [INFO] Executing action 1/50: Launch app: au.com.kmart
[[18:52:23]] [INFO] ExecutionManager: Starting execution of 50 actions...
[[18:52:23]] [SUCCESS] Cleared 1 screenshots from database
[[18:52:23]] [INFO] Clearing screenshots from database before execution...
[[18:52:23]] [SUCCESS] All screenshots deleted successfully
[[18:52:23]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[18:52:23]] [INFO] Skipping report initialization - single test case execution
[[18:52:15]] [SUCCESS] Test case Postcode Flow_AU_ANDROID saved successfully
[[18:52:15]] [INFO] Saving test case "Postcode Flow_AU_ANDROID"...
[[18:52:14]] [SUCCESS] Updated action #7
[[18:52:02]] [SUCCESS] All screenshots deleted successfully
[[18:52:02]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 50 actions
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: swipe
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: exists
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: swipe
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: textClear
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: waitTill
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: swipe
[[18:52:02]] [SUCCESS] Added action: exists
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: waitTill
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: textClear
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: waitTill
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: exists
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: waitTill
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: textClear
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: waitTill
[[18:52:02]] [SUCCESS] Added action: text
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: ifElseSteps
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: waitTill
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: textClear
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: tapOnText
[[18:52:02]] [SUCCESS] Added action: waitTill
[[18:52:02]] [SUCCESS] Added action: multiStep
[[18:52:02]] [SUCCESS] Added action: waitTill
[[18:52:02]] [SUCCESS] Added action: tap
[[18:52:02]] [SUCCESS] Added action: launchApp
[[18:52:02]] [INFO] All actions cleared
[[18:52:02]] [INFO] Cleaning up screenshots...
[[18:51:59]] [SUCCESS] Screenshot refreshed successfully
[[18:51:57]] [SUCCESS] Screenshot refreshed
[[18:51:57]] [INFO] Refreshing screenshot...
[[18:51:56]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[18:51:56]] [INFO] Device info updated: RMX2151
[[18:51:50]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[18:51:47]] [SUCCESS] Found 1 device(s)
[[18:51:47]] [INFO] Refreshing device list...
