Action Log - 2025-07-02 22:09:38
================================================================================

[[22:09:38]] [INFO] Generating execution report...
[[22:09:38]] [SUCCESS] All tests passed successfully!
[[22:09:37]] [SUCCESS] Screenshot refreshed
[[22:09:37]] [INFO] Refreshing screenshot...
[[22:09:37]] [INFO] xyHVihJMBi=pass
[[22:09:34]] [SUCCESS] Screenshot refreshed successfully
[[22:09:34]] [SUCCESS] Screenshot refreshed successfully
[[22:09:34]] [INFO] xyHVihJMBi=running
[[22:09:34]] [INFO] Executing action 51/51: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[22:09:33]] [SUCCESS] Screenshot refreshed
[[22:09:33]] [INFO] Refreshing screenshot...
[[22:09:33]] [INFO] mWeLQtXiL6=pass
[[22:09:28]] [SUCCESS] Screenshot refreshed successfully
[[22:09:28]] [SUCCESS] Screenshot refreshed successfully
[[22:09:28]] [INFO] mWeLQtXiL6=running
[[22:09:28]] [INFO] Executing action 50/51: Swipe from (50%, 70%) to (50%, 30%)
[[22:09:28]] [SUCCESS] Screenshot refreshed
[[22:09:28]] [INFO] Refreshing screenshot...
[[22:09:28]] [INFO] rkwVoJGZG4=pass
[[22:09:25]] [SUCCESS] Screenshot refreshed successfully
[[22:09:25]] [SUCCESS] Screenshot refreshed successfully
[[22:09:25]] [INFO] rkwVoJGZG4=running
[[22:09:25]] [INFO] Executing action 49/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[22:09:24]] [SUCCESS] Screenshot refreshed
[[22:09:24]] [INFO] Refreshing screenshot...
[[22:09:24]] [INFO] 0f2FSZYjWq=pass
[[22:09:21]] [SUCCESS] Screenshot refreshed successfully
[[22:09:21]] [SUCCESS] Screenshot refreshed successfully
[[22:09:11]] [INFO] 0f2FSZYjWq=running
[[22:09:11]] [INFO] Executing action 48/51: Check if element with text="3000" exists
[[22:09:11]] [SUCCESS] Screenshot refreshed
[[22:09:11]] [INFO] Refreshing screenshot...
[[22:09:11]] [INFO] Tebej51pT2=pass
[[22:09:09]] [SUCCESS] Screenshot refreshed successfully
[[22:09:09]] [SUCCESS] Screenshot refreshed successfully
[[22:09:08]] [INFO] Tebej51pT2=running
[[22:09:08]] [INFO] Executing action 47/51: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[22:09:08]] [SUCCESS] Screenshot refreshed
[[22:09:08]] [INFO] Refreshing screenshot...
[[22:09:08]] [INFO] eVytJrry9x=pass
[[22:09:06]] [SUCCESS] Screenshot refreshed successfully
[[22:09:06]] [SUCCESS] Screenshot refreshed successfully
[[22:09:06]] [INFO] eVytJrry9x=running
[[22:09:06]] [INFO] Executing action 46/51: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[22:09:05]] [SUCCESS] Screenshot refreshed
[[22:09:05]] [INFO] Refreshing screenshot...
[[22:09:05]] [INFO] s8h8VDUIOC=pass
[[22:09:02]] [INFO] s8h8VDUIOC=running
[[22:09:02]] [INFO] Executing action 45/51: Swipe from (50%, 70%) to (50%, 30%)
[[22:09:02]] [INFO] GYK47u1y3A=fail
[[22:09:02]] [ERROR] Action 44 failed: Android function execution failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[22:08:30]] [INFO] GYK47u1y3A=running
[[22:08:30]] [INFO] Executing action 44/51: Android Function: send_key_event - Key Event: TAB
[[22:08:23]] [SUCCESS] Screenshot refreshed successfully
[[22:08:23]] [SUCCESS] Screenshot refreshed successfully
[[22:08:23]] [SUCCESS] Screenshot refreshed
[[22:08:23]] [INFO] Refreshing screenshot...
[[22:08:23]] [INFO] ZWpYNcpbFA=pass
[[22:08:15]] [INFO] ZWpYNcpbFA=running
[[22:08:15]] [INFO] Executing action 43/51: Tap on Text: "VIC"
[[22:08:11]] [SUCCESS] Screenshot refreshed successfully
[[22:08:11]] [SUCCESS] Screenshot refreshed successfully
[[22:08:10]] [SUCCESS] Screenshot refreshed
[[22:08:10]] [INFO] Refreshing screenshot...
[[22:08:10]] [INFO] QpBLC6BStn=pass
[[22:08:08]] [INFO] QpBLC6BStn=running
[[22:08:08]] [INFO] Executing action 42/51: textClear action
[[22:08:04]] [SUCCESS] Screenshot refreshed successfully
[[22:08:04]] [SUCCESS] Screenshot refreshed successfully
[[22:08:03]] [SUCCESS] Screenshot refreshed
[[22:08:03]] [INFO] Refreshing screenshot...
[[22:08:03]] [INFO] G4A3KBlXHq=pass
[[22:07:39]] [INFO] G4A3KBlXHq=running
[[22:07:39]] [INFO] Executing action 41/51: Tap on Text: "Nearby"
[[22:07:38]] [SUCCESS] Screenshot refreshed successfully
[[22:07:38]] [SUCCESS] Screenshot refreshed successfully
[[22:07:38]] [SUCCESS] Screenshot refreshed
[[22:07:38]] [INFO] Refreshing screenshot...
[[22:07:38]] [INFO] 3gJsiap2Ds=pass
[[22:07:34]] [SUCCESS] Screenshot refreshed successfully
[[22:07:34]] [SUCCESS] Screenshot refreshed successfully
[[22:07:34]] [INFO] 3gJsiap2Ds=running
[[22:07:34]] [INFO] Executing action 40/51: Tap on Text: "Collect"
[[22:07:33]] [SUCCESS] Screenshot refreshed
[[22:07:33]] [INFO] Refreshing screenshot...
[[22:07:33]] [INFO] qofJDqXBME=pass
[[22:07:28]] [SUCCESS] Screenshot refreshed successfully
[[22:07:28]] [SUCCESS] Screenshot refreshed successfully
[[22:07:28]] [INFO] qofJDqXBME=running
[[22:07:28]] [INFO] Executing action 39/51: Wait till text appears: "Delivery"
[[22:07:27]] [SUCCESS] Screenshot refreshed
[[22:07:27]] [INFO] Refreshing screenshot...
[[22:07:27]] [INFO] rkwVoJGZG4=pass
[[22:07:26]] [SUCCESS] Screenshot refreshed successfully
[[22:07:26]] [SUCCESS] Screenshot refreshed successfully
[[22:07:25]] [INFO] rkwVoJGZG4=running
[[22:07:25]] [INFO] Executing action 38/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[22:07:25]] [SUCCESS] Screenshot refreshed
[[22:07:25]] [INFO] Refreshing screenshot...
[[22:07:25]] [INFO] 94ikwhIEE2=pass
[[22:07:21]] [SUCCESS] Screenshot refreshed successfully
[[22:07:21]] [SUCCESS] Screenshot refreshed successfully
[[22:07:13]] [INFO] 94ikwhIEE2=running
[[22:07:13]] [INFO] Executing action 37/51: Tap on Text: "bag"
[[22:07:12]] [SUCCESS] Screenshot refreshed
[[22:07:12]] [INFO] Refreshing screenshot...
[[22:07:12]] [INFO] DfwaiVZ8Z9=pass
[[22:07:09]] [SUCCESS] Screenshot refreshed successfully
[[22:07:09]] [SUCCESS] Screenshot refreshed successfully
[[22:07:09]] [INFO] DfwaiVZ8Z9=running
[[22:07:09]] [INFO] Executing action 36/51: Swipe from (50%, 70%) to (50%, 50%)
[[22:07:08]] [SUCCESS] Screenshot refreshed
[[22:07:08]] [INFO] Refreshing screenshot...
[[22:07:08]] [INFO] eRCmRhc3re=pass
[[22:07:05]] [SUCCESS] Screenshot refreshed successfully
[[22:07:05]] [SUCCESS] Screenshot refreshed successfully
[[22:07:05]] [INFO] eRCmRhc3re=running
[[22:07:05]] [INFO] Executing action 35/51: Check if element with text="Broadway" exists
[[22:07:05]] [SUCCESS] Screenshot refreshed
[[22:07:05]] [INFO] Refreshing screenshot...
[[22:07:05]] [INFO] E2jpN7BioW=pass
[[22:07:02]] [INFO] E2jpN7BioW=running
[[22:07:02]] [INFO] Executing action 34/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[22:07:02]] [INFO] IOc0IwmLPQ=fail
[[22:07:02]] [ERROR] Action 33 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[22:06:30]] [SUCCESS] Screenshot refreshed successfully
[[22:06:30]] [SUCCESS] Screenshot refreshed successfully
[[22:06:30]] [INFO] IOc0IwmLPQ=running
[[22:06:30]] [INFO] Executing action 33/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[22:06:30]] [SUCCESS] Screenshot refreshed
[[22:06:30]] [INFO] Refreshing screenshot...
[[22:06:30]] [INFO] H0ODFz7sWJ=pass
[[22:06:25]] [SUCCESS] Screenshot refreshed successfully
[[22:06:25]] [SUCCESS] Screenshot refreshed successfully
[[22:06:23]] [INFO] H0ODFz7sWJ=running
[[22:06:23]] [INFO] Executing action 32/51: Tap on Text: "2000"
[[22:06:23]] [SUCCESS] Screenshot refreshed
[[22:06:23]] [INFO] Refreshing screenshot...
[[22:06:23]] [INFO] pldheRUBVi=pass
[[22:06:12]] [SUCCESS] Screenshot refreshed successfully
[[22:06:12]] [SUCCESS] Screenshot refreshed successfully
[[22:06:12]] [INFO] pldheRUBVi=running
[[22:06:12]] [INFO] Executing action 31/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[22:06:11]] [SUCCESS] Screenshot refreshed
[[22:06:11]] [INFO] Refreshing screenshot...
[[22:06:11]] [INFO] uZHvvAzVfx=pass
[[22:06:08]] [INFO] uZHvvAzVfx=running
[[22:06:08]] [INFO] Executing action 30/51: textClear action
[[22:06:04]] [SUCCESS] Screenshot refreshed successfully
[[22:06:04]] [SUCCESS] Screenshot refreshed successfully
[[22:06:03]] [SUCCESS] Screenshot refreshed
[[22:06:03]] [INFO] Refreshing screenshot...
[[22:06:03]] [INFO] WmNWcsWVHv=pass
[[22:05:34]] [INFO] WmNWcsWVHv=running
[[22:05:34]] [INFO] Executing action 29/51: Tap on Text: "4000"
[[22:05:24]] [SUCCESS] Screenshot refreshed successfully
[[22:05:24]] [SUCCESS] Screenshot refreshed successfully
[[22:05:23]] [SUCCESS] Screenshot refreshed
[[22:05:23]] [INFO] Refreshing screenshot...
[[22:05:23]] [INFO] lnjoz8hHUU=pass
[[22:05:14]] [INFO] lnjoz8hHUU=running
[[22:05:14]] [INFO] Executing action 28/51: Tap on Text: "Edit"
[[22:05:04]] [SUCCESS] Screenshot refreshed successfully
[[22:05:04]] [SUCCESS] Screenshot refreshed successfully
[[22:05:03]] [SUCCESS] Screenshot refreshed
[[22:05:03]] [INFO] Refreshing screenshot...
[[22:05:03]] [INFO] BQ7Cxm53HQ=pass
[[22:04:44]] [SUCCESS] Screenshot refreshed successfully
[[22:04:44]] [SUCCESS] Screenshot refreshed successfully
[[22:04:44]] [INFO] BQ7Cxm53HQ=running
[[22:04:44]] [INFO] Executing action 27/51: Wait till text appears: "UNO"
[[22:04:43]] [SUCCESS] Screenshot refreshed
[[22:04:43]] [INFO] Refreshing screenshot...
[[22:04:43]] [INFO] VkUKQbf1Qt=pass
[[22:04:38]] [SUCCESS] Screenshot refreshed successfully
[[22:04:38]] [SUCCESS] Screenshot refreshed successfully
[[22:04:38]] [INFO] VkUKQbf1Qt=running
[[22:04:38]] [INFO] Executing action 26/51: Tap on Text: "UNO"
[[22:04:37]] [SUCCESS] Screenshot refreshed
[[22:04:37]] [INFO] Refreshing screenshot...
[[22:04:37]] [INFO] 73NABkfWyY=pass
[[22:04:31]] [SUCCESS] Screenshot refreshed successfully
[[22:04:31]] [SUCCESS] Screenshot refreshed successfully
[[22:04:31]] [INFO] 73NABkfWyY=running
[[22:04:31]] [INFO] Executing action 25/51: Check if element with text="Toowong" exists
[[22:04:30]] [SUCCESS] Screenshot refreshed
[[22:04:30]] [INFO] Refreshing screenshot...
[[22:04:30]] [INFO] E2jpN7BioW=pass
[[22:04:28]] [SUCCESS] Screenshot refreshed successfully
[[22:04:28]] [SUCCESS] Screenshot refreshed successfully
[[22:04:27]] [INFO] E2jpN7BioW=running
[[22:04:27]] [INFO] Executing action 24/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[22:04:27]] [SUCCESS] Screenshot refreshed
[[22:04:27]] [INFO] Refreshing screenshot...
[[22:04:27]] [INFO] IOc0IwmLPQ=pass
[[22:04:25]] [SUCCESS] Screenshot refreshed successfully
[[22:04:25]] [SUCCESS] Screenshot refreshed successfully
[[22:04:25]] [INFO] IOc0IwmLPQ=running
[[22:04:25]] [INFO] Executing action 23/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[22:04:24]] [SUCCESS] Screenshot refreshed
[[22:04:24]] [INFO] Refreshing screenshot...
[[22:04:24]] [INFO] VkUKQbf1Qt=pass
[[22:04:20]] [SUCCESS] Screenshot refreshed successfully
[[22:04:20]] [SUCCESS] Screenshot refreshed successfully
[[22:04:11]] [INFO] VkUKQbf1Qt=running
[[22:04:11]] [INFO] Executing action 22/51: Tap on Text: "CITY"
[[22:04:10]] [SUCCESS] Screenshot refreshed
[[22:04:10]] [INFO] Refreshing screenshot...
[[22:04:10]] [INFO] pldheRUBVi=pass
[[22:04:08]] [SUCCESS] Screenshot refreshed successfully
[[22:04:08]] [SUCCESS] Screenshot refreshed successfully
[[22:04:08]] [INFO] pldheRUBVi=running
[[22:04:08]] [INFO] Executing action 21/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[22:04:08]] [SUCCESS] Screenshot refreshed
[[22:04:08]] [INFO] Refreshing screenshot...
[[22:04:08]] [INFO] kbdEPCPYod=pass
[[22:04:05]] [SUCCESS] Screenshot refreshed successfully
[[22:04:05]] [SUCCESS] Screenshot refreshed successfully
[[22:04:04]] [INFO] kbdEPCPYod=running
[[22:04:04]] [INFO] Executing action 20/51: textClear action
[[22:04:04]] [SUCCESS] Screenshot refreshed
[[22:04:04]] [INFO] Refreshing screenshot...
[[22:04:04]] [INFO] pldheRUBVi=pass
[[22:03:35]] [SUCCESS] Screenshot refreshed successfully
[[22:03:35]] [SUCCESS] Screenshot refreshed successfully
[[22:03:35]] [INFO] pldheRUBVi=running
[[22:03:35]] [INFO] Executing action 19/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[22:03:34]] [SUCCESS] Screenshot refreshed
[[22:03:34]] [INFO] Refreshing screenshot...
[[22:03:28]] [SUCCESS] Screenshot refreshed successfully
[[22:03:28]] [SUCCESS] Screenshot refreshed successfully
[[22:03:27]] [INFO] Executing action 18/51: Wait for 5 ms
[[22:03:27]] [SUCCESS] Screenshot refreshed
[[22:03:27]] [INFO] Refreshing screenshot...
[[22:03:27]] [INFO] VkUKQbf1Qt=pass
[[22:03:23]] [SUCCESS] Screenshot refreshed successfully
[[22:03:23]] [SUCCESS] Screenshot refreshed successfully
[[22:03:23]] [INFO] VkUKQbf1Qt=running
[[22:03:23]] [INFO] Executing action 17/51: Tap on Text: "Edit"
[[22:03:23]] [SUCCESS] Screenshot refreshed
[[22:03:23]] [INFO] Refreshing screenshot...
[[22:03:23]] [INFO] BQ7Cxm53HQ=pass
[[22:03:08]] [SUCCESS] Screenshot refreshed successfully
[[22:03:08]] [SUCCESS] Screenshot refreshed successfully
[[22:03:08]] [INFO] BQ7Cxm53HQ=running
[[22:03:08]] [INFO] Executing action 16/51: Wait till text appears: "UNO"
[[22:03:08]] [SUCCESS] Screenshot refreshed
[[22:03:08]] [INFO] Refreshing screenshot...
[[22:03:08]] [INFO] IupxLP2Jsr=pass
[[22:03:06]] [SUCCESS] Screenshot refreshed successfully
[[22:03:06]] [SUCCESS] Screenshot refreshed successfully
[[22:03:05]] [INFO] IupxLP2Jsr=running
[[22:03:05]] [INFO] Executing action 15/51: Input text: "P_6225544"
[[22:03:05]] [SUCCESS] Screenshot refreshed
[[22:03:05]] [INFO] Refreshing screenshot...
[[22:03:05]] [INFO] 70iOOakiG7=pass
[[22:02:23]] [SUCCESS] Screenshot refreshed successfully
[[22:02:23]] [SUCCESS] Screenshot refreshed successfully
[[22:02:23]] [INFO] 70iOOakiG7=running
[[22:02:23]] [INFO] Executing action 14/51: Tap on Text: "Find"
[[22:02:22]] [SUCCESS] Screenshot refreshed
[[22:02:22]] [INFO] Refreshing screenshot...
[[22:02:22]] [INFO] Xqj9EIVEfg=pass
[[22:02:10]] [SUCCESS] Screenshot refreshed successfully
[[22:02:10]] [SUCCESS] Screenshot refreshed successfully
[[22:02:09]] [INFO] Xqj9EIVEfg=running
[[22:02:09]] [INFO] Executing action 13/51: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[22:02:09]] [SUCCESS] Screenshot refreshed
[[22:02:09]] [INFO] Refreshing screenshot...
[[22:02:09]] [INFO] E2jpN7BioW=pass
[[22:02:06]] [SUCCESS] Screenshot refreshed successfully
[[22:02:06]] [SUCCESS] Screenshot refreshed successfully
[[22:02:06]] [INFO] E2jpN7BioW=running
[[22:02:06]] [INFO] Executing action 12/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[22:02:06]] [SUCCESS] Screenshot refreshed
[[22:02:06]] [INFO] Refreshing screenshot...
[[22:02:06]] [INFO] kDnmoQJG4o=pass
[[22:02:04]] [SUCCESS] Screenshot refreshed successfully
[[22:02:04]] [SUCCESS] Screenshot refreshed successfully
[[22:02:04]] [INFO] kDnmoQJG4o=running
[[22:02:04]] [INFO] Executing action 11/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[22:02:03]] [SUCCESS] Screenshot refreshed
[[22:02:03]] [INFO] Refreshing screenshot...
[[22:02:03]] [INFO] mw9GQ4mzRE=pass
[[22:01:38]] [SUCCESS] Screenshot refreshed successfully
[[22:01:38]] [SUCCESS] Screenshot refreshed successfully
[[22:01:36]] [INFO] mw9GQ4mzRE=running
[[22:01:36]] [INFO] Executing action 10/51: Tap on Text: "ADELAIDE"
[[22:01:36]] [SUCCESS] Screenshot refreshed
[[22:01:36]] [INFO] Refreshing screenshot...
[[22:01:36]] [INFO] pldheRUBVi=pass
[[22:01:33]] [SUCCESS] Screenshot refreshed successfully
[[22:01:33]] [SUCCESS] Screenshot refreshed successfully
[[22:01:33]] [INFO] pldheRUBVi=running
[[22:01:33]] [INFO] Executing action 9/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[22:01:33]] [SUCCESS] Screenshot refreshed
[[22:01:33]] [INFO] Refreshing screenshot...
[[22:01:33]] [INFO] kbdEPCPYod=pass
[[22:01:29]] [SUCCESS] Screenshot refreshed successfully
[[22:01:29]] [SUCCESS] Screenshot refreshed successfully
[[22:01:29]] [INFO] kbdEPCPYod=running
[[22:01:29]] [INFO] Executing action 8/51: textClear action
[[22:01:29]] [SUCCESS] Screenshot refreshed
[[22:01:29]] [INFO] Refreshing screenshot...
[[22:01:29]] [INFO] pldheRUBVi=pass
[[22:01:26]] [SUCCESS] Screenshot refreshed successfully
[[22:01:26]] [SUCCESS] Screenshot refreshed successfully
[[22:01:25]] [INFO] pldheRUBVi=running
[[22:01:25]] [INFO] Executing action 7/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[22:01:25]] [SUCCESS] Screenshot refreshed
[[22:01:25]] [INFO] Refreshing screenshot...
[[22:01:25]] [INFO] QMXBlswP6H=pass
[[22:01:20]] [SUCCESS] Screenshot refreshed successfully
[[22:01:20]] [SUCCESS] Screenshot refreshed successfully
[[22:01:01]] [INFO] QMXBlswP6H=running
[[22:01:01]] [INFO] Executing action 6/51: Tap on Text: "Edit"
[[22:01:00]] [SUCCESS] Screenshot refreshed
[[22:01:00]] [INFO] Refreshing screenshot...
[[22:01:00]] [INFO] RLz6vQo3ag=pass
[[22:00:56]] [SUCCESS] Screenshot refreshed successfully
[[22:00:56]] [SUCCESS] Screenshot refreshed successfully
[[22:00:55]] [INFO] RLz6vQo3ag=running
[[22:00:55]] [INFO] Executing action 5/51: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[22:00:55]] [SUCCESS] Screenshot refreshed
[[22:00:55]] [INFO] Refreshing screenshot...
[[22:00:55]] [SUCCESS] Screenshot refreshed
[[22:00:55]] [INFO] Refreshing screenshot...
[[22:00:53]] [SUCCESS] Screenshot refreshed successfully
[[22:00:53]] [SUCCESS] Screenshot refreshed successfully
[[22:00:53]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[22:00:52]] [SUCCESS] Screenshot refreshed
[[22:00:52]] [INFO] Refreshing screenshot...
[[22:00:50]] [SUCCESS] Screenshot refreshed successfully
[[22:00:50]] [SUCCESS] Screenshot refreshed successfully
[[22:00:50]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[22:00:50]] [SUCCESS] Screenshot refreshed
[[22:00:50]] [INFO] Refreshing screenshot...
[[22:00:48]] [SUCCESS] Screenshot refreshed successfully
[[22:00:48]] [SUCCESS] Screenshot refreshed successfully
[[22:00:48]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[22:00:47]] [SUCCESS] Screenshot refreshed
[[22:00:47]] [INFO] Refreshing screenshot...
[[22:00:45]] [SUCCESS] Screenshot refreshed successfully
[[22:00:45]] [SUCCESS] Screenshot refreshed successfully
[[22:00:45]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[22:00:44]] [SUCCESS] Screenshot refreshed
[[22:00:44]] [INFO] Refreshing screenshot...
[[22:00:42]] [SUCCESS] Screenshot refreshed successfully
[[22:00:42]] [SUCCESS] Screenshot refreshed successfully
[[22:00:42]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[22:00:42]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[22:00:42]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[22:00:42]] [INFO] xz8njynjpZ=running
[[22:00:42]] [INFO] Executing action 4/51: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[22:00:42]] [SUCCESS] Screenshot refreshed
[[22:00:42]] [INFO] Refreshing screenshot...
[[22:00:42]] [INFO] J9loj6Zl5K=pass
[[22:00:40]] [SUCCESS] Screenshot refreshed successfully
[[22:00:40]] [SUCCESS] Screenshot refreshed successfully
[[22:00:39]] [INFO] J9loj6Zl5K=running
[[22:00:39]] [INFO] Executing action 3/51: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[22:00:39]] [SUCCESS] Screenshot refreshed
[[22:00:39]] [INFO] Refreshing screenshot...
[[22:00:39]] [INFO] Y8vz7AJD1i=pass
[[22:00:35]] [SUCCESS] Screenshot refreshed successfully
[[22:00:35]] [SUCCESS] Screenshot refreshed successfully
[[22:00:35]] [INFO] Y8vz7AJD1i=running
[[22:00:35]] [INFO] Executing action 2/51: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[22:00:34]] [SUCCESS] Screenshot refreshed
[[22:00:34]] [INFO] Refreshing screenshot...
[[22:00:34]] [INFO] H9fy9qcFbZ=pass
[[22:00:31]] [INFO] H9fy9qcFbZ=running
[[22:00:31]] [INFO] Executing action 1/51: Launch app: au.com.kmart
[[22:00:31]] [INFO] ExecutionManager: Starting execution of 51 actions...
[[22:00:31]] [SUCCESS] Cleared 1 screenshots from database
[[22:00:31]] [INFO] Clearing screenshots from database before execution...
[[22:00:31]] [SUCCESS] All screenshots deleted successfully
[[22:00:31]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[22:00:31]] [INFO] Skipping report initialization - single test case execution
[[22:00:28]] [SUCCESS] Test case Postcode Flow_AU_ANDROID saved successfully
[[22:00:28]] [INFO] Saving test case "Postcode Flow_AU_ANDROID"...
[[22:00:26]] [INFO] Action removed at index 18
[[22:00:06]] [SUCCESS] Added wait action
[[22:00:06]] [SUCCESS] Added action: wait
[[21:59:37]] [SUCCESS] Screenshot refreshed successfully
[[21:59:37]] [SUCCESS] Screenshot refreshed successfully
[[21:59:37]] [INFO] Skipping report generation - individual test case execution (not from test suite)
[[21:59:37]] [SUCCESS] Action logs saved successfully
[[21:59:36]] [ERROR] Execution failed but report was generated.
[[21:59:36]] [INFO] Saving 834 action log entries to file...
[[21:59:36]] [INFO] Generating execution report...
[[21:59:36]] [SUCCESS] All tests passed successfully!
[[21:59:36]] [SUCCESS] Screenshot refreshed
[[21:59:36]] [INFO] Refreshing screenshot...
[[21:59:36]] [INFO] xyHVihJMBi=pass
[[21:59:33]] [SUCCESS] Screenshot refreshed successfully
[[21:59:33]] [SUCCESS] Screenshot refreshed successfully
[[21:59:33]] [INFO] xyHVihJMBi=running
[[21:59:33]] [INFO] Executing action 51/51: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[21:59:32]] [SUCCESS] Screenshot refreshed
[[21:59:32]] [INFO] Refreshing screenshot...
[[21:59:32]] [INFO] mWeLQtXiL6=pass
[[21:59:27]] [SUCCESS] Screenshot refreshed successfully
[[21:59:27]] [SUCCESS] Screenshot refreshed successfully
[[21:59:27]] [INFO] mWeLQtXiL6=running
[[21:59:27]] [INFO] Executing action 50/51: Swipe from (50%, 70%) to (50%, 30%)
[[21:59:26]] [SUCCESS] Screenshot refreshed
[[21:59:26]] [INFO] Refreshing screenshot...
[[21:59:26]] [INFO] rkwVoJGZG4=pass
[[21:59:25]] [SUCCESS] Screenshot refreshed successfully
[[21:59:25]] [SUCCESS] Screenshot refreshed successfully
[[21:59:24]] [INFO] rkwVoJGZG4=running
[[21:59:24]] [INFO] Executing action 49/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[21:59:24]] [SUCCESS] Screenshot refreshed
[[21:59:24]] [INFO] Refreshing screenshot...
[[21:59:24]] [INFO] 0f2FSZYjWq=pass
[[21:59:10]] [SUCCESS] Screenshot refreshed successfully
[[21:59:10]] [SUCCESS] Screenshot refreshed successfully
[[21:59:09]] [INFO] 0f2FSZYjWq=running
[[21:59:09]] [INFO] Executing action 48/51: Check if element with text="3000" exists
[[21:59:09]] [SUCCESS] Screenshot refreshed
[[21:59:09]] [INFO] Refreshing screenshot...
[[21:59:09]] [INFO] Tebej51pT2=pass
[[21:59:07]] [SUCCESS] Screenshot refreshed successfully
[[21:59:07]] [SUCCESS] Screenshot refreshed successfully
[[21:59:06]] [INFO] Tebej51pT2=running
[[21:59:06]] [INFO] Executing action 47/51: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[21:59:06]] [SUCCESS] Screenshot refreshed
[[21:59:06]] [INFO] Refreshing screenshot...
[[21:59:06]] [INFO] eVytJrry9x=pass
[[21:59:04]] [SUCCESS] Screenshot refreshed successfully
[[21:59:04]] [SUCCESS] Screenshot refreshed successfully
[[21:59:04]] [INFO] eVytJrry9x=running
[[21:59:04]] [INFO] Executing action 46/51: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[21:59:03]] [SUCCESS] Screenshot refreshed
[[21:59:03]] [INFO] Refreshing screenshot...
[[21:59:03]] [INFO] s8h8VDUIOC=pass
[[21:58:52]] [SUCCESS] Screenshot refreshed successfully
[[21:58:52]] [SUCCESS] Screenshot refreshed successfully
[[21:58:52]] [INFO] s8h8VDUIOC=running
[[21:58:52]] [INFO] Executing action 45/51: Swipe from (50%, 70%) to (50%, 30%)
[[21:58:51]] [SUCCESS] Screenshot refreshed
[[21:58:51]] [INFO] Refreshing screenshot...
[[21:58:51]] [INFO] GYK47u1y3A=pass
[[21:58:49]] [SUCCESS] Screenshot refreshed successfully
[[21:58:49]] [SUCCESS] Screenshot refreshed successfully
[[21:58:49]] [INFO] GYK47u1y3A=running
[[21:58:49]] [INFO] Executing action 44/51: Android Function: send_key_event - Key Event: TAB
[[21:58:49]] [SUCCESS] Screenshot refreshed
[[21:58:49]] [INFO] Refreshing screenshot...
[[21:58:49]] [INFO] ZWpYNcpbFA=pass
[[21:58:45]] [SUCCESS] Screenshot refreshed successfully
[[21:58:45]] [SUCCESS] Screenshot refreshed successfully
[[21:58:43]] [INFO] ZWpYNcpbFA=running
[[21:58:43]] [INFO] Executing action 43/51: Tap on Text: "VIC"
[[21:58:43]] [SUCCESS] Screenshot refreshed
[[21:58:43]] [INFO] Refreshing screenshot...
[[21:58:43]] [INFO] QpBLC6BStn=pass
[[21:58:40]] [SUCCESS] Screenshot refreshed successfully
[[21:58:40]] [SUCCESS] Screenshot refreshed successfully
[[21:58:40]] [INFO] QpBLC6BStn=running
[[21:58:40]] [INFO] Executing action 42/51: textClear action
[[21:58:39]] [SUCCESS] Screenshot refreshed
[[21:58:39]] [INFO] Refreshing screenshot...
[[21:58:39]] [INFO] G4A3KBlXHq=pass
[[21:58:36]] [SUCCESS] Screenshot refreshed successfully
[[21:58:36]] [SUCCESS] Screenshot refreshed successfully
[[21:58:35]] [INFO] G4A3KBlXHq=running
[[21:58:35]] [INFO] Executing action 41/51: Tap on Text: "Nearby"
[[21:58:35]] [SUCCESS] Screenshot refreshed
[[21:58:35]] [INFO] Refreshing screenshot...
[[21:58:35]] [INFO] 3gJsiap2Ds=pass
[[21:58:31]] [SUCCESS] Screenshot refreshed successfully
[[21:58:31]] [SUCCESS] Screenshot refreshed successfully
[[21:58:31]] [INFO] 3gJsiap2Ds=running
[[21:58:31]] [INFO] Executing action 40/51: Tap on Text: "Collect"
[[21:58:31]] [SUCCESS] Screenshot refreshed
[[21:58:31]] [INFO] Refreshing screenshot...
[[21:58:31]] [INFO] qofJDqXBME=pass
[[21:58:25]] [SUCCESS] Screenshot refreshed successfully
[[21:58:25]] [SUCCESS] Screenshot refreshed successfully
[[21:58:25]] [INFO] qofJDqXBME=running
[[21:58:25]] [INFO] Executing action 39/51: Wait till text appears: "Delivery"
[[21:58:24]] [SUCCESS] Screenshot refreshed
[[21:58:24]] [INFO] Refreshing screenshot...
[[21:58:24]] [INFO] rkwVoJGZG4=pass
[[21:58:22]] [SUCCESS] Screenshot refreshed successfully
[[21:58:22]] [SUCCESS] Screenshot refreshed successfully
[[21:58:21]] [INFO] rkwVoJGZG4=running
[[21:58:21]] [INFO] Executing action 38/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[21:58:21]] [SUCCESS] Screenshot refreshed
[[21:58:21]] [INFO] Refreshing screenshot...
[[21:58:21]] [INFO] 94ikwhIEE2=pass
[[21:58:17]] [SUCCESS] Screenshot refreshed successfully
[[21:58:17]] [SUCCESS] Screenshot refreshed successfully
[[21:58:14]] [INFO] 94ikwhIEE2=running
[[21:58:14]] [INFO] Executing action 37/51: Tap on Text: "bag"
[[21:58:13]] [SUCCESS] Screenshot refreshed
[[21:58:13]] [INFO] Refreshing screenshot...
[[21:58:13]] [INFO] DfwaiVZ8Z9=pass
[[21:58:10]] [SUCCESS] Screenshot refreshed successfully
[[21:58:10]] [SUCCESS] Screenshot refreshed successfully
[[21:58:10]] [INFO] DfwaiVZ8Z9=running
[[21:58:10]] [INFO] Executing action 36/51: Swipe from (50%, 70%) to (50%, 50%)
[[21:58:09]] [SUCCESS] Screenshot refreshed
[[21:58:09]] [INFO] Refreshing screenshot...
[[21:58:09]] [INFO] eRCmRhc3re=pass
[[21:58:06]] [SUCCESS] Screenshot refreshed successfully
[[21:58:06]] [SUCCESS] Screenshot refreshed successfully
[[21:58:06]] [INFO] eRCmRhc3re=running
[[21:58:06]] [INFO] Executing action 35/51: Check if element with text="Broadway" exists
[[21:58:06]] [SUCCESS] Screenshot refreshed
[[21:58:06]] [INFO] Refreshing screenshot...
[[21:58:06]] [INFO] E2jpN7BioW=pass
[[21:58:04]] [SUCCESS] Screenshot refreshed successfully
[[21:58:04]] [SUCCESS] Screenshot refreshed successfully
[[21:58:03]] [INFO] E2jpN7BioW=running
[[21:58:03]] [INFO] Executing action 34/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:58:03]] [SUCCESS] Screenshot refreshed
[[21:58:03]] [INFO] Refreshing screenshot...
[[21:58:03]] [INFO] IOc0IwmLPQ=pass
[[21:57:31]] [SUCCESS] Screenshot refreshed successfully
[[21:57:31]] [SUCCESS] Screenshot refreshed successfully
[[21:57:31]] [INFO] IOc0IwmLPQ=running
[[21:57:31]] [INFO] Executing action 33/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:57:30]] [SUCCESS] Screenshot refreshed
[[21:57:30]] [INFO] Refreshing screenshot...
[[21:57:30]] [INFO] H0ODFz7sWJ=pass
[[21:57:25]] [SUCCESS] Screenshot refreshed successfully
[[21:57:25]] [SUCCESS] Screenshot refreshed successfully
[[21:57:25]] [INFO] H0ODFz7sWJ=running
[[21:57:25]] [INFO] Executing action 32/51: Tap on Text: "2000"
[[21:57:24]] [SUCCESS] Screenshot refreshed
[[21:57:24]] [INFO] Refreshing screenshot...
[[21:57:24]] [INFO] pldheRUBVi=pass
[[21:57:22]] [SUCCESS] Screenshot refreshed successfully
[[21:57:22]] [SUCCESS] Screenshot refreshed successfully
[[21:57:21]] [INFO] pldheRUBVi=running
[[21:57:21]] [INFO] Executing action 31/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:57:21]] [SUCCESS] Screenshot refreshed
[[21:57:21]] [INFO] Refreshing screenshot...
[[21:57:21]] [INFO] uZHvvAzVfx=pass
[[21:57:18]] [SUCCESS] Screenshot refreshed successfully
[[21:57:18]] [SUCCESS] Screenshot refreshed successfully
[[21:57:18]] [INFO] uZHvvAzVfx=running
[[21:57:18]] [INFO] Executing action 30/51: textClear action
[[21:57:17]] [SUCCESS] Screenshot refreshed
[[21:57:17]] [INFO] Refreshing screenshot...
[[21:57:17]] [INFO] WmNWcsWVHv=pass
[[21:57:12]] [SUCCESS] Screenshot refreshed successfully
[[21:57:12]] [SUCCESS] Screenshot refreshed successfully
[[21:57:11]] [INFO] WmNWcsWVHv=running
[[21:57:11]] [INFO] Executing action 29/51: Tap on Text: "4000"
[[21:57:11]] [SUCCESS] Screenshot refreshed
[[21:57:11]] [INFO] Refreshing screenshot...
[[21:57:11]] [INFO] lnjoz8hHUU=pass
[[21:57:07]] [SUCCESS] Screenshot refreshed successfully
[[21:57:07]] [SUCCESS] Screenshot refreshed successfully
[[21:57:04]] [INFO] lnjoz8hHUU=running
[[21:57:04]] [INFO] Executing action 28/51: Tap on Text: "Edit"
[[21:57:03]] [SUCCESS] Screenshot refreshed
[[21:57:03]] [INFO] Refreshing screenshot...
[[21:57:03]] [INFO] BQ7Cxm53HQ=pass
[[21:56:44]] [SUCCESS] Screenshot refreshed successfully
[[21:56:44]] [SUCCESS] Screenshot refreshed successfully
[[21:56:44]] [INFO] BQ7Cxm53HQ=running
[[21:56:44]] [INFO] Executing action 27/51: Wait till text appears: "UNO"
[[21:56:44]] [SUCCESS] Screenshot refreshed
[[21:56:44]] [INFO] Refreshing screenshot...
[[21:56:44]] [INFO] VkUKQbf1Qt=pass
[[21:56:38]] [SUCCESS] Screenshot refreshed successfully
[[21:56:38]] [SUCCESS] Screenshot refreshed successfully
[[21:56:38]] [INFO] VkUKQbf1Qt=running
[[21:56:38]] [INFO] Executing action 26/51: Tap on Text: "UNO"
[[21:56:38]] [SUCCESS] Screenshot refreshed
[[21:56:38]] [INFO] Refreshing screenshot...
[[21:56:38]] [INFO] 73NABkfWyY=pass
[[21:56:32]] [SUCCESS] Screenshot refreshed successfully
[[21:56:32]] [SUCCESS] Screenshot refreshed successfully
[[21:56:31]] [INFO] 73NABkfWyY=running
[[21:56:31]] [INFO] Executing action 25/51: Check if element with text="Toowong" exists
[[21:56:31]] [SUCCESS] Screenshot refreshed
[[21:56:31]] [INFO] Refreshing screenshot...
[[21:56:31]] [INFO] E2jpN7BioW=pass
[[21:56:28]] [SUCCESS] Screenshot refreshed successfully
[[21:56:28]] [SUCCESS] Screenshot refreshed successfully
[[21:56:28]] [INFO] E2jpN7BioW=running
[[21:56:28]] [INFO] Executing action 24/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:56:27]] [SUCCESS] Screenshot refreshed
[[21:56:27]] [INFO] Refreshing screenshot...
[[21:56:27]] [INFO] IOc0IwmLPQ=pass
[[21:56:26]] [SUCCESS] Screenshot refreshed successfully
[[21:56:26]] [SUCCESS] Screenshot refreshed successfully
[[21:56:25]] [INFO] IOc0IwmLPQ=running
[[21:56:25]] [INFO] Executing action 23/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:56:25]] [SUCCESS] Screenshot refreshed
[[21:56:25]] [INFO] Refreshing screenshot...
[[21:56:25]] [INFO] VkUKQbf1Qt=pass
[[21:56:21]] [SUCCESS] Screenshot refreshed successfully
[[21:56:21]] [SUCCESS] Screenshot refreshed successfully
[[21:56:12]] [INFO] VkUKQbf1Qt=running
[[21:56:12]] [INFO] Executing action 22/51: Tap on Text: "CITY"
[[21:56:11]] [SUCCESS] Screenshot refreshed
[[21:56:11]] [INFO] Refreshing screenshot...
[[21:56:11]] [INFO] pldheRUBVi=pass
[[21:56:09]] [SUCCESS] Screenshot refreshed successfully
[[21:56:09]] [SUCCESS] Screenshot refreshed successfully
[[21:56:09]] [INFO] pldheRUBVi=running
[[21:56:09]] [INFO] Executing action 21/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:56:08]] [SUCCESS] Screenshot refreshed
[[21:56:08]] [INFO] Refreshing screenshot...
[[21:56:08]] [INFO] kbdEPCPYod=pass
[[21:56:05]] [SUCCESS] Screenshot refreshed successfully
[[21:56:05]] [SUCCESS] Screenshot refreshed successfully
[[21:56:05]] [INFO] kbdEPCPYod=running
[[21:56:05]] [INFO] Executing action 20/51: textClear action
[[21:56:04]] [SUCCESS] Screenshot refreshed
[[21:56:04]] [INFO] Refreshing screenshot...
[[21:56:04]] [INFO] pldheRUBVi=pass
[[21:55:53]] [INFO] pldheRUBVi=running
[[21:55:53]] [INFO] Executing action 19/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:55:53]] [INFO] 0FggZQe6oU=fail
[[21:55:53]] [ERROR] Action 18 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[21:55:22]] [SUCCESS] Screenshot refreshed successfully
[[21:55:22]] [SUCCESS] Screenshot refreshed successfully
[[21:55:21]] [INFO] 0FggZQe6oU=running
[[21:55:21]] [INFO] Executing action 18/51: Wait till xpath=//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:55:21]] [SUCCESS] Screenshot refreshed
[[21:55:21]] [INFO] Refreshing screenshot...
[[21:55:21]] [INFO] VkUKQbf1Qt=pass
[[21:55:15]] [SUCCESS] Screenshot refreshed successfully
[[21:55:15]] [SUCCESS] Screenshot refreshed successfully
[[21:55:15]] [INFO] VkUKQbf1Qt=running
[[21:55:15]] [INFO] Executing action 17/51: Tap on Text: "Edit"
[[21:55:14]] [SUCCESS] Screenshot refreshed
[[21:55:14]] [INFO] Refreshing screenshot...
[[21:55:14]] [INFO] BQ7Cxm53HQ=pass
[[21:55:10]] [SUCCESS] Screenshot refreshed successfully
[[21:55:10]] [SUCCESS] Screenshot refreshed successfully
[[21:55:10]] [INFO] BQ7Cxm53HQ=running
[[21:55:10]] [INFO] Executing action 16/51: Wait till text appears: "UNO"
[[21:55:09]] [SUCCESS] Screenshot refreshed
[[21:55:09]] [INFO] Refreshing screenshot...
[[21:55:09]] [INFO] IupxLP2Jsr=pass
[[21:55:08]] [SUCCESS] Screenshot refreshed successfully
[[21:55:08]] [SUCCESS] Screenshot refreshed successfully
[[21:55:07]] [INFO] IupxLP2Jsr=running
[[21:55:07]] [INFO] Executing action 15/51: Input text: "P_6225544"
[[21:55:07]] [SUCCESS] Screenshot refreshed
[[21:55:07]] [INFO] Refreshing screenshot...
[[21:55:07]] [INFO] 70iOOakiG7=pass
[[21:55:03]] [SUCCESS] Screenshot refreshed successfully
[[21:55:03]] [SUCCESS] Screenshot refreshed successfully
[[21:54:23]] [INFO] 70iOOakiG7=running
[[21:54:23]] [INFO] Executing action 14/51: Tap on Text: "Find"
[[21:54:22]] [SUCCESS] Screenshot refreshed
[[21:54:22]] [INFO] Refreshing screenshot...
[[21:54:22]] [INFO] Xqj9EIVEfg=pass
[[21:54:11]] [SUCCESS] Screenshot refreshed successfully
[[21:54:11]] [SUCCESS] Screenshot refreshed successfully
[[21:54:10]] [INFO] Xqj9EIVEfg=running
[[21:54:10]] [INFO] Executing action 13/51: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[21:54:10]] [SUCCESS] Screenshot refreshed
[[21:54:10]] [INFO] Refreshing screenshot...
[[21:54:10]] [INFO] E2jpN7BioW=pass
[[21:54:07]] [SUCCESS] Screenshot refreshed successfully
[[21:54:07]] [SUCCESS] Screenshot refreshed successfully
[[21:54:07]] [INFO] E2jpN7BioW=running
[[21:54:07]] [INFO] Executing action 12/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:54:06]] [SUCCESS] Screenshot refreshed
[[21:54:06]] [INFO] Refreshing screenshot...
[[21:54:06]] [INFO] kDnmoQJG4o=pass
[[21:54:04]] [SUCCESS] Screenshot refreshed successfully
[[21:54:04]] [SUCCESS] Screenshot refreshed successfully
[[21:54:04]] [INFO] kDnmoQJG4o=running
[[21:54:04]] [INFO] Executing action 11/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:54:04]] [SUCCESS] Screenshot refreshed
[[21:54:04]] [INFO] Refreshing screenshot...
[[21:54:04]] [INFO] mw9GQ4mzRE=pass
[[21:53:23]] [SUCCESS] Screenshot refreshed successfully
[[21:53:23]] [SUCCESS] Screenshot refreshed successfully
[[21:53:23]] [INFO] mw9GQ4mzRE=running
[[21:53:23]] [INFO] Executing action 10/51: Tap on Text: "ADELAIDE"
[[21:53:22]] [SUCCESS] Screenshot refreshed
[[21:53:22]] [INFO] Refreshing screenshot...
[[21:53:22]] [INFO] pldheRUBVi=pass
[[21:53:20]] [SUCCESS] Screenshot refreshed successfully
[[21:53:20]] [SUCCESS] Screenshot refreshed successfully
[[21:53:19]] [INFO] pldheRUBVi=running
[[21:53:19]] [INFO] Executing action 9/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:53:19]] [SUCCESS] Screenshot refreshed
[[21:53:19]] [INFO] Refreshing screenshot...
[[21:53:19]] [INFO] kbdEPCPYod=pass
[[21:53:16]] [SUCCESS] Screenshot refreshed successfully
[[21:53:16]] [SUCCESS] Screenshot refreshed successfully
[[21:53:15]] [INFO] kbdEPCPYod=running
[[21:53:15]] [INFO] Executing action 8/51: textClear action
[[21:53:15]] [SUCCESS] Screenshot refreshed
[[21:53:15]] [INFO] Refreshing screenshot...
[[21:53:15]] [INFO] pldheRUBVi=pass
[[21:53:12]] [SUCCESS] Screenshot refreshed successfully
[[21:53:12]] [SUCCESS] Screenshot refreshed successfully
[[21:53:12]] [INFO] pldheRUBVi=running
[[21:53:12]] [INFO] Executing action 7/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:53:12]] [SUCCESS] Screenshot refreshed
[[21:53:12]] [INFO] Refreshing screenshot...
[[21:53:12]] [INFO] QMXBlswP6H=pass
[[21:53:05]] [SUCCESS] Screenshot refreshed successfully
[[21:53:05]] [SUCCESS] Screenshot refreshed successfully
[[21:53:04]] [INFO] QMXBlswP6H=running
[[21:53:04]] [INFO] Executing action 6/51: Tap on Text: "Edit"
[[21:53:04]] [SUCCESS] Screenshot refreshed
[[21:53:04]] [INFO] Refreshing screenshot...
[[21:53:04]] [INFO] RLz6vQo3ag=pass
[[21:53:01]] [SUCCESS] Screenshot refreshed successfully
[[21:53:01]] [SUCCESS] Screenshot refreshed successfully
[[21:52:49]] [INFO] RLz6vQo3ag=running
[[21:52:49]] [INFO] Executing action 5/51: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[21:52:49]] [SUCCESS] Screenshot refreshed
[[21:52:49]] [INFO] Refreshing screenshot...
[[21:52:49]] [SUCCESS] Screenshot refreshed
[[21:52:49]] [INFO] Refreshing screenshot...
[[21:52:47]] [SUCCESS] Screenshot refreshed successfully
[[21:52:47]] [SUCCESS] Screenshot refreshed successfully
[[21:52:47]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[21:52:46]] [SUCCESS] Screenshot refreshed
[[21:52:46]] [INFO] Refreshing screenshot...
[[21:52:44]] [SUCCESS] Screenshot refreshed successfully
[[21:52:44]] [SUCCESS] Screenshot refreshed successfully
[[21:52:44]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[21:52:44]] [SUCCESS] Screenshot refreshed
[[21:52:44]] [INFO] Refreshing screenshot...
[[21:52:42]] [SUCCESS] Screenshot refreshed successfully
[[21:52:42]] [SUCCESS] Screenshot refreshed successfully
[[21:52:41]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[21:52:41]] [SUCCESS] Screenshot refreshed
[[21:52:41]] [INFO] Refreshing screenshot...
[[21:52:39]] [SUCCESS] Screenshot refreshed successfully
[[21:52:39]] [SUCCESS] Screenshot refreshed successfully
[[21:52:39]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[21:52:38]] [SUCCESS] Screenshot refreshed
[[21:52:38]] [INFO] Refreshing screenshot...
[[21:52:37]] [SUCCESS] Screenshot refreshed successfully
[[21:52:37]] [SUCCESS] Screenshot refreshed successfully
[[21:52:36]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[21:52:36]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[21:52:36]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[21:52:36]] [INFO] xz8njynjpZ=running
[[21:52:36]] [INFO] Executing action 4/51: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[21:52:36]] [SUCCESS] Screenshot refreshed
[[21:52:36]] [INFO] Refreshing screenshot...
[[21:52:36]] [INFO] J9loj6Zl5K=pass
[[21:52:34]] [SUCCESS] Screenshot refreshed successfully
[[21:52:34]] [SUCCESS] Screenshot refreshed successfully
[[21:52:34]] [INFO] J9loj6Zl5K=running
[[21:52:34]] [INFO] Executing action 3/51: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[21:52:33]] [SUCCESS] Screenshot refreshed
[[21:52:33]] [INFO] Refreshing screenshot...
[[21:52:33]] [INFO] Y8vz7AJD1i=pass
[[21:52:30]] [SUCCESS] Screenshot refreshed successfully
[[21:52:30]] [SUCCESS] Screenshot refreshed successfully
[[21:52:29]] [INFO] Y8vz7AJD1i=running
[[21:52:29]] [INFO] Executing action 2/51: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[21:52:28]] [SUCCESS] Screenshot refreshed
[[21:52:28]] [INFO] Refreshing screenshot...
[[21:52:28]] [INFO] H9fy9qcFbZ=pass
[[21:52:25]] [INFO] H9fy9qcFbZ=running
[[21:52:25]] [INFO] Executing action 1/51: Launch app: au.com.kmart
[[21:52:25]] [INFO] ExecutionManager: Starting execution of 51 actions...
[[21:52:25]] [SUCCESS] Cleared 1 screenshots from database
[[21:52:25]] [INFO] Clearing screenshots from database before execution...
[[21:52:25]] [SUCCESS] All screenshots deleted successfully
[[21:52:25]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:52:25]] [INFO] Skipping report initialization - single test case execution
[[21:49:19]] [SUCCESS] Screenshot refreshed successfully
[[21:49:19]] [SUCCESS] Screenshot refreshed successfully
[[21:49:18]] [INFO] Skipping report generation - individual test case execution (not from test suite)
[[21:49:18]] [SUCCESS] Action logs saved successfully
[[21:49:18]] [ERROR] Execution failed but report was generated.
[[21:49:18]] [INFO] Saving 444 action log entries to file...
[[21:49:18]] [INFO] Generating execution report...
[[21:49:18]] [SUCCESS] All tests passed successfully!
[[21:49:18]] [SUCCESS] Screenshot refreshed
[[21:49:18]] [INFO] Refreshing screenshot...
[[21:49:18]] [INFO] xyHVihJMBi=pass
[[21:49:15]] [SUCCESS] Screenshot refreshed successfully
[[21:49:15]] [SUCCESS] Screenshot refreshed successfully
[[21:49:15]] [INFO] xyHVihJMBi=running
[[21:49:15]] [INFO] Executing action 51/51: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[21:49:14]] [SUCCESS] Screenshot refreshed
[[21:49:14]] [INFO] Refreshing screenshot...
[[21:49:14]] [INFO] mWeLQtXiL6=pass
[[21:49:09]] [SUCCESS] Screenshot refreshed successfully
[[21:49:09]] [SUCCESS] Screenshot refreshed successfully
[[21:49:09]] [INFO] mWeLQtXiL6=running
[[21:49:09]] [INFO] Executing action 50/51: Swipe from (50%, 70%) to (50%, 30%)
[[21:49:08]] [SUCCESS] Screenshot refreshed
[[21:49:08]] [INFO] Refreshing screenshot...
[[21:49:08]] [INFO] rkwVoJGZG4=pass
[[21:49:07]] [SUCCESS] Screenshot refreshed successfully
[[21:49:07]] [SUCCESS] Screenshot refreshed successfully
[[21:49:06]] [INFO] rkwVoJGZG4=running
[[21:49:06]] [INFO] Executing action 49/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[21:49:06]] [SUCCESS] Screenshot refreshed
[[21:49:06]] [INFO] Refreshing screenshot...
[[21:49:06]] [INFO] 0f2FSZYjWq=pass
[[21:49:01]] [SUCCESS] Screenshot refreshed successfully
[[21:49:01]] [SUCCESS] Screenshot refreshed successfully
[[21:48:29]] [INFO] 0f2FSZYjWq=running
[[21:48:29]] [INFO] Executing action 48/51: Check if element with text="3000" exists
[[21:48:28]] [SUCCESS] Screenshot refreshed
[[21:48:28]] [INFO] Refreshing screenshot...
[[21:48:28]] [INFO] Tebej51pT2=pass
[[21:48:26]] [SUCCESS] Screenshot refreshed successfully
[[21:48:26]] [SUCCESS] Screenshot refreshed successfully
[[21:48:26]] [INFO] Tebej51pT2=running
[[21:48:26]] [INFO] Executing action 47/51: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[21:48:26]] [SUCCESS] Screenshot refreshed
[[21:48:26]] [INFO] Refreshing screenshot...
[[21:48:26]] [INFO] eVytJrry9x=pass
[[21:48:24]] [SUCCESS] Screenshot refreshed successfully
[[21:48:24]] [SUCCESS] Screenshot refreshed successfully
[[21:48:23]] [INFO] eVytJrry9x=running
[[21:48:23]] [INFO] Executing action 46/51: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[21:48:23]] [SUCCESS] Screenshot refreshed
[[21:48:23]] [INFO] Refreshing screenshot...
[[21:48:23]] [INFO] s8h8VDUIOC=pass
[[21:48:12]] [SUCCESS] Screenshot refreshed successfully
[[21:48:12]] [SUCCESS] Screenshot refreshed successfully
[[21:48:11]] [INFO] s8h8VDUIOC=running
[[21:48:11]] [INFO] Executing action 45/51: Swipe from (50%, 70%) to (50%, 30%)
[[21:48:11]] [SUCCESS] Screenshot refreshed
[[21:48:11]] [INFO] Refreshing screenshot...
[[21:48:11]] [INFO] GYK47u1y3A=pass
[[21:48:08]] [SUCCESS] Screenshot refreshed successfully
[[21:48:08]] [SUCCESS] Screenshot refreshed successfully
[[21:48:08]] [INFO] GYK47u1y3A=running
[[21:48:08]] [INFO] Executing action 44/51: Android Function: send_key_event - Key Event: TAB
[[21:48:08]] [SUCCESS] Screenshot refreshed
[[21:48:08]] [INFO] Refreshing screenshot...
[[21:48:08]] [INFO] ZWpYNcpbFA=pass
[[21:48:04]] [SUCCESS] Screenshot refreshed successfully
[[21:48:04]] [SUCCESS] Screenshot refreshed successfully
[[21:48:04]] [INFO] ZWpYNcpbFA=running
[[21:48:04]] [INFO] Executing action 43/51: Tap on Text: "VIC"
[[21:48:03]] [SUCCESS] Screenshot refreshed
[[21:48:03]] [INFO] Refreshing screenshot...
[[21:48:03]] [INFO] QpBLC6BStn=pass
[[21:47:46]] [SUCCESS] Screenshot refreshed successfully
[[21:47:46]] [SUCCESS] Screenshot refreshed successfully
[[21:47:46]] [INFO] QpBLC6BStn=running
[[21:47:46]] [INFO] Executing action 42/51: textClear action
[[21:47:45]] [SUCCESS] Screenshot refreshed
[[21:47:45]] [INFO] Refreshing screenshot...
[[21:47:45]] [INFO] G4A3KBlXHq=pass
[[21:47:41]] [SUCCESS] Screenshot refreshed successfully
[[21:47:41]] [SUCCESS] Screenshot refreshed successfully
[[21:47:41]] [INFO] G4A3KBlXHq=running
[[21:47:41]] [INFO] Executing action 41/51: Tap on Text: "Nearby"
[[21:47:41]] [SUCCESS] Screenshot refreshed
[[21:47:41]] [INFO] Refreshing screenshot...
[[21:47:41]] [INFO] 3gJsiap2Ds=pass
[[21:47:36]] [SUCCESS] Screenshot refreshed successfully
[[21:47:36]] [SUCCESS] Screenshot refreshed successfully
[[21:47:36]] [INFO] 3gJsiap2Ds=running
[[21:47:36]] [INFO] Executing action 40/51: Tap on Text: "Collect"
[[21:47:35]] [SUCCESS] Screenshot refreshed
[[21:47:35]] [INFO] Refreshing screenshot...
[[21:47:35]] [INFO] qofJDqXBME=pass
[[21:47:29]] [SUCCESS] Screenshot refreshed successfully
[[21:47:29]] [SUCCESS] Screenshot refreshed successfully
[[21:47:29]] [INFO] qofJDqXBME=running
[[21:47:29]] [INFO] Executing action 39/51: Wait till text appears: "Delivery"
[[21:47:28]] [SUCCESS] Screenshot refreshed
[[21:47:28]] [INFO] Refreshing screenshot...
[[21:47:28]] [INFO] rkwVoJGZG4=pass
[[21:47:27]] [SUCCESS] Screenshot refreshed successfully
[[21:47:27]] [SUCCESS] Screenshot refreshed successfully
[[21:47:26]] [INFO] rkwVoJGZG4=running
[[21:47:26]] [INFO] Executing action 38/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[21:47:26]] [SUCCESS] Screenshot refreshed
[[21:47:26]] [INFO] Refreshing screenshot...
[[21:47:26]] [INFO] 94ikwhIEE2=pass
[[21:47:22]] [SUCCESS] Screenshot refreshed successfully
[[21:47:22]] [SUCCESS] Screenshot refreshed successfully
[[21:47:18]] [INFO] 94ikwhIEE2=running
[[21:47:18]] [INFO] Executing action 37/51: Tap on Text: "bag"
[[21:47:18]] [SUCCESS] Screenshot refreshed
[[21:47:18]] [INFO] Refreshing screenshot...
[[21:47:18]] [INFO] DfwaiVZ8Z9=pass
[[21:47:15]] [SUCCESS] Screenshot refreshed successfully
[[21:47:15]] [SUCCESS] Screenshot refreshed successfully
[[21:47:15]] [INFO] DfwaiVZ8Z9=running
[[21:47:15]] [INFO] Executing action 36/51: Swipe from (50%, 70%) to (50%, 50%)
[[21:47:14]] [SUCCESS] Screenshot refreshed
[[21:47:14]] [INFO] Refreshing screenshot...
[[21:47:14]] [INFO] eRCmRhc3re=pass
[[21:47:11]] [SUCCESS] Screenshot refreshed successfully
[[21:47:11]] [SUCCESS] Screenshot refreshed successfully
[[21:47:11]] [INFO] eRCmRhc3re=running
[[21:47:11]] [INFO] Executing action 35/51: Check if element with text="Broadway" exists
[[21:47:11]] [SUCCESS] Screenshot refreshed
[[21:47:11]] [INFO] Refreshing screenshot...
[[21:47:11]] [INFO] E2jpN7BioW=pass
[[21:47:08]] [SUCCESS] Screenshot refreshed successfully
[[21:47:08]] [SUCCESS] Screenshot refreshed successfully
[[21:47:08]] [INFO] E2jpN7BioW=running
[[21:47:08]] [INFO] Executing action 34/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:47:07]] [SUCCESS] Screenshot refreshed
[[21:47:07]] [INFO] Refreshing screenshot...
[[21:47:07]] [INFO] IOc0IwmLPQ=pass
[[21:47:06]] [SUCCESS] Screenshot refreshed successfully
[[21:47:06]] [SUCCESS] Screenshot refreshed successfully
[[21:47:05]] [INFO] IOc0IwmLPQ=running
[[21:47:05]] [INFO] Executing action 33/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:47:05]] [SUCCESS] Screenshot refreshed
[[21:47:05]] [INFO] Refreshing screenshot...
[[21:47:05]] [INFO] H0ODFz7sWJ=pass
[[21:46:28]] [SUCCESS] Screenshot refreshed successfully
[[21:46:28]] [SUCCESS] Screenshot refreshed successfully
[[21:46:27]] [INFO] H0ODFz7sWJ=running
[[21:46:27]] [INFO] Executing action 32/51: Tap on Text: "2000"
[[21:46:27]] [SUCCESS] Screenshot refreshed
[[21:46:27]] [INFO] Refreshing screenshot...
[[21:46:27]] [INFO] pldheRUBVi=pass
[[21:46:24]] [SUCCESS] Screenshot refreshed successfully
[[21:46:24]] [SUCCESS] Screenshot refreshed successfully
[[21:46:24]] [INFO] pldheRUBVi=running
[[21:46:24]] [INFO] Executing action 31/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:46:23]] [SUCCESS] Screenshot refreshed
[[21:46:23]] [INFO] Refreshing screenshot...
[[21:46:23]] [INFO] uZHvvAzVfx=pass
[[21:46:12]] [SUCCESS] Screenshot refreshed successfully
[[21:46:12]] [SUCCESS] Screenshot refreshed successfully
[[21:46:12]] [INFO] uZHvvAzVfx=running
[[21:46:12]] [INFO] Executing action 30/51: textClear action
[[21:46:11]] [SUCCESS] Screenshot refreshed
[[21:46:11]] [INFO] Refreshing screenshot...
[[21:46:11]] [INFO] WmNWcsWVHv=pass
[[21:46:06]] [SUCCESS] Screenshot refreshed successfully
[[21:46:06]] [SUCCESS] Screenshot refreshed successfully
[[21:46:06]] [INFO] WmNWcsWVHv=running
[[21:46:06]] [INFO] Executing action 29/51: Tap on Text: "4000"
[[21:46:05]] [SUCCESS] Screenshot refreshed
[[21:46:05]] [INFO] Refreshing screenshot...
[[21:46:05]] [INFO] lnjoz8hHUU=pass
[[21:45:53]] [INFO] lnjoz8hHUU=running
[[21:45:53]] [INFO] Executing action 28/51: Tap on Text: "Edit"
[[21:45:53]] [INFO] BQ7Cxm53HQ=fail
[[21:45:53]] [ERROR] Action 27 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[21:45:20]] [SUCCESS] Screenshot refreshed successfully
[[21:45:20]] [SUCCESS] Screenshot refreshed successfully
[[21:45:20]] [INFO] BQ7Cxm53HQ=running
[[21:45:20]] [INFO] Executing action 27/51: Wait till text appears: "UNO"
[[21:45:20]] [SUCCESS] Screenshot refreshed
[[21:45:20]] [INFO] Refreshing screenshot...
[[21:45:20]] [INFO] VkUKQbf1Qt=pass
[[21:45:14]] [SUCCESS] Screenshot refreshed successfully
[[21:45:14]] [SUCCESS] Screenshot refreshed successfully
[[21:45:14]] [INFO] VkUKQbf1Qt=running
[[21:45:14]] [INFO] Executing action 26/51: Tap on Text: "UNO"
[[21:45:13]] [SUCCESS] Screenshot refreshed
[[21:45:13]] [INFO] Refreshing screenshot...
[[21:45:13]] [INFO] 73NABkfWyY=pass
[[21:45:08]] [SUCCESS] Screenshot refreshed successfully
[[21:45:08]] [SUCCESS] Screenshot refreshed successfully
[[21:45:07]] [INFO] 73NABkfWyY=running
[[21:45:07]] [INFO] Executing action 25/51: Check if element with text="Toowong" exists
[[21:45:07]] [SUCCESS] Screenshot refreshed
[[21:45:07]] [INFO] Refreshing screenshot...
[[21:45:07]] [INFO] E2jpN7BioW=pass
[[21:45:04]] [SUCCESS] Screenshot refreshed successfully
[[21:45:04]] [SUCCESS] Screenshot refreshed successfully
[[21:45:04]] [INFO] E2jpN7BioW=running
[[21:45:04]] [INFO] Executing action 24/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:45:04]] [SUCCESS] Screenshot refreshed
[[21:45:04]] [INFO] Refreshing screenshot...
[[21:45:04]] [INFO] IOc0IwmLPQ=pass
[[21:45:02]] [INFO] IOc0IwmLPQ=running
[[21:45:02]] [INFO] Executing action 23/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:45:02]] [INFO] VkUKQbf1Qt=fail
[[21:45:02]] [ERROR] Action 22 failed: Error tapping on text: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[21:44:28]] [SUCCESS] Screenshot refreshed successfully
[[21:44:28]] [SUCCESS] Screenshot refreshed successfully
[[21:44:28]] [INFO] VkUKQbf1Qt=running
[[21:44:28]] [INFO] Executing action 22/51: Tap on Text: "CITY"
[[21:44:27]] [SUCCESS] Screenshot refreshed
[[21:44:27]] [INFO] Refreshing screenshot...
[[21:44:27]] [INFO] pldheRUBVi=pass
[[21:44:24]] [SUCCESS] Screenshot refreshed successfully
[[21:44:24]] [SUCCESS] Screenshot refreshed successfully
[[21:44:24]] [INFO] pldheRUBVi=running
[[21:44:24]] [INFO] Executing action 21/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:44:24]] [SUCCESS] Screenshot refreshed
[[21:44:24]] [INFO] Refreshing screenshot...
[[21:44:24]] [INFO] kbdEPCPYod=pass
[[21:44:13]] [SUCCESS] Screenshot refreshed successfully
[[21:44:13]] [SUCCESS] Screenshot refreshed successfully
[[21:44:13]] [INFO] kbdEPCPYod=running
[[21:44:13]] [INFO] Executing action 20/51: textClear action
[[21:44:12]] [SUCCESS] Screenshot refreshed
[[21:44:12]] [INFO] Refreshing screenshot...
[[21:44:12]] [INFO] pldheRUBVi=pass
[[21:44:09]] [SUCCESS] Screenshot refreshed successfully
[[21:44:09]] [SUCCESS] Screenshot refreshed successfully
[[21:44:09]] [INFO] pldheRUBVi=running
[[21:44:09]] [INFO] Executing action 19/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:44:08]] [SUCCESS] Screenshot refreshed
[[21:44:08]] [INFO] Refreshing screenshot...
[[21:44:08]] [INFO] 0FggZQe6oU=pass
[[21:44:06]] [SUCCESS] Screenshot refreshed successfully
[[21:44:06]] [SUCCESS] Screenshot refreshed successfully
[[21:44:06]] [INFO] 0FggZQe6oU=running
[[21:44:06]] [INFO] Executing action 18/51: Wait till xpath=//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:44:06]] [SUCCESS] Screenshot refreshed
[[21:44:06]] [INFO] Refreshing screenshot...
[[21:44:06]] [INFO] VkUKQbf1Qt=pass
[[21:43:20]] [SUCCESS] Screenshot refreshed successfully
[[21:43:20]] [SUCCESS] Screenshot refreshed successfully
[[21:43:20]] [INFO] VkUKQbf1Qt=running
[[21:43:20]] [INFO] Executing action 17/51: Tap on Text: "Edit"
[[21:43:19]] [SUCCESS] Screenshot refreshed
[[21:43:19]] [INFO] Refreshing screenshot...
[[21:43:19]] [INFO] BQ7Cxm53HQ=pass
[[21:43:14]] [SUCCESS] Screenshot refreshed successfully
[[21:43:14]] [SUCCESS] Screenshot refreshed successfully
[[21:43:14]] [INFO] BQ7Cxm53HQ=running
[[21:43:14]] [INFO] Executing action 16/51: Wait till text appears: "UNO"
[[21:43:13]] [SUCCESS] Screenshot refreshed
[[21:43:13]] [INFO] Refreshing screenshot...
[[21:43:13]] [INFO] IupxLP2Jsr=pass
[[21:43:12]] [SUCCESS] Screenshot refreshed successfully
[[21:43:12]] [SUCCESS] Screenshot refreshed successfully
[[21:43:11]] [INFO] IupxLP2Jsr=running
[[21:43:11]] [INFO] Executing action 15/51: Input text: "P_6225544"
[[21:43:11]] [SUCCESS] Screenshot refreshed
[[21:43:11]] [INFO] Refreshing screenshot...
[[21:43:11]] [INFO] 70iOOakiG7=pass
[[21:43:06]] [SUCCESS] Screenshot refreshed successfully
[[21:43:06]] [SUCCESS] Screenshot refreshed successfully
[[21:43:02]] [INFO] 70iOOakiG7=running
[[21:43:02]] [INFO] Executing action 14/51: Tap on Text: "Find"
[[21:43:02]] [SUCCESS] Screenshot refreshed
[[21:43:02]] [INFO] Refreshing screenshot...
[[21:43:02]] [INFO] Xqj9EIVEfg=pass
[[21:42:27]] [SUCCESS] Screenshot refreshed successfully
[[21:42:27]] [SUCCESS] Screenshot refreshed successfully
[[21:42:26]] [INFO] Xqj9EIVEfg=running
[[21:42:26]] [INFO] Executing action 13/51: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[21:42:26]] [SUCCESS] Screenshot refreshed
[[21:42:26]] [INFO] Refreshing screenshot...
[[21:42:26]] [INFO] E2jpN7BioW=pass
[[21:42:23]] [SUCCESS] Screenshot refreshed successfully
[[21:42:23]] [SUCCESS] Screenshot refreshed successfully
[[21:42:23]] [INFO] E2jpN7BioW=running
[[21:42:23]] [INFO] Executing action 12/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:42:22]] [SUCCESS] Screenshot refreshed
[[21:42:22]] [INFO] Refreshing screenshot...
[[21:42:22]] [INFO] kDnmoQJG4o=pass
[[21:42:12]] [SUCCESS] Screenshot refreshed successfully
[[21:42:12]] [SUCCESS] Screenshot refreshed successfully
[[21:42:12]] [INFO] kDnmoQJG4o=running
[[21:42:12]] [INFO] Executing action 11/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:42:11]] [SUCCESS] Screenshot refreshed
[[21:42:11]] [INFO] Refreshing screenshot...
[[21:42:11]] [INFO] mw9GQ4mzRE=pass
[[21:42:08]] [SUCCESS] Screenshot refreshed successfully
[[21:42:08]] [SUCCESS] Screenshot refreshed successfully
[[21:42:05]] [INFO] mw9GQ4mzRE=running
[[21:42:05]] [INFO] Executing action 10/51: Tap on Text: "ADELAIDE"
[[21:42:05]] [SUCCESS] Screenshot refreshed
[[21:42:05]] [INFO] Refreshing screenshot...
[[21:42:05]] [INFO] pldheRUBVi=pass
[[21:42:01]] [SUCCESS] Screenshot refreshed successfully
[[21:42:01]] [SUCCESS] Screenshot refreshed successfully
[[21:42:00]] [INFO] pldheRUBVi=running
[[21:42:00]] [INFO] Executing action 9/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:42:00]] [SUCCESS] Screenshot refreshed
[[21:42:00]] [INFO] Refreshing screenshot...
[[21:42:00]] [INFO] kbdEPCPYod=pass
[[21:41:57]] [SUCCESS] Screenshot refreshed successfully
[[21:41:57]] [SUCCESS] Screenshot refreshed successfully
[[21:41:56]] [INFO] kbdEPCPYod=running
[[21:41:56]] [INFO] Executing action 8/51: textClear action
[[21:41:56]] [SUCCESS] Screenshot refreshed
[[21:41:56]] [INFO] Refreshing screenshot...
[[21:41:56]] [INFO] pldheRUBVi=pass
[[21:41:53]] [SUCCESS] Screenshot refreshed successfully
[[21:41:53]] [SUCCESS] Screenshot refreshed successfully
[[21:41:52]] [INFO] pldheRUBVi=running
[[21:41:52]] [INFO] Executing action 7/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:41:52]] [SUCCESS] Screenshot refreshed
[[21:41:52]] [INFO] Refreshing screenshot...
[[21:41:52]] [INFO] QMXBlswP6H=pass
[[21:41:46]] [SUCCESS] Screenshot refreshed successfully
[[21:41:46]] [SUCCESS] Screenshot refreshed successfully
[[21:41:45]] [INFO] QMXBlswP6H=running
[[21:41:45]] [INFO] Executing action 6/51: Tap on Text: "Edit"
[[21:41:45]] [SUCCESS] Screenshot refreshed
[[21:41:45]] [INFO] Refreshing screenshot...
[[21:41:45]] [INFO] RLz6vQo3ag=pass
[[21:41:41]] [SUCCESS] Screenshot refreshed successfully
[[21:41:41]] [SUCCESS] Screenshot refreshed successfully
[[21:41:21]] [INFO] RLz6vQo3ag=running
[[21:41:21]] [INFO] Executing action 5/51: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[21:41:21]] [SUCCESS] Screenshot refreshed
[[21:41:21]] [INFO] Refreshing screenshot...
[[21:41:20]] [SUCCESS] Screenshot refreshed
[[21:41:20]] [INFO] Refreshing screenshot...
[[21:41:19]] [SUCCESS] Screenshot refreshed successfully
[[21:41:19]] [SUCCESS] Screenshot refreshed successfully
[[21:41:18]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[21:41:18]] [SUCCESS] Screenshot refreshed
[[21:41:18]] [INFO] Refreshing screenshot...
[[21:41:15]] [SUCCESS] Screenshot refreshed successfully
[[21:41:15]] [SUCCESS] Screenshot refreshed successfully
[[21:41:15]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[21:41:14]] [SUCCESS] Screenshot refreshed
[[21:41:14]] [INFO] Refreshing screenshot...
[[21:41:12]] [SUCCESS] Screenshot refreshed successfully
[[21:41:12]] [SUCCESS] Screenshot refreshed successfully
[[21:41:12]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[21:41:11]] [SUCCESS] Screenshot refreshed
[[21:41:11]] [INFO] Refreshing screenshot...
[[21:41:09]] [SUCCESS] Screenshot refreshed successfully
[[21:41:09]] [SUCCESS] Screenshot refreshed successfully
[[21:41:09]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[21:41:08]] [SUCCESS] Screenshot refreshed
[[21:41:08]] [INFO] Refreshing screenshot...
[[21:41:06]] [SUCCESS] Screenshot refreshed successfully
[[21:41:06]] [SUCCESS] Screenshot refreshed successfully
[[21:41:06]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[21:41:06]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[21:41:06]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[21:41:06]] [INFO] xz8njynjpZ=running
[[21:41:06]] [INFO] Executing action 4/51: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[21:41:06]] [SUCCESS] Screenshot refreshed
[[21:41:06]] [INFO] Refreshing screenshot...
[[21:41:06]] [INFO] J9loj6Zl5K=pass
[[21:41:04]] [SUCCESS] Screenshot refreshed successfully
[[21:41:04]] [SUCCESS] Screenshot refreshed successfully
[[21:41:03]] [INFO] J9loj6Zl5K=running
[[21:41:03]] [INFO] Executing action 3/51: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[21:41:03]] [SUCCESS] Screenshot refreshed
[[21:41:03]] [INFO] Refreshing screenshot...
[[21:41:03]] [INFO] Y8vz7AJD1i=pass
[[21:40:59]] [SUCCESS] Screenshot refreshed successfully
[[21:40:59]] [SUCCESS] Screenshot refreshed successfully
[[21:40:59]] [INFO] Y8vz7AJD1i=running
[[21:40:59]] [INFO] Executing action 2/51: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[21:40:58]] [SUCCESS] Screenshot refreshed
[[21:40:58]] [INFO] Refreshing screenshot...
[[21:40:58]] [INFO] H9fy9qcFbZ=pass
[[21:40:54]] [INFO] H9fy9qcFbZ=running
[[21:40:54]] [INFO] Executing action 1/51: Launch app: au.com.kmart
[[21:40:54]] [INFO] ExecutionManager: Starting execution of 51 actions...
[[21:40:54]] [SUCCESS] Cleared 1 screenshots from database
[[21:40:54]] [INFO] Clearing screenshots from database before execution...
[[21:40:54]] [SUCCESS] All screenshots deleted successfully
[[21:40:54]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:40:54]] [INFO] Skipping report initialization - single test case execution
[[21:40:53]] [SUCCESS] All screenshots deleted successfully
[[21:40:53]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 51 actions
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: swipe
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: exists
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: swipe
[[21:40:53]] [SUCCESS] Added action: androidFunctions
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: textClear
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: waitTill
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: swipe
[[21:40:53]] [SUCCESS] Added action: exists
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: waitTill
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: textClear
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: waitTill
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: exists
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: waitTill
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: textClear
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: waitTill
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: waitTill
[[21:40:53]] [SUCCESS] Added action: text
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: ifElseSteps
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: waitTill
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: textClear
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: tapOnText
[[21:40:53]] [SUCCESS] Added action: waitTill
[[21:40:53]] [SUCCESS] Added action: multiStep
[[21:40:53]] [SUCCESS] Added action: waitTill
[[21:40:53]] [SUCCESS] Added action: tap
[[21:40:53]] [SUCCESS] Added action: launchApp
[[21:40:53]] [INFO] All actions cleared
[[21:40:53]] [INFO] Cleaning up screenshots...
[[21:40:34]] [SUCCESS] Screenshot refreshed successfully
[[21:40:33]] [SUCCESS] Screenshot refreshed
[[21:40:33]] [INFO] Refreshing screenshot...
[[21:40:32]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[21:40:32]] [INFO] Device info updated: RMX2151
[[21:40:26]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[21:40:23]] [SUCCESS] Found 1 device(s)
[[21:40:23]] [INFO] Refreshing device list...
