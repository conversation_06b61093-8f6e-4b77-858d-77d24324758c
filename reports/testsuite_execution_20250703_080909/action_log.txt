Action Log - 2025-07-03 09:53:13
================================================================================

[[09:53:13]] [INFO] Generating execution report...
[[09:53:13]] [SUCCESS] All tests passed successfully!
[[09:53:13]] [SUCCESS] Screenshot refreshed
[[09:53:13]] [INFO] Refreshing screenshot...
[[09:53:13]] [INFO] xyHVihJMBi=pass
[[09:53:09]] [SUCCESS] Screenshot refreshed successfully
[[09:53:09]] [SUCCESS] Screenshot refreshed successfully
[[09:53:09]] [INFO] xyHVihJMBi=running
[[09:53:09]] [INFO] Executing action 50/50: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[09:53:09]] [SUCCESS] Screenshot refreshed
[[09:53:09]] [INFO] Refreshing screenshot...
[[09:53:09]] [INFO] mWeLQtXiL6=pass
[[09:53:04]] [SUCCESS] Screenshot refreshed successfully
[[09:53:04]] [SUCCESS] Screenshot refreshed successfully
[[09:53:03]] [INFO] mWeLQtXiL6=running
[[09:53:03]] [INFO] Executing action 49/50: Swipe from (50%, 70%) to (50%, 30%)
[[09:53:03]] [SUCCESS] Screenshot refreshed
[[09:53:03]] [INFO] Refreshing screenshot...
[[09:53:03]] [INFO] rkwVoJGZG4=pass
[[09:52:45]] [SUCCESS] Screenshot refreshed successfully
[[09:52:45]] [SUCCESS] Screenshot refreshed successfully
[[09:52:44]] [INFO] rkwVoJGZG4=running
[[09:52:44]] [INFO] Executing action 48/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[09:52:44]] [SUCCESS] Screenshot refreshed
[[09:52:44]] [INFO] Refreshing screenshot...
[[09:52:44]] [INFO] 0f2FSZYjWq=pass
[[09:52:38]] [SUCCESS] Screenshot refreshed successfully
[[09:52:38]] [SUCCESS] Screenshot refreshed successfully
[[09:52:37]] [INFO] 0f2FSZYjWq=running
[[09:52:37]] [INFO] Executing action 47/50: Check if element with text="3000" exists
[[09:52:37]] [SUCCESS] Screenshot refreshed
[[09:52:37]] [INFO] Refreshing screenshot...
[[09:52:37]] [INFO] Tebej51pT2=pass
[[09:52:35]] [SUCCESS] Screenshot refreshed successfully
[[09:52:35]] [SUCCESS] Screenshot refreshed successfully
[[09:52:34]] [INFO] Tebej51pT2=running
[[09:52:34]] [INFO] Executing action 46/50: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[09:52:34]] [SUCCESS] Screenshot refreshed
[[09:52:34]] [INFO] Refreshing screenshot...
[[09:52:34]] [INFO] JrPVGdts3J=pass
[[09:52:18]] [SUCCESS] Screenshot refreshed successfully
[[09:52:18]] [SUCCESS] Screenshot refreshed successfully
[[09:52:18]] [INFO] JrPVGdts3J=running
[[09:52:18]] [INFO] Executing action 45/50: Tap on image: bag-remove-btn-android.png
[[09:52:17]] [SUCCESS] Screenshot refreshed
[[09:52:17]] [INFO] Refreshing screenshot...
[[09:52:17]] [INFO] s8h8VDUIOC=pass
[[09:52:15]] [SUCCESS] Screenshot refreshed successfully
[[09:52:15]] [SUCCESS] Screenshot refreshed successfully
[[09:52:14]] [INFO] s8h8VDUIOC=running
[[09:52:14]] [INFO] Executing action 44/50: Swipe from (50%, 70%) to (50%, 30%)
[[09:52:14]] [SUCCESS] Screenshot refreshed
[[09:52:14]] [INFO] Refreshing screenshot...
[[09:52:14]] [INFO] GYK47u1y3A=pass
[[09:52:11]] [SUCCESS] Screenshot refreshed successfully
[[09:52:11]] [SUCCESS] Screenshot refreshed successfully
[[09:52:11]] [INFO] GYK47u1y3A=running
[[09:52:11]] [INFO] Executing action 43/50: Android Function: send_key_event - Key Event: TAB
[[09:52:11]] [SUCCESS] Screenshot refreshed
[[09:52:11]] [INFO] Refreshing screenshot...
[[09:52:11]] [INFO] ZWpYNcpbFA=pass
[[09:52:07]] [SUCCESS] Screenshot refreshed successfully
[[09:52:07]] [SUCCESS] Screenshot refreshed successfully
[[09:52:07]] [INFO] ZWpYNcpbFA=running
[[09:52:07]] [INFO] Executing action 42/50: Tap on Text: "VIC"
[[09:52:06]] [SUCCESS] Screenshot refreshed
[[09:52:06]] [INFO] Refreshing screenshot...
[[09:52:06]] [INFO] QpBLC6BStn=pass
[[09:52:04]] [SUCCESS] Screenshot refreshed successfully
[[09:52:04]] [SUCCESS] Screenshot refreshed successfully
[[09:52:04]] [INFO] QpBLC6BStn=running
[[09:52:04]] [INFO] Executing action 41/50: textClear action
[[09:52:03]] [SUCCESS] Screenshot refreshed
[[09:52:03]] [INFO] Refreshing screenshot...
[[09:52:03]] [INFO] G4A3KBlXHq=pass
[[09:51:22]] [SUCCESS] Screenshot refreshed successfully
[[09:51:22]] [SUCCESS] Screenshot refreshed successfully
[[09:51:22]] [INFO] G4A3KBlXHq=running
[[09:51:22]] [INFO] Executing action 40/50: Tap on Text: "Nearby"
[[09:51:21]] [SUCCESS] Screenshot refreshed
[[09:51:21]] [INFO] Refreshing screenshot...
[[09:51:21]] [INFO] 3gJsiap2Ds=pass
[[09:51:18]] [SUCCESS] Screenshot refreshed successfully
[[09:51:18]] [SUCCESS] Screenshot refreshed successfully
[[09:51:17]] [INFO] 3gJsiap2Ds=running
[[09:51:17]] [INFO] Executing action 39/50: Tap on Text: "Collect"
[[09:51:17]] [SUCCESS] Screenshot refreshed
[[09:51:17]] [INFO] Refreshing screenshot...
[[09:51:17]] [INFO] qofJDqXBME=pass
[[09:51:11]] [SUCCESS] Screenshot refreshed successfully
[[09:51:11]] [SUCCESS] Screenshot refreshed successfully
[[09:51:11]] [INFO] qofJDqXBME=running
[[09:51:11]] [INFO] Executing action 38/50: Wait till text appears: "Delivery"
[[09:51:10]] [SUCCESS] Screenshot refreshed
[[09:51:10]] [INFO] Refreshing screenshot...
[[09:51:10]] [INFO] rkwVoJGZG4=pass
[[09:51:08]] [SUCCESS] Screenshot refreshed successfully
[[09:51:08]] [SUCCESS] Screenshot refreshed successfully
[[09:51:07]] [INFO] rkwVoJGZG4=running
[[09:51:07]] [INFO] Executing action 37/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[09:51:07]] [SUCCESS] Screenshot refreshed
[[09:51:07]] [INFO] Refreshing screenshot...
[[09:51:07]] [INFO] 94ikwhIEE2=pass
[[09:51:03]] [SUCCESS] Screenshot refreshed successfully
[[09:51:03]] [SUCCESS] Screenshot refreshed successfully
[[09:50:20]] [INFO] 94ikwhIEE2=running
[[09:50:20]] [INFO] Executing action 36/50: Tap on Text: "bag"
[[09:50:19]] [SUCCESS] Screenshot refreshed
[[09:50:19]] [INFO] Refreshing screenshot...
[[09:50:19]] [INFO] DfwaiVZ8Z9=pass
[[09:50:16]] [SUCCESS] Screenshot refreshed successfully
[[09:50:16]] [SUCCESS] Screenshot refreshed successfully
[[09:50:16]] [INFO] DfwaiVZ8Z9=running
[[09:50:16]] [INFO] Executing action 35/50: Swipe from (50%, 70%) to (50%, 50%)
[[09:50:15]] [SUCCESS] Screenshot refreshed
[[09:50:15]] [INFO] Refreshing screenshot...
[[09:50:15]] [INFO] eRCmRhc3re=pass
[[09:50:12]] [SUCCESS] Screenshot refreshed successfully
[[09:50:12]] [SUCCESS] Screenshot refreshed successfully
[[09:50:12]] [INFO] eRCmRhc3re=running
[[09:50:12]] [INFO] Executing action 34/50: Check if element with text="Broadway" exists
[[09:50:12]] [SUCCESS] Screenshot refreshed
[[09:50:12]] [INFO] Refreshing screenshot...
[[09:50:12]] [INFO] E2jpN7BioW=pass
[[09:50:09]] [SUCCESS] Screenshot refreshed successfully
[[09:50:09]] [SUCCESS] Screenshot refreshed successfully
[[09:50:09]] [INFO] E2jpN7BioW=running
[[09:50:09]] [INFO] Executing action 33/50: Tap on element with accessibility_id: btnSaveOrContinue
[[09:50:09]] [SUCCESS] Screenshot refreshed
[[09:50:09]] [INFO] Refreshing screenshot...
[[09:50:09]] [INFO] kDnmoQJG4o=pass
[[09:50:07]] [SUCCESS] Screenshot refreshed successfully
[[09:50:07]] [SUCCESS] Screenshot refreshed successfully
[[09:50:07]] [INFO] kDnmoQJG4o=running
[[09:50:07]] [INFO] Executing action 32/50: Wait till accessibility_id=btnSaveOrContinue
[[09:50:06]] [SUCCESS] Screenshot refreshed
[[09:50:06]] [INFO] Refreshing screenshot...
[[09:50:06]] [INFO] H0ODFz7sWJ=pass
[[09:50:02]] [SUCCESS] Screenshot refreshed successfully
[[09:50:02]] [SUCCESS] Screenshot refreshed successfully
[[09:49:18]] [INFO] H0ODFz7sWJ=running
[[09:49:18]] [INFO] Executing action 31/50: Tap on Text: "2000"
[[09:49:18]] [SUCCESS] Screenshot refreshed
[[09:49:18]] [INFO] Refreshing screenshot...
[[09:49:18]] [INFO] pldheRUBVi=pass
[[09:49:15]] [SUCCESS] Screenshot refreshed successfully
[[09:49:15]] [SUCCESS] Screenshot refreshed successfully
[[09:49:14]] [INFO] pldheRUBVi=running
[[09:49:14]] [INFO] Executing action 30/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:49:14]] [SUCCESS] Screenshot refreshed
[[09:49:14]] [INFO] Refreshing screenshot...
[[09:49:14]] [INFO] uZHvvAzVfx=pass
[[09:49:12]] [SUCCESS] Screenshot refreshed successfully
[[09:49:12]] [SUCCESS] Screenshot refreshed successfully
[[09:49:11]] [INFO] uZHvvAzVfx=running
[[09:49:11]] [INFO] Executing action 29/50: textClear action
[[09:49:10]] [SUCCESS] Screenshot refreshed
[[09:49:10]] [INFO] Refreshing screenshot...
[[09:49:10]] [INFO] WmNWcsWVHv=pass
[[09:49:06]] [SUCCESS] Screenshot refreshed successfully
[[09:49:06]] [SUCCESS] Screenshot refreshed successfully
[[09:49:05]] [INFO] WmNWcsWVHv=running
[[09:49:05]] [INFO] Executing action 28/50: Tap on Text: "4000"
[[09:49:05]] [SUCCESS] Screenshot refreshed
[[09:49:05]] [INFO] Refreshing screenshot...
[[09:49:05]] [INFO] lnjoz8hHUU=pass
[[09:48:24]] [SUCCESS] Screenshot refreshed successfully
[[09:48:24]] [SUCCESS] Screenshot refreshed successfully
[[09:48:24]] [INFO] lnjoz8hHUU=running
[[09:48:24]] [INFO] Executing action 27/50: Tap on Text: "Edit"
[[09:48:23]] [SUCCESS] Screenshot refreshed
[[09:48:23]] [INFO] Refreshing screenshot...
[[09:48:23]] [INFO] BQ7Cxm53HQ=pass
[[09:48:21]] [SUCCESS] Screenshot refreshed successfully
[[09:48:21]] [SUCCESS] Screenshot refreshed successfully
[[09:48:21]] [INFO] BQ7Cxm53HQ=running
[[09:48:21]] [INFO] Executing action 26/50: Wait till text appears: "UNO"
[[09:48:21]] [SUCCESS] Screenshot refreshed
[[09:48:21]] [INFO] Refreshing screenshot...
[[09:48:21]] [INFO] VkUKQbf1Qt=pass
[[09:48:16]] [SUCCESS] Screenshot refreshed successfully
[[09:48:16]] [SUCCESS] Screenshot refreshed successfully
[[09:48:15]] [INFO] VkUKQbf1Qt=running
[[09:48:15]] [INFO] Executing action 25/50: Tap on Text: "UNO"
[[09:48:15]] [SUCCESS] Screenshot refreshed
[[09:48:15]] [INFO] Refreshing screenshot...
[[09:48:15]] [INFO] 73NABkfWyY=pass
[[09:48:10]] [SUCCESS] Screenshot refreshed successfully
[[09:48:10]] [SUCCESS] Screenshot refreshed successfully
[[09:48:09]] [INFO] 73NABkfWyY=running
[[09:48:09]] [INFO] Executing action 24/50: Check if element with text="Toowong" exists
[[09:48:09]] [SUCCESS] Screenshot refreshed
[[09:48:09]] [INFO] Refreshing screenshot...
[[09:48:09]] [INFO] E2jpN7BioW=pass
[[09:48:06]] [SUCCESS] Screenshot refreshed successfully
[[09:48:06]] [SUCCESS] Screenshot refreshed successfully
[[09:48:06]] [INFO] E2jpN7BioW=running
[[09:48:06]] [INFO] Executing action 23/50: Tap on element with accessibility_id: btnSaveOrContinue
[[09:48:06]] [SUCCESS] Screenshot refreshed
[[09:48:06]] [INFO] Refreshing screenshot...
[[09:48:06]] [INFO] kDnmoQJG4o=pass
[[09:48:04]] [SUCCESS] Screenshot refreshed successfully
[[09:48:04]] [SUCCESS] Screenshot refreshed successfully
[[09:48:04]] [INFO] kDnmoQJG4o=running
[[09:48:04]] [INFO] Executing action 22/50: Wait till accessibility_id=btnSaveOrContinue
[[09:48:03]] [SUCCESS] Screenshot refreshed
[[09:48:03]] [INFO] Refreshing screenshot...
[[09:48:03]] [INFO] VkUKQbf1Qt=pass
[[09:47:25]] [SUCCESS] Screenshot refreshed successfully
[[09:47:25]] [SUCCESS] Screenshot refreshed successfully
[[09:47:25]] [INFO] VkUKQbf1Qt=running
[[09:47:25]] [INFO] Executing action 21/50: Tap on Text: "CITY"
[[09:47:24]] [SUCCESS] Screenshot refreshed
[[09:47:24]] [INFO] Refreshing screenshot...
[[09:47:24]] [INFO] pldheRUBVi=pass
[[09:47:21]] [SUCCESS] Screenshot refreshed successfully
[[09:47:21]] [SUCCESS] Screenshot refreshed successfully
[[09:47:20]] [INFO] pldheRUBVi=running
[[09:47:20]] [INFO] Executing action 20/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:47:20]] [SUCCESS] Screenshot refreshed
[[09:47:20]] [INFO] Refreshing screenshot...
[[09:47:20]] [INFO] kbdEPCPYod=pass
[[09:47:17]] [SUCCESS] Screenshot refreshed successfully
[[09:47:17]] [SUCCESS] Screenshot refreshed successfully
[[09:47:16]] [INFO] kbdEPCPYod=running
[[09:47:16]] [INFO] Executing action 19/50: textClear action
[[09:47:16]] [SUCCESS] Screenshot refreshed
[[09:47:16]] [INFO] Refreshing screenshot...
[[09:47:16]] [INFO] pldheRUBVi=pass
[[09:47:12]] [SUCCESS] Screenshot refreshed successfully
[[09:47:12]] [SUCCESS] Screenshot refreshed successfully
[[09:47:12]] [INFO] pldheRUBVi=running
[[09:47:12]] [INFO] Executing action 18/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:47:12]] [SUCCESS] Screenshot refreshed
[[09:47:12]] [INFO] Refreshing screenshot...
[[09:47:12]] [INFO] YhLhTn3Wtm=pass
[[09:47:05]] [SUCCESS] Screenshot refreshed successfully
[[09:47:05]] [SUCCESS] Screenshot refreshed successfully
[[09:47:05]] [INFO] YhLhTn3Wtm=running
[[09:47:05]] [INFO] Executing action 17/50: Wait for 5 ms
[[09:47:04]] [SUCCESS] Screenshot refreshed
[[09:47:04]] [INFO] Refreshing screenshot...
[[09:47:04]] [INFO] VkUKQbf1Qt=pass
[[09:46:54]] [INFO] VkUKQbf1Qt=running
[[09:46:54]] [INFO] Executing action 16/50: Tap on Text: "Edit"
[[09:46:54]] [INFO] BQ7Cxm53HQ=fail
[[09:46:54]] [ERROR] Action 15 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[09:46:20]] [SUCCESS] Screenshot refreshed successfully
[[09:46:20]] [SUCCESS] Screenshot refreshed successfully
[[09:46:20]] [INFO] BQ7Cxm53HQ=running
[[09:46:20]] [INFO] Executing action 15/50: Wait till text appears: "UNO"
[[09:46:19]] [SUCCESS] Screenshot refreshed
[[09:46:19]] [INFO] Refreshing screenshot...
[[09:46:19]] [INFO] IupxLP2Jsr=pass
[[09:46:18]] [SUCCESS] Screenshot refreshed successfully
[[09:46:18]] [SUCCESS] Screenshot refreshed successfully
[[09:46:17]] [INFO] IupxLP2Jsr=running
[[09:46:17]] [INFO] Executing action 14/50: Input text: "P_6225544"
[[09:46:17]] [SUCCESS] Screenshot refreshed
[[09:46:17]] [INFO] Refreshing screenshot...
[[09:46:17]] [INFO] 70iOOakiG7=pass
[[09:46:13]] [SUCCESS] Screenshot refreshed successfully
[[09:46:13]] [SUCCESS] Screenshot refreshed successfully
[[09:46:09]] [INFO] 70iOOakiG7=running
[[09:46:09]] [INFO] Executing action 13/50: Tap on Text: "Find"
[[09:46:08]] [SUCCESS] Screenshot refreshed
[[09:46:08]] [INFO] Refreshing screenshot...
[[09:46:08]] [INFO] E2jpN7BioW=pass
[[09:46:06]] [SUCCESS] Screenshot refreshed successfully
[[09:46:06]] [SUCCESS] Screenshot refreshed successfully
[[09:46:05]] [INFO] E2jpN7BioW=running
[[09:46:05]] [INFO] Executing action 12/50: Tap on element with accessibility_id: btnSaveOrContinue
[[09:46:05]] [SUCCESS] Screenshot refreshed
[[09:46:05]] [INFO] Refreshing screenshot...
[[09:46:05]] [INFO] kDnmoQJG4o=pass
[[09:46:03]] [SUCCESS] Screenshot refreshed successfully
[[09:46:03]] [SUCCESS] Screenshot refreshed successfully
[[09:46:03]] [INFO] kDnmoQJG4o=running
[[09:46:03]] [INFO] Executing action 11/50: Wait till accessibility_id=btnSaveOrContinue
[[09:46:02]] [SUCCESS] Screenshot refreshed
[[09:46:02]] [INFO] Refreshing screenshot...
[[09:46:02]] [INFO] mw9GQ4mzRE=pass
[[09:45:51]] [SUCCESS] Screenshot refreshed successfully
[[09:45:51]] [SUCCESS] Screenshot refreshed successfully
[[09:45:50]] [INFO] mw9GQ4mzRE=running
[[09:45:50]] [INFO] Executing action 10/50: Tap on Text: "BC"
[[09:45:50]] [SUCCESS] Screenshot refreshed
[[09:45:50]] [INFO] Refreshing screenshot...
[[09:45:50]] [INFO] pldheRUBVi=pass
[[09:45:47]] [SUCCESS] Screenshot refreshed successfully
[[09:45:47]] [SUCCESS] Screenshot refreshed successfully
[[09:45:47]] [INFO] pldheRUBVi=running
[[09:45:47]] [INFO] Executing action 9/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:45:46]] [SUCCESS] Screenshot refreshed
[[09:45:46]] [INFO] Refreshing screenshot...
[[09:45:46]] [INFO] kbdEPCPYod=pass
[[09:45:43]] [SUCCESS] Screenshot refreshed successfully
[[09:45:43]] [SUCCESS] Screenshot refreshed successfully
[[09:45:42]] [INFO] kbdEPCPYod=running
[[09:45:42]] [INFO] Executing action 8/50: textClear action
[[09:45:42]] [SUCCESS] Screenshot refreshed
[[09:45:42]] [INFO] Refreshing screenshot...
[[09:45:42]] [INFO] pldheRUBVi=pass
[[09:45:39]] [SUCCESS] Screenshot refreshed successfully
[[09:45:39]] [SUCCESS] Screenshot refreshed successfully
[[09:45:39]] [INFO] pldheRUBVi=running
[[09:45:39]] [INFO] Executing action 7/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:45:38]] [SUCCESS] Screenshot refreshed
[[09:45:38]] [INFO] Refreshing screenshot...
[[09:45:38]] [INFO] QMXBlswP6H=pass
[[09:45:20]] [SUCCESS] Screenshot refreshed successfully
[[09:45:20]] [SUCCESS] Screenshot refreshed successfully
[[09:45:20]] [INFO] QMXBlswP6H=running
[[09:45:20]] [INFO] Executing action 6/50: Tap on Text: "Edit"
[[09:45:19]] [SUCCESS] Screenshot refreshed
[[09:45:19]] [INFO] Refreshing screenshot...
[[09:45:19]] [INFO] RLz6vQo3ag=pass
[[09:45:15]] [INFO] RLz6vQo3ag=running
[[09:45:15]] [INFO] Executing action 5/50: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[09:45:15]] [SUCCESS] Screenshot refreshed
[[09:45:15]] [INFO] Refreshing screenshot...
[[09:45:14]] [SUCCESS] Screenshot refreshed
[[09:45:14]] [INFO] Refreshing screenshot...
[[09:45:12]] [SUCCESS] Screenshot refreshed successfully
[[09:45:12]] [SUCCESS] Screenshot refreshed successfully
[[09:45:12]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[09:45:11]] [SUCCESS] Screenshot refreshed
[[09:45:11]] [INFO] Refreshing screenshot...
[[09:45:09]] [SUCCESS] Screenshot refreshed successfully
[[09:45:09]] [SUCCESS] Screenshot refreshed successfully
[[09:45:09]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[09:45:09]] [SUCCESS] Screenshot refreshed
[[09:45:09]] [INFO] Refreshing screenshot...
[[09:45:06]] [SUCCESS] Screenshot refreshed successfully
[[09:45:06]] [SUCCESS] Screenshot refreshed successfully
[[09:45:06]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[09:45:05]] [SUCCESS] Screenshot refreshed
[[09:45:05]] [INFO] Refreshing screenshot...
[[09:45:03]] [SUCCESS] Screenshot refreshed successfully
[[09:45:03]] [SUCCESS] Screenshot refreshed successfully
[[09:45:03]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[09:45:03]] [SUCCESS] Screenshot refreshed
[[09:45:03]] [INFO] Refreshing screenshot...
[[09:44:55]] [SUCCESS] Screenshot refreshed successfully
[[09:44:55]] [SUCCESS] Screenshot refreshed successfully
[[09:44:54]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[09:44:54]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[09:44:54]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[09:44:54]] [INFO] xz8njynjpZ=running
[[09:44:54]] [INFO] Executing action 4/50: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[09:44:54]] [SUCCESS] Screenshot refreshed
[[09:44:54]] [INFO] Refreshing screenshot...
[[09:44:54]] [INFO] J9loj6Zl5K=pass
[[09:44:52]] [SUCCESS] Screenshot refreshed successfully
[[09:44:52]] [SUCCESS] Screenshot refreshed successfully
[[09:44:52]] [INFO] J9loj6Zl5K=running
[[09:44:52]] [INFO] Executing action 3/50: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[09:44:51]] [SUCCESS] Screenshot refreshed
[[09:44:51]] [INFO] Refreshing screenshot...
[[09:44:51]] [INFO] Y8vz7AJD1i=pass
[[09:44:45]] [SUCCESS] Screenshot refreshed successfully
[[09:44:45]] [SUCCESS] Screenshot refreshed successfully
[[09:44:45]] [INFO] Y8vz7AJD1i=running
[[09:44:45]] [INFO] Executing action 2/50: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:44:44]] [SUCCESS] Screenshot refreshed
[[09:44:44]] [INFO] Refreshing screenshot...
[[09:44:44]] [INFO] H9fy9qcFbZ=pass
[[09:44:41]] [INFO] H9fy9qcFbZ=running
[[09:44:41]] [INFO] Executing action 1/50: Launch app: au.com.kmart
[[09:44:41]] [INFO] ExecutionManager: Starting execution of 50 actions...
[[09:44:41]] [SUCCESS] Cleared 1 screenshots from database
[[09:44:41]] [INFO] Clearing screenshots from database before execution...
[[09:44:41]] [SUCCESS] All screenshots deleted successfully
[[09:44:41]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[09:44:41]] [INFO] Skipping report initialization - single test case execution
[[09:44:40]] [SUCCESS] All screenshots deleted successfully
[[09:44:40]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 50 actions
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: swipe
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: exists
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: swipe
[[09:44:40]] [SUCCESS] Added action: androidFunctions
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: textClear
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: waitTill
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: swipe
[[09:44:40]] [SUCCESS] Added action: exists
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: waitTill
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: textClear
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: waitTill
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: exists
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: waitTill
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: textClear
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: wait
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: waitTill
[[09:44:40]] [SUCCESS] Added action: text
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: waitTill
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: textClear
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: tapOnText
[[09:44:40]] [SUCCESS] Added action: waitTill
[[09:44:40]] [SUCCESS] Added action: multiStep
[[09:44:40]] [SUCCESS] Added action: waitTill
[[09:44:40]] [SUCCESS] Added action: tap
[[09:44:40]] [SUCCESS] Added action: launchApp
[[09:44:40]] [INFO] All actions cleared
[[09:44:40]] [INFO] Cleaning up screenshots...
[[09:44:13]] [SUCCESS] Screenshot refreshed successfully
[[09:44:12]] [SUCCESS] Screenshot refreshed
[[09:44:12]] [INFO] Refreshing screenshot...
[[09:44:11]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[09:44:11]] [INFO] Device info updated: RMX2151
[[09:44:06]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[09:44:04]] [SUCCESS] Found 1 device(s)
[[09:44:04]] [INFO] Refreshing device list...
