Action Log - 2025-07-03 16:13:33
================================================================================

[[16:13:33]] [INFO] Generating execution report...
[[16:13:33]] [SUCCESS] All tests passed successfully!
[[16:13:32]] [SUCCESS] Screenshot refreshed
[[16:13:32]] [INFO] Refreshing screenshot...
[[16:13:32]] [INFO] xyHVihJMBi=pass
[[16:13:29]] [SUCCESS] Screenshot refreshed successfully
[[16:13:29]] [SUCCESS] Screenshot refreshed successfully
[[16:13:20]] [INFO] xyHVihJMBi=running
[[16:13:20]] [INFO] Executing action 51/51: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:13:20]] [SUCCESS] Screenshot refreshed
[[16:13:20]] [INFO] Refreshing screenshot...
[[16:13:20]] [INFO] mWeLQtXiL6=pass
[[16:13:15]] [SUCCESS] Screenshot refreshed successfully
[[16:13:15]] [SUCCESS] Screenshot refreshed successfully
[[16:13:14]] [INFO] mWeLQtXiL6=running
[[16:13:14]] [INFO] Executing action 50/51: Swipe from (50%, 70%) to (50%, 30%)
[[16:13:14]] [SUCCESS] Screenshot refreshed
[[16:13:14]] [INFO] Refreshing screenshot...
[[16:13:14]] [INFO] rkwVoJGZG4=pass
[[16:13:12]] [SUCCESS] Screenshot refreshed successfully
[[16:13:12]] [SUCCESS] Screenshot refreshed successfully
[[16:13:12]] [INFO] rkwVoJGZG4=running
[[16:13:12]] [INFO] Executing action 49/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:13:11]] [SUCCESS] Screenshot refreshed
[[16:13:11]] [INFO] Refreshing screenshot...
[[16:13:11]] [INFO] 0f2FSZYjWq=pass
[[16:13:04]] [SUCCESS] Screenshot refreshed successfully
[[16:13:04]] [SUCCESS] Screenshot refreshed successfully
[[16:13:03]] [INFO] 0f2FSZYjWq=running
[[16:13:03]] [INFO] Executing action 48/51: Check if element with text="3000" exists
[[16:13:03]] [SUCCESS] Screenshot refreshed
[[16:13:03]] [INFO] Refreshing screenshot...
[[16:13:03]] [INFO] Tebej51pT2=pass
[[16:13:00]] [SUCCESS] Screenshot refreshed successfully
[[16:13:00]] [SUCCESS] Screenshot refreshed successfully
[[16:13:00]] [INFO] Tebej51pT2=running
[[16:13:00]] [INFO] Executing action 47/51: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[16:12:59]] [SUCCESS] Screenshot refreshed
[[16:12:59]] [INFO] Refreshing screenshot...
[[16:12:59]] [INFO] JrPVGdts3J=pass
[[16:12:43]] [SUCCESS] Screenshot refreshed successfully
[[16:12:43]] [SUCCESS] Screenshot refreshed successfully
[[16:12:43]] [INFO] JrPVGdts3J=running
[[16:12:43]] [INFO] Executing action 46/51: Tap on image: bag-remove-btn-android.png
[[16:12:42]] [SUCCESS] Screenshot refreshed
[[16:12:42]] [INFO] Refreshing screenshot...
[[16:12:42]] [INFO] s8h8VDUIOC=pass
[[16:12:39]] [SUCCESS] Screenshot refreshed successfully
[[16:12:39]] [SUCCESS] Screenshot refreshed successfully
[[16:12:39]] [INFO] s8h8VDUIOC=running
[[16:12:39]] [INFO] Executing action 45/51: Swipe from (50%, 70%) to (50%, 30%)
[[16:12:38]] [SUCCESS] Screenshot refreshed
[[16:12:38]] [INFO] Refreshing screenshot...
[[16:12:38]] [INFO] GYK47u1y3A=pass
[[16:12:36]] [SUCCESS] Screenshot refreshed successfully
[[16:12:36]] [SUCCESS] Screenshot refreshed successfully
[[16:12:36]] [INFO] GYK47u1y3A=running
[[16:12:36]] [INFO] Executing action 44/51: Android Function: send_key_event - Key Event: TAB
[[16:12:35]] [SUCCESS] Screenshot refreshed
[[16:12:35]] [INFO] Refreshing screenshot...
[[16:12:35]] [INFO] ZWpYNcpbFA=pass
[[16:12:32]] [SUCCESS] Screenshot refreshed successfully
[[16:12:32]] [SUCCESS] Screenshot refreshed successfully
[[16:12:31]] [INFO] ZWpYNcpbFA=running
[[16:12:31]] [INFO] Executing action 43/51: Tap on Text: "VIC"
[[16:12:31]] [SUCCESS] Screenshot refreshed
[[16:12:31]] [INFO] Refreshing screenshot...
[[16:12:31]] [INFO] QpBLC6BStn=pass
[[16:12:02]] [SUCCESS] Screenshot refreshed successfully
[[16:12:02]] [SUCCESS] Screenshot refreshed successfully
[[16:12:02]] [INFO] QpBLC6BStn=running
[[16:12:02]] [INFO] Executing action 42/51: textClear action
[[16:12:01]] [SUCCESS] Screenshot refreshed
[[16:12:01]] [INFO] Refreshing screenshot...
[[16:12:01]] [INFO] G4A3KBlXHq=pass
[[16:11:58]] [SUCCESS] Screenshot refreshed successfully
[[16:11:58]] [SUCCESS] Screenshot refreshed successfully
[[16:11:57]] [INFO] G4A3KBlXHq=running
[[16:11:57]] [INFO] Executing action 41/51: Tap on Text: "Nearby"
[[16:11:57]] [SUCCESS] Screenshot refreshed
[[16:11:57]] [INFO] Refreshing screenshot...
[[16:11:57]] [INFO] 3gJsiap2Ds=pass
[[16:11:53]] [SUCCESS] Screenshot refreshed successfully
[[16:11:53]] [SUCCESS] Screenshot refreshed successfully
[[16:11:53]] [INFO] 3gJsiap2Ds=running
[[16:11:53]] [INFO] Executing action 40/51: Tap on Text: "Collect"
[[16:11:53]] [SUCCESS] Screenshot refreshed
[[16:11:53]] [INFO] Refreshing screenshot...
[[16:11:53]] [INFO] qofJDqXBME=pass
[[16:11:47]] [SUCCESS] Screenshot refreshed successfully
[[16:11:47]] [SUCCESS] Screenshot refreshed successfully
[[16:11:46]] [INFO] qofJDqXBME=running
[[16:11:46]] [INFO] Executing action 39/51: Wait till text appears: "Delivery"
[[16:11:46]] [SUCCESS] Screenshot refreshed
[[16:11:46]] [INFO] Refreshing screenshot...
[[16:11:46]] [INFO] rkwVoJGZG4=pass
[[16:11:43]] [SUCCESS] Screenshot refreshed successfully
[[16:11:43]] [SUCCESS] Screenshot refreshed successfully
[[16:11:43]] [INFO] rkwVoJGZG4=running
[[16:11:43]] [INFO] Executing action 38/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[16:11:42]] [SUCCESS] Screenshot refreshed
[[16:11:42]] [INFO] Refreshing screenshot...
[[16:11:42]] [INFO] 94ikwhIEE2=pass
[[16:11:38]] [SUCCESS] Screenshot refreshed successfully
[[16:11:38]] [SUCCESS] Screenshot refreshed successfully
[[16:11:34]] [INFO] 94ikwhIEE2=running
[[16:11:34]] [INFO] Executing action 37/51: Tap on Text: "bag"
[[16:11:34]] [SUCCESS] Screenshot refreshed
[[16:11:34]] [INFO] Refreshing screenshot...
[[16:11:34]] [INFO] DfwaiVZ8Z9=pass
[[16:11:31]] [SUCCESS] Screenshot refreshed successfully
[[16:11:31]] [SUCCESS] Screenshot refreshed successfully
[[16:11:30]] [INFO] DfwaiVZ8Z9=running
[[16:11:30]] [INFO] Executing action 36/51: Swipe from (50%, 70%) to (50%, 50%)
[[16:11:30]] [SUCCESS] Screenshot refreshed
[[16:11:30]] [INFO] Refreshing screenshot...
[[16:11:30]] [INFO] eRCmRhc3re=pass
[[16:11:27]] [SUCCESS] Screenshot refreshed successfully
[[16:11:27]] [SUCCESS] Screenshot refreshed successfully
[[16:11:27]] [INFO] eRCmRhc3re=running
[[16:11:27]] [INFO] Executing action 35/51: Check if element with text="Broadway" exists
[[16:11:26]] [SUCCESS] Screenshot refreshed
[[16:11:26]] [INFO] Refreshing screenshot...
[[16:11:26]] [INFO] E2jpN7BioW=pass
[[16:11:23]] [SUCCESS] Screenshot refreshed successfully
[[16:11:23]] [SUCCESS] Screenshot refreshed successfully
[[16:11:23]] [INFO] E2jpN7BioW=running
[[16:11:23]] [INFO] Executing action 34/51: Tap on element with accessibility_id: btnSaveOrContinue
[[16:11:22]] [SUCCESS] Screenshot refreshed
[[16:11:22]] [INFO] Refreshing screenshot...
[[16:11:22]] [INFO] kDnmoQJG4o=pass
[[16:11:20]] [SUCCESS] Screenshot refreshed successfully
[[16:11:20]] [SUCCESS] Screenshot refreshed successfully
[[16:11:20]] [INFO] kDnmoQJG4o=running
[[16:11:20]] [INFO] Executing action 33/51: Wait till accessibility_id=btnSaveOrContinue
[[16:11:19]] [SUCCESS] Screenshot refreshed
[[16:11:19]] [INFO] Refreshing screenshot...
[[16:11:19]] [INFO] H0ODFz7sWJ=pass
[[16:11:14]] [SUCCESS] Screenshot refreshed successfully
[[16:11:14]] [SUCCESS] Screenshot refreshed successfully
[[16:11:13]] [INFO] H0ODFz7sWJ=running
[[16:11:13]] [INFO] Executing action 32/51: Tap on Text: "2000"
[[16:11:13]] [SUCCESS] Screenshot refreshed
[[16:11:13]] [INFO] Refreshing screenshot...
[[16:11:13]] [INFO] pldheRUBVi=pass
[[16:11:10]] [SUCCESS] Screenshot refreshed successfully
[[16:11:10]] [SUCCESS] Screenshot refreshed successfully
[[16:11:10]] [INFO] pldheRUBVi=running
[[16:11:10]] [INFO] Executing action 31/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:11:09]] [SUCCESS] Screenshot refreshed
[[16:11:09]] [INFO] Refreshing screenshot...
[[16:11:09]] [INFO] uZHvvAzVfx=pass
[[16:11:07]] [SUCCESS] Screenshot refreshed successfully
[[16:11:07]] [SUCCESS] Screenshot refreshed successfully
[[16:11:07]] [INFO] uZHvvAzVfx=running
[[16:11:07]] [INFO] Executing action 30/51: textClear action
[[16:11:06]] [SUCCESS] Screenshot refreshed
[[16:11:06]] [INFO] Refreshing screenshot...
[[16:11:06]] [INFO] pldheRUBVi=pass
[[16:11:04]] [SUCCESS] Screenshot refreshed successfully
[[16:11:04]] [SUCCESS] Screenshot refreshed successfully
[[16:11:04]] [INFO] pldheRUBVi=running
[[16:11:04]] [INFO] Executing action 29/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:11:03]] [SUCCESS] Screenshot refreshed
[[16:11:03]] [INFO] Refreshing screenshot...
[[16:11:03]] [INFO] pldheRUBVi=pass
[[16:11:02]] [SUCCESS] Screenshot refreshed successfully
[[16:11:02]] [SUCCESS] Screenshot refreshed successfully
[[16:11:01]] [INFO] pldheRUBVi=running
[[16:11:01]] [INFO] Executing action 28/51: Wait till xpath=//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:11:01]] [SUCCESS] Screenshot refreshed
[[16:11:01]] [INFO] Refreshing screenshot...
[[16:11:01]] [INFO] WmNWcsWVHv=pass
[[16:10:54]] [SUCCESS] Screenshot refreshed successfully
[[16:10:54]] [SUCCESS] Screenshot refreshed successfully
[[16:10:54]] [INFO] WmNWcsWVHv=running
[[16:10:54]] [INFO] Executing action 27/51: Tap on Text: "4000"
[[16:10:53]] [SUCCESS] Screenshot refreshed
[[16:10:53]] [INFO] Refreshing screenshot...
[[16:10:53]] [INFO] lnjoz8hHUU=pass
[[16:10:50]] [SUCCESS] Screenshot refreshed successfully
[[16:10:50]] [SUCCESS] Screenshot refreshed successfully
[[16:10:50]] [INFO] lnjoz8hHUU=running
[[16:10:50]] [INFO] Executing action 26/51: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[16:10:49]] [SUCCESS] Screenshot refreshed
[[16:10:49]] [INFO] Refreshing screenshot...
[[16:10:49]] [INFO] VkUKQbf1Qt=pass
[[16:10:44]] [SUCCESS] Screenshot refreshed successfully
[[16:10:44]] [SUCCESS] Screenshot refreshed successfully
[[16:10:43]] [INFO] VkUKQbf1Qt=running
[[16:10:43]] [INFO] Executing action 25/51: Tap on Text: "UNO"
[[16:10:43]] [SUCCESS] Screenshot refreshed
[[16:10:43]] [INFO] Refreshing screenshot...
[[16:10:43]] [INFO] 73NABkfWyY=pass
[[16:10:42]] [SUCCESS] Screenshot refreshed successfully
[[16:10:42]] [SUCCESS] Screenshot refreshed successfully
[[16:10:39]] [INFO] 73NABkfWyY=running
[[16:10:39]] [INFO] Executing action 24/51: Check if element with text="Toowong" exists
[[16:10:39]] [SUCCESS] Screenshot refreshed
[[16:10:39]] [INFO] Refreshing screenshot...
[[16:10:39]] [INFO] E2jpN7BioW=pass
[[16:10:35]] [SUCCESS] Screenshot refreshed successfully
[[16:10:35]] [SUCCESS] Screenshot refreshed successfully
[[16:10:35]] [INFO] E2jpN7BioW=running
[[16:10:35]] [INFO] Executing action 23/51: Tap on element with accessibility_id: btnSaveOrContinue
[[16:10:35]] [SUCCESS] Screenshot refreshed
[[16:10:35]] [INFO] Refreshing screenshot...
[[16:10:35]] [INFO] kDnmoQJG4o=pass
[[16:10:33]] [SUCCESS] Screenshot refreshed successfully
[[16:10:33]] [SUCCESS] Screenshot refreshed successfully
[[16:10:32]] [INFO] kDnmoQJG4o=running
[[16:10:32]] [INFO] Executing action 22/51: Wait till accessibility_id=btnSaveOrContinue
[[16:10:32]] [SUCCESS] Screenshot refreshed
[[16:10:32]] [INFO] Refreshing screenshot...
[[16:10:32]] [INFO] VkUKQbf1Qt=pass
[[16:10:28]] [SUCCESS] Screenshot refreshed successfully
[[16:10:28]] [SUCCESS] Screenshot refreshed successfully
[[16:10:12]] [INFO] VkUKQbf1Qt=running
[[16:10:12]] [INFO] Executing action 21/51: Tap on Text: "CITY"
[[16:10:11]] [SUCCESS] Screenshot refreshed
[[16:10:11]] [INFO] Refreshing screenshot...
[[16:10:11]] [INFO] pldheRUBVi=pass
[[16:10:08]] [SUCCESS] Screenshot refreshed successfully
[[16:10:08]] [SUCCESS] Screenshot refreshed successfully
[[16:10:08]] [INFO] pldheRUBVi=running
[[16:10:08]] [INFO] Executing action 20/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:10:08]] [SUCCESS] Screenshot refreshed
[[16:10:08]] [INFO] Refreshing screenshot...
[[16:10:08]] [INFO] kbdEPCPYod=pass
[[16:10:05]] [SUCCESS] Screenshot refreshed successfully
[[16:10:05]] [SUCCESS] Screenshot refreshed successfully
[[16:10:04]] [INFO] kbdEPCPYod=running
[[16:10:04]] [INFO] Executing action 19/51: textClear action
[[16:10:04]] [SUCCESS] Screenshot refreshed
[[16:10:04]] [INFO] Refreshing screenshot...
[[16:10:04]] [INFO] pldheRUBVi=pass
[[16:09:41]] [SUCCESS] Screenshot refreshed successfully
[[16:09:41]] [SUCCESS] Screenshot refreshed successfully
[[16:09:41]] [INFO] pldheRUBVi=running
[[16:09:41]] [INFO] Executing action 18/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:09:40]] [SUCCESS] Screenshot refreshed
[[16:09:40]] [INFO] Refreshing screenshot...
[[16:09:40]] [INFO] YhLhTn3Wtm=pass
[[16:09:34]] [SUCCESS] Screenshot refreshed successfully
[[16:09:34]] [SUCCESS] Screenshot refreshed successfully
[[16:09:33]] [INFO] YhLhTn3Wtm=running
[[16:09:33]] [INFO] Executing action 17/51: Wait for 5 ms
[[16:09:33]] [SUCCESS] Screenshot refreshed
[[16:09:33]] [INFO] Refreshing screenshot...
[[16:09:33]] [INFO] VkUKQbf1Qt=pass
[[16:09:27]] [SUCCESS] Screenshot refreshed successfully
[[16:09:27]] [SUCCESS] Screenshot refreshed successfully
[[16:09:27]] [INFO] VkUKQbf1Qt=running
[[16:09:27]] [INFO] Executing action 16/51: Tap on Text: "Edit"
[[16:09:26]] [SUCCESS] Screenshot refreshed
[[16:09:26]] [INFO] Refreshing screenshot...
[[16:09:26]] [INFO] MpdUKUazHa=pass
[[16:09:22]] [SUCCESS] Screenshot refreshed successfully
[[16:09:22]] [SUCCESS] Screenshot refreshed successfully
[[16:09:22]] [INFO] MpdUKUazHa=running
[[16:09:22]] [INFO] Executing action 15/51: Wait till image appears: sort-by-relevance-android.png
[[16:09:22]] [SUCCESS] Screenshot refreshed
[[16:09:22]] [INFO] Refreshing screenshot...
[[16:09:22]] [INFO] IupxLP2Jsr=pass
[[16:09:20]] [SUCCESS] Screenshot refreshed successfully
[[16:09:20]] [SUCCESS] Screenshot refreshed successfully
[[16:09:20]] [INFO] IupxLP2Jsr=running
[[16:09:20]] [INFO] Executing action 14/51: Input text: "P_6225544"
[[16:09:19]] [SUCCESS] Screenshot refreshed
[[16:09:19]] [INFO] Refreshing screenshot...
[[16:09:19]] [INFO] 70iOOakiG7=pass
[[16:09:15]] [SUCCESS] Screenshot refreshed successfully
[[16:09:15]] [SUCCESS] Screenshot refreshed successfully
[[16:09:11]] [INFO] 70iOOakiG7=running
[[16:09:11]] [INFO] Executing action 13/51: Tap on Text: "Find"
[[16:09:11]] [SUCCESS] Screenshot refreshed
[[16:09:11]] [INFO] Refreshing screenshot...
[[16:09:11]] [INFO] E2jpN7BioW=pass
[[16:09:07]] [SUCCESS] Screenshot refreshed successfully
[[16:09:07]] [SUCCESS] Screenshot refreshed successfully
[[16:09:07]] [INFO] E2jpN7BioW=running
[[16:09:07]] [INFO] Executing action 12/51: Tap on element with accessibility_id: btnSaveOrContinue
[[16:09:06]] [SUCCESS] Screenshot refreshed
[[16:09:06]] [INFO] Refreshing screenshot...
[[16:09:06]] [INFO] kDnmoQJG4o=pass
[[16:09:05]] [SUCCESS] Screenshot refreshed successfully
[[16:09:05]] [SUCCESS] Screenshot refreshed successfully
[[16:09:04]] [INFO] kDnmoQJG4o=running
[[16:09:04]] [INFO] Executing action 11/51: Wait till accessibility_id=btnSaveOrContinue
[[16:09:04]] [SUCCESS] Screenshot refreshed
[[16:09:04]] [INFO] Refreshing screenshot...
[[16:09:04]] [INFO] mw9GQ4mzRE=pass
[[16:08:29]] [SUCCESS] Screenshot refreshed successfully
[[16:08:29]] [SUCCESS] Screenshot refreshed successfully
[[16:08:29]] [INFO] mw9GQ4mzRE=running
[[16:08:29]] [INFO] Executing action 10/51: Tap on Text: "BC"
[[16:08:28]] [SUCCESS] Screenshot refreshed
[[16:08:28]] [INFO] Refreshing screenshot...
[[16:08:28]] [INFO] pldheRUBVi=pass
[[16:08:25]] [SUCCESS] Screenshot refreshed successfully
[[16:08:25]] [SUCCESS] Screenshot refreshed successfully
[[16:08:25]] [INFO] pldheRUBVi=running
[[16:08:25]] [INFO] Executing action 9/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:08:24]] [SUCCESS] Screenshot refreshed
[[16:08:24]] [INFO] Refreshing screenshot...
[[16:08:24]] [INFO] kbdEPCPYod=pass
[[16:08:22]] [SUCCESS] Screenshot refreshed successfully
[[16:08:22]] [SUCCESS] Screenshot refreshed successfully
[[16:08:21]] [INFO] kbdEPCPYod=running
[[16:08:21]] [INFO] Executing action 8/51: textClear action
[[16:08:21]] [SUCCESS] Screenshot refreshed
[[16:08:21]] [INFO] Refreshing screenshot...
[[16:08:21]] [INFO] pldheRUBVi=pass
[[16:08:18]] [SUCCESS] Screenshot refreshed successfully
[[16:08:18]] [SUCCESS] Screenshot refreshed successfully
[[16:08:17]] [INFO] pldheRUBVi=running
[[16:08:17]] [INFO] Executing action 7/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:08:17]] [SUCCESS] Screenshot refreshed
[[16:08:17]] [INFO] Refreshing screenshot...
[[16:08:17]] [INFO] QMXBlswP6H=pass
[[16:08:12]] [SUCCESS] Screenshot refreshed successfully
[[16:08:12]] [SUCCESS] Screenshot refreshed successfully
[[16:08:09]] [INFO] QMXBlswP6H=running
[[16:08:09]] [INFO] Executing action 6/51: Tap on Text: "Edit"
[[16:08:08]] [SUCCESS] Screenshot refreshed
[[16:08:08]] [INFO] Refreshing screenshot...
[[16:08:08]] [INFO] RLz6vQo3ag=pass
[[16:08:06]] [SUCCESS] Screenshot refreshed successfully
[[16:08:06]] [SUCCESS] Screenshot refreshed successfully
[[16:08:01]] [INFO] RLz6vQo3ag=running
[[16:08:01]] [INFO] Executing action 5/51: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[16:08:01]] [SUCCESS] Screenshot refreshed
[[16:08:01]] [INFO] Refreshing screenshot...
[[16:08:01]] [SUCCESS] Screenshot refreshed
[[16:08:01]] [INFO] Refreshing screenshot...
[[16:07:59]] [SUCCESS] Screenshot refreshed successfully
[[16:07:59]] [SUCCESS] Screenshot refreshed successfully
[[16:07:58]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[16:07:58]] [SUCCESS] Screenshot refreshed
[[16:07:58]] [INFO] Refreshing screenshot...
[[16:07:56]] [SUCCESS] Screenshot refreshed successfully
[[16:07:56]] [SUCCESS] Screenshot refreshed successfully
[[16:07:56]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:07:55]] [SUCCESS] Screenshot refreshed
[[16:07:55]] [INFO] Refreshing screenshot...
[[16:07:53]] [SUCCESS] Screenshot refreshed successfully
[[16:07:53]] [SUCCESS] Screenshot refreshed successfully
[[16:07:52]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[16:07:52]] [SUCCESS] Screenshot refreshed
[[16:07:52]] [INFO] Refreshing screenshot...
[[16:07:47]] [SUCCESS] Screenshot refreshed successfully
[[16:07:47]] [SUCCESS] Screenshot refreshed successfully
[[16:07:47]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:07:46]] [SUCCESS] Screenshot refreshed
[[16:07:46]] [INFO] Refreshing screenshot...
[[16:07:44]] [SUCCESS] Screenshot refreshed successfully
[[16:07:44]] [SUCCESS] Screenshot refreshed successfully
[[16:07:44]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:07:44]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[16:07:44]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[16:07:44]] [INFO] xz8njynjpZ=running
[[16:07:44]] [INFO] Executing action 4/51: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[16:07:43]] [SUCCESS] Screenshot refreshed
[[16:07:43]] [INFO] Refreshing screenshot...
[[16:07:43]] [INFO] J9loj6Zl5K=pass
[[16:07:42]] [SUCCESS] Screenshot refreshed successfully
[[16:07:42]] [SUCCESS] Screenshot refreshed successfully
[[16:07:41]] [INFO] J9loj6Zl5K=running
[[16:07:41]] [INFO] Executing action 3/51: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:07:41]] [SUCCESS] Screenshot refreshed
[[16:07:41]] [INFO] Refreshing screenshot...
[[16:07:41]] [INFO] Y8vz7AJD1i=pass
[[16:07:38]] [SUCCESS] Screenshot refreshed successfully
[[16:07:38]] [SUCCESS] Screenshot refreshed successfully
[[16:07:37]] [INFO] Y8vz7AJD1i=running
[[16:07:37]] [INFO] Executing action 2/51: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[16:07:37]] [SUCCESS] Screenshot refreshed
[[16:07:37]] [INFO] Refreshing screenshot...
[[16:07:37]] [INFO] H9fy9qcFbZ=pass
[[16:07:33]] [INFO] H9fy9qcFbZ=running
[[16:07:33]] [INFO] Executing action 1/51: Launch app: au.com.kmart
[[16:07:33]] [INFO] ExecutionManager: Starting execution of 51 actions...
[[16:07:33]] [SUCCESS] Cleared 1 screenshots from database
[[16:07:33]] [INFO] Clearing screenshots from database before execution...
[[16:07:33]] [SUCCESS] All screenshots deleted successfully
[[16:07:33]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[16:07:33]] [INFO] Skipping report initialization - single test case execution
[[16:07:26]] [SUCCESS] Test case Postcode Flow_AU_ANDROID saved successfully
[[16:07:25]] [INFO] Saving test case "Postcode Flow_AU_ANDROID"...
[[16:07:23]] [SUCCESS] Updated action #33
[[16:06:55]] [SUCCESS] Screenshot refreshed successfully
[[16:06:55]] [SUCCESS] Screenshot refreshed successfully
[[16:06:54]] [INFO] Skipping report generation - individual test case execution (not from test suite)
[[16:06:54]] [SUCCESS] Action logs saved successfully
[[16:06:54]] [ERROR] Execution failed but report was generated.
[[16:06:54]] [INFO] Saving 834 action log entries to file...
[[16:06:54]] [INFO] Generating execution report...
[[16:06:54]] [SUCCESS] All tests passed successfully!
[[16:06:54]] [SUCCESS] Screenshot refreshed
[[16:06:54]] [INFO] Refreshing screenshot...
[[16:06:54]] [INFO] xyHVihJMBi=pass
[[16:06:51]] [SUCCESS] Screenshot refreshed successfully
[[16:06:51]] [SUCCESS] Screenshot refreshed successfully
[[16:06:50]] [INFO] xyHVihJMBi=running
[[16:06:50]] [INFO] Executing action 51/51: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:06:50]] [SUCCESS] Screenshot refreshed
[[16:06:50]] [INFO] Refreshing screenshot...
[[16:06:50]] [INFO] mWeLQtXiL6=pass
[[16:06:43]] [SUCCESS] Screenshot refreshed successfully
[[16:06:43]] [SUCCESS] Screenshot refreshed successfully
[[16:06:43]] [INFO] mWeLQtXiL6=running
[[16:06:43]] [INFO] Executing action 50/51: Swipe from (50%, 70%) to (50%, 30%)
[[16:06:42]] [SUCCESS] Screenshot refreshed
[[16:06:42]] [INFO] Refreshing screenshot...
[[16:06:42]] [INFO] rkwVoJGZG4=pass
[[16:06:40]] [SUCCESS] Screenshot refreshed successfully
[[16:06:40]] [SUCCESS] Screenshot refreshed successfully
[[16:06:40]] [INFO] rkwVoJGZG4=running
[[16:06:40]] [INFO] Executing action 49/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:06:39]] [SUCCESS] Screenshot refreshed
[[16:06:39]] [INFO] Refreshing screenshot...
[[16:06:39]] [INFO] 0f2FSZYjWq=pass
[[16:06:33]] [SUCCESS] Screenshot refreshed successfully
[[16:06:33]] [SUCCESS] Screenshot refreshed successfully
[[16:06:33]] [INFO] 0f2FSZYjWq=running
[[16:06:33]] [INFO] Executing action 48/51: Check if element with text="3000" exists
[[16:06:32]] [SUCCESS] Screenshot refreshed
[[16:06:32]] [INFO] Refreshing screenshot...
[[16:06:32]] [INFO] Tebej51pT2=pass
[[16:06:30]] [SUCCESS] Screenshot refreshed successfully
[[16:06:30]] [SUCCESS] Screenshot refreshed successfully
[[16:06:30]] [INFO] Tebej51pT2=running
[[16:06:30]] [INFO] Executing action 47/51: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[16:06:29]] [SUCCESS] Screenshot refreshed
[[16:06:29]] [INFO] Refreshing screenshot...
[[16:06:29]] [INFO] JrPVGdts3J=pass
[[16:06:17]] [SUCCESS] Screenshot refreshed successfully
[[16:06:17]] [SUCCESS] Screenshot refreshed successfully
[[16:06:17]] [INFO] JrPVGdts3J=running
[[16:06:17]] [INFO] Executing action 46/51: Tap on image: bag-remove-btn-android.png
[[16:06:16]] [SUCCESS] Screenshot refreshed
[[16:06:16]] [INFO] Refreshing screenshot...
[[16:06:16]] [INFO] s8h8VDUIOC=pass
[[16:06:13]] [SUCCESS] Screenshot refreshed successfully
[[16:06:13]] [SUCCESS] Screenshot refreshed successfully
[[16:06:13]] [INFO] s8h8VDUIOC=running
[[16:06:13]] [INFO] Executing action 45/51: Swipe from (50%, 70%) to (50%, 30%)
[[16:06:12]] [SUCCESS] Screenshot refreshed
[[16:06:12]] [INFO] Refreshing screenshot...
[[16:06:12]] [INFO] GYK47u1y3A=pass
[[16:06:11]] [SUCCESS] Screenshot refreshed successfully
[[16:06:11]] [SUCCESS] Screenshot refreshed successfully
[[16:06:10]] [INFO] GYK47u1y3A=running
[[16:06:10]] [INFO] Executing action 44/51: Android Function: send_key_event - Key Event: TAB
[[16:06:10]] [SUCCESS] Screenshot refreshed
[[16:06:10]] [INFO] Refreshing screenshot...
[[16:06:10]] [INFO] ZWpYNcpbFA=pass
[[16:06:06]] [SUCCESS] Screenshot refreshed successfully
[[16:06:06]] [SUCCESS] Screenshot refreshed successfully
[[16:06:04]] [INFO] ZWpYNcpbFA=running
[[16:06:04]] [INFO] Executing action 43/51: Tap on Text: "VIC"
[[16:06:03]] [SUCCESS] Screenshot refreshed
[[16:06:03]] [INFO] Refreshing screenshot...
[[16:06:03]] [INFO] QpBLC6BStn=pass
[[16:06:01]] [SUCCESS] Screenshot refreshed successfully
[[16:06:01]] [SUCCESS] Screenshot refreshed successfully
[[16:06:00]] [INFO] QpBLC6BStn=running
[[16:06:00]] [INFO] Executing action 42/51: textClear action
[[16:06:00]] [SUCCESS] Screenshot refreshed
[[16:06:00]] [INFO] Refreshing screenshot...
[[16:06:00]] [INFO] G4A3KBlXHq=pass
[[16:05:55]] [SUCCESS] Screenshot refreshed successfully
[[16:05:55]] [SUCCESS] Screenshot refreshed successfully
[[16:05:55]] [INFO] G4A3KBlXHq=running
[[16:05:55]] [INFO] Executing action 41/51: Tap on Text: "Nearby"
[[16:05:55]] [SUCCESS] Screenshot refreshed
[[16:05:55]] [INFO] Refreshing screenshot...
[[16:05:55]] [INFO] 3gJsiap2Ds=pass
[[16:05:50]] [SUCCESS] Screenshot refreshed successfully
[[16:05:50]] [SUCCESS] Screenshot refreshed successfully
[[16:05:49]] [INFO] 3gJsiap2Ds=running
[[16:05:49]] [INFO] Executing action 40/51: Tap on Text: "Collect"
[[16:05:49]] [SUCCESS] Screenshot refreshed
[[16:05:49]] [INFO] Refreshing screenshot...
[[16:05:49]] [INFO] qofJDqXBME=pass
[[16:05:43]] [SUCCESS] Screenshot refreshed successfully
[[16:05:43]] [SUCCESS] Screenshot refreshed successfully
[[16:05:43]] [INFO] qofJDqXBME=running
[[16:05:43]] [INFO] Executing action 39/51: Wait till text appears: "Delivery"
[[16:05:42]] [SUCCESS] Screenshot refreshed
[[16:05:42]] [INFO] Refreshing screenshot...
[[16:05:42]] [INFO] rkwVoJGZG4=pass
[[16:05:40]] [SUCCESS] Screenshot refreshed successfully
[[16:05:40]] [SUCCESS] Screenshot refreshed successfully
[[16:05:39]] [INFO] rkwVoJGZG4=running
[[16:05:39]] [INFO] Executing action 38/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[16:05:39]] [SUCCESS] Screenshot refreshed
[[16:05:39]] [INFO] Refreshing screenshot...
[[16:05:39]] [INFO] 94ikwhIEE2=pass
[[16:05:35]] [SUCCESS] Screenshot refreshed successfully
[[16:05:35]] [SUCCESS] Screenshot refreshed successfully
[[16:05:31]] [INFO] 94ikwhIEE2=running
[[16:05:31]] [INFO] Executing action 37/51: Tap on Text: "bag"
[[16:05:31]] [SUCCESS] Screenshot refreshed
[[16:05:31]] [INFO] Refreshing screenshot...
[[16:05:31]] [INFO] DfwaiVZ8Z9=pass
[[16:05:11]] [SUCCESS] Screenshot refreshed successfully
[[16:05:11]] [SUCCESS] Screenshot refreshed successfully
[[16:05:10]] [INFO] DfwaiVZ8Z9=running
[[16:05:10]] [INFO] Executing action 36/51: Swipe from (50%, 70%) to (50%, 50%)
[[16:05:10]] [SUCCESS] Screenshot refreshed
[[16:05:10]] [INFO] Refreshing screenshot...
[[16:05:10]] [INFO] eRCmRhc3re=pass
[[16:05:07]] [SUCCESS] Screenshot refreshed successfully
[[16:05:07]] [SUCCESS] Screenshot refreshed successfully
[[16:05:07]] [INFO] eRCmRhc3re=running
[[16:05:07]] [INFO] Executing action 35/51: Check if element with text="Broadway" exists
[[16:05:06]] [SUCCESS] Screenshot refreshed
[[16:05:06]] [INFO] Refreshing screenshot...
[[16:05:06]] [INFO] E2jpN7BioW=pass
[[16:05:02]] [INFO] E2jpN7BioW=running
[[16:05:02]] [INFO] Executing action 34/51: Tap on element with accessibility_id: btnSaveOrContinue
[[16:05:02]] [INFO] kDnmoQJG4o=fail
[[16:05:02]] [ERROR] Action 33 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[16:04:31]] [SUCCESS] Screenshot refreshed successfully
[[16:04:31]] [SUCCESS] Screenshot refreshed successfully
[[16:04:30]] [INFO] kDnmoQJG4o=running
[[16:04:30]] [INFO] Executing action 33/51: Wait till accessibility_id=btnSaveOrContinue
[[16:04:30]] [SUCCESS] Screenshot refreshed
[[16:04:30]] [INFO] Refreshing screenshot...
[[16:04:30]] [INFO] H0ODFz7sWJ=pass
[[16:04:26]] [SUCCESS] Screenshot refreshed successfully
[[16:04:26]] [SUCCESS] Screenshot refreshed successfully
[[16:04:25]] [INFO] H0ODFz7sWJ=running
[[16:04:25]] [INFO] Executing action 32/51: Tap on Text: "2000"
[[16:04:25]] [SUCCESS] Screenshot refreshed
[[16:04:25]] [INFO] Refreshing screenshot...
[[16:04:25]] [INFO] pldheRUBVi=pass
[[16:04:22]] [SUCCESS] Screenshot refreshed successfully
[[16:04:22]] [SUCCESS] Screenshot refreshed successfully
[[16:04:22]] [INFO] pldheRUBVi=running
[[16:04:22]] [INFO] Executing action 31/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:04:21]] [SUCCESS] Screenshot refreshed
[[16:04:21]] [INFO] Refreshing screenshot...
[[16:04:21]] [INFO] uZHvvAzVfx=pass
[[16:04:19]] [SUCCESS] Screenshot refreshed successfully
[[16:04:19]] [SUCCESS] Screenshot refreshed successfully
[[16:04:18]] [INFO] uZHvvAzVfx=running
[[16:04:18]] [INFO] Executing action 30/51: textClear action
[[16:04:18]] [SUCCESS] Screenshot refreshed
[[16:04:18]] [INFO] Refreshing screenshot...
[[16:04:18]] [INFO] pldheRUBVi=pass
[[16:04:15]] [SUCCESS] Screenshot refreshed successfully
[[16:04:15]] [SUCCESS] Screenshot refreshed successfully
[[16:04:15]] [INFO] pldheRUBVi=running
[[16:04:15]] [INFO] Executing action 29/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:04:14]] [SUCCESS] Screenshot refreshed
[[16:04:14]] [INFO] Refreshing screenshot...
[[16:04:14]] [INFO] pldheRUBVi=pass
[[16:04:12]] [SUCCESS] Screenshot refreshed successfully
[[16:04:12]] [SUCCESS] Screenshot refreshed successfully
[[16:04:12]] [INFO] pldheRUBVi=running
[[16:04:12]] [INFO] Executing action 28/51: Wait till xpath=//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:04:11]] [SUCCESS] Screenshot refreshed
[[16:04:11]] [INFO] Refreshing screenshot...
[[16:04:11]] [INFO] WmNWcsWVHv=pass
[[16:04:08]] [SUCCESS] Screenshot refreshed successfully
[[16:04:08]] [SUCCESS] Screenshot refreshed successfully
[[16:04:04]] [INFO] WmNWcsWVHv=running
[[16:04:04]] [INFO] Executing action 27/51: Tap on Text: "4000"
[[16:04:04]] [SUCCESS] Screenshot refreshed
[[16:04:04]] [INFO] Refreshing screenshot...
[[16:04:04]] [INFO] lnjoz8hHUU=pass
[[16:03:49]] [SUCCESS] Screenshot refreshed successfully
[[16:03:49]] [SUCCESS] Screenshot refreshed successfully
[[16:03:49]] [INFO] lnjoz8hHUU=running
[[16:03:49]] [INFO] Executing action 26/51: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[16:03:48]] [SUCCESS] Screenshot refreshed
[[16:03:48]] [INFO] Refreshing screenshot...
[[16:03:48]] [INFO] VkUKQbf1Qt=pass
[[16:03:43]] [SUCCESS] Screenshot refreshed successfully
[[16:03:43]] [SUCCESS] Screenshot refreshed successfully
[[16:03:43]] [INFO] VkUKQbf1Qt=running
[[16:03:43]] [INFO] Executing action 25/51: Tap on Text: "UNO"
[[16:03:42]] [SUCCESS] Screenshot refreshed
[[16:03:42]] [INFO] Refreshing screenshot...
[[16:03:42]] [INFO] 73NABkfWyY=pass
[[16:03:36]] [SUCCESS] Screenshot refreshed successfully
[[16:03:36]] [SUCCESS] Screenshot refreshed successfully
[[16:03:35]] [INFO] 73NABkfWyY=running
[[16:03:35]] [INFO] Executing action 24/51: Check if element with text="Toowong" exists
[[16:03:35]] [SUCCESS] Screenshot refreshed
[[16:03:35]] [INFO] Refreshing screenshot...
[[16:03:35]] [INFO] E2jpN7BioW=pass
[[16:03:31]] [SUCCESS] Screenshot refreshed successfully
[[16:03:31]] [SUCCESS] Screenshot refreshed successfully
[[16:03:31]] [INFO] E2jpN7BioW=running
[[16:03:31]] [INFO] Executing action 23/51: Tap on element with accessibility_id: btnSaveOrContinue
[[16:03:30]] [SUCCESS] Screenshot refreshed
[[16:03:30]] [INFO] Refreshing screenshot...
[[16:03:30]] [INFO] kDnmoQJG4o=pass
[[16:03:29]] [SUCCESS] Screenshot refreshed successfully
[[16:03:29]] [SUCCESS] Screenshot refreshed successfully
[[16:03:28]] [INFO] kDnmoQJG4o=running
[[16:03:28]] [INFO] Executing action 22/51: Wait till accessibility_id=btnSaveOrContinue
[[16:03:28]] [SUCCESS] Screenshot refreshed
[[16:03:28]] [INFO] Refreshing screenshot...
[[16:03:28]] [INFO] VkUKQbf1Qt=pass
[[16:03:03]] [SUCCESS] Screenshot refreshed successfully
[[16:03:03]] [SUCCESS] Screenshot refreshed successfully
[[16:03:03]] [INFO] VkUKQbf1Qt=running
[[16:03:03]] [INFO] Executing action 21/51: Tap on Text: "CITY"
[[16:03:02]] [SUCCESS] Screenshot refreshed
[[16:03:02]] [INFO] Refreshing screenshot...
[[16:03:02]] [INFO] pldheRUBVi=pass
[[16:02:37]] [SUCCESS] Screenshot refreshed successfully
[[16:02:37]] [SUCCESS] Screenshot refreshed successfully
[[16:02:36]] [INFO] pldheRUBVi=running
[[16:02:36]] [INFO] Executing action 20/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:02:36]] [SUCCESS] Screenshot refreshed
[[16:02:36]] [INFO] Refreshing screenshot...
[[16:02:36]] [INFO] kbdEPCPYod=pass
[[16:02:33]] [SUCCESS] Screenshot refreshed successfully
[[16:02:33]] [SUCCESS] Screenshot refreshed successfully
[[16:02:32]] [INFO] kbdEPCPYod=running
[[16:02:32]] [INFO] Executing action 19/51: textClear action
[[16:02:32]] [SUCCESS] Screenshot refreshed
[[16:02:32]] [INFO] Refreshing screenshot...
[[16:02:32]] [INFO] pldheRUBVi=pass
[[16:02:13]] [SUCCESS] Screenshot refreshed successfully
[[16:02:13]] [SUCCESS] Screenshot refreshed successfully
[[16:02:12]] [INFO] pldheRUBVi=running
[[16:02:12]] [INFO] Executing action 18/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:02:12]] [SUCCESS] Screenshot refreshed
[[16:02:12]] [INFO] Refreshing screenshot...
[[16:02:12]] [INFO] YhLhTn3Wtm=pass
[[16:02:05]] [SUCCESS] Screenshot refreshed successfully
[[16:02:05]] [SUCCESS] Screenshot refreshed successfully
[[16:02:05]] [INFO] YhLhTn3Wtm=running
[[16:02:05]] [INFO] Executing action 17/51: Wait for 5 ms
[[16:02:05]] [SUCCESS] Screenshot refreshed
[[16:02:05]] [INFO] Refreshing screenshot...
[[16:02:05]] [INFO] VkUKQbf1Qt=pass
[[16:01:27]] [SUCCESS] Screenshot refreshed successfully
[[16:01:27]] [SUCCESS] Screenshot refreshed successfully
[[16:01:26]] [INFO] VkUKQbf1Qt=running
[[16:01:26]] [INFO] Executing action 16/51: Tap on Text: "Edit"
[[16:01:26]] [SUCCESS] Screenshot refreshed
[[16:01:26]] [INFO] Refreshing screenshot...
[[16:01:26]] [INFO] MpdUKUazHa=pass
[[16:01:21]] [SUCCESS] Screenshot refreshed successfully
[[16:01:21]] [SUCCESS] Screenshot refreshed successfully
[[16:01:21]] [INFO] MpdUKUazHa=running
[[16:01:21]] [INFO] Executing action 15/51: Wait till image appears: sort-by-relevance-android.png
[[16:01:21]] [SUCCESS] Screenshot refreshed
[[16:01:21]] [INFO] Refreshing screenshot...
[[16:01:21]] [INFO] IupxLP2Jsr=pass
[[16:01:19]] [SUCCESS] Screenshot refreshed successfully
[[16:01:19]] [SUCCESS] Screenshot refreshed successfully
[[16:01:18]] [INFO] IupxLP2Jsr=running
[[16:01:18]] [INFO] Executing action 14/51: Input text: "P_6225544"
[[16:01:18]] [SUCCESS] Screenshot refreshed
[[16:01:18]] [INFO] Refreshing screenshot...
[[16:01:18]] [INFO] 70iOOakiG7=pass
[[16:01:13]] [SUCCESS] Screenshot refreshed successfully
[[16:01:13]] [SUCCESS] Screenshot refreshed successfully
[[16:01:10]] [INFO] 70iOOakiG7=running
[[16:01:10]] [INFO] Executing action 13/51: Tap on Text: "Find"
[[16:01:09]] [SUCCESS] Screenshot refreshed
[[16:01:09]] [INFO] Refreshing screenshot...
[[16:01:09]] [INFO] E2jpN7BioW=pass
[[16:01:04]] [SUCCESS] Screenshot refreshed successfully
[[16:01:04]] [SUCCESS] Screenshot refreshed successfully
[[16:01:04]] [INFO] E2jpN7BioW=running
[[16:01:04]] [INFO] Executing action 12/51: Tap on element with accessibility_id: btnSaveOrContinue
[[16:01:03]] [SUCCESS] Screenshot refreshed
[[16:01:03]] [INFO] Refreshing screenshot...
[[16:01:03]] [INFO] kDnmoQJG4o=pass
[[16:01:02]] [SUCCESS] Screenshot refreshed successfully
[[16:01:02]] [SUCCESS] Screenshot refreshed successfully
[[16:01:01]] [INFO] kDnmoQJG4o=running
[[16:01:01]] [INFO] Executing action 11/51: Wait till accessibility_id=btnSaveOrContinue
[[16:01:01]] [SUCCESS] Screenshot refreshed
[[16:01:01]] [INFO] Refreshing screenshot...
[[16:01:01]] [INFO] mw9GQ4mzRE=pass
[[16:00:56]] [SUCCESS] Screenshot refreshed successfully
[[16:00:56]] [SUCCESS] Screenshot refreshed successfully
[[16:00:39]] [INFO] mw9GQ4mzRE=running
[[16:00:39]] [INFO] Executing action 10/51: Tap on Text: "BC"
[[16:00:39]] [SUCCESS] Screenshot refreshed
[[16:00:39]] [INFO] Refreshing screenshot...
[[16:00:39]] [INFO] pldheRUBVi=pass
[[16:00:36]] [SUCCESS] Screenshot refreshed successfully
[[16:00:36]] [SUCCESS] Screenshot refreshed successfully
[[16:00:36]] [INFO] pldheRUBVi=running
[[16:00:36]] [INFO] Executing action 9/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:00:35]] [SUCCESS] Screenshot refreshed
[[16:00:35]] [INFO] Refreshing screenshot...
[[16:00:35]] [INFO] kbdEPCPYod=pass
[[16:00:33]] [SUCCESS] Screenshot refreshed successfully
[[16:00:33]] [SUCCESS] Screenshot refreshed successfully
[[16:00:32]] [INFO] kbdEPCPYod=running
[[16:00:32]] [INFO] Executing action 8/51: textClear action
[[16:00:32]] [SUCCESS] Screenshot refreshed
[[16:00:32]] [INFO] Refreshing screenshot...
[[16:00:32]] [INFO] pldheRUBVi=pass
[[16:00:11]] [INFO] pldheRUBVi=running
[[16:00:11]] [INFO] Executing action 7/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:00:11]] [SUCCESS] Screenshot refreshed successfully
[[16:00:11]] [SUCCESS] Screenshot refreshed successfully
[[16:00:10]] [SUCCESS] Screenshot refreshed
[[16:00:10]] [INFO] Refreshing screenshot...
[[16:00:10]] [INFO] QMXBlswP6H=pass
[[16:00:05]] [SUCCESS] Screenshot refreshed successfully
[[16:00:05]] [SUCCESS] Screenshot refreshed successfully
[[16:00:05]] [INFO] QMXBlswP6H=running
[[16:00:05]] [INFO] Executing action 6/51: Tap on Text: "Edit"
[[16:00:04]] [SUCCESS] Screenshot refreshed
[[16:00:04]] [INFO] Refreshing screenshot...
[[16:00:04]] [INFO] RLz6vQo3ag=pass
[[16:00:02]] [SUCCESS] Screenshot refreshed successfully
[[16:00:02]] [SUCCESS] Screenshot refreshed successfully
[[15:59:37]] [INFO] RLz6vQo3ag=running
[[15:59:37]] [INFO] Executing action 5/51: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[15:59:37]] [SUCCESS] Screenshot refreshed
[[15:59:37]] [INFO] Refreshing screenshot...
[[15:59:37]] [SUCCESS] Screenshot refreshed
[[15:59:37]] [INFO] Refreshing screenshot...
[[15:59:35]] [SUCCESS] Screenshot refreshed successfully
[[15:59:35]] [SUCCESS] Screenshot refreshed successfully
[[15:59:34]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[15:59:34]] [SUCCESS] Screenshot refreshed
[[15:59:34]] [INFO] Refreshing screenshot...
[[15:59:31]] [SUCCESS] Screenshot refreshed successfully
[[15:59:31]] [SUCCESS] Screenshot refreshed successfully
[[15:59:31]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[15:59:30]] [SUCCESS] Screenshot refreshed
[[15:59:30]] [INFO] Refreshing screenshot...
[[15:59:28]] [SUCCESS] Screenshot refreshed successfully
[[15:59:28]] [SUCCESS] Screenshot refreshed successfully
[[15:59:28]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[15:59:27]] [SUCCESS] Screenshot refreshed
[[15:59:27]] [INFO] Refreshing screenshot...
[[15:59:25]] [SUCCESS] Screenshot refreshed successfully
[[15:59:25]] [SUCCESS] Screenshot refreshed successfully
[[15:59:25]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[15:59:24]] [SUCCESS] Screenshot refreshed
[[15:59:24]] [INFO] Refreshing screenshot...
[[15:59:23]] [SUCCESS] Screenshot refreshed successfully
[[15:59:23]] [SUCCESS] Screenshot refreshed successfully
[[15:59:22]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[15:59:22]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[15:59:22]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[15:59:22]] [INFO] xz8njynjpZ=running
[[15:59:22]] [INFO] Executing action 4/51: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[15:59:22]] [SUCCESS] Screenshot refreshed
[[15:59:22]] [INFO] Refreshing screenshot...
[[15:59:22]] [INFO] J9loj6Zl5K=pass
[[15:59:03]] [SUCCESS] Screenshot refreshed successfully
[[15:59:03]] [SUCCESS] Screenshot refreshed successfully
[[15:59:02]] [INFO] J9loj6Zl5K=running
[[15:59:02]] [INFO] Executing action 3/51: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[15:59:02]] [SUCCESS] Screenshot refreshed
[[15:59:02]] [INFO] Refreshing screenshot...
[[15:59:02]] [INFO] Y8vz7AJD1i=pass
[[15:58:57]] [SUCCESS] Screenshot refreshed successfully
[[15:58:57]] [SUCCESS] Screenshot refreshed successfully
[[15:58:56]] [INFO] Y8vz7AJD1i=running
[[15:58:56]] [INFO] Executing action 2/51: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:58:56]] [SUCCESS] Screenshot refreshed
[[15:58:56]] [INFO] Refreshing screenshot...
[[15:58:56]] [INFO] H9fy9qcFbZ=pass
[[15:58:52]] [INFO] H9fy9qcFbZ=running
[[15:58:52]] [INFO] Executing action 1/51: Launch app: au.com.kmart
[[15:58:52]] [INFO] ExecutionManager: Starting execution of 51 actions...
[[15:58:52]] [SUCCESS] Cleared 1 screenshots from database
[[15:58:52]] [INFO] Clearing screenshots from database before execution...
[[15:58:52]] [SUCCESS] All screenshots deleted successfully
[[15:58:52]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[15:58:52]] [INFO] Skipping report initialization - single test case execution
[[15:58:47]] [SUCCESS] Test case Postcode Flow_AU_ANDROID saved successfully
[[15:58:47]] [INFO] Saving test case "Postcode Flow_AU_ANDROID"...
[[15:58:41]] [SUCCESS] Duplicated action at index 29
[[15:58:41]] [SUCCESS] Added action at position 30
[[15:57:25]] [SUCCESS] Screenshot refreshed successfully
[[15:57:25]] [SUCCESS] Screenshot refreshed successfully
[[15:57:24]] [INFO] Skipping report generation - individual test case execution (not from test suite)
[[15:57:24]] [SUCCESS] Action logs saved successfully
[[15:57:24]] [SUCCESS] Execution completed successfully.
[[15:57:24]] [INFO] Saving 440 action log entries to file...
[[15:57:24]] [INFO] Generating execution report...
[[15:57:24]] [SUCCESS] All tests passed successfully!
[[15:57:24]] [SUCCESS] Screenshot refreshed
[[15:57:24]] [INFO] Refreshing screenshot...
[[15:57:24]] [INFO] xyHVihJMBi=pass
[[15:57:21]] [SUCCESS] Screenshot refreshed successfully
[[15:57:21]] [SUCCESS] Screenshot refreshed successfully
[[15:57:20]] [INFO] xyHVihJMBi=running
[[15:57:20]] [INFO] Executing action 50/50: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[15:57:20]] [SUCCESS] Screenshot refreshed
[[15:57:20]] [INFO] Refreshing screenshot...
[[15:57:20]] [INFO] mWeLQtXiL6=pass
[[15:57:15]] [SUCCESS] Screenshot refreshed successfully
[[15:57:15]] [SUCCESS] Screenshot refreshed successfully
[[15:57:14]] [INFO] mWeLQtXiL6=running
[[15:57:14]] [INFO] Executing action 49/50: Swipe from (50%, 70%) to (50%, 30%)
[[15:57:14]] [SUCCESS] Screenshot refreshed
[[15:57:14]] [INFO] Refreshing screenshot...
[[15:57:14]] [INFO] rkwVoJGZG4=pass
[[15:57:12]] [SUCCESS] Screenshot refreshed successfully
[[15:57:12]] [SUCCESS] Screenshot refreshed successfully
[[15:57:12]] [INFO] rkwVoJGZG4=running
[[15:57:12]] [INFO] Executing action 48/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[15:57:11]] [SUCCESS] Screenshot refreshed
[[15:57:11]] [INFO] Refreshing screenshot...
[[15:57:11]] [INFO] 0f2FSZYjWq=pass
[[15:57:04]] [SUCCESS] Screenshot refreshed successfully
[[15:57:04]] [SUCCESS] Screenshot refreshed successfully
[[15:56:59]] [INFO] 0f2FSZYjWq=running
[[15:56:59]] [INFO] Executing action 47/50: Check if element with text="3000" exists
[[15:56:59]] [SUCCESS] Screenshot refreshed
[[15:56:59]] [INFO] Refreshing screenshot...
[[15:56:59]] [INFO] Tebej51pT2=pass
[[15:56:57]] [SUCCESS] Screenshot refreshed successfully
[[15:56:57]] [SUCCESS] Screenshot refreshed successfully
[[15:56:56]] [INFO] Tebej51pT2=running
[[15:56:56]] [INFO] Executing action 46/50: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[15:56:56]] [SUCCESS] Screenshot refreshed
[[15:56:56]] [INFO] Refreshing screenshot...
[[15:56:56]] [INFO] JrPVGdts3J=pass
[[15:56:45]] [SUCCESS] Screenshot refreshed successfully
[[15:56:45]] [SUCCESS] Screenshot refreshed successfully
[[15:56:44]] [INFO] JrPVGdts3J=running
[[15:56:44]] [INFO] Executing action 45/50: Tap on image: bag-remove-btn-android.png
[[15:56:44]] [SUCCESS] Screenshot refreshed
[[15:56:44]] [INFO] Refreshing screenshot...
[[15:56:44]] [INFO] s8h8VDUIOC=pass
[[15:56:41]] [SUCCESS] Screenshot refreshed successfully
[[15:56:41]] [SUCCESS] Screenshot refreshed successfully
[[15:56:41]] [INFO] s8h8VDUIOC=running
[[15:56:41]] [INFO] Executing action 44/50: Swipe from (50%, 70%) to (50%, 30%)
[[15:56:40]] [SUCCESS] Screenshot refreshed
[[15:56:40]] [INFO] Refreshing screenshot...
[[15:56:40]] [INFO] GYK47u1y3A=pass
[[15:56:39]] [SUCCESS] Screenshot refreshed successfully
[[15:56:39]] [SUCCESS] Screenshot refreshed successfully
[[15:56:38]] [INFO] GYK47u1y3A=running
[[15:56:38]] [INFO] Executing action 43/50: Android Function: send_key_event - Key Event: TAB
[[15:56:38]] [SUCCESS] Screenshot refreshed
[[15:56:38]] [INFO] Refreshing screenshot...
[[15:56:38]] [INFO] ZWpYNcpbFA=pass
[[15:56:34]] [SUCCESS] Screenshot refreshed successfully
[[15:56:34]] [SUCCESS] Screenshot refreshed successfully
[[15:56:34]] [INFO] ZWpYNcpbFA=running
[[15:56:34]] [INFO] Executing action 42/50: Tap on Text: "VIC"
[[15:56:33]] [SUCCESS] Screenshot refreshed
[[15:56:33]] [INFO] Refreshing screenshot...
[[15:56:33]] [INFO] QpBLC6BStn=pass
[[15:56:31]] [SUCCESS] Screenshot refreshed successfully
[[15:56:31]] [SUCCESS] Screenshot refreshed successfully
[[15:56:30]] [INFO] QpBLC6BStn=running
[[15:56:30]] [INFO] Executing action 41/50: textClear action
[[15:56:30]] [SUCCESS] Screenshot refreshed
[[15:56:30]] [INFO] Refreshing screenshot...
[[15:56:30]] [INFO] G4A3KBlXHq=pass
[[15:56:01]] [SUCCESS] Screenshot refreshed successfully
[[15:56:01]] [SUCCESS] Screenshot refreshed successfully
[[15:56:01]] [INFO] G4A3KBlXHq=running
[[15:56:01]] [INFO] Executing action 40/50: Tap on Text: "Nearby"
[[15:56:00]] [SUCCESS] Screenshot refreshed
[[15:56:00]] [INFO] Refreshing screenshot...
[[15:56:00]] [INFO] 3gJsiap2Ds=pass
[[15:55:57]] [SUCCESS] Screenshot refreshed successfully
[[15:55:57]] [SUCCESS] Screenshot refreshed successfully
[[15:55:57]] [INFO] 3gJsiap2Ds=running
[[15:55:57]] [INFO] Executing action 39/50: Tap on Text: "Collect"
[[15:55:56]] [SUCCESS] Screenshot refreshed
[[15:55:56]] [INFO] Refreshing screenshot...
[[15:55:56]] [INFO] qofJDqXBME=pass
[[15:55:51]] [SUCCESS] Screenshot refreshed successfully
[[15:55:51]] [SUCCESS] Screenshot refreshed successfully
[[15:55:50]] [INFO] qofJDqXBME=running
[[15:55:50]] [INFO] Executing action 38/50: Wait till text appears: "Delivery"
[[15:55:50]] [SUCCESS] Screenshot refreshed
[[15:55:50]] [INFO] Refreshing screenshot...
[[15:55:50]] [INFO] rkwVoJGZG4=pass
[[15:55:48]] [SUCCESS] Screenshot refreshed successfully
[[15:55:48]] [SUCCESS] Screenshot refreshed successfully
[[15:55:48]] [INFO] rkwVoJGZG4=running
[[15:55:48]] [INFO] Executing action 37/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[15:55:47]] [SUCCESS] Screenshot refreshed
[[15:55:47]] [INFO] Refreshing screenshot...
[[15:55:47]] [INFO] 94ikwhIEE2=pass
[[15:55:43]] [SUCCESS] Screenshot refreshed successfully
[[15:55:43]] [SUCCESS] Screenshot refreshed successfully
[[15:55:38]] [INFO] 94ikwhIEE2=running
[[15:55:38]] [INFO] Executing action 36/50: Tap on Text: "bag"
[[15:55:38]] [SUCCESS] Screenshot refreshed
[[15:55:38]] [INFO] Refreshing screenshot...
[[15:55:38]] [INFO] DfwaiVZ8Z9=pass
[[15:55:35]] [SUCCESS] Screenshot refreshed successfully
[[15:55:35]] [SUCCESS] Screenshot refreshed successfully
[[15:55:34]] [INFO] DfwaiVZ8Z9=running
[[15:55:34]] [INFO] Executing action 35/50: Swipe from (50%, 70%) to (50%, 50%)
[[15:55:34]] [SUCCESS] Screenshot refreshed
[[15:55:34]] [INFO] Refreshing screenshot...
[[15:55:34]] [INFO] eRCmRhc3re=pass
[[15:55:31]] [SUCCESS] Screenshot refreshed successfully
[[15:55:31]] [SUCCESS] Screenshot refreshed successfully
[[15:55:31]] [INFO] eRCmRhc3re=running
[[15:55:31]] [INFO] Executing action 34/50: Check if element with text="Broadway" exists
[[15:55:30]] [SUCCESS] Screenshot refreshed
[[15:55:30]] [INFO] Refreshing screenshot...
[[15:55:30]] [INFO] E2jpN7BioW=pass
[[15:55:02]] [SUCCESS] Screenshot refreshed successfully
[[15:55:02]] [SUCCESS] Screenshot refreshed successfully
[[15:55:02]] [INFO] E2jpN7BioW=running
[[15:55:02]] [INFO] Executing action 33/50: Tap on element with accessibility_id: btnSaveOrContinue
[[15:55:01]] [SUCCESS] Screenshot refreshed
[[15:55:01]] [INFO] Refreshing screenshot...
[[15:55:01]] [INFO] kDnmoQJG4o=pass
[[15:55:00]] [SUCCESS] Screenshot refreshed successfully
[[15:55:00]] [SUCCESS] Screenshot refreshed successfully
[[15:54:59]] [INFO] kDnmoQJG4o=running
[[15:54:59]] [INFO] Executing action 32/50: Wait till accessibility_id=btnSaveOrContinue
[[15:54:59]] [SUCCESS] Screenshot refreshed
[[15:54:59]] [INFO] Refreshing screenshot...
[[15:54:59]] [INFO] H0ODFz7sWJ=pass
[[15:54:41]] [SUCCESS] Screenshot refreshed successfully
[[15:54:41]] [SUCCESS] Screenshot refreshed successfully
[[15:54:41]] [INFO] H0ODFz7sWJ=running
[[15:54:41]] [INFO] Executing action 31/50: Tap on Text: "2000"
[[15:54:41]] [SUCCESS] Screenshot refreshed
[[15:54:41]] [INFO] Refreshing screenshot...
[[15:54:41]] [INFO] uZHvvAzVfx=pass
[[15:54:38]] [SUCCESS] Screenshot refreshed successfully
[[15:54:38]] [SUCCESS] Screenshot refreshed successfully
[[15:54:38]] [INFO] uZHvvAzVfx=running
[[15:54:38]] [INFO] Executing action 30/50: textClear action
[[15:54:37]] [SUCCESS] Screenshot refreshed
[[15:54:37]] [INFO] Refreshing screenshot...
[[15:54:37]] [INFO] pldheRUBVi=pass
[[15:54:35]] [SUCCESS] Screenshot refreshed successfully
[[15:54:35]] [SUCCESS] Screenshot refreshed successfully
[[15:54:35]] [INFO] pldheRUBVi=running
[[15:54:35]] [INFO] Executing action 29/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[15:54:35]] [SUCCESS] Screenshot refreshed
[[15:54:35]] [INFO] Refreshing screenshot...
[[15:54:35]] [INFO] pldheRUBVi=pass
[[15:54:33]] [SUCCESS] Screenshot refreshed successfully
[[15:54:33]] [SUCCESS] Screenshot refreshed successfully
[[15:54:32]] [INFO] pldheRUBVi=running
[[15:54:32]] [INFO] Executing action 28/50: Wait till xpath=//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[15:54:32]] [SUCCESS] Screenshot refreshed
[[15:54:32]] [INFO] Refreshing screenshot...
[[15:54:32]] [INFO] WmNWcsWVHv=pass
[[15:54:28]] [SUCCESS] Screenshot refreshed successfully
[[15:54:28]] [SUCCESS] Screenshot refreshed successfully
[[15:54:20]] [INFO] WmNWcsWVHv=running
[[15:54:20]] [INFO] Executing action 27/50: Tap on Text: "4000"
[[15:54:20]] [SUCCESS] Screenshot refreshed
[[15:54:20]] [INFO] Refreshing screenshot...
[[15:54:20]] [INFO] lnjoz8hHUU=pass
[[15:54:17]] [SUCCESS] Screenshot refreshed successfully
[[15:54:17]] [SUCCESS] Screenshot refreshed successfully
[[15:54:16]] [INFO] lnjoz8hHUU=running
[[15:54:16]] [INFO] Executing action 26/50: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[15:54:16]] [SUCCESS] Screenshot refreshed
[[15:54:16]] [INFO] Refreshing screenshot...
[[15:54:16]] [INFO] VkUKQbf1Qt=pass
[[15:54:10]] [SUCCESS] Screenshot refreshed successfully
[[15:54:10]] [SUCCESS] Screenshot refreshed successfully
[[15:54:10]] [INFO] VkUKQbf1Qt=running
[[15:54:10]] [INFO] Executing action 25/50: Tap on Text: "UNO"
[[15:54:09]] [SUCCESS] Screenshot refreshed
[[15:54:09]] [INFO] Refreshing screenshot...
[[15:54:09]] [INFO] 73NABkfWyY=pass
[[15:54:04]] [SUCCESS] Screenshot refreshed successfully
[[15:54:04]] [SUCCESS] Screenshot refreshed successfully
[[15:54:04]] [INFO] 73NABkfWyY=running
[[15:54:04]] [INFO] Executing action 24/50: Check if element with text="Toowong" exists
[[15:54:03]] [SUCCESS] Screenshot refreshed
[[15:54:03]] [INFO] Refreshing screenshot...
[[15:54:03]] [INFO] E2jpN7BioW=pass
[[15:53:36]] [SUCCESS] Screenshot refreshed successfully
[[15:53:36]] [SUCCESS] Screenshot refreshed successfully
[[15:53:36]] [INFO] E2jpN7BioW=running
[[15:53:36]] [INFO] Executing action 23/50: Tap on element with accessibility_id: btnSaveOrContinue
[[15:53:35]] [SUCCESS] Screenshot refreshed
[[15:53:35]] [INFO] Refreshing screenshot...
[[15:53:35]] [INFO] kDnmoQJG4o=pass
[[15:53:33]] [SUCCESS] Screenshot refreshed successfully
[[15:53:33]] [SUCCESS] Screenshot refreshed successfully
[[15:53:33]] [INFO] kDnmoQJG4o=running
[[15:53:33]] [INFO] Executing action 22/50: Wait till accessibility_id=btnSaveOrContinue
[[15:53:33]] [SUCCESS] Screenshot refreshed
[[15:53:33]] [INFO] Refreshing screenshot...
[[15:53:33]] [INFO] VkUKQbf1Qt=pass
[[15:53:28]] [SUCCESS] Screenshot refreshed successfully
[[15:53:28]] [SUCCESS] Screenshot refreshed successfully
[[15:53:26]] [INFO] VkUKQbf1Qt=running
[[15:53:26]] [INFO] Executing action 21/50: Tap on Text: "CITY"
[[15:53:25]] [SUCCESS] Screenshot refreshed
[[15:53:25]] [INFO] Refreshing screenshot...
[[15:53:25]] [INFO] pldheRUBVi=pass
[[15:53:23]] [SUCCESS] Screenshot refreshed successfully
[[15:53:23]] [SUCCESS] Screenshot refreshed successfully
[[15:53:23]] [INFO] pldheRUBVi=running
[[15:53:23]] [INFO] Executing action 20/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[15:53:22]] [SUCCESS] Screenshot refreshed
[[15:53:22]] [INFO] Refreshing screenshot...
[[15:53:22]] [INFO] kbdEPCPYod=pass
[[15:53:19]] [SUCCESS] Screenshot refreshed successfully
[[15:53:19]] [SUCCESS] Screenshot refreshed successfully
[[15:53:19]] [INFO] kbdEPCPYod=running
[[15:53:19]] [INFO] Executing action 19/50: textClear action
[[15:53:18]] [SUCCESS] Screenshot refreshed
[[15:53:18]] [INFO] Refreshing screenshot...
[[15:53:18]] [INFO] pldheRUBVi=pass
[[15:53:12]] [SUCCESS] Screenshot refreshed successfully
[[15:53:12]] [SUCCESS] Screenshot refreshed successfully
[[15:53:12]] [INFO] pldheRUBVi=running
[[15:53:12]] [INFO] Executing action 18/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[15:53:11]] [SUCCESS] Screenshot refreshed
[[15:53:11]] [INFO] Refreshing screenshot...
[[15:53:11]] [INFO] YhLhTn3Wtm=pass
[[15:53:05]] [SUCCESS] Screenshot refreshed successfully
[[15:53:05]] [SUCCESS] Screenshot refreshed successfully
[[15:53:04]] [INFO] YhLhTn3Wtm=running
[[15:53:04]] [INFO] Executing action 17/50: Wait for 5 ms
[[15:53:04]] [SUCCESS] Screenshot refreshed
[[15:53:04]] [INFO] Refreshing screenshot...
[[15:53:04]] [INFO] VkUKQbf1Qt=pass
[[15:52:56]] [SUCCESS] Screenshot refreshed successfully
[[15:52:56]] [SUCCESS] Screenshot refreshed successfully
[[15:52:55]] [INFO] VkUKQbf1Qt=running
[[15:52:55]] [INFO] Executing action 16/50: Tap on Text: "Edit"
[[15:52:55]] [SUCCESS] Screenshot refreshed
[[15:52:55]] [INFO] Refreshing screenshot...
[[15:52:55]] [INFO] MpdUKUazHa=pass
[[15:52:50]] [SUCCESS] Screenshot refreshed successfully
[[15:52:50]] [SUCCESS] Screenshot refreshed successfully
[[15:52:50]] [INFO] MpdUKUazHa=running
[[15:52:50]] [INFO] Executing action 15/50: Wait till image appears: sort-by-relevance-android.png
[[15:52:50]] [SUCCESS] Screenshot refreshed
[[15:52:50]] [INFO] Refreshing screenshot...
[[15:52:50]] [INFO] IupxLP2Jsr=pass
[[15:52:48]] [SUCCESS] Screenshot refreshed successfully
[[15:52:48]] [SUCCESS] Screenshot refreshed successfully
[[15:52:47]] [INFO] IupxLP2Jsr=running
[[15:52:47]] [INFO] Executing action 14/50: Input text: "P_6225544"
[[15:52:47]] [SUCCESS] Screenshot refreshed
[[15:52:47]] [INFO] Refreshing screenshot...
[[15:52:47]] [INFO] 70iOOakiG7=pass
[[15:52:43]] [SUCCESS] Screenshot refreshed successfully
[[15:52:43]] [SUCCESS] Screenshot refreshed successfully
[[15:52:38]] [INFO] 70iOOakiG7=running
[[15:52:38]] [INFO] Executing action 13/50: Tap on Text: "Find"
[[15:52:38]] [SUCCESS] Screenshot refreshed
[[15:52:38]] [INFO] Refreshing screenshot...
[[15:52:38]] [INFO] E2jpN7BioW=pass
[[15:52:34]] [SUCCESS] Screenshot refreshed successfully
[[15:52:34]] [SUCCESS] Screenshot refreshed successfully
[[15:52:34]] [INFO] E2jpN7BioW=running
[[15:52:34]] [INFO] Executing action 12/50: Tap on element with accessibility_id: btnSaveOrContinue
[[15:52:33]] [SUCCESS] Screenshot refreshed
[[15:52:33]] [INFO] Refreshing screenshot...
[[15:52:33]] [INFO] kDnmoQJG4o=pass
[[15:52:32]] [SUCCESS] Screenshot refreshed successfully
[[15:52:32]] [SUCCESS] Screenshot refreshed successfully
[[15:52:31]] [INFO] kDnmoQJG4o=running
[[15:52:31]] [INFO] Executing action 11/50: Wait till accessibility_id=btnSaveOrContinue
[[15:52:31]] [SUCCESS] Screenshot refreshed
[[15:52:31]] [INFO] Refreshing screenshot...
[[15:52:31]] [INFO] mw9GQ4mzRE=pass
[[15:52:16]] [SUCCESS] Screenshot refreshed successfully
[[15:52:16]] [SUCCESS] Screenshot refreshed successfully
[[15:52:15]] [INFO] mw9GQ4mzRE=running
[[15:52:15]] [INFO] Executing action 10/50: Tap on Text: "BC"
[[15:52:15]] [SUCCESS] Screenshot refreshed
[[15:52:15]] [INFO] Refreshing screenshot...
[[15:52:15]] [INFO] pldheRUBVi=pass
[[15:52:12]] [SUCCESS] Screenshot refreshed successfully
[[15:52:12]] [SUCCESS] Screenshot refreshed successfully
[[15:52:12]] [INFO] pldheRUBVi=running
[[15:52:12]] [INFO] Executing action 9/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[15:52:11]] [SUCCESS] Screenshot refreshed
[[15:52:11]] [INFO] Refreshing screenshot...
[[15:52:11]] [INFO] kbdEPCPYod=pass
[[15:52:08]] [SUCCESS] Screenshot refreshed successfully
[[15:52:08]] [SUCCESS] Screenshot refreshed successfully
[[15:52:07]] [INFO] kbdEPCPYod=running
[[15:52:07]] [INFO] Executing action 8/50: textClear action
[[15:52:07]] [SUCCESS] Screenshot refreshed
[[15:52:07]] [INFO] Refreshing screenshot...
[[15:52:07]] [INFO] pldheRUBVi=pass
[[15:52:04]] [SUCCESS] Screenshot refreshed successfully
[[15:52:04]] [SUCCESS] Screenshot refreshed successfully
[[15:52:04]] [INFO] pldheRUBVi=running
[[15:52:04]] [INFO] Executing action 7/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[15:52:04]] [SUCCESS] Screenshot refreshed
[[15:52:04]] [INFO] Refreshing screenshot...
[[15:52:04]] [INFO] QMXBlswP6H=pass
[[15:51:59]] [SUCCESS] Screenshot refreshed successfully
[[15:51:59]] [SUCCESS] Screenshot refreshed successfully
[[15:51:43]] [INFO] QMXBlswP6H=running
[[15:51:43]] [INFO] Executing action 6/50: Tap on Text: "Edit"
[[15:51:43]] [SUCCESS] Screenshot refreshed
[[15:51:43]] [INFO] Refreshing screenshot...
[[15:51:43]] [INFO] RLz6vQo3ag=pass
[[15:51:38]] [INFO] RLz6vQo3ag=running
[[15:51:38]] [INFO] Executing action 5/50: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[15:51:38]] [SUCCESS] Screenshot refreshed
[[15:51:38]] [INFO] Refreshing screenshot...
[[15:51:37]] [SUCCESS] Screenshot refreshed
[[15:51:37]] [INFO] Refreshing screenshot...
[[15:51:35]] [SUCCESS] Screenshot refreshed successfully
[[15:51:35]] [SUCCESS] Screenshot refreshed successfully
[[15:51:35]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[15:51:34]] [SUCCESS] Screenshot refreshed
[[15:51:34]] [INFO] Refreshing screenshot...
[[15:51:32]] [SUCCESS] Screenshot refreshed successfully
[[15:51:32]] [SUCCESS] Screenshot refreshed successfully
[[15:51:32]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[15:51:31]] [SUCCESS] Screenshot refreshed
[[15:51:31]] [INFO] Refreshing screenshot...
[[15:51:29]] [SUCCESS] Screenshot refreshed successfully
[[15:51:29]] [SUCCESS] Screenshot refreshed successfully
[[15:51:28]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[15:51:28]] [SUCCESS] Screenshot refreshed
[[15:51:28]] [INFO] Refreshing screenshot...
[[15:51:26]] [SUCCESS] Screenshot refreshed successfully
[[15:51:26]] [SUCCESS] Screenshot refreshed successfully
[[15:51:26]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[15:51:25]] [SUCCESS] Screenshot refreshed
[[15:51:25]] [INFO] Refreshing screenshot...
[[15:51:23]] [SUCCESS] Screenshot refreshed successfully
[[15:51:23]] [SUCCESS] Screenshot refreshed successfully
[[15:51:23]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[15:51:23]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[15:51:23]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[15:51:23]] [INFO] xz8njynjpZ=running
[[15:51:23]] [INFO] Executing action 4/50: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[15:51:23]] [SUCCESS] Screenshot refreshed
[[15:51:23]] [INFO] Refreshing screenshot...
[[15:51:23]] [INFO] J9loj6Zl5K=pass
[[15:51:21]] [SUCCESS] Screenshot refreshed successfully
[[15:51:21]] [SUCCESS] Screenshot refreshed successfully
[[15:51:21]] [INFO] J9loj6Zl5K=running
[[15:51:21]] [INFO] Executing action 3/50: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[15:51:20]] [SUCCESS] Screenshot refreshed
[[15:51:20]] [INFO] Refreshing screenshot...
[[15:51:20]] [INFO] Y8vz7AJD1i=pass
[[15:51:14]] [SUCCESS] Screenshot refreshed successfully
[[15:51:14]] [SUCCESS] Screenshot refreshed successfully
[[15:51:14]] [INFO] Y8vz7AJD1i=running
[[15:51:14]] [INFO] Executing action 2/50: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:51:13]] [SUCCESS] Screenshot refreshed
[[15:51:13]] [INFO] Refreshing screenshot...
[[15:51:13]] [INFO] H9fy9qcFbZ=pass
[[15:51:10]] [INFO] H9fy9qcFbZ=running
[[15:51:10]] [INFO] Executing action 1/50: Launch app: au.com.kmart
[[15:51:10]] [INFO] ExecutionManager: Starting execution of 50 actions...
[[15:51:10]] [SUCCESS] Cleared 1 screenshots from database
[[15:51:10]] [INFO] Clearing screenshots from database before execution...
[[15:51:10]] [SUCCESS] All screenshots deleted successfully
[[15:51:10]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[15:51:10]] [INFO] Skipping report initialization - single test case execution
[[15:51:09]] [SUCCESS] Screenshot refreshed successfully
[[15:51:09]] [SUCCESS] All screenshots deleted successfully
[[15:51:09]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 50 actions
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: swipe
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: exists
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: swipe
[[15:51:09]] [SUCCESS] Added action: androidFunctions
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: textClear
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: waitTill
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: swipe
[[15:51:09]] [SUCCESS] Added action: exists
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: waitTill
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: textClear
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: waitTill
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: waitTill
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: exists
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: waitTill
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: textClear
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: wait
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: waitTill
[[15:51:09]] [SUCCESS] Added action: text
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: waitTill
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: textClear
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: tapOnText
[[15:51:09]] [SUCCESS] Added action: waitTill
[[15:51:09]] [SUCCESS] Added action: multiStep
[[15:51:09]] [SUCCESS] Added action: waitTill
[[15:51:09]] [SUCCESS] Added action: tap
[[15:51:09]] [SUCCESS] Added action: launchApp
[[15:51:09]] [INFO] All actions cleared
[[15:51:09]] [INFO] Cleaning up screenshots...
[[15:51:07]] [SUCCESS] Screenshot refreshed
[[15:51:07]] [INFO] Refreshing screenshot...
[[15:51:06]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[15:51:06]] [INFO] Device info updated: RMX2151
[[15:50:51]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[15:50:36]] [SUCCESS] Found 1 device(s)
[[15:50:35]] [INFO] Refreshing device list...
