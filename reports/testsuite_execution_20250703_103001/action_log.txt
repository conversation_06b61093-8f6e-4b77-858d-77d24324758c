Action Log - 2025-07-03 10:36:52
================================================================================

[[10:36:52]] [INFO] Generating execution report...
[[10:36:52]] [SUCCESS] All tests passed successfully!
[[10:36:51]] [SUCCESS] Screenshot refreshed
[[10:36:51]] [INFO] Refreshing screenshot...
[[10:36:51]] [INFO] xyHVihJMBi=pass
[[10:36:48]] [SUCCESS] Screenshot refreshed successfully
[[10:36:48]] [SUCCESS] Screenshot refreshed successfully
[[10:36:48]] [INFO] xyHVihJMBi=running
[[10:36:48]] [INFO] Executing action 50/50: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[10:36:47]] [SUCCESS] Screenshot refreshed
[[10:36:47]] [INFO] Refreshing screenshot...
[[10:36:47]] [INFO] mWeLQtXiL6=pass
[[10:36:42]] [SUCCESS] Screenshot refreshed successfully
[[10:36:42]] [SUCCESS] Screenshot refreshed successfully
[[10:36:42]] [INFO] mWeLQtXiL6=running
[[10:36:42]] [INFO] Executing action 49/50: Swipe from (50%, 70%) to (50%, 30%)
[[10:36:42]] [SUCCESS] Screenshot refreshed
[[10:36:42]] [INFO] Refreshing screenshot...
[[10:36:42]] [INFO] rkwVoJGZG4=pass
[[10:36:40]] [SUCCESS] Screenshot refreshed successfully
[[10:36:40]] [SUCCESS] Screenshot refreshed successfully
[[10:36:39]] [INFO] rkwVoJGZG4=running
[[10:36:39]] [INFO] Executing action 48/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[10:36:39]] [SUCCESS] Screenshot refreshed
[[10:36:39]] [INFO] Refreshing screenshot...
[[10:36:39]] [INFO] 0f2FSZYjWq=pass
[[10:36:33]] [SUCCESS] Screenshot refreshed successfully
[[10:36:33]] [SUCCESS] Screenshot refreshed successfully
[[10:36:32]] [INFO] 0f2FSZYjWq=running
[[10:36:32]] [INFO] Executing action 47/50: Check if element with text="3000" exists
[[10:36:32]] [SUCCESS] Screenshot refreshed
[[10:36:32]] [INFO] Refreshing screenshot...
[[10:36:32]] [INFO] Tebej51pT2=pass
[[10:36:30]] [SUCCESS] Screenshot refreshed successfully
[[10:36:30]] [SUCCESS] Screenshot refreshed successfully
[[10:36:30]] [INFO] Tebej51pT2=running
[[10:36:30]] [INFO] Executing action 46/50: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[10:36:29]] [SUCCESS] Screenshot refreshed
[[10:36:29]] [INFO] Refreshing screenshot...
[[10:36:29]] [INFO] JrPVGdts3J=pass
[[10:36:15]] [SUCCESS] Screenshot refreshed successfully
[[10:36:15]] [SUCCESS] Screenshot refreshed successfully
[[10:36:14]] [INFO] JrPVGdts3J=running
[[10:36:14]] [INFO] Executing action 45/50: Tap on image: bag-remove-btn-android.png
[[10:36:14]] [SUCCESS] Screenshot refreshed
[[10:36:14]] [INFO] Refreshing screenshot...
[[10:36:14]] [INFO] s8h8VDUIOC=pass
[[10:36:11]] [SUCCESS] Screenshot refreshed successfully
[[10:36:11]] [SUCCESS] Screenshot refreshed successfully
[[10:36:11]] [INFO] s8h8VDUIOC=running
[[10:36:11]] [INFO] Executing action 44/50: Swipe from (50%, 70%) to (50%, 30%)
[[10:36:10]] [SUCCESS] Screenshot refreshed
[[10:36:10]] [INFO] Refreshing screenshot...
[[10:36:10]] [INFO] GYK47u1y3A=pass
[[10:36:08]] [SUCCESS] Screenshot refreshed successfully
[[10:36:08]] [SUCCESS] Screenshot refreshed successfully
[[10:36:08]] [INFO] GYK47u1y3A=running
[[10:36:08]] [INFO] Executing action 43/50: Android Function: send_key_event - Key Event: TAB
[[10:36:08]] [SUCCESS] Screenshot refreshed
[[10:36:08]] [INFO] Refreshing screenshot...
[[10:36:08]] [INFO] ZWpYNcpbFA=pass
[[10:36:04]] [SUCCESS] Screenshot refreshed successfully
[[10:36:04]] [SUCCESS] Screenshot refreshed successfully
[[10:36:04]] [INFO] ZWpYNcpbFA=running
[[10:36:04]] [INFO] Executing action 42/50: Tap on Text: "VIC"
[[10:36:04]] [SUCCESS] Screenshot refreshed
[[10:36:04]] [INFO] Refreshing screenshot...
[[10:36:04]] [INFO] QpBLC6BStn=pass
[[10:35:43]] [SUCCESS] Screenshot refreshed successfully
[[10:35:43]] [SUCCESS] Screenshot refreshed successfully
[[10:35:43]] [INFO] QpBLC6BStn=running
[[10:35:43]] [INFO] Executing action 41/50: textClear action
[[10:35:42]] [SUCCESS] Screenshot refreshed
[[10:35:42]] [INFO] Refreshing screenshot...
[[10:35:42]] [INFO] G4A3KBlXHq=pass
[[10:35:37]] [SUCCESS] Screenshot refreshed successfully
[[10:35:37]] [SUCCESS] Screenshot refreshed successfully
[[10:35:37]] [INFO] G4A3KBlXHq=running
[[10:35:37]] [INFO] Executing action 40/50: Tap on Text: "Nearby"
[[10:35:36]] [SUCCESS] Screenshot refreshed
[[10:35:36]] [INFO] Refreshing screenshot...
[[10:35:36]] [INFO] 3gJsiap2Ds=pass
[[10:35:33]] [SUCCESS] Screenshot refreshed successfully
[[10:35:33]] [SUCCESS] Screenshot refreshed successfully
[[10:35:33]] [INFO] 3gJsiap2Ds=running
[[10:35:33]] [INFO] Executing action 39/50: Tap on Text: "Collect"
[[10:35:32]] [SUCCESS] Screenshot refreshed
[[10:35:32]] [INFO] Refreshing screenshot...
[[10:35:32]] [INFO] qofJDqXBME=pass
[[10:35:27]] [SUCCESS] Screenshot refreshed successfully
[[10:35:27]] [SUCCESS] Screenshot refreshed successfully
[[10:35:26]] [INFO] qofJDqXBME=running
[[10:35:26]] [INFO] Executing action 38/50: Wait till text appears: "Delivery"
[[10:35:26]] [SUCCESS] Screenshot refreshed
[[10:35:26]] [INFO] Refreshing screenshot...
[[10:35:26]] [INFO] rkwVoJGZG4=pass
[[10:35:24]] [SUCCESS] Screenshot refreshed successfully
[[10:35:24]] [SUCCESS] Screenshot refreshed successfully
[[10:35:24]] [INFO] rkwVoJGZG4=running
[[10:35:24]] [INFO] Executing action 37/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[10:35:23]] [SUCCESS] Screenshot refreshed
[[10:35:23]] [INFO] Refreshing screenshot...
[[10:35:23]] [INFO] 94ikwhIEE2=pass
[[10:35:19]] [SUCCESS] Screenshot refreshed successfully
[[10:35:19]] [SUCCESS] Screenshot refreshed successfully
[[10:35:16]] [INFO] 94ikwhIEE2=running
[[10:35:16]] [INFO] Executing action 36/50: Tap on Text: "bag"
[[10:35:15]] [SUCCESS] Screenshot refreshed
[[10:35:15]] [INFO] Refreshing screenshot...
[[10:35:15]] [INFO] DfwaiVZ8Z9=pass
[[10:35:12]] [SUCCESS] Screenshot refreshed successfully
[[10:35:12]] [SUCCESS] Screenshot refreshed successfully
[[10:35:11]] [INFO] DfwaiVZ8Z9=running
[[10:35:11]] [INFO] Executing action 35/50: Swipe from (50%, 70%) to (50%, 50%)
[[10:35:11]] [SUCCESS] Screenshot refreshed
[[10:35:11]] [INFO] Refreshing screenshot...
[[10:35:11]] [INFO] eRCmRhc3re=pass
[[10:35:07]] [SUCCESS] Screenshot refreshed successfully
[[10:35:07]] [SUCCESS] Screenshot refreshed successfully
[[10:35:06]] [INFO] eRCmRhc3re=running
[[10:35:06]] [INFO] Executing action 34/50: Check if element with text="Broadway" exists
[[10:35:06]] [SUCCESS] Screenshot refreshed
[[10:35:06]] [INFO] Refreshing screenshot...
[[10:35:06]] [INFO] E2jpN7BioW=pass
[[10:35:04]] [SUCCESS] Screenshot refreshed successfully
[[10:35:04]] [SUCCESS] Screenshot refreshed successfully
[[10:35:03]] [INFO] E2jpN7BioW=running
[[10:35:03]] [INFO] Executing action 33/50: Tap on element with accessibility_id: btnSaveOrContinue
[[10:35:03]] [SUCCESS] Screenshot refreshed
[[10:35:03]] [INFO] Refreshing screenshot...
[[10:35:03]] [INFO] kDnmoQJG4o=pass
[[10:34:47]] [SUCCESS] Screenshot refreshed successfully
[[10:34:47]] [SUCCESS] Screenshot refreshed successfully
[[10:34:47]] [INFO] kDnmoQJG4o=running
[[10:34:47]] [INFO] Executing action 32/50: Wait till accessibility_id=btnSaveOrContinue
[[10:34:46]] [SUCCESS] Screenshot refreshed
[[10:34:46]] [INFO] Refreshing screenshot...
[[10:34:46]] [INFO] H0ODFz7sWJ=pass
[[10:34:42]] [SUCCESS] Screenshot refreshed successfully
[[10:34:42]] [SUCCESS] Screenshot refreshed successfully
[[10:34:39]] [INFO] H0ODFz7sWJ=running
[[10:34:39]] [INFO] Executing action 31/50: Tap on Text: "2000"
[[10:34:39]] [SUCCESS] Screenshot refreshed
[[10:34:39]] [INFO] Refreshing screenshot...
[[10:34:39]] [INFO] pldheRUBVi=pass
[[10:34:12]] [SUCCESS] Screenshot refreshed successfully
[[10:34:12]] [SUCCESS] Screenshot refreshed successfully
[[10:34:12]] [INFO] pldheRUBVi=running
[[10:34:12]] [INFO] Executing action 30/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[10:34:11]] [SUCCESS] Screenshot refreshed
[[10:34:11]] [INFO] Refreshing screenshot...
[[10:34:11]] [INFO] uZHvvAzVfx=pass
[[10:34:09]] [SUCCESS] Screenshot refreshed successfully
[[10:34:09]] [SUCCESS] Screenshot refreshed successfully
[[10:34:08]] [INFO] uZHvvAzVfx=running
[[10:34:08]] [INFO] Executing action 29/50: textClear action
[[10:34:08]] [SUCCESS] Screenshot refreshed
[[10:34:08]] [INFO] Refreshing screenshot...
[[10:34:08]] [INFO] WmNWcsWVHv=pass
[[10:34:04]] [SUCCESS] Screenshot refreshed successfully
[[10:34:04]] [SUCCESS] Screenshot refreshed successfully
[[10:34:04]] [INFO] WmNWcsWVHv=running
[[10:34:04]] [INFO] Executing action 28/50: Tap on Text: "4000"
[[10:34:04]] [SUCCESS] Screenshot refreshed
[[10:34:04]] [INFO] Refreshing screenshot...
[[10:34:04]] [INFO] lnjoz8hHUU=pass
[[10:33:46]] [INFO] lnjoz8hHUU=running
[[10:33:46]] [INFO] Executing action 27/50: Tap on Text: "Edit"
[[10:33:46]] [INFO] BQ7Cxm53HQ=fail
[[10:33:46]] [ERROR] Action 26 failed: Element with xpath '//android.widget.Button[@text="Filter"]' not found within timeout of 30.0 seconds
[[10:33:14]] [SUCCESS] Screenshot refreshed successfully
[[10:33:14]] [SUCCESS] Screenshot refreshed successfully
[[10:33:14]] [INFO] BQ7Cxm53HQ=running
[[10:33:14]] [INFO] Executing action 26/50: Wait till xpath=//android.widget.Button[@text="Filter"]
[[10:33:13]] [SUCCESS] Screenshot refreshed
[[10:33:13]] [INFO] Refreshing screenshot...
[[10:33:13]] [INFO] VkUKQbf1Qt=pass
[[10:33:08]] [SUCCESS] Screenshot refreshed successfully
[[10:33:08]] [SUCCESS] Screenshot refreshed successfully
[[10:33:07]] [INFO] VkUKQbf1Qt=running
[[10:33:07]] [INFO] Executing action 25/50: Tap on Text: "UNO"
[[10:33:07]] [SUCCESS] Screenshot refreshed
[[10:33:07]] [INFO] Refreshing screenshot...
[[10:33:07]] [INFO] 73NABkfWyY=pass
[[10:33:02]] [SUCCESS] Screenshot refreshed successfully
[[10:33:02]] [SUCCESS] Screenshot refreshed successfully
[[10:32:59]] [INFO] 73NABkfWyY=running
[[10:32:59]] [INFO] Executing action 24/50: Check if element with text="Toowong" exists
[[10:32:59]] [SUCCESS] Screenshot refreshed
[[10:32:59]] [INFO] Refreshing screenshot...
[[10:32:59]] [INFO] E2jpN7BioW=pass
[[10:32:56]] [SUCCESS] Screenshot refreshed successfully
[[10:32:56]] [SUCCESS] Screenshot refreshed successfully
[[10:32:56]] [INFO] E2jpN7BioW=running
[[10:32:56]] [INFO] Executing action 23/50: Tap on element with accessibility_id: btnSaveOrContinue
[[10:32:55]] [SUCCESS] Screenshot refreshed
[[10:32:55]] [INFO] Refreshing screenshot...
[[10:32:55]] [INFO] kDnmoQJG4o=pass
[[10:32:53]] [SUCCESS] Screenshot refreshed successfully
[[10:32:53]] [SUCCESS] Screenshot refreshed successfully
[[10:32:53]] [INFO] kDnmoQJG4o=running
[[10:32:53]] [INFO] Executing action 22/50: Wait till accessibility_id=btnSaveOrContinue
[[10:32:53]] [SUCCESS] Screenshot refreshed
[[10:32:53]] [INFO] Refreshing screenshot...
[[10:32:53]] [INFO] VkUKQbf1Qt=pass
[[10:32:49]] [SUCCESS] Screenshot refreshed successfully
[[10:32:49]] [SUCCESS] Screenshot refreshed successfully
[[10:32:47]] [INFO] VkUKQbf1Qt=running
[[10:32:47]] [INFO] Executing action 21/50: Tap on Text: "CITY"
[[10:32:46]] [SUCCESS] Screenshot refreshed
[[10:32:46]] [INFO] Refreshing screenshot...
[[10:32:46]] [INFO] pldheRUBVi=pass
[[10:32:43]] [SUCCESS] Screenshot refreshed successfully
[[10:32:43]] [SUCCESS] Screenshot refreshed successfully
[[10:32:43]] [INFO] pldheRUBVi=running
[[10:32:43]] [INFO] Executing action 20/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[10:32:42]] [SUCCESS] Screenshot refreshed
[[10:32:42]] [INFO] Refreshing screenshot...
[[10:32:42]] [INFO] kbdEPCPYod=pass
[[10:32:40]] [SUCCESS] Screenshot refreshed successfully
[[10:32:40]] [SUCCESS] Screenshot refreshed successfully
[[10:32:39]] [INFO] kbdEPCPYod=running
[[10:32:39]] [INFO] Executing action 19/50: textClear action
[[10:32:38]] [SUCCESS] Screenshot refreshed
[[10:32:38]] [INFO] Refreshing screenshot...
[[10:32:38]] [INFO] pldheRUBVi=pass
[[10:32:31]] [SUCCESS] Screenshot refreshed successfully
[[10:32:31]] [SUCCESS] Screenshot refreshed successfully
[[10:32:31]] [INFO] pldheRUBVi=running
[[10:32:31]] [INFO] Executing action 18/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[10:32:30]] [SUCCESS] Screenshot refreshed
[[10:32:30]] [INFO] Refreshing screenshot...
[[10:32:30]] [INFO] YhLhTn3Wtm=pass
[[10:32:23]] [SUCCESS] Screenshot refreshed successfully
[[10:32:23]] [SUCCESS] Screenshot refreshed successfully
[[10:32:23]] [INFO] YhLhTn3Wtm=running
[[10:32:23]] [INFO] Executing action 17/50: Wait for 5 ms
[[10:32:23]] [SUCCESS] Screenshot refreshed
[[10:32:23]] [INFO] Refreshing screenshot...
[[10:32:23]] [INFO] VkUKQbf1Qt=pass
[[10:32:19]] [SUCCESS] Screenshot refreshed successfully
[[10:32:19]] [SUCCESS] Screenshot refreshed successfully
[[10:32:19]] [INFO] VkUKQbf1Qt=running
[[10:32:19]] [INFO] Executing action 16/50: Tap on Text: "Edit"
[[10:32:18]] [SUCCESS] Screenshot refreshed
[[10:32:18]] [INFO] Refreshing screenshot...
[[10:32:18]] [INFO] 4xOIAk8ThT=pass
[[10:32:15]] [SUCCESS] Screenshot refreshed successfully
[[10:32:15]] [SUCCESS] Screenshot refreshed successfully
[[10:32:15]] [INFO] 4xOIAk8ThT=running
[[10:32:15]] [INFO] Executing action 15/50: Wait till xpath=//android.widget.Button[@text="Filter"]
[[10:32:14]] [SUCCESS] Screenshot refreshed
[[10:32:14]] [INFO] Refreshing screenshot...
[[10:32:14]] [INFO] IupxLP2Jsr=pass
[[10:32:13]] [SUCCESS] Screenshot refreshed successfully
[[10:32:13]] [SUCCESS] Screenshot refreshed successfully
[[10:32:12]] [INFO] IupxLP2Jsr=running
[[10:32:12]] [INFO] Executing action 14/50: Input text: "P_6225544"
[[10:32:12]] [SUCCESS] Screenshot refreshed
[[10:32:12]] [INFO] Refreshing screenshot...
[[10:32:12]] [INFO] 70iOOakiG7=pass
[[10:32:05]] [SUCCESS] Screenshot refreshed successfully
[[10:32:05]] [SUCCESS] Screenshot refreshed successfully
[[10:32:04]] [INFO] 70iOOakiG7=running
[[10:32:04]] [INFO] Executing action 13/50: Tap on Text: "Find"
[[10:32:04]] [SUCCESS] Screenshot refreshed
[[10:32:04]] [INFO] Refreshing screenshot...
[[10:32:04]] [INFO] E2jpN7BioW=pass
[[10:31:46]] [SUCCESS] Screenshot refreshed successfully
[[10:31:46]] [SUCCESS] Screenshot refreshed successfully
[[10:31:46]] [INFO] E2jpN7BioW=running
[[10:31:46]] [INFO] Executing action 12/50: Tap on element with accessibility_id: btnSaveOrContinue
[[10:31:46]] [SUCCESS] Screenshot refreshed
[[10:31:46]] [INFO] Refreshing screenshot...
[[10:31:46]] [INFO] kDnmoQJG4o=pass
[[10:31:44]] [SUCCESS] Screenshot refreshed successfully
[[10:31:44]] [SUCCESS] Screenshot refreshed successfully
[[10:31:44]] [INFO] kDnmoQJG4o=running
[[10:31:44]] [INFO] Executing action 11/50: Wait till accessibility_id=btnSaveOrContinue
[[10:31:43]] [SUCCESS] Screenshot refreshed
[[10:31:43]] [INFO] Refreshing screenshot...
[[10:31:43]] [INFO] mw9GQ4mzRE=pass
[[10:31:39]] [SUCCESS] Screenshot refreshed successfully
[[10:31:39]] [SUCCESS] Screenshot refreshed successfully
[[10:31:39]] [INFO] mw9GQ4mzRE=running
[[10:31:39]] [INFO] Executing action 10/50: Tap on Text: "BC"
[[10:31:38]] [SUCCESS] Screenshot refreshed
[[10:31:38]] [INFO] Refreshing screenshot...
[[10:31:38]] [INFO] pldheRUBVi=pass
[[10:31:11]] [SUCCESS] Screenshot refreshed successfully
[[10:31:11]] [SUCCESS] Screenshot refreshed successfully
[[10:31:11]] [INFO] pldheRUBVi=running
[[10:31:11]] [INFO] Executing action 9/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[10:31:10]] [SUCCESS] Screenshot refreshed
[[10:31:10]] [INFO] Refreshing screenshot...
[[10:31:10]] [INFO] kbdEPCPYod=pass
[[10:31:07]] [SUCCESS] Screenshot refreshed successfully
[[10:31:07]] [SUCCESS] Screenshot refreshed successfully
[[10:31:07]] [INFO] kbdEPCPYod=running
[[10:31:07]] [INFO] Executing action 8/50: textClear action
[[10:31:07]] [SUCCESS] Screenshot refreshed
[[10:31:07]] [INFO] Refreshing screenshot...
[[10:31:07]] [INFO] pldheRUBVi=pass
[[10:31:05]] [SUCCESS] Screenshot refreshed successfully
[[10:31:05]] [SUCCESS] Screenshot refreshed successfully
[[10:31:05]] [INFO] pldheRUBVi=running
[[10:31:05]] [INFO] Executing action 7/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[10:31:04]] [SUCCESS] Screenshot refreshed
[[10:31:04]] [INFO] Refreshing screenshot...
[[10:31:04]] [INFO] QMXBlswP6H=pass
[[10:30:52]] [SUCCESS] Screenshot refreshed successfully
[[10:30:52]] [SUCCESS] Screenshot refreshed successfully
[[10:30:52]] [INFO] QMXBlswP6H=running
[[10:30:52]] [INFO] Executing action 6/50: Tap on Text: "Edit"
[[10:30:51]] [SUCCESS] Screenshot refreshed
[[10:30:51]] [INFO] Refreshing screenshot...
[[10:30:51]] [INFO] RLz6vQo3ag=pass
[[10:30:47]] [INFO] RLz6vQo3ag=running
[[10:30:47]] [INFO] Executing action 5/50: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[10:30:47]] [SUCCESS] Screenshot refreshed
[[10:30:47]] [INFO] Refreshing screenshot...
[[10:30:46]] [SUCCESS] Screenshot refreshed
[[10:30:46]] [INFO] Refreshing screenshot...
[[10:30:44]] [SUCCESS] Screenshot refreshed successfully
[[10:30:44]] [SUCCESS] Screenshot refreshed successfully
[[10:30:44]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[10:30:43]] [SUCCESS] Screenshot refreshed
[[10:30:43]] [INFO] Refreshing screenshot...
[[10:30:41]] [SUCCESS] Screenshot refreshed successfully
[[10:30:41]] [SUCCESS] Screenshot refreshed successfully
[[10:30:41]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[10:30:41]] [SUCCESS] Screenshot refreshed
[[10:30:41]] [INFO] Refreshing screenshot...
[[10:30:38]] [SUCCESS] Screenshot refreshed successfully
[[10:30:38]] [SUCCESS] Screenshot refreshed successfully
[[10:30:38]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[10:30:37]] [SUCCESS] Screenshot refreshed
[[10:30:37]] [INFO] Refreshing screenshot...
[[10:30:13]] [SUCCESS] Screenshot refreshed successfully
[[10:30:13]] [SUCCESS] Screenshot refreshed successfully
[[10:30:13]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[10:30:12]] [SUCCESS] Screenshot refreshed
[[10:30:12]] [INFO] Refreshing screenshot...
[[10:30:10]] [SUCCESS] Screenshot refreshed successfully
[[10:30:10]] [SUCCESS] Screenshot refreshed successfully
[[10:30:10]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[10:30:10]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[10:30:10]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[10:30:10]] [INFO] xz8njynjpZ=running
[[10:30:10]] [INFO] Executing action 4/50: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[10:30:10]] [SUCCESS] Screenshot refreshed
[[10:30:10]] [INFO] Refreshing screenshot...
[[10:30:10]] [INFO] J9loj6Zl5K=pass
[[10:30:08]] [SUCCESS] Screenshot refreshed successfully
[[10:30:08]] [SUCCESS] Screenshot refreshed successfully
[[10:30:08]] [INFO] J9loj6Zl5K=running
[[10:30:08]] [INFO] Executing action 3/50: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[10:30:07]] [SUCCESS] Screenshot refreshed
[[10:30:07]] [INFO] Refreshing screenshot...
[[10:30:07]] [INFO] Y8vz7AJD1i=pass
[[10:30:01]] [SUCCESS] Screenshot refreshed successfully
[[10:30:01]] [SUCCESS] Screenshot refreshed successfully
[[10:30:01]] [INFO] Y8vz7AJD1i=running
[[10:30:01]] [INFO] Executing action 2/50: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:30:01]] [SUCCESS] Screenshot refreshed
[[10:30:01]] [INFO] Refreshing screenshot...
[[10:30:01]] [INFO] H9fy9qcFbZ=pass
[[10:29:58]] [INFO] H9fy9qcFbZ=running
[[10:29:58]] [INFO] Executing action 1/50: Launch app: au.com.kmart
[[10:29:58]] [INFO] ExecutionManager: Starting execution of 50 actions...
[[10:29:58]] [SUCCESS] Cleared 1 screenshots from database
[[10:29:58]] [INFO] Clearing screenshots from database before execution...
[[10:29:58]] [SUCCESS] All screenshots deleted successfully
[[10:29:58]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:29:58]] [INFO] Skipping report initialization - single test case execution
[[10:29:45]] [SUCCESS] Test case Postcode Flow_AU_ANDROID saved successfully
[[10:29:45]] [INFO] Saving test case "Postcode Flow_AU_ANDROID"...
[[10:29:44]] [SUCCESS] Updated action #26
[[10:29:17]] [SUCCESS] All screenshots deleted successfully
[[10:29:17]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 50 actions
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: swipe
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: exists
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: swipe
[[10:29:17]] [SUCCESS] Added action: androidFunctions
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: textClear
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: waitTill
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: swipe
[[10:29:17]] [SUCCESS] Added action: exists
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: waitTill
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: textClear
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: waitTill
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: exists
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: waitTill
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: textClear
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: wait
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: waitTill
[[10:29:17]] [SUCCESS] Added action: text
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: waitTill
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: textClear
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: tapOnText
[[10:29:17]] [SUCCESS] Added action: waitTill
[[10:29:17]] [SUCCESS] Added action: multiStep
[[10:29:17]] [SUCCESS] Added action: waitTill
[[10:29:17]] [SUCCESS] Added action: tap
[[10:29:17]] [SUCCESS] Added action: launchApp
[[10:29:17]] [INFO] All actions cleared
[[10:29:17]] [INFO] Cleaning up screenshots...
[[10:28:53]] [SUCCESS] Opened Appium Web Inspector with pre-filled connection details.
[[10:28:53]] [INFO] Opening URL: http://localhost:4723/inspector?host=127.0.0.1&port=4723&path=/wd/hub/
[[10:28:53]] [INFO] Found active session ID: d236cb0a-1f38-4ed0-aaf8-d8f879b9f78f. Inspector should list this session under 'Attach to Session'.
[[10:28:53]] [INFO] Opening Appium Web Inspector in a new window...
[[10:28:53]] [INFO] Checking Appium Web Inspector availability...
[[10:28:49]] [SUCCESS] Screenshot refreshed successfully
[[10:28:48]] [SUCCESS] Screenshot refreshed
[[10:28:48]] [INFO] Refreshing screenshot...
[[10:28:47]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[10:28:47]] [INFO] Device info updated: RMX2151
[[10:28:42]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[10:28:40]] [SUCCESS] Found 1 device(s)
[[10:28:40]] [INFO] Refreshing device list...
