Action Log - 2025-07-01 10:26:56
================================================================================

[[10:26:56]] [INFO] Generating execution report...
[[10:26:56]] [WARNING] 1 test failed.
[[10:26:56]] [SUCCESS] Screenshot refreshed
[[10:26:56]] [INFO] Refreshing screenshot...
[[10:26:55]] [SUCCESS] Screenshot refreshed
[[10:26:55]] [INFO] Refreshing screenshot...
[[10:26:54]] [SUCCESS] Screenshot refreshed successfully
[[10:26:54]] [SUCCESS] Screenshot refreshed successfully
[[10:26:54]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:26:53]] [SUCCESS] Screenshot refreshed
[[10:26:53]] [INFO] Refreshing screenshot...
[[10:26:41]] [SUCCESS] Screenshot refreshed successfully
[[10:26:41]] [SUCCESS] Screenshot refreshed successfully
[[10:26:41]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:26:41]] [SUCCESS] Screenshot refreshed
[[10:26:41]] [INFO] Refreshing screenshot...
[[10:26:37]] [SUCCESS] Screenshot refreshed successfully
[[10:26:37]] [SUCCESS] Screenshot refreshed successfully
[[10:26:37]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:26:36]] [SUCCESS] Screenshot refreshed
[[10:26:36]] [INFO] Refreshing screenshot...
[[10:26:34]] [SUCCESS] Screenshot refreshed successfully
[[10:26:34]] [SUCCESS] Screenshot refreshed successfully
[[10:26:33]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:26:32]] [SUCCESS] Screenshot refreshed
[[10:26:32]] [INFO] Refreshing screenshot...
[[10:26:25]] [SUCCESS] Screenshot refreshed successfully
[[10:26:25]] [SUCCESS] Screenshot refreshed successfully
[[10:26:25]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:26:24]] [SUCCESS] Screenshot refreshed
[[10:26:24]] [INFO] Refreshing screenshot...
[[10:26:17]] [SUCCESS] Screenshot refreshed successfully
[[10:26:17]] [SUCCESS] Screenshot refreshed successfully
[[10:26:17]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[10:26:17]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[10:26:17]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[10:26:16]] [INFO] A2P2SB1bbN=running
[[10:26:16]] [INFO] Executing action 351/351: cleanupSteps action
[[10:26:16]] [SUCCESS] Screenshot refreshed
[[10:26:16]] [INFO] Refreshing screenshot...
[[10:26:16]] [INFO] arH1CZCPXh=pass
[[10:26:11]] [SUCCESS] Screenshot refreshed successfully
[[10:26:11]] [SUCCESS] Screenshot refreshed successfully
[[10:26:11]] [INFO] arH1CZCPXh=running
[[10:26:11]] [INFO] Executing action 350/351: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[10:26:10]] [SUCCESS] Screenshot refreshed
[[10:26:10]] [INFO] Refreshing screenshot...
[[10:26:10]] [INFO] RbD937Xbte=pass
[[10:26:05]] [SUCCESS] Screenshot refreshed successfully
[[10:26:05]] [SUCCESS] Screenshot refreshed successfully
[[10:26:05]] [INFO] RbD937Xbte=running
[[10:26:05]] [INFO] Executing action 349/351: Tap on Text: "out"
[[10:26:05]] [SUCCESS] Screenshot refreshed
[[10:26:05]] [INFO] Refreshing screenshot...
[[10:26:05]] [INFO] ylslyLAYKb=pass
[[10:26:01]] [SUCCESS] Screenshot refreshed successfully
[[10:26:01]] [SUCCESS] Screenshot refreshed successfully
[[10:26:00]] [INFO] ylslyLAYKb=running
[[10:26:00]] [INFO] Executing action 348/351: Swipe from (50%, 70%) to (50%, 30%)
[[10:26:00]] [SUCCESS] Screenshot refreshed
[[10:26:00]] [INFO] Refreshing screenshot...
[[10:26:00]] [INFO] wguGCt7OoB=pass
[[10:25:56]] [SUCCESS] Screenshot refreshed successfully
[[10:25:56]] [SUCCESS] Screenshot refreshed successfully
[[10:25:56]] [INFO] wguGCt7OoB=running
[[10:25:56]] [INFO] Executing action 347/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:25:56]] [SUCCESS] Screenshot refreshed
[[10:25:56]] [INFO] Refreshing screenshot...
[[10:25:56]] [INFO] RDQCFIxjA0=pass
[[10:25:52]] [SUCCESS] Screenshot refreshed successfully
[[10:25:52]] [SUCCESS] Screenshot refreshed successfully
[[10:25:52]] [INFO] RDQCFIxjA0=running
[[10:25:52]] [INFO] Executing action 346/351: Swipe from (90%, 20%) to (30%, 20%)
[[10:25:51]] [SUCCESS] Screenshot refreshed
[[10:25:51]] [INFO] Refreshing screenshot...
[[10:25:51]] [INFO] x4Mid4HQ0Z=pass
[[10:25:48]] [SUCCESS] Screenshot refreshed successfully
[[10:25:48]] [SUCCESS] Screenshot refreshed successfully
[[10:25:48]] [INFO] x4Mid4HQ0Z=running
[[10:25:48]] [INFO] Executing action 345/351: Swipe from (90%, 20%) to (30%, 20%)
[[10:25:47]] [SUCCESS] Screenshot refreshed
[[10:25:47]] [INFO] Refreshing screenshot...
[[10:25:47]] [INFO] OKCHAK6HCJ=pass
[[10:25:43]] [SUCCESS] Screenshot refreshed successfully
[[10:25:43]] [SUCCESS] Screenshot refreshed successfully
[[10:25:43]] [INFO] OKCHAK6HCJ=running
[[10:25:43]] [INFO] Executing action 344/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[10:25:42]] [SUCCESS] Screenshot refreshed
[[10:25:42]] [INFO] Refreshing screenshot...
[[10:25:42]] [INFO] Ef6OumM2eS=pass
[[10:25:38]] [SUCCESS] Screenshot refreshed successfully
[[10:25:38]] [SUCCESS] Screenshot refreshed successfully
[[10:25:38]] [INFO] Ef6OumM2eS=running
[[10:25:38]] [INFO] Executing action 343/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[10:25:37]] [SUCCESS] Screenshot refreshed
[[10:25:37]] [INFO] Refreshing screenshot...
[[10:25:37]] [INFO] 0bnBNoqPt8=pass
[[10:25:32]] [SUCCESS] Screenshot refreshed successfully
[[10:25:32]] [SUCCESS] Screenshot refreshed successfully
[[10:25:32]] [INFO] 0bnBNoqPt8=running
[[10:25:32]] [INFO] Executing action 342/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[10:25:32]] [SUCCESS] Screenshot refreshed
[[10:25:32]] [INFO] Refreshing screenshot...
[[10:25:32]] [INFO] xmelRkcdVx=pass
[[10:25:28]] [SUCCESS] Screenshot refreshed successfully
[[10:25:28]] [SUCCESS] Screenshot refreshed successfully
[[10:25:27]] [INFO] xmelRkcdVx=running
[[10:25:27]] [INFO] Executing action 341/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:25:27]] [SUCCESS] Screenshot refreshed
[[10:25:27]] [INFO] Refreshing screenshot...
[[10:25:27]] [INFO] ksCBjJiwHZ=pass
[[10:25:23]] [SUCCESS] Screenshot refreshed successfully
[[10:25:23]] [SUCCESS] Screenshot refreshed successfully
[[10:25:23]] [INFO] ksCBjJiwHZ=running
[[10:25:23]] [INFO] Executing action 340/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[10:25:23]] [SUCCESS] Screenshot refreshed
[[10:25:23]] [INFO] Refreshing screenshot...
[[10:25:23]] [INFO] RuPGkdCdah=pass
[[10:25:18]] [SUCCESS] Screenshot refreshed successfully
[[10:25:18]] [SUCCESS] Screenshot refreshed successfully
[[10:25:18]] [INFO] RuPGkdCdah=running
[[10:25:18]] [INFO] Executing action 339/351: iOS Function: text - Text: "enn[cooker-id]"
[[10:25:18]] [SUCCESS] Screenshot refreshed
[[10:25:18]] [INFO] Refreshing screenshot...
[[10:25:18]] [INFO] ewuLtuqVuo=pass
[[10:25:13]] [SUCCESS] Screenshot refreshed successfully
[[10:25:13]] [SUCCESS] Screenshot refreshed successfully
[[10:25:12]] [INFO] ewuLtuqVuo=running
[[10:25:12]] [INFO] Executing action 338/351: Tap on Text: "Find"
[[10:25:12]] [SUCCESS] Screenshot refreshed
[[10:25:12]] [INFO] Refreshing screenshot...
[[10:25:12]] [INFO] GTXmST3hEA=pass
[[10:25:09]] [SUCCESS] Screenshot refreshed successfully
[[10:25:09]] [SUCCESS] Screenshot refreshed successfully
[[10:25:08]] [INFO] GTXmST3hEA=running
[[10:25:08]] [INFO] Executing action 337/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:25:08]] [SUCCESS] Screenshot refreshed
[[10:25:08]] [INFO] Refreshing screenshot...
[[10:25:08]] [INFO] qkZ5KShdEU=pass
[[10:25:02]] [SUCCESS] Screenshot refreshed successfully
[[10:25:02]] [SUCCESS] Screenshot refreshed successfully
[[10:25:02]] [INFO] qkZ5KShdEU=running
[[10:25:02]] [INFO] Executing action 336/351: iOS Function: text - Text: "env[pwd]"
[[10:25:02]] [SUCCESS] Screenshot refreshed
[[10:25:02]] [INFO] Refreshing screenshot...
[[10:25:02]] [INFO] 7g2LmvjtEZ=pass
[[10:24:57]] [SUCCESS] Screenshot refreshed successfully
[[10:24:57]] [SUCCESS] Screenshot refreshed successfully
[[10:24:56]] [INFO] 7g2LmvjtEZ=running
[[10:24:56]] [INFO] Executing action 335/351: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:24:56]] [SUCCESS] Screenshot refreshed
[[10:24:56]] [INFO] Refreshing screenshot...
[[10:24:56]] [INFO] OUT2ASweb6=pass
[[10:24:51]] [SUCCESS] Screenshot refreshed successfully
[[10:24:51]] [SUCCESS] Screenshot refreshed successfully
[[10:24:51]] [INFO] OUT2ASweb6=running
[[10:24:51]] [INFO] Executing action 334/351: iOS Function: text - Text: "env[uname]"
[[10:24:50]] [SUCCESS] Screenshot refreshed
[[10:24:50]] [INFO] Refreshing screenshot...
[[10:24:50]] [INFO] TV4kJIIV9v=pass
[[10:24:46]] [SUCCESS] Screenshot refreshed successfully
[[10:24:46]] [SUCCESS] Screenshot refreshed successfully
[[10:24:46]] [INFO] TV4kJIIV9v=running
[[10:24:46]] [INFO] Executing action 333/351: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:24:46]] [SUCCESS] Screenshot refreshed
[[10:24:46]] [INFO] Refreshing screenshot...
[[10:24:46]] [INFO] kQJbqm7uCi=pass
[[10:24:43]] [SUCCESS] Screenshot refreshed successfully
[[10:24:43]] [SUCCESS] Screenshot refreshed successfully
[[10:24:43]] [INFO] kQJbqm7uCi=running
[[10:24:43]] [INFO] Executing action 332/351: iOS Function: alert_accept
[[10:24:42]] [SUCCESS] Screenshot refreshed
[[10:24:42]] [INFO] Refreshing screenshot...
[[10:24:42]] [INFO] SPE01N6pyp=pass
[[10:24:37]] [SUCCESS] Screenshot refreshed successfully
[[10:24:37]] [SUCCESS] Screenshot refreshed successfully
[[10:24:36]] [INFO] SPE01N6pyp=running
[[10:24:36]] [INFO] Executing action 331/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:24:36]] [SUCCESS] Screenshot refreshed
[[10:24:36]] [INFO] Refreshing screenshot...
[[10:24:36]] [INFO] WEB5St2Mb7=pass
[[10:24:30]] [SUCCESS] Screenshot refreshed successfully
[[10:24:30]] [SUCCESS] Screenshot refreshed successfully
[[10:24:30]] [INFO] WEB5St2Mb7=running
[[10:24:30]] [INFO] Executing action 330/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[10:24:30]] [SUCCESS] Screenshot refreshed
[[10:24:30]] [INFO] Refreshing screenshot...
[[10:24:30]] [INFO] To7bij5MnF=pass
[[10:24:24]] [SUCCESS] Screenshot refreshed successfully
[[10:24:24]] [SUCCESS] Screenshot refreshed successfully
[[10:24:24]] [INFO] To7bij5MnF=running
[[10:24:24]] [INFO] Executing action 329/351: Swipe from (5%, 50%) to (90%, 50%)
[[10:24:24]] [SUCCESS] Screenshot refreshed
[[10:24:24]] [INFO] Refreshing screenshot...
[[10:24:24]] [INFO] NkybTKfs2U=pass
[[10:24:18]] [SUCCESS] Screenshot refreshed successfully
[[10:24:18]] [SUCCESS] Screenshot refreshed successfully
[[10:24:18]] [INFO] NkybTKfs2U=running
[[10:24:18]] [INFO] Executing action 328/351: Swipe from (5%, 50%) to (90%, 50%)
[[10:24:18]] [SUCCESS] Screenshot refreshed
[[10:24:18]] [INFO] Refreshing screenshot...
[[10:24:18]] [INFO] dYEtjrv6lz=pass
[[10:24:13]] [SUCCESS] Screenshot refreshed successfully
[[10:24:13]] [SUCCESS] Screenshot refreshed successfully
[[10:24:13]] [INFO] dYEtjrv6lz=running
[[10:24:13]] [INFO] Executing action 327/351: Tap on Text: "Months"
[[10:24:12]] [SUCCESS] Screenshot refreshed
[[10:24:12]] [INFO] Refreshing screenshot...
[[10:24:12]] [INFO] eGQ7VrKUSo=pass
[[10:24:08]] [SUCCESS] Screenshot refreshed successfully
[[10:24:08]] [SUCCESS] Screenshot refreshed successfully
[[10:24:08]] [INFO] eGQ7VrKUSo=running
[[10:24:08]] [INFO] Executing action 326/351: Tap on Text: "Age"
[[10:24:07]] [SUCCESS] Screenshot refreshed
[[10:24:07]] [INFO] Refreshing screenshot...
[[10:24:07]] [INFO] zNRPvs2cC4=pass
[[10:24:02]] [SUCCESS] Screenshot refreshed successfully
[[10:24:02]] [SUCCESS] Screenshot refreshed successfully
[[10:24:02]] [INFO] zNRPvs2cC4=running
[[10:24:02]] [INFO] Executing action 325/351: Tap on Text: "Toys"
[[10:24:02]] [SUCCESS] Screenshot refreshed
[[10:24:02]] [INFO] Refreshing screenshot...
[[10:24:02]] [INFO] KyyS139agr=pass
[[10:23:58]] [SUCCESS] Screenshot refreshed successfully
[[10:23:58]] [SUCCESS] Screenshot refreshed successfully
[[10:23:58]] [INFO] KyyS139agr=running
[[10:23:58]] [INFO] Executing action 324/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[10:23:57]] [SUCCESS] Screenshot refreshed
[[10:23:57]] [INFO] Refreshing screenshot...
[[10:23:57]] [INFO] 5e4LeoW1YU=pass
[[10:23:54]] [SUCCESS] Screenshot refreshed successfully
[[10:23:54]] [SUCCESS] Screenshot refreshed successfully
[[10:23:52]] [INFO] 5e4LeoW1YU=running
[[10:23:52]] [INFO] Executing action 323/351: Restart app: env[appid]
[[10:23:52]] [SUCCESS] Screenshot refreshed
[[10:23:52]] [INFO] Refreshing screenshot...
[[10:23:51]] [SUCCESS] Screenshot refreshed
[[10:23:51]] [INFO] Refreshing screenshot...
[[10:23:35]] [SUCCESS] Screenshot refreshed successfully
[[10:23:35]] [SUCCESS] Screenshot refreshed successfully
[[10:23:35]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[10:23:34]] [SUCCESS] Screenshot refreshed
[[10:23:34]] [INFO] Refreshing screenshot...
[[10:22:48]] [SUCCESS] Screenshot refreshed successfully
[[10:22:48]] [SUCCESS] Screenshot refreshed successfully
[[10:22:48]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[10:22:48]] [SUCCESS] Screenshot refreshed
[[10:22:48]] [INFO] Refreshing screenshot...
[[10:22:30]] [SUCCESS] Screenshot refreshed successfully
[[10:22:30]] [SUCCESS] Screenshot refreshed successfully
[[10:22:30]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[10:22:29]] [SUCCESS] Screenshot refreshed
[[10:22:29]] [INFO] Refreshing screenshot...
[[10:21:44]] [SUCCESS] Screenshot refreshed successfully
[[10:21:44]] [SUCCESS] Screenshot refreshed successfully
[[10:21:44]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[10:21:44]] [SUCCESS] Screenshot refreshed
[[10:21:44]] [INFO] Refreshing screenshot...
[[10:21:28]] [SUCCESS] Screenshot refreshed successfully
[[10:21:28]] [SUCCESS] Screenshot refreshed successfully
[[10:21:28]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[10:21:27]] [SUCCESS] Screenshot refreshed
[[10:21:27]] [INFO] Refreshing screenshot...
[[10:20:43]] [SUCCESS] Screenshot refreshed successfully
[[10:20:43]] [SUCCESS] Screenshot refreshed successfully
[[10:20:42]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[10:20:42]] [SUCCESS] Screenshot refreshed
[[10:20:42]] [INFO] Refreshing screenshot...
[[10:20:27]] [SUCCESS] Screenshot refreshed successfully
[[10:20:27]] [SUCCESS] Screenshot refreshed successfully
[[10:20:27]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[10:20:26]] [SUCCESS] Screenshot refreshed
[[10:20:26]] [INFO] Refreshing screenshot...
[[10:19:42]] [SUCCESS] Screenshot refreshed successfully
[[10:19:42]] [SUCCESS] Screenshot refreshed successfully
[[10:19:41]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[10:19:41]] [SUCCESS] Screenshot refreshed
[[10:19:41]] [INFO] Refreshing screenshot...
[[10:19:25]] [SUCCESS] Screenshot refreshed successfully
[[10:19:25]] [SUCCESS] Screenshot refreshed successfully
[[10:19:25]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[10:19:24]] [SUCCESS] Screenshot refreshed
[[10:19:24]] [INFO] Refreshing screenshot...
[[10:18:37]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[10:18:37]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[10:18:37]] [SUCCESS] Screenshot refreshed successfully
[[10:18:37]] [SUCCESS] Screenshot refreshed successfully
[[10:18:37]] [INFO] Loading steps for multiStep action: Click_Paginations
[[10:18:37]] [INFO] aqs7O0Yq2p=running
[[10:18:37]] [INFO] Executing action 322/351: Execute Test Case: Click_Paginations (10 steps)
[[10:18:36]] [SUCCESS] Screenshot refreshed
[[10:18:36]] [INFO] Refreshing screenshot...
[[10:18:36]] [INFO] IL6kON0uQ9=pass
[[10:18:32]] [SUCCESS] Screenshot refreshed successfully
[[10:18:32]] [SUCCESS] Screenshot refreshed successfully
[[10:18:31]] [INFO] IL6kON0uQ9=running
[[10:18:31]] [INFO] Executing action 321/351: iOS Function: text - Text: "kids toys"
[[10:18:31]] [SUCCESS] Screenshot refreshed
[[10:18:31]] [INFO] Refreshing screenshot...
[[10:18:31]] [INFO] 6G6P3UE7Uy=pass
[[10:18:26]] [SUCCESS] Screenshot refreshed successfully
[[10:18:26]] [SUCCESS] Screenshot refreshed successfully
[[10:18:25]] [INFO] 6G6P3UE7Uy=running
[[10:18:25]] [INFO] Executing action 320/351: Tap on Text: "Find"
[[10:18:25]] [SUCCESS] Screenshot refreshed
[[10:18:25]] [INFO] Refreshing screenshot...
[[10:18:25]] [INFO] 7xs3GiydGF=pass
[[10:18:21]] [SUCCESS] Screenshot refreshed successfully
[[10:18:21]] [SUCCESS] Screenshot refreshed successfully
[[10:18:20]] [INFO] 7xs3GiydGF=running
[[10:18:20]] [INFO] Executing action 319/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[10:18:20]] [SUCCESS] Screenshot refreshed
[[10:18:20]] [INFO] Refreshing screenshot...
[[10:18:20]] [INFO] OR0SKKnFxy=pass
[[10:18:15]] [SUCCESS] Screenshot refreshed successfully
[[10:18:15]] [SUCCESS] Screenshot refreshed successfully
[[10:18:15]] [INFO] OR0SKKnFxy=running
[[10:18:15]] [INFO] Executing action 318/351: Restart app: env[appid]
[[10:18:14]] [SUCCESS] Screenshot refreshed
[[10:18:14]] [INFO] Refreshing screenshot...
[[10:18:14]] [INFO] Vy3WZ0LTJF=pass
[[10:18:08]] [SUCCESS] Screenshot refreshed successfully
[[10:18:08]] [SUCCESS] Screenshot refreshed successfully
[[10:18:08]] [INFO] Vy3WZ0LTJF=running
[[10:18:08]] [INFO] Executing action 317/351: Tap on Text: "Done"
[[10:18:07]] [SUCCESS] Screenshot refreshed
[[10:18:07]] [INFO] Refreshing screenshot...
[[10:18:07]] [INFO] iDtcdR3nSL=pass
[[10:17:53]] [SUCCESS] Screenshot refreshed successfully
[[10:17:53]] [SUCCESS] Screenshot refreshed successfully
[[10:17:53]] [INFO] iDtcdR3nSL=running
[[10:17:53]] [INFO] Executing action 316/351: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"]
[[10:17:53]] [SUCCESS] Screenshot refreshed
[[10:17:53]] [INFO] Refreshing screenshot...
[[10:17:53]] [INFO] bQrT7FZsxl=pass
[[10:17:46]] [INFO] bQrT7FZsxl=running
[[10:17:46]] [INFO] Executing action 315/351: Swipe from (50%, 70%) to (50%, 30%)
[[10:17:46]] [SUCCESS] Screenshot refreshed successfully
[[10:17:46]] [SUCCESS] Screenshot refreshed successfully
[[10:17:46]] [SUCCESS] Screenshot refreshed
[[10:17:46]] [INFO] Refreshing screenshot...
[[10:17:46]] [INFO] 9Jhn4eWZwR=pass
[[10:17:42]] [SUCCESS] Screenshot refreshed successfully
[[10:17:42]] [SUCCESS] Screenshot refreshed successfully
[[10:17:42]] [INFO] 9Jhn4eWZwR=running
[[10:17:42]] [INFO] Executing action 314/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:17:42]] [SUCCESS] Screenshot refreshed
[[10:17:42]] [INFO] Refreshing screenshot...
[[10:17:42]] [INFO] yNAxs8bgMy=pass
[[10:17:37]] [SUCCESS] Screenshot refreshed successfully
[[10:17:37]] [SUCCESS] Screenshot refreshed successfully
[[10:17:37]] [INFO] yNAxs8bgMy=running
[[10:17:37]] [INFO] Executing action 313/351: iOS Function: text - Text: "P_43515028"
[[10:17:37]] [SUCCESS] Screenshot refreshed
[[10:17:37]] [INFO] Refreshing screenshot...
[[10:17:37]] [INFO] 6G6P3UE7Uy=pass
[[10:17:32]] [SUCCESS] Screenshot refreshed successfully
[[10:17:32]] [SUCCESS] Screenshot refreshed successfully
[[10:17:31]] [INFO] 6G6P3UE7Uy=running
[[10:17:31]] [INFO] Executing action 312/351: Tap on Text: "Find"
[[10:17:30]] [SUCCESS] Screenshot refreshed
[[10:17:30]] [INFO] Refreshing screenshot...
[[10:17:30]] [INFO] OR0SKKnFxy=pass
[[10:17:15]] [SUCCESS] Screenshot refreshed successfully
[[10:17:15]] [SUCCESS] Screenshot refreshed successfully
[[10:17:15]] [INFO] OR0SKKnFxy=running
[[10:17:15]] [INFO] Executing action 311/351: Restart app: env[appid]
[[10:17:15]] [SUCCESS] Screenshot refreshed
[[10:17:15]] [INFO] Refreshing screenshot...
[[10:17:14]] [SUCCESS] Screenshot refreshed
[[10:17:14]] [INFO] Refreshing screenshot...
[[10:17:13]] [SUCCESS] Screenshot refreshed successfully
[[10:17:13]] [SUCCESS] Screenshot refreshed successfully
[[10:17:13]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:17:12]] [SUCCESS] Screenshot refreshed
[[10:17:12]] [INFO] Refreshing screenshot...
[[10:17:01]] [SUCCESS] Screenshot refreshed successfully
[[10:17:01]] [SUCCESS] Screenshot refreshed successfully
[[10:17:01]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:17:00]] [SUCCESS] Screenshot refreshed
[[10:17:00]] [INFO] Refreshing screenshot...
[[10:16:56]] [SUCCESS] Screenshot refreshed successfully
[[10:16:56]] [SUCCESS] Screenshot refreshed successfully
[[10:16:56]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:16:56]] [SUCCESS] Screenshot refreshed
[[10:16:56]] [INFO] Refreshing screenshot...
[[10:16:53]] [SUCCESS] Screenshot refreshed successfully
[[10:16:53]] [SUCCESS] Screenshot refreshed successfully
[[10:16:52]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:16:52]] [SUCCESS] Screenshot refreshed
[[10:16:52]] [INFO] Refreshing screenshot...
[[10:16:45]] [SUCCESS] Screenshot refreshed successfully
[[10:16:45]] [SUCCESS] Screenshot refreshed successfully
[[10:16:45]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:16:44]] [SUCCESS] Screenshot refreshed
[[10:16:44]] [INFO] Refreshing screenshot...
[[10:16:37]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[10:16:37]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[10:16:36]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[10:16:36]] [INFO] bUr5BliEHt=running
[[10:16:36]] [INFO] Executing action 310/351: cleanupSteps action
[[10:16:36]] [SUCCESS] Screenshot refreshed successfully
[[10:16:36]] [SUCCESS] Screenshot refreshed successfully
[[10:16:36]] [SUCCESS] Screenshot refreshed
[[10:16:36]] [INFO] Refreshing screenshot...
[[10:16:36]] [INFO] K0c1gL9UK1=pass
[[10:16:32]] [SUCCESS] Screenshot refreshed successfully
[[10:16:32]] [SUCCESS] Screenshot refreshed successfully
[[10:16:31]] [INFO] K0c1gL9UK1=running
[[10:16:31]] [INFO] Executing action 309/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[10:16:31]] [SUCCESS] Screenshot refreshed
[[10:16:31]] [INFO] Refreshing screenshot...
[[10:16:31]] [INFO] IW6uAwdtiW=pass
[[10:16:27]] [SUCCESS] Screenshot refreshed successfully
[[10:16:27]] [SUCCESS] Screenshot refreshed successfully
[[10:16:27]] [INFO] IW6uAwdtiW=running
[[10:16:27]] [INFO] Executing action 308/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[10:16:26]] [SUCCESS] Screenshot refreshed
[[10:16:26]] [INFO] Refreshing screenshot...
[[10:16:26]] [INFO] DbM0d0m6rU=pass
[[10:16:23]] [SUCCESS] Screenshot refreshed successfully
[[10:16:23]] [SUCCESS] Screenshot refreshed successfully
[[10:16:23]] [INFO] DbM0d0m6rU=running
[[10:16:23]] [INFO] Executing action 307/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[10:16:22]] [SUCCESS] Screenshot refreshed
[[10:16:22]] [INFO] Refreshing screenshot...
[[10:16:22]] [INFO] UpUSVInizv=pass
[[10:16:19]] [SUCCESS] Screenshot refreshed successfully
[[10:16:19]] [SUCCESS] Screenshot refreshed successfully
[[10:16:18]] [INFO] UpUSVInizv=running
[[10:16:18]] [INFO] Executing action 306/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[10:16:18]] [SUCCESS] Screenshot refreshed
[[10:16:18]] [INFO] Refreshing screenshot...
[[10:16:18]] [INFO] c4T3INQkzn=pass
[[10:16:13]] [SUCCESS] Screenshot refreshed successfully
[[10:16:13]] [SUCCESS] Screenshot refreshed successfully
[[10:16:12]] [INFO] c4T3INQkzn=running
[[10:16:12]] [INFO] Executing action 305/351: Restart app: env[appid]
[[10:16:12]] [SUCCESS] Screenshot refreshed
[[10:16:12]] [INFO] Refreshing screenshot...
[[10:16:12]] [INFO] Iab9zCfpqO=pass
[[10:16:05]] [SUCCESS] Screenshot refreshed successfully
[[10:16:05]] [SUCCESS] Screenshot refreshed successfully
[[10:16:05]] [INFO] Iab9zCfpqO=running
[[10:16:05]] [INFO] Executing action 304/351: Tap on element with accessibility_id: Add to bag
[[10:16:04]] [SUCCESS] Screenshot refreshed
[[10:16:04]] [INFO] Refreshing screenshot...
[[10:16:04]] [INFO] n57KEWjTea=pass
[[10:16:00]] [SUCCESS] Screenshot refreshed successfully
[[10:16:00]] [SUCCESS] Screenshot refreshed successfully
[[10:16:00]] [INFO] n57KEWjTea=running
[[10:16:00]] [INFO] Executing action 303/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:15:59]] [SUCCESS] Screenshot refreshed
[[10:15:59]] [INFO] Refreshing screenshot...
[[10:15:59]] [INFO] n57KEWjTea=pass
[[10:15:56]] [SUCCESS] Screenshot refreshed successfully
[[10:15:56]] [SUCCESS] Screenshot refreshed successfully
[[10:15:55]] [INFO] n57KEWjTea=running
[[10:15:55]] [INFO] Executing action 302/351: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:15:55]] [SUCCESS] Screenshot refreshed
[[10:15:55]] [INFO] Refreshing screenshot...
[[10:15:55]] [INFO] UZkF5rnoLo=pass
[[10:15:51]] [SUCCESS] Screenshot refreshed successfully
[[10:15:51]] [SUCCESS] Screenshot refreshed successfully
[[10:15:50]] [INFO] UZkF5rnoLo=running
[[10:15:50]] [INFO] Executing action 301/351: iOS Function: text - Text: "notebook"
[[10:15:50]] [SUCCESS] Screenshot refreshed
[[10:15:50]] [INFO] Refreshing screenshot...
[[10:15:50]] [INFO] OKiI82VdnE=pass
[[10:15:45]] [SUCCESS] Screenshot refreshed successfully
[[10:15:45]] [SUCCESS] Screenshot refreshed successfully
[[10:15:44]] [INFO] OKiI82VdnE=running
[[10:15:44]] [INFO] Executing action 300/351: Tap on Text: "Find"
[[10:15:44]] [SUCCESS] Screenshot refreshed
[[10:15:44]] [INFO] Refreshing screenshot...
[[10:15:44]] [INFO] 3KNqlNy6Bj=pass
[[10:15:40]] [SUCCESS] Screenshot refreshed successfully
[[10:15:40]] [SUCCESS] Screenshot refreshed successfully
[[10:15:39]] [INFO] 3KNqlNy6Bj=running
[[10:15:39]] [INFO] Executing action 299/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[10:15:39]] [SUCCESS] Screenshot refreshed
[[10:15:39]] [INFO] Refreshing screenshot...
[[10:15:39]] [INFO] fTdGMJ3NH3=pass
[[10:15:35]] [SUCCESS] Screenshot refreshed successfully
[[10:15:35]] [SUCCESS] Screenshot refreshed successfully
[[10:15:35]] [INFO] fTdGMJ3NH3=running
[[10:15:35]] [INFO] Executing action 298/351: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.co.nz"]
[[10:15:34]] [SUCCESS] Screenshot refreshed
[[10:15:34]] [INFO] Refreshing screenshot...
[[10:15:34]] [INFO] ISpNHH3V9g=pass
[[10:15:31]] [INFO] ISpNHH3V9g=running
[[10:15:31]] [INFO] Executing action 297/351: iOS Function: text - Text: "kmart nz"
[[10:15:31]] [SUCCESS] Screenshot refreshed successfully
[[10:15:31]] [SUCCESS] Screenshot refreshed successfully
[[10:15:30]] [SUCCESS] Screenshot refreshed
[[10:15:30]] [INFO] Refreshing screenshot...
[[10:15:30]] [INFO] 0Q0fm6OTij=pass
[[10:15:28]] [SUCCESS] Screenshot refreshed successfully
[[10:15:28]] [SUCCESS] Screenshot refreshed successfully
[[10:15:27]] [INFO] 0Q0fm6OTij=running
[[10:15:27]] [INFO] Executing action 296/351: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[10:15:27]] [SUCCESS] Screenshot refreshed
[[10:15:27]] [INFO] Refreshing screenshot...
[[10:15:27]] [INFO] xVuuejtCFA=pass
[[10:15:22]] [SUCCESS] Screenshot refreshed successfully
[[10:15:22]] [SUCCESS] Screenshot refreshed successfully
[[10:15:22]] [INFO] xVuuejtCFA=running
[[10:15:22]] [INFO] Executing action 295/351: Restart app: com.apple.mobilesafari
[[10:15:21]] [SUCCESS] Screenshot refreshed
[[10:15:21]] [INFO] Refreshing screenshot...
[[10:15:21]] [INFO] LcYLwUffqj=pass
[[10:15:16]] [SUCCESS] Screenshot refreshed successfully
[[10:15:16]] [SUCCESS] Screenshot refreshed successfully
[[10:15:16]] [INFO] LcYLwUffqj=running
[[10:15:16]] [INFO] Executing action 294/351: Tap on Text: "out"
[[10:15:16]] [SUCCESS] Screenshot refreshed
[[10:15:16]] [INFO] Refreshing screenshot...
[[10:15:16]] [INFO] oqTdx3vL0d=pass
[[10:15:11]] [SUCCESS] Screenshot refreshed successfully
[[10:15:11]] [SUCCESS] Screenshot refreshed successfully
[[10:15:11]] [INFO] oqTdx3vL0d=running
[[10:15:11]] [INFO] Executing action 293/351: Swipe from (50%, 70%) to (50%, 30%)
[[10:15:11]] [SUCCESS] Screenshot refreshed
[[10:15:11]] [INFO] Refreshing screenshot...
[[10:15:11]] [INFO] UpUSVInizv=pass
[[10:15:07]] [SUCCESS] Screenshot refreshed successfully
[[10:15:07]] [SUCCESS] Screenshot refreshed successfully
[[10:15:07]] [INFO] UpUSVInizv=running
[[10:15:07]] [INFO] Executing action 292/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[10:15:06]] [SUCCESS] Screenshot refreshed
[[10:15:06]] [INFO] Refreshing screenshot...
[[10:15:06]] [INFO] hCCEvRtj1A=pass
[[10:15:01]] [INFO] hCCEvRtj1A=running
[[10:15:01]] [INFO] Executing action 291/351: Restart app: env[appid]
[[10:15:01]] [SUCCESS] Screenshot refreshed successfully
[[10:15:01]] [SUCCESS] Screenshot refreshed successfully
[[10:15:00]] [SUCCESS] Screenshot refreshed
[[10:15:00]] [INFO] Refreshing screenshot...
[[10:15:00]] [INFO] V42eHtTRYW=pass
[[10:14:54]] [INFO] V42eHtTRYW=running
[[10:14:54]] [INFO] Executing action 290/351: Wait for 5 ms
[[10:14:53]] [SUCCESS] Screenshot refreshed successfully
[[10:14:53]] [SUCCESS] Screenshot refreshed successfully
[[10:14:53]] [SUCCESS] Screenshot refreshed
[[10:14:53]] [INFO] Refreshing screenshot...
[[10:14:53]] [INFO] GRwHMVK4sA=pass
[[10:14:51]] [INFO] GRwHMVK4sA=running
[[10:14:51]] [INFO] Executing action 289/351: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[10:14:51]] [SUCCESS] Screenshot refreshed successfully
[[10:14:51]] [SUCCESS] Screenshot refreshed successfully
[[10:14:50]] [SUCCESS] Screenshot refreshed
[[10:14:50]] [INFO] Refreshing screenshot...
[[10:14:50]] [INFO] LfyQctrEJn=pass
[[10:14:48]] [SUCCESS] Screenshot refreshed successfully
[[10:14:48]] [SUCCESS] Screenshot refreshed successfully
[[10:14:48]] [INFO] LfyQctrEJn=running
[[10:14:48]] [INFO] Executing action 288/351: Launch app: com.apple.Preferences
[[10:14:48]] [SUCCESS] Screenshot refreshed
[[10:14:48]] [INFO] Refreshing screenshot...
[[10:14:48]] [INFO] seQcUKjkSU=pass
[[10:14:46]] [SUCCESS] Screenshot refreshed successfully
[[10:14:46]] [SUCCESS] Screenshot refreshed successfully
[[10:14:46]] [INFO] seQcUKjkSU=running
[[10:14:46]] [INFO] Executing action 287/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:14:45]] [SUCCESS] Screenshot refreshed
[[10:14:45]] [INFO] Refreshing screenshot...
[[10:14:45]] [INFO] UpUSVInizv=pass
[[10:14:43]] [SUCCESS] Screenshot refreshed successfully
[[10:14:43]] [SUCCESS] Screenshot refreshed successfully
[[10:14:43]] [INFO] UpUSVInizv=running
[[10:14:43]] [INFO] Executing action 286/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[10:14:42]] [SUCCESS] Screenshot refreshed
[[10:14:42]] [INFO] Refreshing screenshot...
[[10:14:42]] [INFO] WoymrHdtrO=pass
[[10:14:40]] [SUCCESS] Screenshot refreshed successfully
[[10:14:40]] [SUCCESS] Screenshot refreshed successfully
[[10:14:40]] [INFO] WoymrHdtrO=running
[[10:14:40]] [INFO] Executing action 285/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:14:40]] [SUCCESS] Screenshot refreshed
[[10:14:40]] [INFO] Refreshing screenshot...
[[10:14:40]] [INFO] 6xgrAWyfZ4=pass
[[10:14:37]] [SUCCESS] Screenshot refreshed successfully
[[10:14:37]] [SUCCESS] Screenshot refreshed successfully
[[10:14:37]] [INFO] 6xgrAWyfZ4=running
[[10:14:37]] [INFO] Executing action 284/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[10:14:37]] [SUCCESS] Screenshot refreshed
[[10:14:37]] [INFO] Refreshing screenshot...
[[10:14:37]] [INFO] eSr9EFlJek=pass
[[10:14:35]] [SUCCESS] Screenshot refreshed successfully
[[10:14:35]] [SUCCESS] Screenshot refreshed successfully
[[10:14:34]] [INFO] eSr9EFlJek=running
[[10:14:34]] [INFO] Executing action 283/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:14:34]] [SUCCESS] Screenshot refreshed
[[10:14:34]] [INFO] Refreshing screenshot...
[[10:14:34]] [INFO] 3KNqlNy6Bj=pass
[[10:14:31]] [SUCCESS] Screenshot refreshed successfully
[[10:14:31]] [SUCCESS] Screenshot refreshed successfully
[[10:14:31]] [INFO] 3KNqlNy6Bj=running
[[10:14:31]] [INFO] Executing action 282/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[10:14:31]] [SUCCESS] Screenshot refreshed
[[10:14:31]] [INFO] Refreshing screenshot...
[[10:14:31]] [INFO] cokvFXhj4c=pass
[[10:14:29]] [SUCCESS] Screenshot refreshed successfully
[[10:14:29]] [SUCCESS] Screenshot refreshed successfully
[[10:14:29]] [INFO] cokvFXhj4c=running
[[10:14:29]] [INFO] Executing action 281/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:14:28]] [SUCCESS] Screenshot refreshed
[[10:14:28]] [INFO] Refreshing screenshot...
[[10:14:28]] [INFO] oSQ8sPdVOJ=pass
[[10:14:23]] [INFO] oSQ8sPdVOJ=running
[[10:14:23]] [INFO] Executing action 280/351: Restart app: env[appid]
[[10:14:23]] [SUCCESS] Screenshot refreshed successfully
[[10:14:23]] [SUCCESS] Screenshot refreshed successfully
[[10:14:22]] [SUCCESS] Screenshot refreshed
[[10:14:22]] [INFO] Refreshing screenshot...
[[10:14:22]] [INFO] jUCAk6GJc4=pass
[[10:14:20]] [INFO] jUCAk6GJc4=running
[[10:14:20]] [INFO] Executing action 279/351: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[10:14:20]] [SUCCESS] Screenshot refreshed successfully
[[10:14:20]] [SUCCESS] Screenshot refreshed successfully
[[10:14:19]] [SUCCESS] Screenshot refreshed
[[10:14:19]] [INFO] Refreshing screenshot...
[[10:14:19]] [INFO] w1RV76df9x=pass
[[10:14:15]] [INFO] w1RV76df9x=running
[[10:14:15]] [INFO] Executing action 278/351: Tap on Text: "Wi-Fi"
[[10:14:14]] [SUCCESS] Screenshot refreshed successfully
[[10:14:14]] [SUCCESS] Screenshot refreshed successfully
[[10:14:14]] [SUCCESS] Screenshot refreshed
[[10:14:14]] [INFO] Refreshing screenshot...
[[10:14:14]] [INFO] LfyQctrEJn=pass
[[10:14:12]] [SUCCESS] Screenshot refreshed successfully
[[10:14:12]] [SUCCESS] Screenshot refreshed successfully
[[10:14:11]] [INFO] LfyQctrEJn=running
[[10:14:11]] [INFO] Executing action 277/351: Launch app: com.apple.Preferences
[[10:14:11]] [SUCCESS] Screenshot refreshed
[[10:14:11]] [INFO] Refreshing screenshot...
[[10:14:11]] [INFO] mIKA85kXaW=pass
[[10:14:08]] [SUCCESS] Screenshot refreshed successfully
[[10:14:08]] [SUCCESS] Screenshot refreshed successfully
[[10:14:07]] [INFO] mIKA85kXaW=running
[[10:14:07]] [INFO] Executing action 276/351: Terminate app: com.apple.Preferences
[[10:14:07]] [SUCCESS] Screenshot refreshed
[[10:14:07]] [INFO] Refreshing screenshot...
[[10:14:07]] [SUCCESS] Screenshot refreshed
[[10:14:07]] [INFO] Refreshing screenshot...
[[10:14:01]] [SUCCESS] Screenshot refreshed successfully
[[10:14:01]] [SUCCESS] Screenshot refreshed successfully
[[10:14:00]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:14:00]] [SUCCESS] Screenshot refreshed
[[10:14:00]] [INFO] Refreshing screenshot...
[[10:13:56]] [SUCCESS] Screenshot refreshed successfully
[[10:13:56]] [SUCCESS] Screenshot refreshed successfully
[[10:13:56]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:13:55]] [SUCCESS] Screenshot refreshed
[[10:13:55]] [INFO] Refreshing screenshot...
[[10:13:51]] [SUCCESS] Screenshot refreshed successfully
[[10:13:51]] [SUCCESS] Screenshot refreshed successfully
[[10:13:51]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:13:50]] [SUCCESS] Screenshot refreshed
[[10:13:50]] [INFO] Refreshing screenshot...
[[10:13:47]] [SUCCESS] Screenshot refreshed successfully
[[10:13:47]] [SUCCESS] Screenshot refreshed successfully
[[10:13:46]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:13:46]] [SUCCESS] Screenshot refreshed
[[10:13:46]] [INFO] Refreshing screenshot...
[[10:13:40]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:13:40]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[10:13:40]] [SUCCESS] Screenshot refreshed successfully
[[10:13:40]] [SUCCESS] Screenshot refreshed successfully
[[10:13:40]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[10:13:40]] [INFO] a12JYjIm66=running
[[10:13:40]] [INFO] Executing action 275/351: Execute Test Case: Kmart-NZ-Signin (6 steps)
[[10:13:39]] [SUCCESS] Screenshot refreshed
[[10:13:39]] [INFO] Refreshing screenshot...
[[10:13:39]] [INFO] rJ86z4njuR=pass
[[10:13:37]] [SUCCESS] Screenshot refreshed successfully
[[10:13:37]] [SUCCESS] Screenshot refreshed successfully
[[10:13:36]] [INFO] rJ86z4njuR=running
[[10:13:36]] [INFO] Executing action 274/351: iOS Function: alert_accept
[[10:13:36]] [SUCCESS] Screenshot refreshed
[[10:13:36]] [INFO] Refreshing screenshot...
[[10:13:36]] [INFO] veukWo4573=pass
[[10:13:31]] [SUCCESS] Screenshot refreshed successfully
[[10:13:31]] [SUCCESS] Screenshot refreshed successfully
[[10:13:31]] [INFO] veukWo4573=running
[[10:13:31]] [INFO] Executing action 273/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[10:13:30]] [SUCCESS] Screenshot refreshed
[[10:13:30]] [INFO] Refreshing screenshot...
[[10:13:30]] [INFO] XEbZHdi0GT=pass
[[10:13:15]] [SUCCESS] Screenshot refreshed successfully
[[10:13:15]] [SUCCESS] Screenshot refreshed successfully
[[10:13:15]] [INFO] XEbZHdi0GT=running
[[10:13:15]] [INFO] Executing action 272/351: Restart app: env[appid]
[[10:13:15]] [SUCCESS] Screenshot refreshed
[[10:13:15]] [INFO] Refreshing screenshot...
[[10:13:14]] [SUCCESS] Screenshot refreshed
[[10:13:14]] [INFO] Refreshing screenshot...
[[10:13:13]] [SUCCESS] Screenshot refreshed successfully
[[10:13:13]] [SUCCESS] Screenshot refreshed successfully
[[10:13:13]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:13:12]] [SUCCESS] Screenshot refreshed
[[10:13:12]] [INFO] Refreshing screenshot...
[[10:13:00]] [SUCCESS] Screenshot refreshed successfully
[[10:13:00]] [SUCCESS] Screenshot refreshed successfully
[[10:13:00]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:12:59]] [SUCCESS] Screenshot refreshed
[[10:12:59]] [INFO] Refreshing screenshot...
[[10:12:56]] [SUCCESS] Screenshot refreshed successfully
[[10:12:56]] [SUCCESS] Screenshot refreshed successfully
[[10:12:56]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:12:55]] [SUCCESS] Screenshot refreshed
[[10:12:55]] [INFO] Refreshing screenshot...
[[10:12:52]] [SUCCESS] Screenshot refreshed successfully
[[10:12:52]] [SUCCESS] Screenshot refreshed successfully
[[10:12:52]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:12:51]] [SUCCESS] Screenshot refreshed
[[10:12:51]] [INFO] Refreshing screenshot...
[[10:12:45]] [SUCCESS] Screenshot refreshed successfully
[[10:12:45]] [SUCCESS] Screenshot refreshed successfully
[[10:12:44]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:12:44]] [SUCCESS] Screenshot refreshed
[[10:12:44]] [INFO] Refreshing screenshot...
[[10:12:37]] [SUCCESS] Screenshot refreshed successfully
[[10:12:37]] [SUCCESS] Screenshot refreshed successfully
[[10:12:36]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[10:12:36]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[10:12:36]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[10:12:36]] [INFO] 2L5CHzd7qs=running
[[10:12:36]] [INFO] Executing action 271/351: cleanupSteps action
[[10:12:35]] [SUCCESS] Screenshot refreshed
[[10:12:35]] [INFO] Refreshing screenshot...
[[10:12:35]] [INFO] DGzGrOfbSq=pass
[[10:12:31]] [SUCCESS] Screenshot refreshed successfully
[[10:12:31]] [SUCCESS] Screenshot refreshed successfully
[[10:12:31]] [INFO] DGzGrOfbSq=running
[[10:12:31]] [INFO] Executing action 270/351: Swipe from (50%, 70%) to (50%, 10%)
[[10:12:30]] [SUCCESS] Screenshot refreshed
[[10:12:30]] [INFO] Refreshing screenshot...
[[10:12:30]] [INFO] OyUowAaBzD=pass
[[10:12:26]] [SUCCESS] Screenshot refreshed successfully
[[10:12:26]] [SUCCESS] Screenshot refreshed successfully
[[10:12:26]] [INFO] OyUowAaBzD=running
[[10:12:26]] [INFO] Executing action 269/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:12:25]] [SUCCESS] Screenshot refreshed
[[10:12:25]] [INFO] Refreshing screenshot...
[[10:12:25]] [INFO] jIeR7BPEPu=pass
[[10:12:21]] [SUCCESS] Screenshot refreshed successfully
[[10:12:21]] [SUCCESS] Screenshot refreshed successfully
[[10:12:21]] [INFO] jIeR7BPEPu=running
[[10:12:21]] [INFO] Executing action 268/351: Swipe from (50%, 70%) to (50%, 30%)
[[10:12:20]] [SUCCESS] Screenshot refreshed
[[10:12:20]] [INFO] Refreshing screenshot...
[[10:12:20]] [INFO] k3mu9Mt7Ec=pass
[[10:12:17]] [SUCCESS] Screenshot refreshed successfully
[[10:12:17]] [SUCCESS] Screenshot refreshed successfully
[[10:12:16]] [INFO] k3mu9Mt7Ec=running
[[10:12:16]] [INFO] Executing action 267/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:12:16]] [SUCCESS] Screenshot refreshed
[[10:12:16]] [INFO] Refreshing screenshot...
[[10:12:16]] [INFO] yhmzeynQyu=pass
[[10:12:12]] [SUCCESS] Screenshot refreshed successfully
[[10:12:12]] [SUCCESS] Screenshot refreshed successfully
[[10:12:11]] [INFO] yhmzeynQyu=running
[[10:12:11]] [INFO] Executing action 266/351: Tap on Text: "Remove"
[[10:12:11]] [SUCCESS] Screenshot refreshed
[[10:12:11]] [INFO] Refreshing screenshot...
[[10:12:11]] [INFO] Q0fomJIDoQ=pass
[[10:12:06]] [SUCCESS] Screenshot refreshed successfully
[[10:12:06]] [SUCCESS] Screenshot refreshed successfully
[[10:12:06]] [INFO] Q0fomJIDoQ=running
[[10:12:06]] [INFO] Executing action 265/351: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[10:12:05]] [SUCCESS] Screenshot refreshed
[[10:12:05]] [INFO] Refreshing screenshot...
[[10:12:05]] [INFO] yhmzeynQyu=pass
[[10:12:00]] [SUCCESS] Screenshot refreshed successfully
[[10:12:00]] [SUCCESS] Screenshot refreshed successfully
[[10:12:00]] [INFO] yhmzeynQyu=running
[[10:12:00]] [INFO] Executing action 264/351: Tap on Text: "Remove"
[[10:11:59]] [SUCCESS] Screenshot refreshed
[[10:11:59]] [INFO] Refreshing screenshot...
[[10:11:59]] [INFO] Q0fomJIDoQ=pass
[[10:11:55]] [SUCCESS] Screenshot refreshed successfully
[[10:11:55]] [SUCCESS] Screenshot refreshed successfully
[[10:11:54]] [INFO] Q0fomJIDoQ=running
[[10:11:54]] [INFO] Executing action 263/351: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[10:11:54]] [SUCCESS] Screenshot refreshed
[[10:11:54]] [INFO] Refreshing screenshot...
[[10:11:54]] [INFO] F1olhgKhUt=pass
[[10:11:50]] [SUCCESS] Screenshot refreshed successfully
[[10:11:50]] [SUCCESS] Screenshot refreshed successfully
[[10:11:50]] [INFO] F1olhgKhUt=running
[[10:11:50]] [INFO] Executing action 262/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[10:11:49]] [SUCCESS] Screenshot refreshed
[[10:11:49]] [INFO] Refreshing screenshot...
[[10:11:49]] [INFO] 8umPSX0vrr=pass
[[10:11:45]] [INFO] 8umPSX0vrr=running
[[10:11:45]] [INFO] Executing action 261/351: Tap on image: banner-close-updated.png
[[10:11:45]] [SUCCESS] Screenshot refreshed successfully
[[10:11:45]] [SUCCESS] Screenshot refreshed successfully
[[10:11:44]] [SUCCESS] Screenshot refreshed
[[10:11:44]] [INFO] Refreshing screenshot...
[[10:11:44]] [INFO] pr9o8Zsm5p=pass
[[10:11:40]] [SUCCESS] Screenshot refreshed successfully
[[10:11:40]] [SUCCESS] Screenshot refreshed successfully
[[10:11:40]] [INFO] pr9o8Zsm5p=running
[[10:11:40]] [INFO] Executing action 260/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[10:11:40]] [SUCCESS] Screenshot refreshed
[[10:11:40]] [INFO] Refreshing screenshot...
[[10:11:40]] [INFO] Qbg9bipTGs=pass
[[10:11:36]] [SUCCESS] Screenshot refreshed successfully
[[10:11:36]] [SUCCESS] Screenshot refreshed successfully
[[10:11:36]] [INFO] Qbg9bipTGs=running
[[10:11:36]] [INFO] Executing action 259/351: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[10:11:36]] [SUCCESS] Screenshot refreshed
[[10:11:36]] [INFO] Refreshing screenshot...
[[10:11:36]] [INFO] jIeR7BPEPu=pass
[[10:11:29]] [SUCCESS] Screenshot refreshed successfully
[[10:11:29]] [SUCCESS] Screenshot refreshed successfully
[[10:11:29]] [INFO] jIeR7BPEPu=running
[[10:11:29]] [INFO] Executing action 258/351: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Move to wishlist"]" is visible
[[10:11:28]] [SUCCESS] Screenshot refreshed
[[10:11:28]] [INFO] Refreshing screenshot...
[[10:11:28]] [INFO] lWIRxRm6HE=pass
[[10:11:25]] [SUCCESS] Screenshot refreshed successfully
[[10:11:25]] [SUCCESS] Screenshot refreshed successfully
[[10:11:24]] [INFO] lWIRxRm6HE=running
[[10:11:24]] [INFO] Executing action 257/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[10:11:24]] [SUCCESS] Screenshot refreshed
[[10:11:24]] [INFO] Refreshing screenshot...
[[10:11:24]] [INFO] uOt2cFGhGr=pass
[[10:11:19]] [SUCCESS] Screenshot refreshed successfully
[[10:11:19]] [SUCCESS] Screenshot refreshed successfully
[[10:11:19]] [INFO] uOt2cFGhGr=running
[[10:11:19]] [INFO] Executing action 256/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[10:11:19]] [SUCCESS] Screenshot refreshed
[[10:11:19]] [INFO] Refreshing screenshot...
[[10:11:19]] [INFO] Q0fomJIDoQ=pass
[[10:11:15]] [SUCCESS] Screenshot refreshed successfully
[[10:11:15]] [SUCCESS] Screenshot refreshed successfully
[[10:11:15]] [INFO] Q0fomJIDoQ=running
[[10:11:15]] [INFO] Executing action 255/351: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[10:11:14]] [SUCCESS] Screenshot refreshed
[[10:11:14]] [INFO] Refreshing screenshot...
[[10:11:14]] [INFO] yhmzeynQyu=pass
[[10:11:10]] [SUCCESS] Screenshot refreshed successfully
[[10:11:10]] [SUCCESS] Screenshot refreshed successfully
[[10:11:10]] [INFO] yhmzeynQyu=running
[[10:11:10]] [INFO] Executing action 254/351: Tap on Text: "Remove"
[[10:11:09]] [SUCCESS] Screenshot refreshed
[[10:11:09]] [INFO] Refreshing screenshot...
[[10:11:09]] [INFO] YNMjmPGi1h=pass
[[10:11:02]] [SUCCESS] Screenshot refreshed successfully
[[10:11:02]] [SUCCESS] Screenshot refreshed successfully
[[10:11:02]] [INFO] YNMjmPGi1h=running
[[10:11:02]] [INFO] Executing action 253/351: Wait for 5 ms
[[10:11:02]] [SUCCESS] Screenshot refreshed
[[10:11:02]] [INFO] Refreshing screenshot...
[[10:11:02]] [INFO] Q0fomJIDoQ=pass
[[10:10:58]] [SUCCESS] Screenshot refreshed successfully
[[10:10:58]] [SUCCESS] Screenshot refreshed successfully
[[10:10:58]] [INFO] Q0fomJIDoQ=running
[[10:10:58]] [INFO] Executing action 252/351: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[10:10:57]] [SUCCESS] Screenshot refreshed
[[10:10:57]] [INFO] Refreshing screenshot...
[[10:10:57]] [INFO] y4i304JeJj=pass
[[10:10:53]] [SUCCESS] Screenshot refreshed successfully
[[10:10:53]] [SUCCESS] Screenshot refreshed successfully
[[10:10:53]] [INFO] y4i304JeJj=running
[[10:10:53]] [INFO] Executing action 251/351: Tap on Text: "Move"
[[10:10:52]] [SUCCESS] Screenshot refreshed
[[10:10:52]] [INFO] Refreshing screenshot...
[[10:10:52]] [INFO] 6pAgesZD43=pass
[[10:10:45]] [SUCCESS] Screenshot refreshed successfully
[[10:10:45]] [SUCCESS] Screenshot refreshed successfully
[[10:10:45]] [INFO] 6pAgesZD43=running
[[10:10:45]] [INFO] Executing action 250/351: Wait for 5 ms
[[10:10:45]] [SUCCESS] Screenshot refreshed
[[10:10:45]] [INFO] Refreshing screenshot...
[[10:10:45]] [INFO] Q0fomJIDoQ=pass
[[10:10:41]] [SUCCESS] Screenshot refreshed successfully
[[10:10:41]] [SUCCESS] Screenshot refreshed successfully
[[10:10:41]] [INFO] Q0fomJIDoQ=running
[[10:10:41]] [INFO] Executing action 249/351: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[10:10:40]] [SUCCESS] Screenshot refreshed
[[10:10:40]] [INFO] Refreshing screenshot...
[[10:10:40]] [INFO] Q0fomJIDoQ=pass
[[10:10:37]] [SUCCESS] Screenshot refreshed successfully
[[10:10:37]] [SUCCESS] Screenshot refreshed successfully
[[10:10:37]] [INFO] Q0fomJIDoQ=running
[[10:10:37]] [INFO] Executing action 248/351: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[10:10:37]] [SUCCESS] Screenshot refreshed
[[10:10:37]] [INFO] Refreshing screenshot...
[[10:10:37]] [INFO] F1olhgKhUt=pass
[[10:10:32]] [SUCCESS] Screenshot refreshed successfully
[[10:10:32]] [SUCCESS] Screenshot refreshed successfully
[[10:10:32]] [INFO] F1olhgKhUt=running
[[10:10:32]] [INFO] Executing action 247/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[10:10:31]] [SUCCESS] Screenshot refreshed
[[10:10:31]] [INFO] Refreshing screenshot...
[[10:10:31]] [INFO] alaozIePOy=pass
[[10:10:27]] [SUCCESS] Screenshot refreshed successfully
[[10:10:27]] [SUCCESS] Screenshot refreshed successfully
[[10:10:27]] [INFO] alaozIePOy=running
[[10:10:27]] [INFO] Executing action 246/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[10:10:26]] [SUCCESS] Screenshot refreshed
[[10:10:26]] [INFO] Refreshing screenshot...
[[10:10:26]] [INFO] BzTvnSrykE=pass
[[10:10:22]] [INFO] BzTvnSrykE=running
[[10:10:22]] [INFO] Executing action 245/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[10:10:22]] [SUCCESS] Screenshot refreshed successfully
[[10:10:22]] [SUCCESS] Screenshot refreshed successfully
[[10:10:21]] [SUCCESS] Screenshot refreshed
[[10:10:21]] [INFO] Refreshing screenshot...
[[10:10:21]] [INFO] PH8FFnzohm=pass
[[10:10:18]] [SUCCESS] Screenshot refreshed successfully
[[10:10:18]] [SUCCESS] Screenshot refreshed successfully
[[10:10:17]] [INFO] PH8FFnzohm=running
[[10:10:17]] [INFO] Executing action 244/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]
[[10:10:17]] [SUCCESS] Screenshot refreshed
[[10:10:17]] [INFO] Refreshing screenshot...
[[10:10:17]] [INFO] k404YWYgSk=pass
[[10:10:14]] [SUCCESS] Screenshot refreshed successfully
[[10:10:14]] [SUCCESS] Screenshot refreshed successfully
[[10:10:13]] [INFO] k404YWYgSk=running
[[10:10:13]] [INFO] Executing action 243/351: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]
[[10:10:13]] [SUCCESS] Screenshot refreshed
[[10:10:13]] [INFO] Refreshing screenshot...
[[10:10:13]] [INFO] Z86YU6Mq0g=pass
[[10:10:09]] [SUCCESS] Screenshot refreshed successfully
[[10:10:09]] [SUCCESS] Screenshot refreshed successfully
[[10:10:08]] [INFO] Z86YU6Mq0g=running
[[10:10:08]] [INFO] Executing action 242/351: Tap on image: env[device-back-img]
[[10:10:07]] [SUCCESS] Screenshot refreshed
[[10:10:07]] [INFO] Refreshing screenshot...
[[10:10:07]] [INFO] alaozIePOy=pass
[[10:10:03]] [SUCCESS] Screenshot refreshed successfully
[[10:10:03]] [SUCCESS] Screenshot refreshed successfully
[[10:10:02]] [INFO] alaozIePOy=running
[[10:10:02]] [INFO] Executing action 241/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[10:10:01]] [SUCCESS] Screenshot refreshed
[[10:10:01]] [INFO] Refreshing screenshot...
[[10:10:01]] [INFO] BzTvnSrykE=pass
[[10:09:57]] [INFO] BzTvnSrykE=running
[[10:09:57]] [INFO] Executing action 240/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[10:09:57]] [SUCCESS] Screenshot refreshed successfully
[[10:09:57]] [SUCCESS] Screenshot refreshed successfully
[[10:09:56]] [SUCCESS] Screenshot refreshed
[[10:09:56]] [INFO] Refreshing screenshot...
[[10:09:56]] [INFO] oWLIFhrzr1=pass
[[10:09:53]] [SUCCESS] Screenshot refreshed successfully
[[10:09:53]] [SUCCESS] Screenshot refreshed successfully
[[10:09:52]] [INFO] oWLIFhrzr1=running
[[10:09:52]] [INFO] Executing action 239/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]
[[10:09:52]] [SUCCESS] Screenshot refreshed
[[10:09:52]] [INFO] Refreshing screenshot...
[[10:09:52]] [INFO] zzd5ufNDih=pass
[[10:09:48]] [SUCCESS] Screenshot refreshed successfully
[[10:09:48]] [SUCCESS] Screenshot refreshed successfully
[[10:09:47]] [INFO] zzd5ufNDih=running
[[10:09:47]] [INFO] Executing action 238/351: Tap on image: env[device-back-img]
[[10:09:47]] [SUCCESS] Screenshot refreshed
[[10:09:47]] [INFO] Refreshing screenshot...
[[10:09:47]] [INFO] WbxRVpWtjw=pass
[[10:09:42]] [SUCCESS] Screenshot refreshed successfully
[[10:09:42]] [SUCCESS] Screenshot refreshed successfully
[[10:09:42]] [INFO] WbxRVpWtjw=running
[[10:09:42]] [INFO] Executing action 237/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[10:09:41]] [SUCCESS] Screenshot refreshed
[[10:09:41]] [INFO] Refreshing screenshot...
[[10:09:41]] [INFO] ITHvSyXXmu=pass
[[10:09:37]] [INFO] ITHvSyXXmu=running
[[10:09:37]] [INFO] Executing action 236/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[10:09:37]] [SUCCESS] Screenshot refreshed successfully
[[10:09:37]] [SUCCESS] Screenshot refreshed successfully
[[10:09:37]] [SUCCESS] Screenshot refreshed
[[10:09:37]] [INFO] Refreshing screenshot...
[[10:09:37]] [INFO] eLxHVWKeDQ=pass
[[10:09:34]] [SUCCESS] Screenshot refreshed successfully
[[10:09:34]] [SUCCESS] Screenshot refreshed successfully
[[10:09:33]] [INFO] eLxHVWKeDQ=running
[[10:09:33]] [INFO] Executing action 235/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:09:32]] [SUCCESS] Screenshot refreshed
[[10:09:32]] [INFO] Refreshing screenshot...
[[10:09:32]] [INFO] eLxHVWKeDQ=pass
[[10:09:29]] [SUCCESS] Screenshot refreshed successfully
[[10:09:29]] [SUCCESS] Screenshot refreshed successfully
[[10:09:28]] [INFO] eLxHVWKeDQ=running
[[10:09:28]] [INFO] Executing action 234/351: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:09:28]] [SUCCESS] Screenshot refreshed
[[10:09:28]] [INFO] Refreshing screenshot...
[[10:09:28]] [INFO] nAB6Q8LAdv=pass
[[10:09:24]] [SUCCESS] Screenshot refreshed successfully
[[10:09:24]] [SUCCESS] Screenshot refreshed successfully
[[10:09:24]] [INFO] nAB6Q8LAdv=running
[[10:09:24]] [INFO] Executing action 233/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[10:09:23]] [SUCCESS] Screenshot refreshed
[[10:09:23]] [INFO] Refreshing screenshot...
[[10:09:23]] [INFO] sc2KH9bG6H=pass
[[10:09:19]] [SUCCESS] Screenshot refreshed successfully
[[10:09:19]] [SUCCESS] Screenshot refreshed successfully
[[10:09:19]] [INFO] sc2KH9bG6H=running
[[10:09:19]] [INFO] Executing action 232/351: iOS Function: text - Text: "Uno card"
[[10:09:18]] [SUCCESS] Screenshot refreshed
[[10:09:18]] [INFO] Refreshing screenshot...
[[10:09:18]] [INFO] rqLJpAP0mA=pass
[[10:09:14]] [SUCCESS] Screenshot refreshed successfully
[[10:09:14]] [SUCCESS] Screenshot refreshed successfully
[[10:09:13]] [INFO] rqLJpAP0mA=running
[[10:09:13]] [INFO] Executing action 231/351: Tap on Text: "Find"
[[10:09:13]] [SUCCESS] Screenshot refreshed
[[10:09:13]] [INFO] Refreshing screenshot...
[[10:09:12]] [SUCCESS] Screenshot refreshed
[[10:09:12]] [INFO] Refreshing screenshot...
[[10:09:07]] [SUCCESS] Screenshot refreshed successfully
[[10:09:07]] [SUCCESS] Screenshot refreshed successfully
[[10:09:07]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:09:07]] [SUCCESS] Screenshot refreshed
[[10:09:07]] [INFO] Refreshing screenshot...
[[10:09:02]] [SUCCESS] Screenshot refreshed successfully
[[10:09:02]] [SUCCESS] Screenshot refreshed successfully
[[10:09:02]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:09:02]] [SUCCESS] Screenshot refreshed
[[10:09:02]] [INFO] Refreshing screenshot...
[[10:08:57]] [SUCCESS] Screenshot refreshed successfully
[[10:08:57]] [SUCCESS] Screenshot refreshed successfully
[[10:08:57]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:08:56]] [SUCCESS] Screenshot refreshed
[[10:08:56]] [INFO] Refreshing screenshot...
[[10:08:53]] [SUCCESS] Screenshot refreshed successfully
[[10:08:53]] [SUCCESS] Screenshot refreshed successfully
[[10:08:52]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:08:52]] [SUCCESS] Screenshot refreshed
[[10:08:52]] [INFO] Refreshing screenshot...
[[10:08:46]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:08:46]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[10:08:46]] [SUCCESS] Screenshot refreshed successfully
[[10:08:46]] [SUCCESS] Screenshot refreshed successfully
[[10:08:46]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[10:08:46]] [INFO] w8dueydByT=running
[[10:08:46]] [INFO] Executing action 230/351: Execute Test Case: Kmart-NZ-Signin (6 steps)
[[10:08:45]] [SUCCESS] Screenshot refreshed
[[10:08:45]] [INFO] Refreshing screenshot...
[[10:08:45]] [INFO] 3caMBvQX7k=pass
[[10:08:42]] [SUCCESS] Screenshot refreshed successfully
[[10:08:42]] [SUCCESS] Screenshot refreshed successfully
[[10:08:41]] [INFO] 3caMBvQX7k=running
[[10:08:41]] [INFO] Executing action 229/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:08:41]] [SUCCESS] Screenshot refreshed
[[10:08:41]] [INFO] Refreshing screenshot...
[[10:08:41]] [INFO] yUJyVO5Wev=pass
[[10:08:38]] [SUCCESS] Screenshot refreshed successfully
[[10:08:38]] [SUCCESS] Screenshot refreshed successfully
[[10:08:38]] [INFO] yUJyVO5Wev=running
[[10:08:38]] [INFO] Executing action 228/351: iOS Function: alert_accept
[[10:08:37]] [SUCCESS] Screenshot refreshed
[[10:08:37]] [INFO] Refreshing screenshot...
[[10:08:37]] [INFO] rkL0oz4kiL=pass
[[10:08:30]] [SUCCESS] Screenshot refreshed successfully
[[10:08:30]] [SUCCESS] Screenshot refreshed successfully
[[10:08:29]] [INFO] rkL0oz4kiL=running
[[10:08:29]] [INFO] Executing action 227/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:08:29]] [SUCCESS] Screenshot refreshed
[[10:08:29]] [INFO] Refreshing screenshot...
[[10:08:29]] [INFO] HotUJOd6oB=pass
[[10:08:14]] [SUCCESS] Screenshot refreshed successfully
[[10:08:14]] [SUCCESS] Screenshot refreshed successfully
[[10:08:14]] [INFO] HotUJOd6oB=running
[[10:08:14]] [INFO] Executing action 226/351: Restart app: env[appid]
[[10:08:14]] [SUCCESS] Screenshot refreshed
[[10:08:14]] [INFO] Refreshing screenshot...
[[10:08:13]] [SUCCESS] Screenshot refreshed
[[10:08:13]] [INFO] Refreshing screenshot...
[[10:08:11]] [SUCCESS] Screenshot refreshed successfully
[[10:08:11]] [SUCCESS] Screenshot refreshed successfully
[[10:08:10]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:08:10]] [SUCCESS] Screenshot refreshed
[[10:08:10]] [INFO] Refreshing screenshot...
[[10:07:57]] [SUCCESS] Screenshot refreshed successfully
[[10:07:57]] [SUCCESS] Screenshot refreshed successfully
[[10:07:57]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:07:57]] [SUCCESS] Screenshot refreshed
[[10:07:57]] [INFO] Refreshing screenshot...
[[10:07:53]] [SUCCESS] Screenshot refreshed successfully
[[10:07:53]] [SUCCESS] Screenshot refreshed successfully
[[10:07:53]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:07:53]] [SUCCESS] Screenshot refreshed
[[10:07:53]] [INFO] Refreshing screenshot...
[[10:07:49]] [SUCCESS] Screenshot refreshed successfully
[[10:07:49]] [SUCCESS] Screenshot refreshed successfully
[[10:07:49]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:07:48]] [SUCCESS] Screenshot refreshed
[[10:07:48]] [INFO] Refreshing screenshot...
[[10:07:42]] [SUCCESS] Screenshot refreshed successfully
[[10:07:42]] [SUCCESS] Screenshot refreshed successfully
[[10:07:41]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:07:41]] [SUCCESS] Screenshot refreshed
[[10:07:41]] [INFO] Refreshing screenshot...
[[10:07:34]] [SUCCESS] Screenshot refreshed successfully
[[10:07:34]] [SUCCESS] Screenshot refreshed successfully
[[10:07:34]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[10:07:33]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[10:07:33]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[10:07:33]] [INFO] DcZCTgLVW9=running
[[10:07:33]] [INFO] Executing action 225/351: cleanupSteps action
[[10:07:33]] [SUCCESS] Screenshot refreshed
[[10:07:33]] [INFO] Refreshing screenshot...
[[10:07:33]] [INFO] w0CWlknXmX=pass
[[10:07:26]] [SUCCESS] Screenshot refreshed successfully
[[10:07:26]] [SUCCESS] Screenshot refreshed successfully
[[10:07:26]] [INFO] w0CWlknXmX=running
[[10:07:26]] [INFO] Executing action 224/351: Wait for 5 ms
[[10:07:25]] [SUCCESS] Screenshot refreshed
[[10:07:25]] [INFO] Refreshing screenshot...
[[10:07:25]] [INFO] Bdhe5AoUlM=pass
[[10:07:21]] [SUCCESS] Screenshot refreshed successfully
[[10:07:21]] [SUCCESS] Screenshot refreshed successfully
[[10:07:21]] [INFO] Bdhe5AoUlM=running
[[10:07:21]] [INFO] Executing action 223/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:07:20]] [SUCCESS] Screenshot refreshed
[[10:07:20]] [INFO] Refreshing screenshot...
[[10:07:20]] [INFO] ISfUFUnvFh=pass
[[10:07:14]] [SUCCESS] Screenshot refreshed successfully
[[10:07:14]] [SUCCESS] Screenshot refreshed successfully
[[10:07:14]] [INFO] ISfUFUnvFh=running
[[10:07:14]] [INFO] Executing action 222/351: Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible
[[10:07:13]] [SUCCESS] Screenshot refreshed
[[10:07:13]] [INFO] Refreshing screenshot...
[[10:07:13]] [INFO] FARWZvOj0x=pass
[[10:07:10]] [SUCCESS] Screenshot refreshed successfully
[[10:07:10]] [SUCCESS] Screenshot refreshed successfully
[[10:07:10]] [INFO] FARWZvOj0x=running
[[10:07:10]] [INFO] Executing action 221/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:07:09]] [SUCCESS] Screenshot refreshed
[[10:07:09]] [INFO] Refreshing screenshot...
[[10:07:09]] [INFO] bZCkx4U9Gk=pass
[[10:07:04]] [SUCCESS] Screenshot refreshed successfully
[[10:07:04]] [SUCCESS] Screenshot refreshed successfully
[[10:07:04]] [INFO] bZCkx4U9Gk=running
[[10:07:04]] [INFO] Executing action 220/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:07:04]] [SUCCESS] Screenshot refreshed
[[10:07:04]] [INFO] Refreshing screenshot...
[[10:07:04]] [INFO] vwFwkK6ydQ=pass
[[10:06:59]] [SUCCESS] Screenshot refreshed successfully
[[10:06:59]] [SUCCESS] Screenshot refreshed successfully
[[10:06:59]] [INFO] vwFwkK6ydQ=running
[[10:06:59]] [INFO] Executing action 219/351: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[10:06:58]] [SUCCESS] Screenshot refreshed
[[10:06:58]] [INFO] Refreshing screenshot...
[[10:06:58]] [INFO] xLGm9FefWE=pass
[[10:06:53]] [SUCCESS] Screenshot refreshed successfully
[[10:06:53]] [SUCCESS] Screenshot refreshed successfully
[[10:06:53]] [INFO] xLGm9FefWE=running
[[10:06:53]] [INFO] Executing action 218/351: Tap on Text: "Google"
[[10:06:53]] [SUCCESS] Screenshot refreshed
[[10:06:53]] [INFO] Refreshing screenshot...
[[10:06:53]] [INFO] SDtskxyVpg=pass
[[10:06:49]] [SUCCESS] Screenshot refreshed successfully
[[10:06:49]] [SUCCESS] Screenshot refreshed successfully
[[10:06:49]] [INFO] SDtskxyVpg=running
[[10:06:49]] [INFO] Executing action 217/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:06:48]] [SUCCESS] Screenshot refreshed
[[10:06:48]] [INFO] Refreshing screenshot...
[[10:06:48]] [INFO] 6HhScBaqQp=pass
[[10:06:46]] [SUCCESS] Screenshot refreshed successfully
[[10:06:46]] [SUCCESS] Screenshot refreshed successfully
[[10:06:45]] [INFO] 6HhScBaqQp=running
[[10:06:45]] [INFO] Executing action 216/351: iOS Function: alert_accept
[[10:06:45]] [SUCCESS] Screenshot refreshed
[[10:06:45]] [INFO] Refreshing screenshot...
[[10:06:45]] [INFO] quzlwPw42x=pass
[[10:06:39]] [SUCCESS] Screenshot refreshed successfully
[[10:06:39]] [SUCCESS] Screenshot refreshed successfully
[[10:06:39]] [INFO] quzlwPw42x=running
[[10:06:39]] [INFO] Executing action 215/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:06:38]] [SUCCESS] Screenshot refreshed
[[10:06:38]] [INFO] Refreshing screenshot...
[[10:06:38]] [INFO] jQYHQIvQ8l=pass
[[10:06:35]] [SUCCESS] Screenshot refreshed successfully
[[10:06:35]] [SUCCESS] Screenshot refreshed successfully
[[10:06:35]] [INFO] jQYHQIvQ8l=running
[[10:06:35]] [INFO] Executing action 214/351: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[10:06:34]] [SUCCESS] Screenshot refreshed
[[10:06:34]] [INFO] Refreshing screenshot...
[[10:06:34]] [INFO] ts3qyFxyMf=pass
[[10:06:29]] [SUCCESS] Screenshot refreshed successfully
[[10:06:29]] [SUCCESS] Screenshot refreshed successfully
[[10:06:29]] [INFO] ts3qyFxyMf=running
[[10:06:29]] [INFO] Executing action 213/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:06:29]] [SUCCESS] Screenshot refreshed
[[10:06:29]] [INFO] Refreshing screenshot...
[[10:06:29]] [INFO] uuatVQwQFW=pass
[[10:06:22]] [SUCCESS] Screenshot refreshed successfully
[[10:06:22]] [SUCCESS] Screenshot refreshed successfully
[[10:06:22]] [INFO] uuatVQwQFW=running
[[10:06:22]] [INFO] Executing action 212/351: Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible
[[10:06:22]] [SUCCESS] Screenshot refreshed
[[10:06:22]] [INFO] Refreshing screenshot...
[[10:06:22]] [INFO] CWkqGp5ndO=pass
[[10:06:19]] [SUCCESS] Screenshot refreshed successfully
[[10:06:19]] [SUCCESS] Screenshot refreshed successfully
[[10:06:18]] [INFO] CWkqGp5ndO=running
[[10:06:18]] [INFO] Executing action 211/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:06:18]] [SUCCESS] Screenshot refreshed
[[10:06:18]] [INFO] Refreshing screenshot...
[[10:06:18]] [INFO] KfMHchi8cx=pass
[[10:06:09]] [INFO] KfMHchi8cx=running
[[10:06:09]] [INFO] Executing action 210/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:06:09]] [SUCCESS] Screenshot refreshed successfully
[[10:06:09]] [SUCCESS] Screenshot refreshed successfully
[[10:06:09]] [SUCCESS] Screenshot refreshed
[[10:06:09]] [INFO] Refreshing screenshot...
[[10:06:09]] [INFO] zsVeGHiIgX=pass
[[10:06:06]] [INFO] zsVeGHiIgX=running
[[10:06:06]] [INFO] Executing action 209/351: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[10:06:05]] [SUCCESS] Screenshot refreshed successfully
[[10:06:05]] [SUCCESS] Screenshot refreshed successfully
[[10:06:05]] [SUCCESS] Screenshot refreshed
[[10:06:05]] [INFO] Refreshing screenshot...
[[10:06:05]] [INFO] 5nsUXQ5L7u=pass
[[10:06:02]] [INFO] 5nsUXQ5L7u=running
[[10:06:02]] [INFO] Executing action 208/351: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[10:06:02]] [SUCCESS] Screenshot refreshed successfully
[[10:06:02]] [SUCCESS] Screenshot refreshed successfully
[[10:06:01]] [SUCCESS] Screenshot refreshed
[[10:06:01]] [INFO] Refreshing screenshot...
[[10:06:01]] [INFO] iSckENpXrN=pass
[[10:05:58]] [INFO] iSckENpXrN=running
[[10:05:58]] [INFO] Executing action 207/351: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[10:05:58]] [SUCCESS] Screenshot refreshed successfully
[[10:05:58]] [SUCCESS] Screenshot refreshed successfully
[[10:05:57]] [SUCCESS] Screenshot refreshed
[[10:05:57]] [INFO] Refreshing screenshot...
[[10:05:57]] [INFO] J7BPGVnRJI=pass
[[10:05:54]] [INFO] J7BPGVnRJI=running
[[10:05:54]] [INFO] Executing action 206/351: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[10:05:54]] [SUCCESS] Screenshot refreshed successfully
[[10:05:54]] [SUCCESS] Screenshot refreshed successfully
[[10:05:54]] [SUCCESS] Screenshot refreshed
[[10:05:54]] [INFO] Refreshing screenshot...
[[10:05:54]] [INFO] 0pwZCYAtOv=pass
[[10:05:51]] [INFO] 0pwZCYAtOv=running
[[10:05:51]] [INFO] Executing action 205/351: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[10:05:50]] [SUCCESS] Screenshot refreshed successfully
[[10:05:50]] [SUCCESS] Screenshot refreshed successfully
[[10:05:50]] [SUCCESS] Screenshot refreshed
[[10:05:50]] [INFO] Refreshing screenshot...
[[10:05:50]] [INFO] soKM0KayFJ=pass
[[10:05:47]] [INFO] soKM0KayFJ=running
[[10:05:47]] [INFO] Executing action 204/351: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[10:05:47]] [SUCCESS] Screenshot refreshed successfully
[[10:05:47]] [SUCCESS] Screenshot refreshed successfully
[[10:05:46]] [SUCCESS] Screenshot refreshed
[[10:05:46]] [INFO] Refreshing screenshot...
[[10:05:46]] [INFO] hnH3ayslCh=pass
[[10:05:43]] [INFO] hnH3ayslCh=running
[[10:05:43]] [INFO] Executing action 203/351: Tap on Text: "Passcode"
[[10:05:43]] [SUCCESS] Screenshot refreshed successfully
[[10:05:43]] [SUCCESS] Screenshot refreshed successfully
[[10:05:42]] [SUCCESS] Screenshot refreshed
[[10:05:42]] [INFO] Refreshing screenshot...
[[10:05:42]] [INFO] CzVeOTdAX9=pass
[[10:05:31]] [SUCCESS] Screenshot refreshed successfully
[[10:05:31]] [SUCCESS] Screenshot refreshed successfully
[[10:05:30]] [INFO] CzVeOTdAX9=running
[[10:05:30]] [INFO] Executing action 202/351: Wait for 10 ms
[[10:05:30]] [SUCCESS] Screenshot refreshed
[[10:05:30]] [INFO] Refreshing screenshot...
[[10:05:30]] [INFO] NL2gtj6qIu=pass
[[10:05:25]] [SUCCESS] Screenshot refreshed successfully
[[10:05:25]] [SUCCESS] Screenshot refreshed successfully
[[10:05:25]] [INFO] NL2gtj6qIu=running
[[10:05:25]] [INFO] Executing action 201/351: Tap on Text: "Apple"
[[10:05:24]] [SUCCESS] Screenshot refreshed
[[10:05:24]] [INFO] Refreshing screenshot...
[[10:05:24]] [INFO] CJ88OgjKXp=pass
[[10:05:21]] [SUCCESS] Screenshot refreshed successfully
[[10:05:21]] [SUCCESS] Screenshot refreshed successfully
[[10:05:20]] [INFO] CJ88OgjKXp=running
[[10:05:20]] [INFO] Executing action 200/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:05:20]] [SUCCESS] Screenshot refreshed
[[10:05:20]] [INFO] Refreshing screenshot...
[[10:05:20]] [INFO] 2kwu2VBmuZ=pass
[[10:05:18]] [SUCCESS] Screenshot refreshed successfully
[[10:05:18]] [SUCCESS] Screenshot refreshed successfully
[[10:05:17]] [INFO] 2kwu2VBmuZ=running
[[10:05:17]] [INFO] Executing action 199/351: iOS Function: alert_accept
[[10:05:17]] [SUCCESS] Screenshot refreshed
[[10:05:17]] [INFO] Refreshing screenshot...
[[10:05:17]] [INFO] cJDpd7aK3d=pass
[[10:05:11]] [SUCCESS] Screenshot refreshed successfully
[[10:05:11]] [SUCCESS] Screenshot refreshed successfully
[[10:05:11]] [INFO] cJDpd7aK3d=running
[[10:05:11]] [INFO] Executing action 198/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:05:10]] [SUCCESS] Screenshot refreshed
[[10:05:10]] [INFO] Refreshing screenshot...
[[10:05:10]] [INFO] FlEukNkjlS=pass
[[10:05:07]] [SUCCESS] Screenshot refreshed successfully
[[10:05:07]] [SUCCESS] Screenshot refreshed successfully
[[10:05:06]] [INFO] FlEukNkjlS=running
[[10:05:06]] [INFO] Executing action 197/351: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[10:05:06]] [SUCCESS] Screenshot refreshed
[[10:05:06]] [INFO] Refreshing screenshot...
[[10:05:06]] [INFO] LlRfimKPrn=pass
[[10:05:00]] [SUCCESS] Screenshot refreshed successfully
[[10:05:00]] [SUCCESS] Screenshot refreshed successfully
[[10:05:00]] [INFO] LlRfimKPrn=running
[[10:05:00]] [INFO] Executing action 196/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:05:00]] [SUCCESS] Screenshot refreshed
[[10:05:00]] [INFO] Refreshing screenshot...
[[10:05:00]] [INFO] xqHGFj3tDd=pass
[[10:04:53]] [SUCCESS] Screenshot refreshed successfully
[[10:04:53]] [SUCCESS] Screenshot refreshed successfully
[[10:04:53]] [INFO] xqHGFj3tDd=running
[[10:04:53]] [INFO] Executing action 195/351: Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible
[[10:04:53]] [SUCCESS] Screenshot refreshed
[[10:04:53]] [INFO] Refreshing screenshot...
[[10:04:53]] [INFO] 08NzsvhQXK=pass
[[10:04:50]] [SUCCESS] Screenshot refreshed successfully
[[10:04:50]] [SUCCESS] Screenshot refreshed successfully
[[10:04:49]] [INFO] 08NzsvhQXK=running
[[10:04:49]] [INFO] Executing action 194/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:04:49]] [SUCCESS] Screenshot refreshed
[[10:04:49]] [INFO] Refreshing screenshot...
[[10:04:49]] [INFO] IsGWxLFpIn=pass
[[10:04:46]] [SUCCESS] Screenshot refreshed successfully
[[10:04:46]] [SUCCESS] Screenshot refreshed successfully
[[10:04:45]] [INFO] IsGWxLFpIn=running
[[10:04:45]] [INFO] Executing action 193/351: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:04:45]] [SUCCESS] Screenshot refreshed
[[10:04:45]] [INFO] Refreshing screenshot...
[[10:04:45]] [INFO] CtWhaVwbJC=pass
[[10:04:40]] [SUCCESS] Screenshot refreshed successfully
[[10:04:40]] [SUCCESS] Screenshot refreshed successfully
[[10:04:40]] [INFO] CtWhaVwbJC=running
[[10:04:40]] [INFO] Executing action 192/351: iOS Function: text - Text: "env[pwd]"
[[10:04:39]] [SUCCESS] Screenshot refreshed
[[10:04:39]] [INFO] Refreshing screenshot...
[[10:04:39]] [INFO] I5bRbYY1hD=pass
[[10:04:35]] [SUCCESS] Screenshot refreshed successfully
[[10:04:35]] [SUCCESS] Screenshot refreshed successfully
[[10:04:35]] [INFO] I5bRbYY1hD=running
[[10:04:35]] [INFO] Executing action 191/351: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:04:34]] [SUCCESS] Screenshot refreshed
[[10:04:34]] [INFO] Refreshing screenshot...
[[10:04:34]] [INFO] rSxM47lUdy=pass
[[10:04:29]] [SUCCESS] Screenshot refreshed successfully
[[10:04:29]] [SUCCESS] Screenshot refreshed successfully
[[10:04:29]] [INFO] rSxM47lUdy=running
[[10:04:29]] [INFO] Executing action 190/351: iOS Function: text - Text: "env[uname]"
[[10:04:28]] [SUCCESS] Screenshot refreshed
[[10:04:28]] [INFO] Refreshing screenshot...
[[10:04:28]] [INFO] 8OsQmoVYqW=pass
[[10:04:24]] [SUCCESS] Screenshot refreshed successfully
[[10:04:24]] [SUCCESS] Screenshot refreshed successfully
[[10:04:24]] [INFO] 8OsQmoVYqW=running
[[10:04:24]] [INFO] Executing action 189/351: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:04:24]] [SUCCESS] Screenshot refreshed
[[10:04:24]] [INFO] Refreshing screenshot...
[[10:04:24]] [INFO] ImienLpJEN=pass
[[10:04:20]] [SUCCESS] Screenshot refreshed successfully
[[10:04:20]] [SUCCESS] Screenshot refreshed successfully
[[10:04:20]] [INFO] ImienLpJEN=running
[[10:04:20]] [INFO] Executing action 188/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:04:19]] [SUCCESS] Screenshot refreshed
[[10:04:19]] [INFO] Refreshing screenshot...
[[10:04:19]] [INFO] q4hPXCBtx4=pass
[[10:04:17]] [SUCCESS] Screenshot refreshed successfully
[[10:04:17]] [SUCCESS] Screenshot refreshed successfully
[[10:04:16]] [INFO] q4hPXCBtx4=running
[[10:04:16]] [INFO] Executing action 187/351: iOS Function: alert_accept
[[10:04:16]] [SUCCESS] Screenshot refreshed
[[10:04:16]] [INFO] Refreshing screenshot...
[[10:04:16]] [INFO] 2cTZvK1psn=pass
[[10:04:09]] [SUCCESS] Screenshot refreshed successfully
[[10:04:09]] [SUCCESS] Screenshot refreshed successfully
[[10:04:09]] [INFO] 2cTZvK1psn=running
[[10:04:09]] [INFO] Executing action 186/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:04:08]] [SUCCESS] Screenshot refreshed
[[10:04:08]] [INFO] Refreshing screenshot...
[[10:04:08]] [INFO] Vxt7QOYeDD=pass
[[10:03:54]] [SUCCESS] Screenshot refreshed successfully
[[10:03:54]] [SUCCESS] Screenshot refreshed successfully
[[10:03:53]] [INFO] Vxt7QOYeDD=running
[[10:03:53]] [INFO] Executing action 185/351: Restart app: env[appid]
[[10:03:53]] [SUCCESS] Screenshot refreshed
[[10:03:53]] [INFO] Refreshing screenshot...
[[10:03:52]] [SUCCESS] Screenshot refreshed
[[10:03:52]] [INFO] Refreshing screenshot...
[[10:03:51]] [SUCCESS] Screenshot refreshed successfully
[[10:03:51]] [SUCCESS] Screenshot refreshed successfully
[[10:03:51]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:03:50]] [SUCCESS] Screenshot refreshed
[[10:03:50]] [INFO] Refreshing screenshot...
[[10:03:39]] [SUCCESS] Screenshot refreshed successfully
[[10:03:39]] [SUCCESS] Screenshot refreshed successfully
[[10:03:39]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:03:38]] [SUCCESS] Screenshot refreshed
[[10:03:38]] [INFO] Refreshing screenshot...
[[10:03:35]] [SUCCESS] Screenshot refreshed successfully
[[10:03:35]] [SUCCESS] Screenshot refreshed successfully
[[10:03:34]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:03:34]] [SUCCESS] Screenshot refreshed
[[10:03:34]] [INFO] Refreshing screenshot...
[[10:03:30]] [SUCCESS] Screenshot refreshed successfully
[[10:03:30]] [SUCCESS] Screenshot refreshed successfully
[[10:03:30]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:03:29]] [SUCCESS] Screenshot refreshed
[[10:03:29]] [INFO] Refreshing screenshot...
[[10:03:23]] [SUCCESS] Screenshot refreshed successfully
[[10:03:23]] [SUCCESS] Screenshot refreshed successfully
[[10:03:22]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:03:22]] [SUCCESS] Screenshot refreshed
[[10:03:22]] [INFO] Refreshing screenshot...
[[10:03:15]] [SUCCESS] Screenshot refreshed successfully
[[10:03:15]] [SUCCESS] Screenshot refreshed successfully
[[10:03:14]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[10:03:14]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[10:03:14]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[10:03:14]] [INFO] DsDODm7uZx=running
[[10:03:14]] [INFO] Executing action 184/351: cleanupSteps action
[[10:03:14]] [SUCCESS] Screenshot refreshed
[[10:03:14]] [INFO] Refreshing screenshot...
[[10:03:14]] [INFO] Vyrkv4wK1v=pass
[[10:03:07]] [SUCCESS] Screenshot refreshed successfully
[[10:03:07]] [SUCCESS] Screenshot refreshed successfully
[[10:03:06]] [INFO] Vyrkv4wK1v=running
[[10:03:06]] [INFO] Executing action 183/351: Wait for 5 ms
[[10:03:06]] [SUCCESS] Screenshot refreshed
[[10:03:06]] [INFO] Refreshing screenshot...
[[10:03:06]] [INFO] 7WYExJTqjp=pass
[[10:03:02]] [SUCCESS] Screenshot refreshed successfully
[[10:03:02]] [SUCCESS] Screenshot refreshed successfully
[[10:03:01]] [INFO] 7WYExJTqjp=running
[[10:03:01]] [INFO] Executing action 182/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:03:01]] [SUCCESS] Screenshot refreshed
[[10:03:01]] [INFO] Refreshing screenshot...
[[10:03:01]] [INFO] NQGIFb5O7u=pass
[[10:02:56]] [SUCCESS] Screenshot refreshed successfully
[[10:02:56]] [SUCCESS] Screenshot refreshed successfully
[[10:02:56]] [INFO] NQGIFb5O7u=running
[[10:02:56]] [INFO] Executing action 181/351: Swipe from (50%, 70%) to (50%, 30%)
[[10:02:56]] [SUCCESS] Screenshot refreshed
[[10:02:56]] [INFO] Refreshing screenshot...
[[10:02:56]] [INFO] NurQsFoMkE=pass
[[10:02:53]] [SUCCESS] Screenshot refreshed successfully
[[10:02:53]] [SUCCESS] Screenshot refreshed successfully
[[10:02:52]] [INFO] NurQsFoMkE=running
[[10:02:52]] [INFO] Executing action 180/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:02:52]] [SUCCESS] Screenshot refreshed
[[10:02:52]] [INFO] Refreshing screenshot...
[[10:02:51]] [SUCCESS] Screenshot refreshed
[[10:02:51]] [INFO] Refreshing screenshot...
[[10:02:47]] [SUCCESS] Screenshot refreshed successfully
[[10:02:47]] [SUCCESS] Screenshot refreshed successfully
[[10:02:47]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:02:46]] [SUCCESS] Screenshot refreshed
[[10:02:46]] [INFO] Refreshing screenshot...
[[10:02:43]] [SUCCESS] Screenshot refreshed successfully
[[10:02:43]] [SUCCESS] Screenshot refreshed successfully
[[10:02:42]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:02:42]] [SUCCESS] Screenshot refreshed
[[10:02:42]] [INFO] Refreshing screenshot...
[[10:02:38]] [SUCCESS] Screenshot refreshed successfully
[[10:02:38]] [SUCCESS] Screenshot refreshed successfully
[[10:02:37]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:02:37]] [SUCCESS] Screenshot refreshed
[[10:02:37]] [INFO] Refreshing screenshot...
[[10:02:32]] [SUCCESS] Screenshot refreshed successfully
[[10:02:32]] [SUCCESS] Screenshot refreshed successfully
[[10:02:32]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:02:32]] [SUCCESS] Screenshot refreshed
[[10:02:32]] [INFO] Refreshing screenshot...
[[10:02:26]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:02:26]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[10:02:26]] [SUCCESS] Screenshot refreshed successfully
[[10:02:26]] [SUCCESS] Screenshot refreshed successfully
[[10:02:25]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[10:02:25]] [INFO] 6imys82Dy0=running
[[10:02:25]] [INFO] Executing action 179/351: Execute Test Case: Kmart-NZ-Signin (5 steps)
[[10:02:25]] [SUCCESS] Screenshot refreshed
[[10:02:25]] [INFO] Refreshing screenshot...
[[10:02:25]] [INFO] XuLgjNG74w=pass
[[10:02:22]] [SUCCESS] Screenshot refreshed successfully
[[10:02:22]] [SUCCESS] Screenshot refreshed successfully
[[10:02:22]] [INFO] XuLgjNG74w=running
[[10:02:22]] [INFO] Executing action 178/351: iOS Function: alert_accept
[[10:02:21]] [SUCCESS] Screenshot refreshed
[[10:02:21]] [INFO] Refreshing screenshot...
[[10:02:21]] [INFO] L6wTorOX8B=pass
[[10:02:18]] [SUCCESS] Screenshot refreshed successfully
[[10:02:18]] [SUCCESS] Screenshot refreshed successfully
[[10:02:17]] [INFO] L6wTorOX8B=running
[[10:02:17]] [INFO] Executing action 177/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[10:02:17]] [SUCCESS] Screenshot refreshed
[[10:02:17]] [INFO] Refreshing screenshot...
[[10:02:17]] [INFO] ydRnBBO1vR=pass
[[10:02:14]] [SUCCESS] Screenshot refreshed successfully
[[10:02:14]] [SUCCESS] Screenshot refreshed successfully
[[10:02:13]] [INFO] ydRnBBO1vR=running
[[10:02:13]] [INFO] Executing action 176/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:02:13]] [SUCCESS] Screenshot refreshed
[[10:02:13]] [INFO] Refreshing screenshot...
[[10:02:13]] [INFO] lCSewtjn1z=pass
[[10:02:08]] [SUCCESS] Screenshot refreshed successfully
[[10:02:08]] [SUCCESS] Screenshot refreshed successfully
[[10:02:07]] [INFO] lCSewtjn1z=running
[[10:02:07]] [INFO] Executing action 175/351: Restart app: env[appid]
[[10:02:07]] [SUCCESS] Screenshot refreshed
[[10:02:07]] [INFO] Refreshing screenshot...
[[10:02:07]] [INFO] A1Wz7p1iVG=pass
[[10:02:02]] [SUCCESS] Screenshot refreshed successfully
[[10:02:02]] [SUCCESS] Screenshot refreshed successfully
[[10:02:02]] [INFO] A1Wz7p1iVG=running
[[10:02:02]] [INFO] Executing action 174/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:02:02]] [SUCCESS] Screenshot refreshed
[[10:02:02]] [INFO] Refreshing screenshot...
[[10:02:02]] [INFO] BCM1sS8SGA=pass
[[10:01:57]] [SUCCESS] Screenshot refreshed successfully
[[10:01:57]] [SUCCESS] Screenshot refreshed successfully
[[10:01:57]] [INFO] BCM1sS8SGA=running
[[10:01:57]] [INFO] Executing action 173/351: Swipe from (50%, 70%) to (50%, 30%)
[[10:01:57]] [SUCCESS] Screenshot refreshed
[[10:01:57]] [INFO] Refreshing screenshot...
[[10:01:57]] [INFO] ydRnBBO1vR=pass
[[10:01:54]] [SUCCESS] Screenshot refreshed successfully
[[10:01:54]] [SUCCESS] Screenshot refreshed successfully
[[10:01:53]] [INFO] ydRnBBO1vR=running
[[10:01:53]] [INFO] Executing action 172/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:01:53]] [SUCCESS] Screenshot refreshed
[[10:01:53]] [INFO] Refreshing screenshot...
[[10:01:53]] [INFO] quZwUwj3a8=pass
[[10:01:49]] [SUCCESS] Screenshot refreshed successfully
[[10:01:49]] [SUCCESS] Screenshot refreshed successfully
[[10:01:49]] [INFO] quZwUwj3a8=running
[[10:01:49]] [INFO] Executing action 171/351: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[10:01:48]] [SUCCESS] Screenshot refreshed
[[10:01:48]] [INFO] Refreshing screenshot...
[[10:01:48]] [INFO] FHRlQXe58T=pass
[[10:01:45]] [SUCCESS] Screenshot refreshed successfully
[[10:01:45]] [SUCCESS] Screenshot refreshed successfully
[[10:01:44]] [INFO] FHRlQXe58T=running
[[10:01:44]] [INFO] Executing action 170/351: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[10:01:44]] [SUCCESS] Screenshot refreshed
[[10:01:44]] [INFO] Refreshing screenshot...
[[10:01:44]] [SUCCESS] Screenshot refreshed
[[10:01:44]] [INFO] Refreshing screenshot...
[[10:01:40]] [SUCCESS] Screenshot refreshed successfully
[[10:01:40]] [SUCCESS] Screenshot refreshed successfully
[[10:01:39]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:01:39]] [SUCCESS] Screenshot refreshed
[[10:01:39]] [INFO] Refreshing screenshot...
[[10:01:35]] [SUCCESS] Screenshot refreshed successfully
[[10:01:35]] [SUCCESS] Screenshot refreshed successfully
[[10:01:35]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:01:34]] [SUCCESS] Screenshot refreshed
[[10:01:34]] [INFO] Refreshing screenshot...
[[10:01:29]] [SUCCESS] Screenshot refreshed successfully
[[10:01:29]] [SUCCESS] Screenshot refreshed successfully
[[10:01:29]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:01:29]] [SUCCESS] Screenshot refreshed
[[10:01:29]] [INFO] Refreshing screenshot...
[[10:01:25]] [SUCCESS] Screenshot refreshed successfully
[[10:01:25]] [SUCCESS] Screenshot refreshed successfully
[[10:01:25]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:01:24]] [SUCCESS] Screenshot refreshed
[[10:01:24]] [INFO] Refreshing screenshot...
[[10:01:19]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:01:19]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[10:01:18]] [SUCCESS] Screenshot refreshed successfully
[[10:01:18]] [SUCCESS] Screenshot refreshed successfully
[[10:01:18]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[10:01:18]] [INFO] 3KXtlKCIes=running
[[10:01:18]] [INFO] Executing action 169/351: Execute Test Case: Kmart-NZ-Signin (5 steps)
[[10:01:18]] [SUCCESS] Screenshot refreshed
[[10:01:18]] [INFO] Refreshing screenshot...
[[10:01:18]] [INFO] 6mHVWI3j5e=pass
[[10:01:14]] [SUCCESS] Screenshot refreshed successfully
[[10:01:14]] [SUCCESS] Screenshot refreshed successfully
[[10:01:14]] [INFO] 6mHVWI3j5e=running
[[10:01:14]] [INFO] Executing action 168/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:01:13]] [SUCCESS] Screenshot refreshed
[[10:01:13]] [INFO] Refreshing screenshot...
[[10:01:13]] [INFO] rJVGLpLWM3=pass
[[10:01:11]] [SUCCESS] Screenshot refreshed successfully
[[10:01:11]] [SUCCESS] Screenshot refreshed successfully
[[10:01:10]] [INFO] rJVGLpLWM3=running
[[10:01:10]] [INFO] Executing action 167/351: iOS Function: alert_accept
[[10:01:10]] [SUCCESS] Screenshot refreshed
[[10:01:10]] [INFO] Refreshing screenshot...
[[10:01:10]] [INFO] WlISsMf9QA=pass
[[10:01:06]] [SUCCESS] Screenshot refreshed successfully
[[10:01:06]] [SUCCESS] Screenshot refreshed successfully
[[10:01:06]] [INFO] WlISsMf9QA=running
[[10:01:06]] [INFO] Executing action 166/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[10:01:06]] [SUCCESS] Screenshot refreshed
[[10:01:06]] [INFO] Refreshing screenshot...
[[10:01:06]] [INFO] IvqPpScAJa=pass
[[10:01:03]] [SUCCESS] Screenshot refreshed successfully
[[10:01:03]] [SUCCESS] Screenshot refreshed successfully
[[10:01:01]] [INFO] IvqPpScAJa=running
[[10:01:01]] [INFO] Executing action 165/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[10:01:01]] [SUCCESS] Screenshot refreshed
[[10:01:01]] [INFO] Refreshing screenshot...
[[10:01:01]] [INFO] bGo3feCwBQ=pass
[[10:00:56]] [SUCCESS] Screenshot refreshed successfully
[[10:00:56]] [SUCCESS] Screenshot refreshed successfully
[[10:00:56]] [INFO] bGo3feCwBQ=running
[[10:00:56]] [INFO] Executing action 164/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:00:56]] [SUCCESS] Screenshot refreshed
[[10:00:56]] [INFO] Refreshing screenshot...
[[10:00:56]] [INFO] GHH3xhNGgr=pass
[[10:00:51]] [SUCCESS] Screenshot refreshed successfully
[[10:00:51]] [SUCCESS] Screenshot refreshed successfully
[[10:00:51]] [INFO] GHH3xhNGgr=running
[[10:00:51]] [INFO] Executing action 163/351: Swipe from (50%, 70%) to (50%, 30%)
[[10:00:51]] [SUCCESS] Screenshot refreshed
[[10:00:51]] [INFO] Refreshing screenshot...
[[10:00:51]] [INFO] F0gZF1jEnT=pass
[[10:00:49]] [SUCCESS] Screenshot refreshed successfully
[[10:00:49]] [SUCCESS] Screenshot refreshed successfully
[[10:00:47]] [INFO] F0gZF1jEnT=running
[[10:00:47]] [INFO] Executing action 162/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:00:47]] [SUCCESS] Screenshot refreshed
[[10:00:47]] [INFO] Refreshing screenshot...
[[10:00:46]] [SUCCESS] Screenshot refreshed
[[10:00:46]] [INFO] Refreshing screenshot...
[[10:00:42]] [SUCCESS] Screenshot refreshed successfully
[[10:00:42]] [SUCCESS] Screenshot refreshed successfully
[[10:00:42]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:00:41]] [SUCCESS] Screenshot refreshed
[[10:00:41]] [INFO] Refreshing screenshot...
[[10:00:37]] [SUCCESS] Screenshot refreshed successfully
[[10:00:37]] [SUCCESS] Screenshot refreshed successfully
[[10:00:37]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:00:37]] [SUCCESS] Screenshot refreshed
[[10:00:37]] [INFO] Refreshing screenshot...
[[10:00:31]] [SUCCESS] Screenshot refreshed successfully
[[10:00:31]] [SUCCESS] Screenshot refreshed successfully
[[10:00:31]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:00:30]] [SUCCESS] Screenshot refreshed
[[10:00:30]] [INFO] Refreshing screenshot...
[[10:00:26]] [SUCCESS] Screenshot refreshed successfully
[[10:00:26]] [SUCCESS] Screenshot refreshed successfully
[[10:00:26]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:00:26]] [SUCCESS] Screenshot refreshed
[[10:00:26]] [INFO] Refreshing screenshot...
[[10:00:20]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:00:20]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[10:00:20]] [SUCCESS] Screenshot refreshed successfully
[[10:00:20]] [SUCCESS] Screenshot refreshed successfully
[[10:00:20]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[10:00:20]] [INFO] f71lbL6wHw=running
[[10:00:20]] [INFO] Executing action 161/351: Execute Test Case: Kmart-NZ-Signin (5 steps)
[[10:00:19]] [SUCCESS] Screenshot refreshed
[[10:00:19]] [INFO] Refreshing screenshot...
[[10:00:19]] [INFO] eJnHS9n9VL=pass
[[10:00:16]] [SUCCESS] Screenshot refreshed successfully
[[10:00:16]] [SUCCESS] Screenshot refreshed successfully
[[10:00:16]] [INFO] eJnHS9n9VL=running
[[10:00:16]] [INFO] Executing action 160/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:00:15]] [SUCCESS] Screenshot refreshed
[[10:00:15]] [INFO] Refreshing screenshot...
[[10:00:15]] [INFO] XuLgjNG74w=pass
[[10:00:13]] [SUCCESS] Screenshot refreshed successfully
[[10:00:13]] [SUCCESS] Screenshot refreshed successfully
[[10:00:12]] [INFO] XuLgjNG74w=running
[[10:00:12]] [INFO] Executing action 159/351: iOS Function: alert_accept
[[10:00:12]] [SUCCESS] Screenshot refreshed
[[10:00:12]] [INFO] Refreshing screenshot...
[[10:00:12]] [INFO] qA1ap4n1m4=pass
[[10:00:06]] [SUCCESS] Screenshot refreshed successfully
[[10:00:06]] [SUCCESS] Screenshot refreshed successfully
[[10:00:06]] [INFO] qA1ap4n1m4=running
[[10:00:06]] [INFO] Executing action 158/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:00:05]] [SUCCESS] Screenshot refreshed
[[10:00:05]] [INFO] Refreshing screenshot...
[[10:00:05]] [INFO] JXFxYCr98V=pass
[[09:59:51]] [SUCCESS] Screenshot refreshed successfully
[[09:59:51]] [SUCCESS] Screenshot refreshed successfully
[[09:59:50]] [INFO] JXFxYCr98V=running
[[09:59:50]] [INFO] Executing action 157/351: Restart app: env[appid]
[[09:59:50]] [SUCCESS] Screenshot refreshed
[[09:59:50]] [INFO] Refreshing screenshot...
[[09:59:49]] [SUCCESS] Screenshot refreshed
[[09:59:49]] [INFO] Refreshing screenshot...
[[09:59:48]] [SUCCESS] Screenshot refreshed successfully
[[09:59:48]] [SUCCESS] Screenshot refreshed successfully
[[09:59:48]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[09:59:47]] [SUCCESS] Screenshot refreshed
[[09:59:47]] [INFO] Refreshing screenshot...
[[09:59:36]] [SUCCESS] Screenshot refreshed successfully
[[09:59:36]] [SUCCESS] Screenshot refreshed successfully
[[09:59:35]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[09:59:35]] [SUCCESS] Screenshot refreshed
[[09:59:35]] [INFO] Refreshing screenshot...
[[09:59:32]] [SUCCESS] Screenshot refreshed successfully
[[09:59:32]] [SUCCESS] Screenshot refreshed successfully
[[09:59:31]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[09:59:31]] [SUCCESS] Screenshot refreshed
[[09:59:31]] [INFO] Refreshing screenshot...
[[09:59:27]] [SUCCESS] Screenshot refreshed successfully
[[09:59:27]] [SUCCESS] Screenshot refreshed successfully
[[09:59:27]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:59:26]] [SUCCESS] Screenshot refreshed
[[09:59:26]] [INFO] Refreshing screenshot...
[[09:59:20]] [SUCCESS] Screenshot refreshed successfully
[[09:59:20]] [SUCCESS] Screenshot refreshed successfully
[[09:59:19]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[09:59:19]] [SUCCESS] Screenshot refreshed
[[09:59:19]] [INFO] Refreshing screenshot...
[[09:59:12]] [SUCCESS] Screenshot refreshed successfully
[[09:59:12]] [SUCCESS] Screenshot refreshed successfully
[[09:59:12]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[09:59:11]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[09:59:11]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[09:59:11]] [INFO] jSY75QGPr8=running
[[09:59:11]] [INFO] Executing action 156/351: cleanupSteps action
[[09:59:11]] [SUCCESS] Screenshot refreshed
[[09:59:11]] [INFO] Refreshing screenshot...
[[09:59:11]] [INFO] BracBsfa3Y=pass
[[09:59:06]] [SUCCESS] Screenshot refreshed successfully
[[09:59:06]] [SUCCESS] Screenshot refreshed successfully
[[09:59:06]] [INFO] BracBsfa3Y=running
[[09:59:06]] [INFO] Executing action 155/351: Tap on Text: "out"
[[09:59:05]] [SUCCESS] Screenshot refreshed
[[09:59:05]] [INFO] Refreshing screenshot...
[[09:59:05]] [INFO] P2OkZzbCB3=pass
[[09:59:01]] [SUCCESS] Screenshot refreshed successfully
[[09:59:01]] [SUCCESS] Screenshot refreshed successfully
[[09:59:01]] [INFO] P2OkZzbCB3=running
[[09:59:01]] [INFO] Executing action 154/351: Tap on image: env[device-back-img]
[[09:59:00]] [SUCCESS] Screenshot refreshed
[[09:59:00]] [INFO] Refreshing screenshot...
[[09:59:00]] [INFO] BracBsfa3Y=pass
[[09:58:56]] [SUCCESS] Screenshot refreshed successfully
[[09:58:56]] [SUCCESS] Screenshot refreshed successfully
[[09:58:55]] [INFO] BracBsfa3Y=running
[[09:58:55]] [INFO] Executing action 153/351: Tap on Text: "Customer"
[[09:58:55]] [SUCCESS] Screenshot refreshed
[[09:58:55]] [INFO] Refreshing screenshot...
[[09:58:55]] [INFO] q70JSbqKNk=pass
[[09:58:51]] [SUCCESS] Screenshot refreshed successfully
[[09:58:51]] [SUCCESS] Screenshot refreshed successfully
[[09:58:51]] [INFO] q70JSbqKNk=running
[[09:58:51]] [INFO] Executing action 152/351: Tap on image: env[device-back-img]
[[09:58:50]] [SUCCESS] Screenshot refreshed
[[09:58:50]] [INFO] Refreshing screenshot...
[[09:58:50]] [INFO] YuuQe2KupX=pass
[[09:58:46]] [SUCCESS] Screenshot refreshed successfully
[[09:58:46]] [SUCCESS] Screenshot refreshed successfully
[[09:58:46]] [INFO] YuuQe2KupX=running
[[09:58:46]] [INFO] Executing action 151/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[09:58:45]] [SUCCESS] Screenshot refreshed
[[09:58:45]] [INFO] Refreshing screenshot...
[[09:58:45]] [INFO] nVWzLauG8N=pass
[[09:58:40]] [SUCCESS] Screenshot refreshed successfully
[[09:58:40]] [SUCCESS] Screenshot refreshed successfully
[[09:58:40]] [INFO] nVWzLauG8N=running
[[09:58:40]] [INFO] Executing action 150/351: Tap on Text: "AUCKLAND"
[[09:58:40]] [SUCCESS] Screenshot refreshed
[[09:58:40]] [INFO] Refreshing screenshot...
[[09:58:40]] [INFO] 3CTsyFe28F=pass
[[09:58:32]] [SUCCESS] Screenshot refreshed successfully
[[09:58:32]] [SUCCESS] Screenshot refreshed successfully
[[09:58:32]] [INFO] 3CTsyFe28F=running
[[09:58:32]] [INFO] Executing action 149/351: Tap and Type at (29, 262): "0616"
[[09:58:32]] [SUCCESS] Screenshot refreshed
[[09:58:32]] [INFO] Refreshing screenshot...
[[09:58:32]] [INFO] 2FnAZDskt1=pass
[[09:58:08]] [SUCCESS] Screenshot refreshed successfully
[[09:58:08]] [SUCCESS] Screenshot refreshed successfully
[[09:58:08]] [INFO] 2FnAZDskt1=running
[[09:58:08]] [INFO] Executing action 148/351: If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Allow While Using App"]
[[09:58:08]] [SUCCESS] Screenshot refreshed
[[09:58:08]] [INFO] Refreshing screenshot...
[[09:58:08]] [INFO] BracBsfa3Y=pass
[[09:58:00]] [INFO] BracBsfa3Y=running
[[09:58:00]] [INFO] Executing action 147/351: Tap on Text: "Nearby"
[[09:58:00]] [SUCCESS] Screenshot refreshed successfully
[[09:58:00]] [SUCCESS] Screenshot refreshed successfully
[[09:58:00]] [SUCCESS] Screenshot refreshed
[[09:58:00]] [INFO] Refreshing screenshot...
[[09:58:00]] [INFO] BracBsfa3Y=pass
[[09:57:55]] [SUCCESS] Screenshot refreshed successfully
[[09:57:55]] [SUCCESS] Screenshot refreshed successfully
[[09:57:55]] [INFO] BracBsfa3Y=running
[[09:57:55]] [INFO] Executing action 146/351: Tap on Text: "locator"
[[09:57:54]] [SUCCESS] Screenshot refreshed
[[09:57:54]] [INFO] Refreshing screenshot...
[[09:57:54]] [INFO] EJkHvEQccu=pass
[[09:57:50]] [SUCCESS] Screenshot refreshed successfully
[[09:57:50]] [SUCCESS] Screenshot refreshed successfully
[[09:57:50]] [INFO] EJkHvEQccu=running
[[09:57:50]] [INFO] Executing action 145/351: Swipe from (50%, 70%) to (50%, 30%)
[[09:57:49]] [SUCCESS] Screenshot refreshed
[[09:57:49]] [INFO] Refreshing screenshot...
[[09:57:49]] [INFO] oETU9DzLi4=pass
[[09:57:46]] [SUCCESS] Screenshot refreshed successfully
[[09:57:46]] [SUCCESS] Screenshot refreshed successfully
[[09:57:45]] [INFO] oETU9DzLi4=running
[[09:57:45]] [INFO] Executing action 144/351: Tap on image: env[device-back-img]
[[09:57:45]] [SUCCESS] Screenshot refreshed
[[09:57:45]] [INFO] Refreshing screenshot...
[[09:57:45]] [INFO] M1IXnYddFx=pass
[[09:57:41]] [INFO] M1IXnYddFx=running
[[09:57:41]] [INFO] Executing action 143/351: Tap on image: env[device-back-img]
[[09:57:41]] [SUCCESS] Screenshot refreshed successfully
[[09:57:41]] [SUCCESS] Screenshot refreshed successfully
[[09:57:40]] [SUCCESS] Screenshot refreshed
[[09:57:40]] [INFO] Refreshing screenshot...
[[09:57:40]] [INFO] napKDohf3Z=pass
[[09:57:36]] [SUCCESS] Screenshot refreshed successfully
[[09:57:36]] [SUCCESS] Screenshot refreshed successfully
[[09:57:36]] [INFO] napKDohf3Z=running
[[09:57:36]] [INFO] Executing action 142/351: Tap on Text: "payment"
[[09:57:35]] [SUCCESS] Screenshot refreshed
[[09:57:35]] [INFO] Refreshing screenshot...
[[09:57:35]] [INFO] 9MqlsILCgk=pass
[[09:57:31]] [INFO] 9MqlsILCgk=running
[[09:57:31]] [INFO] Executing action 141/351: Tap on image: env[device-back-img]
[[09:57:31]] [SUCCESS] Screenshot refreshed successfully
[[09:57:31]] [SUCCESS] Screenshot refreshed successfully
[[09:57:30]] [SUCCESS] Screenshot refreshed
[[09:57:30]] [INFO] Refreshing screenshot...
[[09:57:30]] [INFO] 20qUCJgpE9=pass
[[09:57:26]] [SUCCESS] Screenshot refreshed successfully
[[09:57:26]] [SUCCESS] Screenshot refreshed successfully
[[09:57:25]] [INFO] 20qUCJgpE9=running
[[09:57:25]] [INFO] Executing action 140/351: Tap on Text: "address"
[[09:57:25]] [SUCCESS] Screenshot refreshed
[[09:57:25]] [INFO] Refreshing screenshot...
[[09:57:25]] [INFO] WwIZzJEW9W=pass
[[09:57:21]] [INFO] WwIZzJEW9W=running
[[09:57:21]] [INFO] Executing action 139/351: Tap on image: env[device-back-img]
[[09:57:21]] [SUCCESS] Screenshot refreshed successfully
[[09:57:21]] [SUCCESS] Screenshot refreshed successfully
[[09:57:20]] [SUCCESS] Screenshot refreshed
[[09:57:20]] [INFO] Refreshing screenshot...
[[09:57:20]] [INFO] 3hOTINBVMf=pass
[[09:57:16]] [SUCCESS] Screenshot refreshed successfully
[[09:57:16]] [SUCCESS] Screenshot refreshed successfully
[[09:57:16]] [INFO] 3hOTINBVMf=running
[[09:57:16]] [INFO] Executing action 138/351: Tap on Text: "details"
[[09:57:15]] [SUCCESS] Screenshot refreshed
[[09:57:15]] [INFO] Refreshing screenshot...
[[09:57:15]] [INFO] V59u3l1wkM=pass
[[09:57:12]] [SUCCESS] Screenshot refreshed successfully
[[09:57:12]] [SUCCESS] Screenshot refreshed successfully
[[09:57:12]] [INFO] V59u3l1wkM=running
[[09:57:12]] [INFO] Executing action 137/351: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[09:57:11]] [SUCCESS] Screenshot refreshed
[[09:57:11]] [INFO] Refreshing screenshot...
[[09:57:11]] [INFO] PbfHAtFQPP=pass
[[09:57:08]] [SUCCESS] Screenshot refreshed successfully
[[09:57:08]] [SUCCESS] Screenshot refreshed successfully
[[09:57:07]] [INFO] PbfHAtFQPP=running
[[09:57:07]] [INFO] Executing action 136/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:57:07]] [SUCCESS] Screenshot refreshed
[[09:57:07]] [INFO] Refreshing screenshot...
[[09:57:07]] [INFO] 6qZnk86hGg=pass
[[09:57:03]] [SUCCESS] Screenshot refreshed successfully
[[09:57:03]] [SUCCESS] Screenshot refreshed successfully
[[09:57:02]] [INFO] 6qZnk86hGg=running
[[09:57:02]] [INFO] Executing action 135/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:57:02]] [SUCCESS] Screenshot refreshed
[[09:57:02]] [INFO] Refreshing screenshot...
[[09:57:02]] [INFO] FAvQgIuHc1=pass
[[09:56:57]] [SUCCESS] Screenshot refreshed successfully
[[09:56:57]] [SUCCESS] Screenshot refreshed successfully
[[09:56:57]] [INFO] FAvQgIuHc1=running
[[09:56:57]] [INFO] Executing action 134/351: Tap on Text: "Return"
[[09:56:56]] [SUCCESS] Screenshot refreshed
[[09:56:56]] [INFO] Refreshing screenshot...
[[09:56:56]] [INFO] EJkHvEQccu=pass
[[09:56:52]] [SUCCESS] Screenshot refreshed successfully
[[09:56:52]] [SUCCESS] Screenshot refreshed successfully
[[09:56:52]] [INFO] EJkHvEQccu=running
[[09:56:52]] [INFO] Executing action 133/351: Swipe from (50%, 70%) to (50%, 30%)
[[09:56:51]] [SUCCESS] Screenshot refreshed
[[09:56:51]] [INFO] Refreshing screenshot...
[[09:56:51]] [INFO] YuuQe2KupX=pass
[[09:56:47]] [INFO] YuuQe2KupX=running
[[09:56:47]] [INFO] Executing action 132/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[09:56:47]] [SUCCESS] Screenshot refreshed successfully
[[09:56:47]] [SUCCESS] Screenshot refreshed successfully
[[09:56:46]] [SUCCESS] Screenshot refreshed
[[09:56:46]] [INFO] Refreshing screenshot...
[[09:56:46]] [INFO] g0PE7Mofye=pass
[[09:56:41]] [SUCCESS] Screenshot refreshed successfully
[[09:56:41]] [SUCCESS] Screenshot refreshed successfully
[[09:56:41]] [INFO] g0PE7Mofye=running
[[09:56:41]] [INFO] Executing action 131/351: Tap on element with accessibility_id: Print order details
[[09:56:40]] [SUCCESS] Screenshot refreshed
[[09:56:40]] [INFO] Refreshing screenshot...
[[09:56:40]] [INFO] GgQaBLWYkb=pass
[[09:56:37]] [SUCCESS] Screenshot refreshed successfully
[[09:56:37]] [SUCCESS] Screenshot refreshed successfully
[[09:56:37]] [INFO] GgQaBLWYkb=running
[[09:56:37]] [INFO] Executing action 130/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[09:56:36]] [SUCCESS] Screenshot refreshed
[[09:56:36]] [INFO] Refreshing screenshot...
[[09:56:36]] [INFO] 0962MtId5t=pass
[[09:56:26]] [SUCCESS] Screenshot refreshed successfully
[[09:56:26]] [SUCCESS] Screenshot refreshed successfully
[[09:56:26]] [INFO] 0962MtId5t=running
[[09:56:26]] [INFO] Executing action 129/351: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[09:56:25]] [SUCCESS] Screenshot refreshed
[[09:56:25]] [INFO] Refreshing screenshot...
[[09:56:25]] [INFO] 7g6MFJSGIO=pass
[[09:56:21]] [SUCCESS] Screenshot refreshed successfully
[[09:56:21]] [SUCCESS] Screenshot refreshed successfully
[[09:56:21]] [INFO] 7g6MFJSGIO=running
[[09:56:21]] [INFO] Executing action 128/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="main"]//XCUIElementTypeLink)[4]
[[09:56:21]] [SUCCESS] Screenshot refreshed
[[09:56:21]] [INFO] Refreshing screenshot...
[[09:56:21]] [INFO] Z6g3sGuHTp=pass
[[09:56:14]] [INFO] Z6g3sGuHTp=running
[[09:56:14]] [INFO] Executing action 127/351: Wait for 5 ms
[[09:56:14]] [SUCCESS] Screenshot refreshed successfully
[[09:56:14]] [SUCCESS] Screenshot refreshed successfully
[[09:56:13]] [SUCCESS] Screenshot refreshed
[[09:56:13]] [INFO] Refreshing screenshot...
[[09:56:13]] [INFO] pFlYwTS53v=pass
[[09:56:09]] [SUCCESS] Screenshot refreshed successfully
[[09:56:09]] [SUCCESS] Screenshot refreshed successfully
[[09:56:09]] [INFO] pFlYwTS53v=running
[[09:56:09]] [INFO] Executing action 126/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy orders"]
[[09:56:09]] [SUCCESS] Screenshot refreshed
[[09:56:09]] [INFO] Refreshing screenshot...
[[09:56:09]] [INFO] fDgFGQYpCw=pass
[[09:56:05]] [SUCCESS] Screenshot refreshed successfully
[[09:56:05]] [SUCCESS] Screenshot refreshed successfully
[[09:56:05]] [INFO] fDgFGQYpCw=running
[[09:56:05]] [INFO] Executing action 125/351: Wait till xpath=//XCUIElementTypeButton[@name="txtMy orders"]
[[09:56:05]] [SUCCESS] Screenshot refreshed
[[09:56:05]] [INFO] Refreshing screenshot...
[[09:56:05]] [INFO] V59u3l1wkM=pass
[[09:56:02]] [SUCCESS] Screenshot refreshed successfully
[[09:56:02]] [SUCCESS] Screenshot refreshed successfully
[[09:56:01]] [INFO] V59u3l1wkM=running
[[09:56:01]] [INFO] Executing action 124/351: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[09:56:00]] [SUCCESS] Screenshot refreshed
[[09:56:00]] [INFO] Refreshing screenshot...
[[09:56:00]] [INFO] sl3Wk1gK8X=pass
[[09:55:57]] [SUCCESS] Screenshot refreshed successfully
[[09:55:57]] [SUCCESS] Screenshot refreshed successfully
[[09:55:56]] [INFO] sl3Wk1gK8X=running
[[09:55:56]] [INFO] Executing action 123/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:55:56]] [SUCCESS] Screenshot refreshed
[[09:55:56]] [INFO] Refreshing screenshot...
[[09:55:55]] [SUCCESS] Screenshot refreshed
[[09:55:55]] [INFO] Refreshing screenshot...
[[09:55:50]] [SUCCESS] Screenshot refreshed successfully
[[09:55:50]] [SUCCESS] Screenshot refreshed successfully
[[09:55:50]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[09:55:50]] [SUCCESS] Screenshot refreshed
[[09:55:50]] [INFO] Refreshing screenshot...
[[09:55:46]] [SUCCESS] Screenshot refreshed successfully
[[09:55:46]] [SUCCESS] Screenshot refreshed successfully
[[09:55:46]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:55:45]] [SUCCESS] Screenshot refreshed
[[09:55:45]] [INFO] Refreshing screenshot...
[[09:55:41]] [SUCCESS] Screenshot refreshed successfully
[[09:55:41]] [SUCCESS] Screenshot refreshed successfully
[[09:55:41]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[09:55:40]] [SUCCESS] Screenshot refreshed
[[09:55:40]] [INFO] Refreshing screenshot...
[[09:55:36]] [SUCCESS] Screenshot refreshed successfully
[[09:55:36]] [SUCCESS] Screenshot refreshed successfully
[[09:55:36]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:55:36]] [SUCCESS] Screenshot refreshed
[[09:55:36]] [INFO] Refreshing screenshot...
[[09:55:29]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:55:29]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[09:55:29]] [SUCCESS] Screenshot refreshed successfully
[[09:55:29]] [SUCCESS] Screenshot refreshed successfully
[[09:55:29]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[09:55:29]] [INFO] RLvvFEJpgz=running
[[09:55:29]] [INFO] Executing action 122/351: Execute Test Case: Kmart-NZ-Signin (6 steps)
[[09:55:28]] [SUCCESS] Screenshot refreshed
[[09:55:28]] [INFO] Refreshing screenshot...
[[09:55:28]] [INFO] ly2oT3zqmf=pass
[[09:55:26]] [SUCCESS] Screenshot refreshed successfully
[[09:55:26]] [SUCCESS] Screenshot refreshed successfully
[[09:55:25]] [INFO] ly2oT3zqmf=running
[[09:55:25]] [INFO] Executing action 121/351: iOS Function: alert_accept
[[09:55:25]] [SUCCESS] Screenshot refreshed
[[09:55:25]] [INFO] Refreshing screenshot...
[[09:55:25]] [INFO] xAPeBnVHrT=pass
[[09:55:18]] [SUCCESS] Screenshot refreshed successfully
[[09:55:18]] [SUCCESS] Screenshot refreshed successfully
[[09:55:18]] [INFO] xAPeBnVHrT=running
[[09:55:18]] [INFO] Executing action 120/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:55:17]] [SUCCESS] Screenshot refreshed
[[09:55:17]] [INFO] Refreshing screenshot...
[[09:55:17]] [INFO] u6bRYZZFAv=pass
[[09:55:11]] [SUCCESS] Screenshot refreshed successfully
[[09:55:11]] [SUCCESS] Screenshot refreshed successfully
[[09:55:10]] [INFO] u6bRYZZFAv=running
[[09:55:10]] [INFO] Executing action 119/351: Wait for 5 ms
[[09:55:10]] [SUCCESS] Screenshot refreshed
[[09:55:10]] [INFO] Refreshing screenshot...
[[09:55:10]] [INFO] pjFNt3w5Fr=pass
[[09:54:55]] [SUCCESS] Screenshot refreshed successfully
[[09:54:55]] [SUCCESS] Screenshot refreshed successfully
[[09:54:55]] [INFO] pjFNt3w5Fr=running
[[09:54:55]] [INFO] Executing action 118/351: Restart app: env[appid]
[[09:54:55]] [SUCCESS] Screenshot refreshed
[[09:54:55]] [INFO] Refreshing screenshot...
[[09:54:54]] [SUCCESS] Screenshot refreshed
[[09:54:54]] [INFO] Refreshing screenshot...
[[09:54:53]] [SUCCESS] Screenshot refreshed successfully
[[09:54:53]] [SUCCESS] Screenshot refreshed successfully
[[09:54:52]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[09:54:52]] [SUCCESS] Screenshot refreshed
[[09:54:52]] [INFO] Refreshing screenshot...
[[09:54:40]] [SUCCESS] Screenshot refreshed successfully
[[09:54:40]] [SUCCESS] Screenshot refreshed successfully
[[09:54:40]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[09:54:40]] [SUCCESS] Screenshot refreshed
[[09:54:40]] [INFO] Refreshing screenshot...
[[09:54:36]] [SUCCESS] Screenshot refreshed successfully
[[09:54:36]] [SUCCESS] Screenshot refreshed successfully
[[09:54:36]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[09:54:36]] [SUCCESS] Screenshot refreshed
[[09:54:36]] [INFO] Refreshing screenshot...
[[09:54:33]] [SUCCESS] Screenshot refreshed successfully
[[09:54:33]] [SUCCESS] Screenshot refreshed successfully
[[09:54:31]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:54:31]] [SUCCESS] Screenshot refreshed
[[09:54:31]] [INFO] Refreshing screenshot...
[[09:54:24]] [SUCCESS] Screenshot refreshed successfully
[[09:54:24]] [SUCCESS] Screenshot refreshed successfully
[[09:54:24]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[09:54:23]] [SUCCESS] Screenshot refreshed
[[09:54:23]] [INFO] Refreshing screenshot...
[[09:54:16]] [SUCCESS] Screenshot refreshed successfully
[[09:54:16]] [SUCCESS] Screenshot refreshed successfully
[[09:54:16]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[09:54:16]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[09:54:16]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[09:54:16]] [INFO] ynNeRrFTrg=running
[[09:54:16]] [INFO] Executing action 117/351: cleanupSteps action
[[09:54:16]] [SUCCESS] Screenshot refreshed
[[09:54:16]] [INFO] Refreshing screenshot...
[[09:54:15]] [SUCCESS] Screenshot refreshed
[[09:54:15]] [INFO] Refreshing screenshot...
[[09:54:11]] [INFO] Executing Multi Step action step 33/33: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[09:54:11]] [SUCCESS] Screenshot refreshed successfully
[[09:54:11]] [SUCCESS] Screenshot refreshed successfully
[[09:54:11]] [SUCCESS] Screenshot refreshed
[[09:54:11]] [INFO] Refreshing screenshot...
[[09:54:07]] [INFO] Executing Multi Step action step 32/33: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:54:07]] [SUCCESS] Screenshot refreshed successfully
[[09:54:07]] [SUCCESS] Screenshot refreshed successfully
[[09:54:06]] [SUCCESS] Screenshot refreshed
[[09:54:06]] [INFO] Refreshing screenshot...
[[09:54:03]] [SUCCESS] Screenshot refreshed successfully
[[09:54:03]] [SUCCESS] Screenshot refreshed successfully
[[09:54:02]] [INFO] Executing Multi Step action step 31/33: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:54:02]] [SUCCESS] Screenshot refreshed
[[09:54:02]] [INFO] Refreshing screenshot...
[[09:53:58]] [SUCCESS] Screenshot refreshed successfully
[[09:53:58]] [SUCCESS] Screenshot refreshed successfully
[[09:53:58]] [INFO] Executing Multi Step action step 30/33: Tap on image: banner-close-updated.png
[[09:53:57]] [SUCCESS] Screenshot refreshed
[[09:53:57]] [INFO] Refreshing screenshot...
[[09:53:53]] [SUCCESS] Screenshot refreshed successfully
[[09:53:53]] [SUCCESS] Screenshot refreshed successfully
[[09:53:53]] [INFO] Executing Multi Step action step 29/33: Tap on image: banner-close-updated.png
[[09:53:53]] [SUCCESS] Screenshot refreshed
[[09:53:53]] [INFO] Refreshing screenshot...
[[09:53:47]] [INFO] Executing Multi Step action step 28/33: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[09:53:47]] [SUCCESS] Screenshot refreshed successfully
[[09:53:47]] [SUCCESS] Screenshot refreshed successfully
[[09:53:47]] [SUCCESS] Screenshot refreshed
[[09:53:47]] [INFO] Refreshing screenshot...
[[09:53:43]] [SUCCESS] Screenshot refreshed successfully
[[09:53:43]] [SUCCESS] Screenshot refreshed successfully
[[09:53:43]] [INFO] Executing Multi Step action step 27/33: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[09:53:42]] [SUCCESS] Screenshot refreshed
[[09:53:42]] [INFO] Refreshing screenshot...
[[09:53:39]] [SUCCESS] Screenshot refreshed successfully
[[09:53:39]] [SUCCESS] Screenshot refreshed successfully
[[09:53:39]] [INFO] Executing Multi Step action step 26/33: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[09:53:38]] [SUCCESS] Screenshot refreshed
[[09:53:38]] [INFO] Refreshing screenshot...
[[09:53:35]] [SUCCESS] Screenshot refreshed successfully
[[09:53:35]] [SUCCESS] Screenshot refreshed successfully
[[09:53:34]] [INFO] Executing Multi Step action step 25/33: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[09:53:34]] [SUCCESS] Screenshot refreshed
[[09:53:34]] [INFO] Refreshing screenshot...
[[09:53:30]] [SUCCESS] Screenshot refreshed successfully
[[09:53:30]] [SUCCESS] Screenshot refreshed successfully
[[09:53:30]] [INFO] Executing Multi Step action step 24/33: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[09:53:30]] [SUCCESS] Screenshot refreshed
[[09:53:30]] [INFO] Refreshing screenshot...
[[09:53:26]] [SUCCESS] Screenshot refreshed successfully
[[09:53:26]] [SUCCESS] Screenshot refreshed successfully
[[09:53:26]] [INFO] Executing Multi Step action step 23/33: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[09:53:26]] [SUCCESS] Screenshot refreshed
[[09:53:26]] [INFO] Refreshing screenshot...
[[09:53:18]] [SUCCESS] Screenshot refreshed successfully
[[09:53:18]] [SUCCESS] Screenshot refreshed successfully
[[09:53:18]] [INFO] Executing Multi Step action step 22/33: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[09:53:17]] [SUCCESS] Screenshot refreshed
[[09:53:17]] [INFO] Refreshing screenshot...
[[09:53:14]] [SUCCESS] Screenshot refreshed successfully
[[09:53:14]] [SUCCESS] Screenshot refreshed successfully
[[09:53:13]] [INFO] Executing Multi Step action step 21/33: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[09:53:13]] [SUCCESS] Screenshot refreshed
[[09:53:13]] [INFO] Refreshing screenshot...
[[09:53:09]] [SUCCESS] Screenshot refreshed successfully
[[09:53:09]] [SUCCESS] Screenshot refreshed successfully
[[09:53:09]] [INFO] Executing Multi Step action step 20/33: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[09:53:09]] [SUCCESS] Screenshot refreshed
[[09:53:09]] [INFO] Refreshing screenshot...
[[09:53:01]] [SUCCESS] Screenshot refreshed successfully
[[09:53:01]] [SUCCESS] Screenshot refreshed successfully
[[09:53:01]] [INFO] Executing Multi Step action step 19/33: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[09:53:00]] [SUCCESS] Screenshot refreshed
[[09:53:00]] [INFO] Refreshing screenshot...
[[09:52:55]] [SUCCESS] Screenshot refreshed successfully
[[09:52:55]] [SUCCESS] Screenshot refreshed successfully
[[09:52:55]] [INFO] Executing Multi Step action step 18/33: Tap on Text: "Quay"
[[09:52:54]] [SUCCESS] Screenshot refreshed
[[09:52:54]] [INFO] Refreshing screenshot...
[[09:52:50]] [SUCCESS] Screenshot refreshed successfully
[[09:52:50]] [SUCCESS] Screenshot refreshed successfully
[[09:52:50]] [INFO] Executing Multi Step action step 17/33: Tap on image: keyboard_done_iphoneSE.png
[[09:52:50]] [SUCCESS] Screenshot refreshed
[[09:52:50]] [INFO] Refreshing screenshot...
[[09:52:43]] [SUCCESS] Screenshot refreshed successfully
[[09:52:43]] [SUCCESS] Screenshot refreshed successfully
[[09:52:43]] [INFO] Executing Multi Step action step 16/33: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "Quay Street"
[[09:52:42]] [SUCCESS] Screenshot refreshed
[[09:52:42]] [INFO] Refreshing screenshot...
[[09:52:37]] [SUCCESS] Screenshot refreshed successfully
[[09:52:37]] [SUCCESS] Screenshot refreshed successfully
[[09:52:37]] [INFO] Executing Multi Step action step 15/33: Tap on Text: "address"
[[09:52:37]] [SUCCESS] Screenshot refreshed
[[09:52:37]] [INFO] Refreshing screenshot...
[[09:52:25]] [SUCCESS] Screenshot refreshed successfully
[[09:52:25]] [SUCCESS] Screenshot refreshed successfully
[[09:52:25]] [INFO] Executing Multi Step action step 14/33: Wait for 10 ms
[[09:52:24]] [SUCCESS] Screenshot refreshed
[[09:52:24]] [INFO] Refreshing screenshot...
[[09:52:21]] [SUCCESS] Screenshot refreshed successfully
[[09:52:21]] [SUCCESS] Screenshot refreshed successfully
[[09:52:21]] [INFO] Executing Multi Step action step 13/33: iOS Function: text - Text: " "
[[09:52:20]] [SUCCESS] Screenshot refreshed
[[09:52:20]] [INFO] Refreshing screenshot...
[[09:52:16]] [SUCCESS] Screenshot refreshed successfully
[[09:52:16]] [SUCCESS] Screenshot refreshed successfully
[[09:52:16]] [INFO] Executing Multi Step action step 12/33: textClear action
[[09:52:15]] [SUCCESS] Screenshot refreshed
[[09:52:15]] [INFO] Refreshing screenshot...
[[09:52:12]] [SUCCESS] Screenshot refreshed successfully
[[09:52:12]] [SUCCESS] Screenshot refreshed successfully
[[09:52:12]] [INFO] Executing Multi Step action step 11/33: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[09:52:11]] [SUCCESS] Screenshot refreshed
[[09:52:11]] [INFO] Refreshing screenshot...
[[09:52:07]] [SUCCESS] Screenshot refreshed successfully
[[09:52:07]] [SUCCESS] Screenshot refreshed successfully
[[09:52:07]] [INFO] Executing Multi Step action step 10/33: textClear action
[[09:52:07]] [SUCCESS] Screenshot refreshed
[[09:52:07]] [INFO] Refreshing screenshot...
[[09:52:02]] [SUCCESS] Screenshot refreshed successfully
[[09:52:02]] [SUCCESS] Screenshot refreshed successfully
[[09:52:02]] [INFO] Executing Multi Step action step 9/33: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:52:02]] [SUCCESS] Screenshot refreshed
[[09:52:02]] [INFO] Refreshing screenshot...
[[09:51:57]] [SUCCESS] Screenshot refreshed successfully
[[09:51:57]] [SUCCESS] Screenshot refreshed successfully
[[09:51:57]] [INFO] Executing Multi Step action step 8/33: textClear action
[[09:51:57]] [SUCCESS] Screenshot refreshed
[[09:51:57]] [INFO] Refreshing screenshot...
[[09:51:53]] [SUCCESS] Screenshot refreshed successfully
[[09:51:53]] [SUCCESS] Screenshot refreshed successfully
[[09:51:53]] [INFO] Executing Multi Step action step 7/33: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[09:51:53]] [SUCCESS] Screenshot refreshed
[[09:51:53]] [INFO] Refreshing screenshot...
[[09:51:49]] [SUCCESS] Screenshot refreshed successfully
[[09:51:49]] [SUCCESS] Screenshot refreshed successfully
[[09:51:49]] [INFO] Executing Multi Step action step 6/33: textClear action
[[09:51:48]] [SUCCESS] Screenshot refreshed
[[09:51:48]] [INFO] Refreshing screenshot...
[[09:51:45]] [SUCCESS] Screenshot refreshed successfully
[[09:51:45]] [SUCCESS] Screenshot refreshed successfully
[[09:51:45]] [INFO] Executing Multi Step action step 5/33: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[09:51:44]] [SUCCESS] Screenshot refreshed
[[09:51:44]] [INFO] Refreshing screenshot...
[[09:51:40]] [SUCCESS] Screenshot refreshed successfully
[[09:51:40]] [SUCCESS] Screenshot refreshed successfully
[[09:51:40]] [INFO] Executing Multi Step action step 4/33: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[09:51:40]] [SUCCESS] Screenshot refreshed
[[09:51:40]] [INFO] Refreshing screenshot...
[[09:51:29]] [SUCCESS] Screenshot refreshed successfully
[[09:51:29]] [SUCCESS] Screenshot refreshed successfully
[[09:51:29]] [INFO] Executing Multi Step action step 3/33: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[09:51:29]] [SUCCESS] Screenshot refreshed
[[09:51:29]] [INFO] Refreshing screenshot...
[[09:51:25]] [SUCCESS] Screenshot refreshed successfully
[[09:51:25]] [SUCCESS] Screenshot refreshed successfully
[[09:51:25]] [INFO] Executing Multi Step action step 2/33: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[09:51:25]] [SUCCESS] Screenshot refreshed
[[09:51:25]] [INFO] Refreshing screenshot...
[[09:51:19]] [INFO] Executing Multi Step action step 1/33: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[09:51:19]] [INFO] Loaded 33 steps from test case: Delivery Buy Step NZ
[[09:51:19]] [SUCCESS] Screenshot refreshed successfully
[[09:51:19]] [SUCCESS] Screenshot refreshed successfully
[[09:51:19]] [INFO] Loading steps for multiStep action: Delivery Buy Step NZ
[[09:51:19]] [INFO] MuX1dfl3aB=running
[[09:51:19]] [INFO] Executing action 116/351: Execute Test Case: Delivery Buy Step NZ (33 steps)
[[09:51:18]] [SUCCESS] Screenshot refreshed
[[09:51:18]] [INFO] Refreshing screenshot...
[[09:51:18]] [INFO] jY0oPjKbuS=pass
[[09:51:15]] [SUCCESS] Screenshot refreshed successfully
[[09:51:15]] [SUCCESS] Screenshot refreshed successfully
[[09:51:15]] [INFO] jY0oPjKbuS=running
[[09:51:15]] [INFO] Executing action 115/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:51:14]] [SUCCESS] Screenshot refreshed
[[09:51:14]] [INFO] Refreshing screenshot...
[[09:51:14]] [INFO] FnrbyHq7bU=pass
[[09:51:11]] [SUCCESS] Screenshot refreshed successfully
[[09:51:11]] [SUCCESS] Screenshot refreshed successfully
[[09:51:10]] [INFO] FnrbyHq7bU=running
[[09:51:10]] [INFO] Executing action 114/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[09:51:10]] [SUCCESS] Screenshot refreshed
[[09:51:10]] [INFO] Refreshing screenshot...
[[09:51:10]] [INFO] nAB6Q8LAdv=pass
[[09:51:07]] [SUCCESS] Screenshot refreshed successfully
[[09:51:07]] [SUCCESS] Screenshot refreshed successfully
[[09:51:07]] [INFO] nAB6Q8LAdv=running
[[09:51:07]] [INFO] Executing action 113/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:51:06]] [SUCCESS] Screenshot refreshed
[[09:51:06]] [INFO] Refreshing screenshot...
[[09:51:06]] [INFO] 13YG4jrM9E=pass
[[09:51:00]] [SUCCESS] Screenshot refreshed successfully
[[09:51:00]] [SUCCESS] Screenshot refreshed successfully
[[09:51:00]] [INFO] 13YG4jrM9E=running
[[09:51:00]] [INFO] Executing action 112/351: iOS Function: text - Text: "P_43250042"
[[09:51:00]] [SUCCESS] Screenshot refreshed
[[09:51:00]] [INFO] Refreshing screenshot...
[[09:51:00]] [INFO] rqLJpAP0mA=pass
[[09:50:55]] [SUCCESS] Screenshot refreshed successfully
[[09:50:55]] [SUCCESS] Screenshot refreshed successfully
[[09:50:54]] [INFO] rqLJpAP0mA=running
[[09:50:54]] [INFO] Executing action 111/351: Tap on Text: "Find"
[[09:50:54]] [SUCCESS] Screenshot refreshed
[[09:50:54]] [INFO] Refreshing screenshot...
[[09:50:54]] [INFO] cKNu2QoRC1=pass
[[09:50:50]] [SUCCESS] Screenshot refreshed successfully
[[09:50:50]] [SUCCESS] Screenshot refreshed successfully
[[09:50:49]] [INFO] cKNu2QoRC1=running
[[09:50:49]] [INFO] Executing action 110/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:50:49]] [SUCCESS] Screenshot refreshed
[[09:50:49]] [INFO] Refreshing screenshot...
[[09:50:49]] [INFO] OyUowAaBzD=pass
[[09:50:45]] [SUCCESS] Screenshot refreshed successfully
[[09:50:45]] [SUCCESS] Screenshot refreshed successfully
[[09:50:45]] [INFO] OyUowAaBzD=running
[[09:50:45]] [INFO] Executing action 109/351: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[09:50:44]] [SUCCESS] Screenshot refreshed
[[09:50:44]] [INFO] Refreshing screenshot...
[[09:50:44]] [INFO] Ob26qqcA0p=pass
[[09:50:37]] [SUCCESS] Screenshot refreshed successfully
[[09:50:37]] [SUCCESS] Screenshot refreshed successfully
[[09:50:37]] [INFO] Ob26qqcA0p=running
[[09:50:37]] [INFO] Executing action 108/351: Swipe from (50%, 70%) to (50%, 30%)
[[09:50:37]] [SUCCESS] Screenshot refreshed
[[09:50:37]] [INFO] Refreshing screenshot...
[[09:50:37]] [INFO] k3mu9Mt7Ec=pass
[[09:50:34]] [SUCCESS] Screenshot refreshed successfully
[[09:50:34]] [SUCCESS] Screenshot refreshed successfully
[[09:50:33]] [INFO] k3mu9Mt7Ec=running
[[09:50:33]] [INFO] Executing action 107/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:50:32]] [SUCCESS] Screenshot refreshed
[[09:50:32]] [INFO] Refreshing screenshot...
[[09:50:32]] [INFO] 8umPSX0vrr=pass
[[09:50:28]] [SUCCESS] Screenshot refreshed successfully
[[09:50:28]] [SUCCESS] Screenshot refreshed successfully
[[09:50:28]] [INFO] 8umPSX0vrr=running
[[09:50:28]] [INFO] Executing action 106/351: Tap on image: banner-close-updated.png
[[09:50:28]] [SUCCESS] Screenshot refreshed
[[09:50:28]] [INFO] Refreshing screenshot...
[[09:50:28]] [INFO] pr9o8Zsm5p=pass
[[09:50:24]] [SUCCESS] Screenshot refreshed successfully
[[09:50:24]] [SUCCESS] Screenshot refreshed successfully
[[09:50:24]] [INFO] pr9o8Zsm5p=running
[[09:50:24]] [INFO] Executing action 105/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:50:23]] [SUCCESS] Screenshot refreshed
[[09:50:23]] [INFO] Refreshing screenshot...
[[09:50:23]] [INFO] Qbg9bipTGs=pass
[[09:50:19]] [SUCCESS] Screenshot refreshed successfully
[[09:50:19]] [SUCCESS] Screenshot refreshed successfully
[[09:50:19]] [INFO] Qbg9bipTGs=running
[[09:50:19]] [INFO] Executing action 104/351: Swipe from (50%, 70%) to (50%, 30%)
[[09:50:18]] [SUCCESS] Screenshot refreshed
[[09:50:18]] [INFO] Refreshing screenshot...
[[09:50:18]] [INFO] qjj0i3rcUh=pass
[[09:50:13]] [SUCCESS] Screenshot refreshed successfully
[[09:50:13]] [SUCCESS] Screenshot refreshed successfully
[[09:50:13]] [INFO] qjj0i3rcUh=running
[[09:50:13]] [INFO] Executing action 103/351: Tap on Text: "Collect"
[[09:50:13]] [SUCCESS] Screenshot refreshed
[[09:50:13]] [INFO] Refreshing screenshot...
[[09:50:13]] [INFO] uM5FOSrU5U=pass
[[09:50:00]] [SUCCESS] Screenshot refreshed successfully
[[09:50:00]] [SUCCESS] Screenshot refreshed successfully
[[09:50:00]] [INFO] uM5FOSrU5U=running
[[09:50:00]] [INFO] Executing action 102/351: Check if image "cnc-tab-se.png" exists on screen
[[09:49:59]] [SUCCESS] Screenshot refreshed
[[09:49:59]] [INFO] Refreshing screenshot...
[[09:49:59]] [INFO] F1olhgKhUt=pass
[[09:49:56]] [SUCCESS] Screenshot refreshed successfully
[[09:49:56]] [SUCCESS] Screenshot refreshed successfully
[[09:49:56]] [INFO] F1olhgKhUt=running
[[09:49:56]] [INFO] Executing action 101/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:49:55]] [SUCCESS] Screenshot refreshed
[[09:49:55]] [INFO] Refreshing screenshot...
[[09:49:55]] [INFO] jY0oPjKbuS=pass
[[09:49:52]] [SUCCESS] Screenshot refreshed successfully
[[09:49:52]] [SUCCESS] Screenshot refreshed successfully
[[09:49:52]] [INFO] jY0oPjKbuS=running
[[09:49:52]] [INFO] Executing action 100/351: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[09:49:52]] [SUCCESS] Screenshot refreshed
[[09:49:52]] [INFO] Refreshing screenshot...
[[09:49:52]] [INFO] FnrbyHq7bU=pass
[[09:49:48]] [SUCCESS] Screenshot refreshed successfully
[[09:49:48]] [SUCCESS] Screenshot refreshed successfully
[[09:49:48]] [INFO] FnrbyHq7bU=running
[[09:49:48]] [INFO] Executing action 99/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[09:49:47]] [SUCCESS] Screenshot refreshed
[[09:49:47]] [INFO] Refreshing screenshot...
[[09:49:47]] [INFO] nAB6Q8LAdv=pass
[[09:49:44]] [SUCCESS] Screenshot refreshed successfully
[[09:49:44]] [SUCCESS] Screenshot refreshed successfully
[[09:49:44]] [INFO] nAB6Q8LAdv=running
[[09:49:44]] [INFO] Executing action 98/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:49:43]] [SUCCESS] Screenshot refreshed
[[09:49:43]] [INFO] Refreshing screenshot...
[[09:49:43]] [INFO] 13YG4jrM9E=pass
[[09:49:39]] [SUCCESS] Screenshot refreshed successfully
[[09:49:39]] [SUCCESS] Screenshot refreshed successfully
[[09:49:39]] [INFO] 13YG4jrM9E=running
[[09:49:39]] [INFO] Executing action 97/351: iOS Function: text - Text: "P_43250042"
[[09:49:39]] [SUCCESS] Screenshot refreshed
[[09:49:39]] [INFO] Refreshing screenshot...
[[09:49:39]] [INFO] rqLJpAP0mA=pass
[[09:49:34]] [SUCCESS] Screenshot refreshed successfully
[[09:49:34]] [SUCCESS] Screenshot refreshed successfully
[[09:49:33]] [INFO] rqLJpAP0mA=running
[[09:49:33]] [INFO] Executing action 96/351: Tap on Text: "Find"
[[09:49:33]] [SUCCESS] Screenshot refreshed
[[09:49:33]] [INFO] Refreshing screenshot...
[[09:49:32]] [SUCCESS] Screenshot refreshed
[[09:49:32]] [INFO] Refreshing screenshot...
[[09:49:28]] [SUCCESS] Screenshot refreshed successfully
[[09:49:28]] [SUCCESS] Screenshot refreshed successfully
[[09:49:28]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[09:49:27]] [SUCCESS] Screenshot refreshed
[[09:49:27]] [INFO] Refreshing screenshot...
[[09:49:23]] [SUCCESS] Screenshot refreshed successfully
[[09:49:23]] [SUCCESS] Screenshot refreshed successfully
[[09:49:23]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:49:22]] [SUCCESS] Screenshot refreshed
[[09:49:22]] [INFO] Refreshing screenshot...
[[09:49:18]] [SUCCESS] Screenshot refreshed successfully
[[09:49:18]] [SUCCESS] Screenshot refreshed successfully
[[09:49:18]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[09:49:17]] [SUCCESS] Screenshot refreshed
[[09:49:17]] [INFO] Refreshing screenshot...
[[09:49:13]] [SUCCESS] Screenshot refreshed successfully
[[09:49:13]] [SUCCESS] Screenshot refreshed successfully
[[09:49:13]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:49:13]] [SUCCESS] Screenshot refreshed
[[09:49:13]] [INFO] Refreshing screenshot...
[[09:49:07]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:49:07]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[09:49:07]] [SUCCESS] Screenshot refreshed successfully
[[09:49:07]] [SUCCESS] Screenshot refreshed successfully
[[09:49:07]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[09:49:07]] [INFO] SVt620PG1t=running
[[09:49:07]] [INFO] Executing action 95/351: Execute Test Case: Kmart-NZ-Signin (6 steps)
[[09:49:06]] [SUCCESS] Screenshot refreshed
[[09:49:06]] [INFO] Refreshing screenshot...
[[09:49:06]] [INFO] 3caMBvQX7k=pass
[[09:49:03]] [SUCCESS] Screenshot refreshed successfully
[[09:49:03]] [SUCCESS] Screenshot refreshed successfully
[[09:49:03]] [INFO] 3caMBvQX7k=running
[[09:49:03]] [INFO] Executing action 94/351: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:49:02]] [SUCCESS] Screenshot refreshed
[[09:49:02]] [INFO] Refreshing screenshot...
[[09:49:02]] [INFO] yUJyVO5Wev=pass
[[09:49:00]] [SUCCESS] Screenshot refreshed successfully
[[09:49:00]] [SUCCESS] Screenshot refreshed successfully
[[09:48:59]] [INFO] yUJyVO5Wev=running
[[09:48:59]] [INFO] Executing action 93/351: iOS Function: alert_accept
[[09:48:59]] [SUCCESS] Screenshot refreshed
[[09:48:59]] [INFO] Refreshing screenshot...
[[09:48:59]] [INFO] rkL0oz4kiL=pass
[[09:48:52]] [SUCCESS] Screenshot refreshed successfully
[[09:48:52]] [SUCCESS] Screenshot refreshed successfully
[[09:48:52]] [INFO] rkL0oz4kiL=running
[[09:48:52]] [INFO] Executing action 92/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:48:51]] [SUCCESS] Screenshot refreshed
[[09:48:51]] [INFO] Refreshing screenshot...
[[09:48:51]] [INFO] HotUJOd6oB=pass
[[09:48:37]] [SUCCESS] Screenshot refreshed successfully
[[09:48:37]] [SUCCESS] Screenshot refreshed successfully
[[09:48:36]] [INFO] HotUJOd6oB=running
[[09:48:36]] [INFO] Executing action 91/351: Restart app: env[appid]
[[09:48:36]] [SUCCESS] Screenshot refreshed
[[09:48:36]] [INFO] Refreshing screenshot...
[[09:48:35]] [SUCCESS] Screenshot refreshed
[[09:48:35]] [INFO] Refreshing screenshot...
[[09:48:34]] [SUCCESS] Screenshot refreshed successfully
[[09:48:34]] [SUCCESS] Screenshot refreshed successfully
[[09:48:34]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[09:48:33]] [SUCCESS] Screenshot refreshed
[[09:48:33]] [INFO] Refreshing screenshot...
[[09:48:22]] [SUCCESS] Screenshot refreshed successfully
[[09:48:22]] [SUCCESS] Screenshot refreshed successfully
[[09:48:22]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[09:48:21]] [SUCCESS] Screenshot refreshed
[[09:48:21]] [INFO] Refreshing screenshot...
[[09:48:18]] [SUCCESS] Screenshot refreshed successfully
[[09:48:18]] [SUCCESS] Screenshot refreshed successfully
[[09:48:17]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[09:48:17]] [SUCCESS] Screenshot refreshed
[[09:48:17]] [INFO] Refreshing screenshot...
[[09:48:14]] [SUCCESS] Screenshot refreshed successfully
[[09:48:14]] [SUCCESS] Screenshot refreshed successfully
[[09:48:13]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:48:13]] [SUCCESS] Screenshot refreshed
[[09:48:13]] [INFO] Refreshing screenshot...
[[09:48:06]] [SUCCESS] Screenshot refreshed successfully
[[09:48:06]] [SUCCESS] Screenshot refreshed successfully
[[09:48:06]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[09:48:05]] [SUCCESS] Screenshot refreshed
[[09:48:05]] [INFO] Refreshing screenshot...
[[09:47:59]] [SUCCESS] Screenshot refreshed successfully
[[09:47:59]] [SUCCESS] Screenshot refreshed successfully
[[09:47:59]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[09:47:59]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[09:47:59]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[09:47:59]] [INFO] 4SHkoWH0yY=running
[[09:47:59]] [INFO] Executing action 90/351: cleanupSteps action
[[09:47:58]] [SUCCESS] Screenshot refreshed
[[09:47:58]] [INFO] Refreshing screenshot...
[[09:47:58]] [INFO] x4yLCZHaCR=pass
[[09:47:55]] [INFO] x4yLCZHaCR=running
[[09:47:55]] [INFO] Executing action 89/351: Terminate app: env[appid]
[[09:47:55]] [SUCCESS] Screenshot refreshed successfully
[[09:47:55]] [SUCCESS] Screenshot refreshed successfully
[[09:47:54]] [SUCCESS] Screenshot refreshed
[[09:47:54]] [INFO] Refreshing screenshot...
[[09:47:54]] [INFO] 2p13JoJbbA=pass
[[09:47:51]] [SUCCESS] Screenshot refreshed successfully
[[09:47:51]] [SUCCESS] Screenshot refreshed successfully
[[09:47:50]] [INFO] 2p13JoJbbA=running
[[09:47:50]] [INFO] Executing action 88/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:47:50]] [SUCCESS] Screenshot refreshed
[[09:47:50]] [INFO] Refreshing screenshot...
[[09:47:50]] [INFO] 2p13JoJbbA=pass
[[09:47:46]] [SUCCESS] Screenshot refreshed successfully
[[09:47:46]] [SUCCESS] Screenshot refreshed successfully
[[09:47:46]] [INFO] 2p13JoJbbA=running
[[09:47:46]] [INFO] Executing action 87/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:47:46]] [SUCCESS] Screenshot refreshed
[[09:47:46]] [INFO] Refreshing screenshot...
[[09:47:46]] [INFO] ZCsqeOXrY1=pass
[[09:47:42]] [INFO] ZCsqeOXrY1=running
[[09:47:42]] [INFO] Executing action 86/351: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Remove")]
[[09:47:42]] [SUCCESS] Screenshot refreshed successfully
[[09:47:42]] [SUCCESS] Screenshot refreshed successfully
[[09:47:42]] [SUCCESS] Screenshot refreshed
[[09:47:42]] [INFO] Refreshing screenshot...
[[09:47:42]] [INFO] F4NGh9HrLw=pass
[[09:47:38]] [SUCCESS] Screenshot refreshed successfully
[[09:47:38]] [SUCCESS] Screenshot refreshed successfully
[[09:47:38]] [INFO] F4NGh9HrLw=running
[[09:47:38]] [INFO] Executing action 85/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:47:37]] [SUCCESS] Screenshot refreshed
[[09:47:37]] [INFO] Refreshing screenshot...
[[09:47:37]] [INFO] kz9lnCdwoH=pass
[[09:47:34]] [SUCCESS] Screenshot refreshed successfully
[[09:47:34]] [SUCCESS] Screenshot refreshed successfully
[[09:47:33]] [INFO] kz9lnCdwoH=running
[[09:47:33]] [INFO] Executing action 84/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[09:47:32]] [SUCCESS] Screenshot refreshed
[[09:47:32]] [INFO] Refreshing screenshot...
[[09:47:32]] [INFO] KlfYmNjrq8=pass
[[09:47:28]] [SUCCESS] Screenshot refreshed successfully
[[09:47:28]] [SUCCESS] Screenshot refreshed successfully
[[09:47:28]] [INFO] KlfYmNjrq8=running
[[09:47:28]] [INFO] Executing action 83/351: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:47:27]] [SUCCESS] Screenshot refreshed
[[09:47:27]] [INFO] Refreshing screenshot...
[[09:47:27]] [INFO] iwqbVl90WJ=pass
[[09:47:23]] [SUCCESS] Screenshot refreshed successfully
[[09:47:23]] [SUCCESS] Screenshot refreshed successfully
[[09:47:23]] [INFO] iwqbVl90WJ=running
[[09:47:23]] [INFO] Executing action 82/351: iOS Function: text - Text: "notebook"
[[09:47:23]] [SUCCESS] Screenshot refreshed
[[09:47:23]] [INFO] Refreshing screenshot...
[[09:47:23]] [INFO] DY8MfL0wXI=pass
[[09:47:17]] [SUCCESS] Screenshot refreshed successfully
[[09:47:17]] [SUCCESS] Screenshot refreshed successfully
[[09:47:17]] [INFO] DY8MfL0wXI=running
[[09:47:17]] [INFO] Executing action 81/351: Tap on Text: "Find"
[[09:47:16]] [SUCCESS] Screenshot refreshed
[[09:47:16]] [INFO] Refreshing screenshot...
[[09:47:16]] [INFO] Ey8MUB57vM=pass
[[09:47:11]] [SUCCESS] Screenshot refreshed successfully
[[09:47:11]] [SUCCESS] Screenshot refreshed successfully
[[09:47:11]] [INFO] Ey8MUB57vM=running
[[09:47:11]] [INFO] Executing action 80/351: Restart app: env[appid]
[[09:47:10]] [SUCCESS] Screenshot refreshed
[[09:47:10]] [INFO] Refreshing screenshot...
[[09:47:10]] [INFO] 5Gj5mgIxVu=pass
[[09:47:06]] [SUCCESS] Screenshot refreshed successfully
[[09:47:06]] [SUCCESS] Screenshot refreshed successfully
[[09:47:06]] [INFO] 5Gj5mgIxVu=running
[[09:47:06]] [INFO] Executing action 79/351: Tap on image: env[device-back-img]
[[09:47:06]] [SUCCESS] Screenshot refreshed
[[09:47:06]] [INFO] Refreshing screenshot...
[[09:47:06]] [INFO] QPKR6jUF9O=pass
[[09:47:04]] [SUCCESS] Screenshot refreshed successfully
[[09:47:04]] [SUCCESS] Screenshot refreshed successfully
[[09:47:03]] [INFO] QPKR6jUF9O=running
[[09:47:03]] [INFO] Executing action 78/351: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[09:47:02]] [SUCCESS] Screenshot refreshed
[[09:47:02]] [INFO] Refreshing screenshot...
[[09:47:02]] [INFO] vfwUVEyq6X=pass
[[09:46:59]] [SUCCESS] Screenshot refreshed successfully
[[09:46:59]] [SUCCESS] Screenshot refreshed successfully
[[09:46:59]] [INFO] vfwUVEyq6X=running
[[09:46:59]] [INFO] Executing action 77/351: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[09:46:59]] [SUCCESS] Screenshot refreshed
[[09:46:59]] [INFO] Refreshing screenshot...
[[09:46:59]] [INFO] Xr6F8gdd8q=pass
[[09:46:55]] [SUCCESS] Screenshot refreshed successfully
[[09:46:55]] [SUCCESS] Screenshot refreshed successfully
[[09:46:55]] [INFO] Xr6F8gdd8q=running
[[09:46:55]] [INFO] Executing action 76/351: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[09:46:54]] [SUCCESS] Screenshot refreshed
[[09:46:54]] [INFO] Refreshing screenshot...
[[09:46:54]] [INFO] 92tKl3T5N8=pass
[[09:46:51]] [SUCCESS] Screenshot refreshed successfully
[[09:46:51]] [SUCCESS] Screenshot refreshed successfully
[[09:46:50]] [INFO] 92tKl3T5N8=running
[[09:46:50]] [INFO] Executing action 75/351: Tap on image: env[device-back-img]
[[09:46:50]] [SUCCESS] Screenshot refreshed
[[09:46:50]] [INFO] Refreshing screenshot...
[[09:46:50]] [INFO] ky6rfmPv0u=pass
[[09:46:46]] [SUCCESS] Screenshot refreshed successfully
[[09:46:46]] [SUCCESS] Screenshot refreshed successfully
[[09:46:46]] [INFO] ky6rfmPv0u=running
[[09:46:46]] [INFO] Executing action 74/351: Tap on image: env[device-back-img]
[[09:46:45]] [SUCCESS] Screenshot refreshed
[[09:46:45]] [INFO] Refreshing screenshot...
[[09:46:45]] [INFO] fPX582qHkp=pass
[[09:46:22]] [SUCCESS] Screenshot refreshed successfully
[[09:46:22]] [SUCCESS] Screenshot refreshed successfully
[[09:46:22]] [INFO] fPX582qHkp=running
[[09:46:22]] [INFO] Executing action 73/351: Check if image "search-result-test-se.png" exists on screen
[[09:46:21]] [SUCCESS] Screenshot refreshed
[[09:46:21]] [INFO] Refreshing screenshot...
[[09:46:21]] [INFO] JRheDTvpJf=pass
[[09:46:17]] [SUCCESS] Screenshot refreshed successfully
[[09:46:17]] [SUCCESS] Screenshot refreshed successfully
[[09:46:17]] [INFO] JRheDTvpJf=running
[[09:46:17]] [INFO] Executing action 72/351: iOS Function: text - Text: "Kid toy"
[[09:46:16]] [SUCCESS] Screenshot refreshed
[[09:46:16]] [INFO] Refreshing screenshot...
[[09:46:16]] [INFO] yEga5MkcRe=pass
[[09:46:13]] [SUCCESS] Screenshot refreshed successfully
[[09:46:13]] [SUCCESS] Screenshot refreshed successfully
[[09:46:13]] [INFO] yEga5MkcRe=running
[[09:46:13]] [INFO] Executing action 71/351: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[09:46:12]] [SUCCESS] Screenshot refreshed
[[09:46:12]] [INFO] Refreshing screenshot...
[[09:46:12]] [INFO] F4NGh9HrLw=pass
[[09:46:08]] [INFO] F4NGh9HrLw=running
[[09:46:08]] [INFO] Executing action 70/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[09:46:08]] [SUCCESS] Screenshot refreshed successfully
[[09:46:08]] [SUCCESS] Screenshot refreshed successfully
[[09:46:08]] [SUCCESS] Screenshot refreshed
[[09:46:08]] [INFO] Refreshing screenshot...
[[09:46:08]] [INFO] DhWa2PCBXE=pass
[[09:46:04]] [SUCCESS] Screenshot refreshed successfully
[[09:46:04]] [SUCCESS] Screenshot refreshed successfully
[[09:46:03]] [INFO] DhWa2PCBXE=running
[[09:46:03]] [INFO] Executing action 69/351: Tap on Text: "more"
[[09:46:02]] [SUCCESS] Screenshot refreshed
[[09:46:02]] [INFO] Refreshing screenshot...
[[09:46:02]] [INFO] pk2DLZFBmx=pass
[[09:45:57]] [SUCCESS] Screenshot refreshed successfully
[[09:45:57]] [SUCCESS] Screenshot refreshed successfully
[[09:45:57]] [INFO] pk2DLZFBmx=running
[[09:45:57]] [INFO] Executing action 68/351: Swipe from (50%, 70%) to (50%, 50%)
[[09:45:56]] [SUCCESS] Screenshot refreshed
[[09:45:56]] [INFO] Refreshing screenshot...
[[09:45:56]] [INFO] F9UfvzyNii=pass
[[09:45:53]] [SUCCESS] Screenshot refreshed successfully
[[09:45:53]] [SUCCESS] Screenshot refreshed successfully
[[09:45:52]] [INFO] F9UfvzyNii=running
[[09:45:52]] [INFO] Executing action 67/351: Tap on element with xpath: //XCUIElementTypeButton[@name="Add to bag"]
[[09:45:52]] [SUCCESS] Screenshot refreshed
[[09:45:52]] [INFO] Refreshing screenshot...
[[09:45:52]] [INFO] s0WyiD1w0B=pass
[[09:45:48]] [SUCCESS] Screenshot refreshed successfully
[[09:45:48]] [SUCCESS] Screenshot refreshed successfully
[[09:45:47]] [INFO] s0WyiD1w0B=running
[[09:45:47]] [INFO] Executing action 66/351: Tap on image: banner-close-updated.png
[[09:45:47]] [SUCCESS] Screenshot refreshed
[[09:45:47]] [INFO] Refreshing screenshot...
[[09:45:47]] [INFO] gekNSY5O2E=pass
[[09:45:34]] [SUCCESS] Screenshot refreshed successfully
[[09:45:34]] [SUCCESS] Screenshot refreshed successfully
[[09:45:33]] [INFO] gekNSY5O2E=running
[[09:45:33]] [INFO] Executing action 65/351: Check if image "product-share-logo.png" exists on screen
[[09:45:32]] [SUCCESS] Screenshot refreshed
[[09:45:32]] [INFO] Refreshing screenshot...
[[09:45:32]] [INFO] 83tV9A4NOn=pass
[[09:45:29]] [SUCCESS] Screenshot refreshed successfully
[[09:45:29]] [SUCCESS] Screenshot refreshed successfully
[[09:45:28]] [INFO] 83tV9A4NOn=running
[[09:45:28]] [INFO] Executing action 64/351: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[09:45:28]] [SUCCESS] Screenshot refreshed
[[09:45:28]] [INFO] Refreshing screenshot...
[[09:45:28]] [INFO] YbamBpASJi=pass
[[09:45:24]] [SUCCESS] Screenshot refreshed successfully
[[09:45:24]] [SUCCESS] Screenshot refreshed successfully
[[09:45:23]] [INFO] YbamBpASJi=running
[[09:45:23]] [INFO] Executing action 63/351: Tap on image: env[product-share-img]
[[09:45:23]] [SUCCESS] Screenshot refreshed
[[09:45:23]] [INFO] Refreshing screenshot...
[[09:45:23]] [INFO] zWrzEgdH3Q=pass
[[09:45:19]] [INFO] zWrzEgdH3Q=running
[[09:45:19]] [INFO] Executing action 62/351: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[09:45:19]] [SUCCESS] Screenshot refreshed successfully
[[09:45:19]] [SUCCESS] Screenshot refreshed successfully
[[09:45:18]] [SUCCESS] Screenshot refreshed
[[09:45:18]] [INFO] Refreshing screenshot...
[[09:45:18]] [INFO] kAQ1yIIw3h=pass
[[09:45:15]] [SUCCESS] Screenshot refreshed successfully
[[09:45:15]] [SUCCESS] Screenshot refreshed successfully
[[09:45:14]] [INFO] kAQ1yIIw3h=running
[[09:45:14]] [INFO] Executing action 61/351: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)
[[09:45:14]] [SUCCESS] Screenshot refreshed
[[09:45:14]] [INFO] Refreshing screenshot...
[[09:45:14]] [INFO] OmKfD9iBjD=pass
[[09:45:10]] [SUCCESS] Screenshot refreshed successfully
[[09:45:10]] [SUCCESS] Screenshot refreshed successfully
[[09:45:10]] [INFO] OmKfD9iBjD=running
[[09:45:10]] [INFO] Executing action 60/351: Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::*
[[09:45:10]] [SUCCESS] Screenshot refreshed
[[09:45:10]] [INFO] Refreshing screenshot...
[[09:45:10]] [INFO] eHLWiRoqqS=pass
[[09:45:05]] [SUCCESS] Screenshot refreshed successfully
[[09:45:05]] [SUCCESS] Screenshot refreshed successfully
[[09:45:05]] [INFO] eHLWiRoqqS=running
[[09:45:05]] [INFO] Executing action 59/351: Swipe from (50%, 70%) to (50%, 30%)
[[09:45:04]] [SUCCESS] Screenshot refreshed
[[09:45:04]] [INFO] Refreshing screenshot...
[[09:45:04]] [INFO] xUbWFa8Ok2=pass
[[09:45:00]] [SUCCESS] Screenshot refreshed successfully
[[09:45:00]] [SUCCESS] Screenshot refreshed successfully
[[09:45:00]] [INFO] xUbWFa8Ok2=running
[[09:45:00]] [INFO] Executing action 58/351: Tap on Text: "Latest"
[[09:44:59]] [SUCCESS] Screenshot refreshed
[[09:44:59]] [INFO] Refreshing screenshot...
[[09:44:59]] [INFO] RbNtEW6N9T=pass
[[09:44:55]] [SUCCESS] Screenshot refreshed successfully
[[09:44:55]] [SUCCESS] Screenshot refreshed successfully
[[09:44:55]] [INFO] RbNtEW6N9T=running
[[09:44:55]] [INFO] Executing action 57/351: Tap on Text: "Toys"
[[09:44:54]] [SUCCESS] Screenshot refreshed
[[09:44:54]] [INFO] Refreshing screenshot...
[[09:44:54]] [INFO] eHvkAVake5=pass
[[09:44:51]] [SUCCESS] Screenshot refreshed successfully
[[09:44:51]] [SUCCESS] Screenshot refreshed successfully
[[09:44:51]] [INFO] eHvkAVake5=running
[[09:44:51]] [INFO] Executing action 56/351: Wait till xpath=//XCUIElementTypeOther[@name="txtShopMenuTitle"]
[[09:44:50]] [SUCCESS] Screenshot refreshed
[[09:44:50]] [INFO] Refreshing screenshot...
[[09:44:50]] [INFO] F4NGh9HrLw=pass
[[09:44:47]] [SUCCESS] Screenshot refreshed successfully
[[09:44:47]] [SUCCESS] Screenshot refreshed successfully
[[09:44:46]] [INFO] F4NGh9HrLw=running
[[09:44:46]] [INFO] Executing action 55/351: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[09:44:46]] [SUCCESS] Screenshot refreshed
[[09:44:46]] [INFO] Refreshing screenshot...
[[09:44:46]] [INFO] H9fy9qcFbZ=pass
[[09:44:32]] [SUCCESS] Screenshot refreshed successfully
[[09:44:32]] [SUCCESS] Screenshot refreshed successfully
[[09:44:31]] [INFO] H9fy9qcFbZ=running
[[09:44:31]] [INFO] Executing action 54/351: Restart app: env[appid]
[[09:44:31]] [SUCCESS] Screenshot refreshed
[[09:44:31]] [INFO] Refreshing screenshot...
[[09:44:30]] [SUCCESS] Screenshot refreshed
[[09:44:30]] [INFO] Refreshing screenshot...
[[09:44:29]] [SUCCESS] Screenshot refreshed successfully
[[09:44:29]] [SUCCESS] Screenshot refreshed successfully
[[09:44:28]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[09:44:28]] [SUCCESS] Screenshot refreshed
[[09:44:28]] [INFO] Refreshing screenshot...
[[09:44:23]] [SUCCESS] Screenshot refreshed successfully
[[09:44:23]] [SUCCESS] Screenshot refreshed successfully
[[09:44:23]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[09:44:22]] [SUCCESS] Screenshot refreshed
[[09:44:22]] [INFO] Refreshing screenshot...
[[09:44:19]] [SUCCESS] Screenshot refreshed successfully
[[09:44:19]] [SUCCESS] Screenshot refreshed successfully
[[09:44:19]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[09:44:18]] [SUCCESS] Screenshot refreshed
[[09:44:18]] [INFO] Refreshing screenshot...
[[09:44:15]] [SUCCESS] Screenshot refreshed successfully
[[09:44:15]] [SUCCESS] Screenshot refreshed successfully
[[09:44:14]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:44:14]] [SUCCESS] Screenshot refreshed
[[09:44:14]] [INFO] Refreshing screenshot...
[[09:44:07]] [SUCCESS] Screenshot refreshed successfully
[[09:44:07]] [SUCCESS] Screenshot refreshed successfully
[[09:44:07]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[09:44:06]] [SUCCESS] Screenshot refreshed
[[09:44:06]] [INFO] Refreshing screenshot...
[[09:43:59]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[09:43:59]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[09:43:59]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[09:43:58]] [INFO] rt1xZqSe89=running
[[09:43:58]] [INFO] Executing action 53/351: cleanupSteps action
[[09:43:58]] [INFO] Skipping remaining steps in failed test case (moving from action 2 to 52), but preserving cleanup steps
[[09:43:58]] [INFO] Y8vz7AJD1i=fail
[[09:43:58]] [ERROR] Action 2 failed: All locators failed (primary + 1 fallbacks)
[[09:43:38]] [SUCCESS] Screenshot refreshed successfully
[[09:43:38]] [SUCCESS] Screenshot refreshed successfully
[[09:43:37]] [INFO] Y8vz7AJD1i=running
[[09:43:37]] [INFO] Executing action 2/351: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:43:37]] [SUCCESS] Screenshot refreshed
[[09:43:37]] [INFO] Refreshing screenshot...
[[09:43:37]] [INFO] H9fy9qcFbZ=pass
[[09:43:35]] [INFO] Collapsed all test cases
[[09:43:32]] [INFO] H9fy9qcFbZ=running
[[09:43:32]] [INFO] Executing action 1/351: Restart app: env[appid]
[[09:43:32]] [INFO] ExecutionManager: Starting execution of 351 actions...
[[09:43:32]] [SUCCESS] Cleared 1 screenshots from database
[[09:43:32]] [INFO] Clearing screenshots from database before execution...
[[09:43:32]] [SUCCESS] All screenshots deleted successfully
[[09:43:32]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[09:43:32]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250701_094332/screenshots
[[09:43:32]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250701_094332
[[09:43:32]] [SUCCESS] Report directory initialized successfully
[[09:43:32]] [INFO] Initializing report directory and screenshots folder for test suite...
[[09:43:30]] [SUCCESS] All screenshots deleted successfully
[[09:43:30]] [INFO] All actions cleared
[[09:43:30]] [INFO] Cleaning up screenshots...
[[09:38:02]] [SUCCESS] Screenshot refreshed successfully
[[09:38:02]] [SUCCESS] Screenshot refreshed successfully
[[09:38:01]] [INFO] kPdSiomhwu=pass
[[09:38:01]] [SUCCESS] Action executed: Action executed successfully: cleanupSteps
[[09:38:01]] [SUCCESS] Screenshot refreshed
[[09:38:01]] [INFO] Refreshing screenshot...
[[09:37:36]] [SUCCESS] Screenshot refreshed successfully
[[09:37:36]] [SUCCESS] Screenshot refreshed successfully
[[09:37:34]] [INFO] Executing action: cleanupSteps action
[[09:37:34]] [INFO] Qb1AArnpCH=pass
[[09:37:34]] [SUCCESS] Action executed: Action executed successfully: wait
[[09:37:34]] [SUCCESS] Screenshot refreshed
[[09:37:34]] [INFO] Refreshing screenshot...
[[09:37:28]] [SUCCESS] Screenshot refreshed successfully
[[09:37:28]] [SUCCESS] Screenshot refreshed successfully
[[09:37:28]] [INFO] Executing action: Wait for 5 ms
[[09:37:28]] [INFO] 0SHxVJkq0l=pass
[[09:37:28]] [SUCCESS] Action executed: Action executed successfully: ifElseSteps
[[09:37:28]] [SUCCESS] Screenshot refreshed
[[09:37:28]] [INFO] Refreshing screenshot...
[[09:37:06]] [SUCCESS] Screenshot refreshed successfully
[[09:37:06]] [SUCCESS] Screenshot refreshed successfully
[[09:37:06]] [INFO] Executing action: If exists: id="//XCUIElementTypeButton[contains(@name,"Remove")]" (timeout: 20s) → Then tap at (0, 0)
[[09:37:06]] [INFO] UpUSVInizv=pass
[[09:37:06]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:37:06]] [SUCCESS] Screenshot refreshed
[[09:37:06]] [INFO] Refreshing screenshot...
[[09:37:04]] [SUCCESS] Screenshot refreshed successfully
[[09:37:04]] [SUCCESS] Screenshot refreshed successfully
[[09:37:02]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[09:37:02]] [INFO] c4T3INQkzn=pass
[[09:37:02]] [SUCCESS] Action executed: Action executed successfully: restartApp
[[09:37:02]] [SUCCESS] Screenshot refreshed
[[09:37:02]] [INFO] Refreshing screenshot...
[[09:36:58]] [SUCCESS] Screenshot refreshed successfully
[[09:36:58]] [SUCCESS] Screenshot refreshed successfully
[[09:36:56]] [INFO] Executing action: Restart app: env[appid]
[[09:36:56]] [INFO] Cr1z26u7Va=pass
[[09:36:56]] [SUCCESS] Action executed: Action executed successfully: ifElseSteps
[[09:36:56]] [SUCCESS] Screenshot refreshed
[[09:36:56]] [INFO] Refreshing screenshot...
[[09:36:52]] [SUCCESS] Screenshot refreshed successfully
[[09:36:52]] [SUCCESS] Screenshot refreshed successfully
[[09:36:49]] [INFO] Executing action: If exists: accessibility_id="Add to bag" (timeout: 15s) → Then tap at (0, 0)
[[09:36:49]] [INFO] 2hGhWulI52=pass
[[09:36:49]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:36:49]] [SUCCESS] Screenshot refreshed
[[09:36:49]] [INFO] Refreshing screenshot...
[[09:36:49]] [SUCCESS] Screenshot refreshed successfully
[[09:36:49]] [SUCCESS] Screenshot refreshed successfully
[[09:36:45]] [INFO] Executing action: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[09:36:45]] [INFO] n57KEWjTea=pass
[[09:36:45]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:36:45]] [SUCCESS] Screenshot refreshed
[[09:36:45]] [INFO] Refreshing screenshot...
[[09:36:42]] [SUCCESS] Screenshot refreshed successfully
[[09:36:42]] [SUCCESS] Screenshot refreshed successfully
[[09:36:40]] [INFO] Executing action: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[09:36:40]] [INFO] L59V5hqMX9=pass
[[09:36:40]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:36:40]] [SUCCESS] Screenshot refreshed
[[09:36:40]] [INFO] Refreshing screenshot...
[[09:36:38]] [SUCCESS] Screenshot refreshed successfully
[[09:36:38]] [SUCCESS] Screenshot refreshed successfully
[[09:36:35]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[09:36:35]] [INFO] OKiI82VdnE=pass
[[09:36:35]] [SUCCESS] Action executed: Action executed successfully: swipeTillVisible
[[09:36:35]] [SUCCESS] Screenshot refreshed
[[09:36:35]] [INFO] Refreshing screenshot...
[[09:36:30]] [SUCCESS] Screenshot refreshed successfully
[[09:36:30]] [SUCCESS] Screenshot refreshed successfully
[[09:36:28]] [INFO] Executing action: Swipe up till element xpath: "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]" is visible
[[09:36:28]] [INFO] 3KNqlNy6Bj=pass
[[09:36:28]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:36:28]] [SUCCESS] Screenshot refreshed
[[09:36:28]] [INFO] Refreshing screenshot...
[[09:36:25]] [SUCCESS] Screenshot refreshed successfully
[[09:36:25]] [SUCCESS] Screenshot refreshed successfully
[[09:36:23]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[09:36:23]] [INFO] 3NOS1fbxZs=pass
[[09:36:23]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:36:23]] [SUCCESS] Screenshot refreshed
[[09:36:23]] [INFO] Refreshing screenshot...
[[09:36:19]] [SUCCESS] Screenshot refreshed successfully
[[09:36:19]] [SUCCESS] Screenshot refreshed successfully
[[09:36:18]] [INFO] Executing action: Tap on image: banner-close-updated.png
[[09:36:18]] [INFO] K0c1gL9UK1=pass
[[09:36:18]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:36:18]] [SUCCESS] Screenshot refreshed
[[09:36:18]] [INFO] Refreshing screenshot...
[[09:36:16]] [SUCCESS] Screenshot refreshed successfully
[[09:36:16]] [SUCCESS] Screenshot refreshed successfully
[[09:36:14]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:36:14]] [INFO] IW6uAwdtiW=pass
[[09:36:14]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:36:14]] [SUCCESS] Screenshot refreshed
[[09:36:14]] [INFO] Refreshing screenshot...
[[09:36:12]] [SUCCESS] Screenshot refreshed successfully
[[09:36:12]] [SUCCESS] Screenshot refreshed successfully
[[09:36:10]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[09:36:10]] [INFO] DbM0d0m6rU=pass
[[09:36:10]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:36:10]] [SUCCESS] Screenshot refreshed
[[09:36:10]] [INFO] Refreshing screenshot...
[[09:36:06]] [SUCCESS] Screenshot refreshed successfully
[[09:36:06]] [SUCCESS] Screenshot refreshed successfully
[[09:36:05]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[09:36:05]] [INFO] UpUSVInizv=pass
[[09:36:05]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:36:05]] [SUCCESS] Screenshot refreshed
[[09:36:05]] [INFO] Refreshing screenshot...
[[09:36:03]] [SUCCESS] Screenshot refreshed successfully
[[09:36:03]] [SUCCESS] Screenshot refreshed successfully
[[09:36:00]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[09:36:00]] [INFO] Iab9zCfpqO=pass
[[09:36:00]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:36:00]] [SUCCESS] Screenshot refreshed
[[09:36:00]] [INFO] Refreshing screenshot...
[[09:35:57]] [SUCCESS] Screenshot refreshed successfully
[[09:35:57]] [SUCCESS] Screenshot refreshed successfully
[[09:35:53]] [INFO] Executing action: Tap on element with accessibility_id: Add to bag
[[09:35:53]] [INFO] Qy0Y0uJchm=pass
[[09:35:53]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:35:53]] [SUCCESS] Screenshot refreshed
[[09:35:53]] [INFO] Refreshing screenshot...
[[09:35:51]] [SUCCESS] Screenshot refreshed successfully
[[09:35:51]] [SUCCESS] Screenshot refreshed successfully
[[09:35:49]] [INFO] Executing action: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[09:35:49]] [INFO] YHaMIjULRf=pass
[[09:35:49]] [SUCCESS] Action executed: Action executed successfully: tapOnText
[[09:35:49]] [SUCCESS] Screenshot refreshed
[[09:35:49]] [INFO] Refreshing screenshot...
[[09:35:44]] [SUCCESS] Screenshot refreshed successfully
[[09:35:44]] [SUCCESS] Screenshot refreshed successfully
[[09:35:42]] [INFO] Executing action: Tap on Text: "List"
[[09:35:42]] [INFO] igReeDqips=pass
[[09:35:42]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:35:42]] [SUCCESS] Screenshot refreshed
[[09:35:42]] [INFO] Refreshing screenshot...
[[09:35:38]] [SUCCESS] Screenshot refreshed successfully
[[09:35:38]] [SUCCESS] Screenshot refreshed successfully
[[09:35:36]] [INFO] Executing action: Tap on image: env[catalogue-menu-img]
[[09:35:36]] [INFO] Xqj9EIVE7g=pass
[[09:35:36]] [SUCCESS] Action executed: Action executed successfully: ifElseSteps
[[09:35:36]] [SUCCESS] Screenshot refreshed
[[09:35:36]] [INFO] Refreshing screenshot...
[[09:35:15]] [SUCCESS] Screenshot refreshed successfully
[[09:35:15]] [SUCCESS] Screenshot refreshed successfully
[[09:35:13]] [INFO] Executing action: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[09:35:13]] [INFO] gkkQzTCmma=pass
[[09:35:13]] [SUCCESS] Action executed: Action executed successfully: tapOnText
[[09:35:13]] [SUCCESS] Screenshot refreshed
[[09:35:13]] [INFO] Refreshing screenshot...
[[09:35:08]] [SUCCESS] Screenshot refreshed successfully
[[09:35:08]] [SUCCESS] Screenshot refreshed successfully
[[09:35:07]] [INFO] Executing action: Tap on Text: "Catalogue"
[[09:35:07]] [INFO] UpUSVInizv=pass
[[09:35:07]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:35:07]] [SUCCESS] Screenshot refreshed
[[09:35:07]] [INFO] Refreshing screenshot...
[[09:35:05]] [SUCCESS] Screenshot refreshed successfully
[[09:35:05]] [SUCCESS] Screenshot refreshed successfully
[[09:35:03]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[09:35:03]] [INFO] Cmvm82hiAa=pass
[[09:35:03]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:35:03]] [SUCCESS] Screenshot refreshed
[[09:35:03]] [INFO] Refreshing screenshot...
[[09:34:58]] [SUCCESS] Screenshot refreshed successfully
[[09:34:58]] [SUCCESS] Screenshot refreshed successfully
[[09:34:56]] [INFO] Executing action: Tap on element with accessibility_id: Add to bag
[[09:34:56]] [INFO] JcAR0JctQ6=pass
[[09:34:56]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:34:56]] [SUCCESS] Screenshot refreshed
[[09:34:56]] [INFO] Refreshing screenshot...
[[09:34:54]] [SUCCESS] Screenshot refreshed successfully
[[09:34:54]] [SUCCESS] Screenshot refreshed successfully
[[09:34:52]] [INFO] Executing action: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[09:34:52]] [INFO] Pd7cReoJM6=pass
[[09:34:52]] [SUCCESS] Action executed: Action executed successfully: tapOnText
[[09:34:52]] [SUCCESS] Screenshot refreshed
[[09:34:52]] [INFO] Refreshing screenshot...
[[09:34:47]] [SUCCESS] Screenshot refreshed successfully
[[09:34:47]] [SUCCESS] Screenshot refreshed successfully
[[09:34:45]] [INFO] Executing action: Tap on Text: "List"
[[09:34:45]] [INFO] igReeDqips=pass
[[09:34:45]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:34:45]] [SUCCESS] Screenshot refreshed
[[09:34:45]] [INFO] Refreshing screenshot...
[[09:34:42]] [SUCCESS] Screenshot refreshed successfully
[[09:34:41]] [SUCCESS] Screenshot refreshed successfully
[[09:34:40]] [INFO] Executing action: Tap on image: env[catalogue-menu-img]
[[09:34:40]] [INFO] Xqj9EIVE7g=pass
[[09:34:40]] [SUCCESS] Action executed: Action executed successfully: ifElseSteps
[[09:34:40]] [SUCCESS] Screenshot refreshed
[[09:34:40]] [INFO] Refreshing screenshot...
[[09:34:20]] [SUCCESS] Screenshot refreshed successfully
[[09:34:20]] [SUCCESS] Screenshot refreshed successfully
[[09:34:17]] [INFO] Executing action: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[09:34:17]] [INFO] gkkQzTCmma=pass
[[09:34:17]] [SUCCESS] Action executed: Action executed successfully: tapOnText
[[09:34:17]] [SUCCESS] Screenshot refreshed
[[09:34:17]] [INFO] Refreshing screenshot...
[[09:34:12]] [SUCCESS] Screenshot refreshed successfully
[[09:34:12]] [SUCCESS] Screenshot refreshed successfully
[[09:34:12]] [INFO] Executing action: Tap on Text: "Catalogue"
[[09:34:12]] [INFO] UpUSVInizv=pass
[[09:34:12]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:34:12]] [SUCCESS] Screenshot refreshed
[[09:34:12]] [INFO] Refreshing screenshot...
[[09:34:10]] [SUCCESS] Screenshot refreshed successfully
[[09:34:10]] [SUCCESS] Screenshot refreshed successfully
[[09:34:07]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[09:34:07]] [INFO] 0QtNHB5WEK=pass
[[09:34:07]] [SUCCESS] Action executed: Action executed successfully: exists
[[09:34:07]] [SUCCESS] Screenshot refreshed
[[09:34:07]] [INFO] Refreshing screenshot...
[[09:34:06]] [SUCCESS] Screenshot refreshed successfully
[[09:34:06]] [SUCCESS] Screenshot refreshed successfully
[[09:34:04]] [INFO] Executing action: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[09:34:04]] [INFO] fTdGMJ3NH3=pass
[[09:34:04]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:34:04]] [SUCCESS] Screenshot refreshed
[[09:34:04]] [INFO] Refreshing screenshot...
[[09:34:02]] [SUCCESS] Screenshot refreshed successfully
[[09:34:02]] [SUCCESS] Screenshot refreshed successfully
[[09:34:00]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[09:34:00]] [INFO] rYJcLPh8Aq=pass
[[09:34:00]] [SUCCESS] Action executed: Action executed successfully: iosFunctions
[[09:34:00]] [SUCCESS] Screenshot refreshed
[[09:34:00]] [INFO] Refreshing screenshot...
[[09:33:58]] [SUCCESS] Screenshot refreshed successfully
[[09:33:58]] [SUCCESS] Screenshot refreshed successfully
[[09:33:57]] [INFO] Executing action: iOS Function: text - Text: "kmart au"
[[09:33:57]] [INFO] 0Q0fm6OTij=pass
[[09:33:57]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:33:57]] [SUCCESS] Screenshot refreshed
[[09:33:57]] [INFO] Refreshing screenshot...
[[09:33:55]] [SUCCESS] Screenshot refreshed successfully
[[09:33:55]] [SUCCESS] Screenshot refreshed successfully
[[09:33:54]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[09:33:54]] [INFO] xVuuejtCFA=pass
[[09:33:54]] [SUCCESS] Action executed: Action executed successfully: restartApp
[[09:33:54]] [SUCCESS] Screenshot refreshed
[[09:33:54]] [INFO] Refreshing screenshot...
[[09:33:51]] [SUCCESS] Screenshot refreshed successfully
[[09:33:51]] [SUCCESS] Screenshot refreshed successfully
[[09:33:49]] [INFO] Executing action: Restart app: com.apple.mobilesafari
[[09:33:49]] [INFO] LcYLwUffqj=pass
[[09:33:49]] [SUCCESS] Action executed: Action executed successfully: tapOnText
[[09:33:49]] [SUCCESS] Screenshot refreshed
[[09:33:49]] [INFO] Refreshing screenshot...
[[09:33:45]] [SUCCESS] Screenshot refreshed successfully
[[09:33:45]] [SUCCESS] Screenshot refreshed successfully
[[09:33:44]] [INFO] Executing action: Tap on Text: "out"
[[09:33:44]] [INFO] ZZPNqTJ65s=pass
[[09:33:44]] [SUCCESS] Action executed: Action executed successfully: swipe
[[09:33:44]] [SUCCESS] Screenshot refreshed
[[09:33:44]] [INFO] Refreshing screenshot...
[[09:33:41]] [SUCCESS] Screenshot refreshed successfully
[[09:33:41]] [SUCCESS] Screenshot refreshed successfully
[[09:33:40]] [INFO] Executing action: Swipe from (50%, 70%) to (50%, 30%)
[[09:33:40]] [INFO] UpUSVInizv=pass
[[09:33:40]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:33:40]] [SUCCESS] Screenshot refreshed
[[09:33:40]] [INFO] Refreshing screenshot...
[[09:33:38]] [SUCCESS] Screenshot refreshed successfully
[[09:33:38]] [SUCCESS] Screenshot refreshed successfully
[[09:33:35]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[09:33:35]] [INFO] hCCEvRtj1A=pass
[[09:33:35]] [SUCCESS] Action executed: Action executed successfully: restartApp
[[09:33:35]] [SUCCESS] Screenshot refreshed
[[09:33:35]] [INFO] Refreshing screenshot...
[[09:33:31]] [SUCCESS] Screenshot refreshed successfully
[[09:33:31]] [SUCCESS] Screenshot refreshed successfully
[[09:33:30]] [INFO] Executing action: Restart app: env[appid]
[[09:33:30]] [INFO] V42eHtTRYW=pass
[[09:33:30]] [SUCCESS] Action executed: Action executed successfully: wait
[[09:33:30]] [SUCCESS] Screenshot refreshed
[[09:33:30]] [INFO] Refreshing screenshot...
[[09:33:24]] [SUCCESS] Screenshot refreshed successfully
[[09:33:24]] [SUCCESS] Screenshot refreshed successfully
[[09:33:24]] [INFO] Executing action: Wait for 5 ms
[[09:33:24]] [INFO] GRwHMVK4sA=pass
[[09:33:24]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:33:24]] [SUCCESS] Screenshot refreshed
[[09:33:24]] [INFO] Refreshing screenshot...
[[09:33:22]] [SUCCESS] Screenshot refreshed successfully
[[09:33:22]] [SUCCESS] Screenshot refreshed successfully
[[09:33:21]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[09:33:21]] [INFO] V42eHtTRYW=pass
[[09:33:21]] [SUCCESS] Action executed: Action executed successfully: wait
[[09:33:21]] [SUCCESS] Screenshot refreshed
[[09:33:21]] [INFO] Refreshing screenshot...
[[09:33:15]] [SUCCESS] Screenshot refreshed successfully
[[09:33:15]] [SUCCESS] Screenshot refreshed successfully
[[09:33:15]] [INFO] Executing action: Wait for 5 ms
[[09:33:15]] [INFO] LfyQctrEJn=pass
[[09:33:15]] [SUCCESS] Action executed: Action executed successfully: launchApp
[[09:33:15]] [SUCCESS] Screenshot refreshed
[[09:33:15]] [INFO] Refreshing screenshot...
[[09:33:14]] [SUCCESS] Screenshot refreshed successfully
[[09:33:14]] [SUCCESS] Screenshot refreshed successfully
[[09:33:13]] [INFO] Executing action: Launch app: com.apple.Preferences
[[09:33:13]] [INFO] seQcUKjkSU=pass
[[09:33:13]] [SUCCESS] Action executed: Action executed successfully: exists
[[09:33:13]] [SUCCESS] Screenshot refreshed
[[09:33:13]] [INFO] Refreshing screenshot...
[[09:33:12]] [SUCCESS] Screenshot refreshed successfully
[[09:33:12]] [SUCCESS] Screenshot refreshed successfully
[[09:33:11]] [INFO] Executing action: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[09:33:11]] [INFO] UpUSVInizv=pass
[[09:33:11]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:33:11]] [SUCCESS] Screenshot refreshed
[[09:33:11]] [INFO] Refreshing screenshot...
[[09:33:10]] [SUCCESS] Screenshot refreshed successfully
[[09:33:10]] [SUCCESS] Screenshot refreshed successfully
[[09:33:09]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[09:33:09]] [INFO] WoymrHdtrO=pass
[[09:33:09]] [SUCCESS] Action executed: Action executed successfully: exists
[[09:33:09]] [SUCCESS] Screenshot refreshed
[[09:33:09]] [INFO] Refreshing screenshot...
[[09:33:08]] [SUCCESS] Screenshot refreshed successfully
[[09:33:08]] [SUCCESS] Screenshot refreshed successfully
[[09:33:07]] [INFO] Executing action: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[09:33:07]] [INFO] 6xgrAWyfZ4=pass
[[09:33:07]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:33:07]] [SUCCESS] Screenshot refreshed
[[09:33:07]] [INFO] Refreshing screenshot...
[[09:33:06]] [SUCCESS] Screenshot refreshed successfully
[[09:33:06]] [SUCCESS] Screenshot refreshed successfully
[[09:33:05]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[09:33:05]] [INFO] eSr9EFlJek=pass
[[09:33:05]] [SUCCESS] Action executed: Action executed successfully: exists
[[09:33:05]] [SUCCESS] Screenshot refreshed
[[09:33:05]] [INFO] Refreshing screenshot...
[[09:33:04]] [SUCCESS] Screenshot refreshed successfully
[[09:33:04]] [SUCCESS] Screenshot refreshed successfully
[[09:33:03]] [INFO] Executing action: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[09:33:03]] [INFO] 3KNqlNy6Bj=pass
[[09:33:03]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:33:03]] [SUCCESS] Screenshot refreshed
[[09:33:03]] [INFO] Refreshing screenshot...
[[09:33:02]] [SUCCESS] Screenshot refreshed successfully
[[09:33:02]] [SUCCESS] Screenshot refreshed successfully
[[09:33:01]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[09:33:01]] [INFO] cokvFXhj4c=pass
[[09:33:01]] [SUCCESS] Action executed: Action executed successfully: exists
[[09:33:01]] [SUCCESS] Screenshot refreshed
[[09:33:01]] [INFO] Refreshing screenshot...
[[09:33:00]] [SUCCESS] Screenshot refreshed successfully
[[09:33:00]] [SUCCESS] Screenshot refreshed successfully
[[09:32:59]] [INFO] Executing action: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[09:32:59]] [INFO] oSQ8sPdVOJ=pass
[[09:32:59]] [SUCCESS] Action executed: Action executed successfully: restartApp
[[09:32:59]] [SUCCESS] Screenshot refreshed
[[09:32:59]] [INFO] Refreshing screenshot...
[[09:32:54]] [SUCCESS] Screenshot refreshed successfully
[[09:32:54]] [SUCCESS] Screenshot refreshed successfully
[[09:32:54]] [INFO] Executing action: Restart app: env[appid]
[[09:32:54]] [INFO] V42eHtTRYW=pass
[[09:32:54]] [SUCCESS] Action executed: Action executed successfully: wait
[[09:32:54]] [SUCCESS] Screenshot refreshed
[[09:32:54]] [INFO] Refreshing screenshot...
[[09:32:48]] [SUCCESS] Screenshot refreshed successfully
[[09:32:48]] [SUCCESS] Screenshot refreshed successfully
[[09:32:47]] [INFO] Executing action: Wait for 5 ms
[[09:32:47]] [INFO] jUCAk6GJc4=pass
[[09:32:47]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:32:47]] [SUCCESS] Screenshot refreshed
[[09:32:47]] [INFO] Refreshing screenshot...
[[09:32:45]] [SUCCESS] Screenshot refreshed successfully
[[09:32:45]] [SUCCESS] Screenshot refreshed successfully
[[09:32:45]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[09:32:45]] [INFO] V42eHtTRYW=pass
[[09:32:45]] [SUCCESS] Action executed: Action executed successfully: wait
[[09:32:45]] [SUCCESS] Screenshot refreshed
[[09:32:45]] [INFO] Refreshing screenshot...
[[09:32:39]] [SUCCESS] Screenshot refreshed successfully
[[09:32:39]] [SUCCESS] Screenshot refreshed successfully
[[09:32:39]] [INFO] Executing action: Wait for 5 ms
[[09:32:39]] [INFO] w1RV76df9x=pass
[[09:32:39]] [SUCCESS] Action executed: Action executed successfully: tapOnText
[[09:32:39]] [SUCCESS] Screenshot refreshed
[[09:32:39]] [INFO] Refreshing screenshot...
[[09:32:35]] [SUCCESS] Screenshot refreshed successfully
[[09:32:35]] [SUCCESS] Screenshot refreshed successfully
[[09:32:34]] [INFO] Executing action: Tap on Text: "Wi-Fi"
[[09:32:34]] [INFO] LfyQctrEJn=pass
[[09:32:34]] [SUCCESS] Action executed: Action executed successfully: launchApp
[[09:32:34]] [SUCCESS] Screenshot refreshed
[[09:32:34]] [INFO] Refreshing screenshot...
[[09:32:33]] [SUCCESS] Screenshot refreshed successfully
[[09:32:33]] [SUCCESS] Screenshot refreshed successfully
[[09:32:31]] [INFO] Executing action: Launch app: com.apple.Preferences
[[09:32:31]] [INFO] mIKA85kXaW=pass
[[09:32:31]] [SUCCESS] Action executed: Action executed successfully: terminateApp
[[09:32:31]] [SUCCESS] Screenshot refreshed
[[09:32:31]] [INFO] Refreshing screenshot...
[[09:32:30]] [SUCCESS] Screenshot refreshed successfully
[[09:32:30]] [SUCCESS] Screenshot refreshed successfully
[[09:32:28]] [INFO] Executing action: Terminate app: com.apple.Preferences
[[09:32:28]] [INFO] x6vffndoRV=pass
[[09:32:28]] [SUCCESS] Action executed: Action executed successfully: multiStep
[[09:32:28]] [SUCCESS] Screenshot refreshed
[[09:32:28]] [INFO] Refreshing screenshot...
[[09:32:11]] [SUCCESS] Screenshot refreshed successfully
[[09:32:11]] [SUCCESS] Screenshot refreshed successfully
[[09:32:09]] [INFO] Executing action: Execute Test Case: Kmart-Signin (6 steps)
[[09:32:09]] [INFO] rJ86z4njuR=pass
[[09:32:09]] [SUCCESS] Action executed: Action executed successfully: iosFunctions
[[09:32:09]] [SUCCESS] Screenshot refreshed
[[09:32:09]] [INFO] Refreshing screenshot...
[[09:32:08]] [SUCCESS] Screenshot refreshed successfully
[[09:32:08]] [SUCCESS] Screenshot refreshed successfully
[[09:32:06]] [INFO] Executing action: iOS Function: alert_accept
[[09:32:06]] [INFO] veukWo4573=pass
[[09:32:06]] [SUCCESS] Action executed: Action executed successfully: tap
[[09:32:06]] [SUCCESS] Screenshot refreshed
[[09:32:06]] [INFO] Refreshing screenshot...
[[09:32:04]] [SUCCESS] Screenshot refreshed successfully
[[09:32:04]] [SUCCESS] Screenshot refreshed successfully
[[09:32:01]] [INFO] Executing action: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[09:32:01]] [INFO] XEbZHdi0GT=pass
[[09:32:01]] [SUCCESS] Action executed: Action executed successfully: restartApp
[[09:32:01]] [SUCCESS] Screenshot refreshed
[[09:32:01]] [INFO] Refreshing screenshot...
[[09:31:55]] [INFO] Executing action: Restart app: env[appid]
[[09:16:35]] [SUCCESS] All screenshots deleted successfully
[[09:16:35]] [INFO] Cleaning up screenshots...
[[09:16:35]] [SUCCESS] Execution report generated successfully
[[09:16:25]] [SUCCESS] Screenshot refreshed successfully
[[09:16:25]] [SUCCESS] Screenshot refreshed successfully
[[09:16:24]] [SUCCESS] Action logs saved successfully
[[09:16:24]] [ERROR] Execution failed but report was generated.
[[09:16:24]] [INFO] Saving 4856 action log entries to file...
[[09:16:24]] [INFO] Generating execution report...
[[09:16:24]] [WARNING] 1 test failed.
[[09:16:24]] [SUCCESS] Screenshot refreshed
[[09:16:24]] [INFO] Refreshing screenshot...
[[09:16:23]] [SUCCESS] Screenshot refreshed
[[09:16:23]] [INFO] Refreshing screenshot...
[[09:16:20]] [SUCCESS] Screenshot refreshed successfully
[[09:16:20]] [SUCCESS] Screenshot refreshed successfully
[[09:16:20]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[09:16:20]] [SUCCESS] Screenshot refreshed
[[09:16:20]] [INFO] Refreshing screenshot...
[[09:16:07]] [SUCCESS] Screenshot refreshed successfully
[[09:16:07]] [SUCCESS] Screenshot refreshed successfully
[[09:16:07]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[09:16:07]] [SUCCESS] Screenshot refreshed
[[09:16:07]] [INFO] Refreshing screenshot...
[[09:16:03]] [SUCCESS] Screenshot refreshed successfully
[[09:16:03]] [SUCCESS] Screenshot refreshed successfully
[[09:16:03]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[09:16:02]] [SUCCESS] Screenshot refreshed
[[09:16:02]] [INFO] Refreshing screenshot...
[[09:15:59]] [SUCCESS] Screenshot refreshed successfully
[[09:15:59]] [SUCCESS] Screenshot refreshed successfully
[[09:15:58]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:15:58]] [SUCCESS] Screenshot refreshed
[[09:15:58]] [INFO] Refreshing screenshot...
[[09:15:51]] [SUCCESS] Screenshot refreshed successfully
[[09:15:51]] [SUCCESS] Screenshot refreshed successfully
[[09:15:51]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[09:15:50]] [SUCCESS] Screenshot refreshed
[[09:15:50]] [INFO] Refreshing screenshot...
[[09:15:44]] [SUCCESS] Screenshot refreshed successfully
[[09:15:44]] [SUCCESS] Screenshot refreshed successfully
[[09:15:44]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[09:15:44]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[09:15:44]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[09:15:44]] [INFO] Ll4UlkE3L9=running
[[09:15:44]] [INFO] Executing action 576/576: cleanupSteps action
[[09:15:43]] [SUCCESS] Screenshot refreshed
[[09:15:43]] [INFO] Refreshing screenshot...
[[09:15:43]] [INFO] 25UEKPIknm=pass
[[09:15:40]] [SUCCESS] Screenshot refreshed successfully
[[09:15:40]] [SUCCESS] Screenshot refreshed successfully
[[09:15:40]] [INFO] 25UEKPIknm=running
[[09:15:40]] [INFO] Executing action 575/576: Terminate app: env[appid]
[[09:15:39]] [SUCCESS] Screenshot refreshed
[[09:15:39]] [INFO] Refreshing screenshot...
[[09:15:39]] [INFO] UqgDn5CuPY=pass
[[09:15:36]] [SUCCESS] Screenshot refreshed successfully
[[09:15:36]] [SUCCESS] Screenshot refreshed successfully
[[09:15:36]] [INFO] UqgDn5CuPY=running
[[09:15:36]] [INFO] Executing action 574/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[09:15:36]] [SUCCESS] Screenshot refreshed
[[09:15:36]] [INFO] Refreshing screenshot...
[[09:15:36]] [INFO] VfTTTtrliQ=pass
[[09:15:33]] [SUCCESS] Screenshot refreshed successfully
[[09:15:33]] [SUCCESS] Screenshot refreshed successfully
[[09:15:33]] [INFO] VfTTTtrliQ=running
[[09:15:33]] [INFO] Executing action 573/576: iOS Function: alert_accept
[[09:15:32]] [SUCCESS] Screenshot refreshed
[[09:15:32]] [INFO] Refreshing screenshot...
[[09:15:32]] [INFO] ipT2XD9io6=pass
[[09:15:28]] [SUCCESS] Screenshot refreshed successfully
[[09:15:28]] [SUCCESS] Screenshot refreshed successfully
[[09:15:28]] [INFO] ipT2XD9io6=running
[[09:15:28]] [INFO] Executing action 572/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountJoinTodayButton"]
[[09:15:27]] [SUCCESS] Screenshot refreshed
[[09:15:27]] [INFO] Refreshing screenshot...
[[09:15:27]] [INFO] OKCHAK6HCJ=pass
[[09:15:23]] [SUCCESS] Screenshot refreshed successfully
[[09:15:23]] [SUCCESS] Screenshot refreshed successfully
[[09:15:22]] [INFO] OKCHAK6HCJ=running
[[09:15:22]] [INFO] Executing action 571/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:15:22]] [SUCCESS] Screenshot refreshed
[[09:15:22]] [INFO] Refreshing screenshot...
[[09:15:22]] [INFO] AEnFqnkOa1=pass
[[09:15:18]] [SUCCESS] Screenshot refreshed successfully
[[09:15:18]] [SUCCESS] Screenshot refreshed successfully
[[09:15:17]] [INFO] AEnFqnkOa1=running
[[09:15:17]] [INFO] Executing action 570/576: Tap on image: banner-close-updated.png
[[09:15:17]] [SUCCESS] Screenshot refreshed
[[09:15:17]] [INFO] Refreshing screenshot...
[[09:15:17]] [INFO] z1CfcW4xYT=pass
[[09:15:13]] [SUCCESS] Screenshot refreshed successfully
[[09:15:13]] [SUCCESS] Screenshot refreshed successfully
[[09:15:13]] [INFO] z1CfcW4xYT=running
[[09:15:13]] [INFO] Executing action 569/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:15:12]] [SUCCESS] Screenshot refreshed
[[09:15:12]] [INFO] Refreshing screenshot...
[[09:15:12]] [INFO] dJNRgTXoqs=pass
[[09:15:08]] [SUCCESS] Screenshot refreshed successfully
[[09:15:08]] [SUCCESS] Screenshot refreshed successfully
[[09:15:08]] [INFO] dJNRgTXoqs=running
[[09:15:08]] [INFO] Executing action 568/576: Swipe from (50%, 30%) to (50%, 70%)
[[09:15:07]] [SUCCESS] Screenshot refreshed
[[09:15:07]] [INFO] Refreshing screenshot...
[[09:15:07]] [INFO] ceF4VRTJlO=pass
[[09:15:03]] [SUCCESS] Screenshot refreshed successfully
[[09:15:03]] [SUCCESS] Screenshot refreshed successfully
[[09:15:03]] [INFO] ceF4VRTJlO=running
[[09:15:03]] [INFO] Executing action 567/576: Tap on image: banner-close-updated.png
[[09:15:03]] [SUCCESS] Screenshot refreshed
[[09:15:03]] [INFO] Refreshing screenshot...
[[09:15:03]] [INFO] 8hCPyY2zPt=pass
[[09:14:58]] [SUCCESS] Screenshot refreshed successfully
[[09:14:58]] [SUCCESS] Screenshot refreshed successfully
[[09:14:58]] [INFO] 8hCPyY2zPt=running
[[09:14:58]] [INFO] Executing action 566/576: Tap on element with xpath: //XCUIElementTypeButton[@name="About KHub Stores"]
[[09:14:58]] [SUCCESS] Screenshot refreshed
[[09:14:58]] [INFO] Refreshing screenshot...
[[09:14:58]] [INFO] r0FfJ85LFM=pass
[[09:14:54]] [SUCCESS] Screenshot refreshed successfully
[[09:14:54]] [SUCCESS] Screenshot refreshed successfully
[[09:14:54]] [INFO] r0FfJ85LFM=running
[[09:14:54]] [INFO] Executing action 565/576: Tap on image: banner-close-updated.png
[[09:14:53]] [SUCCESS] Screenshot refreshed
[[09:14:53]] [INFO] Refreshing screenshot...
[[09:14:53]] [INFO] 2QEdm5WM18=pass
[[09:14:49]] [SUCCESS] Screenshot refreshed successfully
[[09:14:49]] [SUCCESS] Screenshot refreshed successfully
[[09:14:49]] [INFO] 2QEdm5WM18=running
[[09:14:49]] [INFO] Executing action 564/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Bags for Click & Collect orders"]
[[09:14:49]] [SUCCESS] Screenshot refreshed
[[09:14:49]] [INFO] Refreshing screenshot...
[[09:14:49]] [INFO] NW6M15JbAy=pass
[[09:14:36]] [SUCCESS] Screenshot refreshed successfully
[[09:14:36]] [SUCCESS] Screenshot refreshed successfully
[[09:14:36]] [INFO] NW6M15JbAy=running
[[09:14:36]] [INFO] Executing action 563/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Bags for Click & Collect orders"]" is visible
[[09:14:35]] [SUCCESS] Screenshot refreshed
[[09:14:35]] [INFO] Refreshing screenshot...
[[09:14:35]] [INFO] njiHWyVooT=pass
[[09:14:31]] [SUCCESS] Screenshot refreshed successfully
[[09:14:31]] [SUCCESS] Screenshot refreshed successfully
[[09:14:30]] [INFO] njiHWyVooT=running
[[09:14:30]] [INFO] Executing action 562/576: Tap on image: banner-close-updated.png
[[09:14:30]] [SUCCESS] Screenshot refreshed
[[09:14:30]] [INFO] Refreshing screenshot...
[[09:14:30]] [INFO] 93bAew9Y4Y=pass
[[09:14:25]] [SUCCESS] Screenshot refreshed successfully
[[09:14:25]] [SUCCESS] Screenshot refreshed successfully
[[09:14:25]] [INFO] 93bAew9Y4Y=running
[[09:14:25]] [INFO] Executing action 561/576: Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,"store details")])[1]
[[09:14:24]] [SUCCESS] Screenshot refreshed
[[09:14:24]] [INFO] Refreshing screenshot...
[[09:14:24]] [INFO] rPQ5EkTza1=pass
[[09:14:20]] [SUCCESS] Screenshot refreshed successfully
[[09:14:20]] [SUCCESS] Screenshot refreshed successfully
[[09:14:20]] [INFO] rPQ5EkTza1=running
[[09:14:20]] [INFO] Executing action 560/576: Tap on Text: "Click"
[[09:14:20]] [SUCCESS] Screenshot refreshed
[[09:14:20]] [INFO] Refreshing screenshot...
[[09:14:19]] [SUCCESS] Screenshot refreshed
[[09:14:19]] [INFO] Refreshing screenshot...
[[09:14:16]] [INFO] Executing Multi Step action step 6/6: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[09:14:15]] [SUCCESS] Screenshot refreshed successfully
[[09:14:15]] [SUCCESS] Screenshot refreshed successfully
[[09:14:15]] [SUCCESS] Screenshot refreshed
[[09:14:15]] [INFO] Refreshing screenshot...
[[09:14:11]] [SUCCESS] Screenshot refreshed successfully
[[09:14:11]] [SUCCESS] Screenshot refreshed successfully
[[09:14:11]] [INFO] Executing Multi Step action step 5/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:14:10]] [SUCCESS] Screenshot refreshed
[[09:14:10]] [INFO] Refreshing screenshot...
[[09:14:06]] [SUCCESS] Screenshot refreshed successfully
[[09:14:06]] [SUCCESS] Screenshot refreshed successfully
[[09:14:06]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[09:14:05]] [SUCCESS] Screenshot refreshed
[[09:14:05]] [INFO] Refreshing screenshot...
[[09:14:01]] [SUCCESS] Screenshot refreshed successfully
[[09:14:01]] [SUCCESS] Screenshot refreshed successfully
[[09:14:01]] [INFO] Executing Multi Step action step 3/6: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:14:00]] [SUCCESS] Screenshot refreshed
[[09:14:00]] [INFO] Refreshing screenshot...
[[09:13:56]] [SUCCESS] Screenshot refreshed successfully
[[09:13:56]] [SUCCESS] Screenshot refreshed successfully
[[09:13:56]] [INFO] Executing Multi Step action step 2/6: iOS Function: text - Text: "Notebook"
[[09:13:55]] [SUCCESS] Screenshot refreshed
[[09:13:55]] [INFO] Refreshing screenshot...
[[09:13:48]] [SUCCESS] Screenshot refreshed successfully
[[09:13:48]] [SUCCESS] Screenshot refreshed successfully
[[09:13:47]] [INFO] Executing Multi Step action step 1/6: Tap on Text: "Find"
[[09:13:47]] [INFO] Loaded 6 steps from test case: Search and Add (Notebooks)
[[09:13:47]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[09:13:47]] [INFO] 0YgZZfWdYY=running
[[09:13:47]] [INFO] Executing action 559/576: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[09:13:47]] [SUCCESS] Screenshot refreshed
[[09:13:47]] [INFO] Refreshing screenshot...
[[09:13:47]] [INFO] arH1CZCPXh=pass
[[09:13:42]] [SUCCESS] Screenshot refreshed successfully
[[09:13:42]] [SUCCESS] Screenshot refreshed successfully
[[09:13:41]] [INFO] arH1CZCPXh=running
[[09:13:41]] [INFO] Executing action 558/576: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[09:13:41]] [SUCCESS] Screenshot refreshed
[[09:13:41]] [INFO] Refreshing screenshot...
[[09:13:41]] [INFO] JLAJhxPdsl=pass
[[09:13:35]] [SUCCESS] Screenshot refreshed successfully
[[09:13:35]] [SUCCESS] Screenshot refreshed successfully
[[09:13:35]] [INFO] JLAJhxPdsl=running
[[09:13:35]] [INFO] Executing action 557/576: Tap on Text: "Cancel"
[[09:13:35]] [SUCCESS] Screenshot refreshed
[[09:13:35]] [INFO] Refreshing screenshot...
[[09:13:35]] [INFO] UqgDn5CuPY=pass
[[09:13:32]] [SUCCESS] Screenshot refreshed successfully
[[09:13:32]] [SUCCESS] Screenshot refreshed successfully
[[09:13:31]] [INFO] UqgDn5CuPY=running
[[09:13:31]] [INFO] Executing action 556/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[09:13:30]] [SUCCESS] Screenshot refreshed
[[09:13:30]] [INFO] Refreshing screenshot...
[[09:13:30]] [INFO] VfTTTtrliQ=pass
[[09:13:28]] [SUCCESS] Screenshot refreshed successfully
[[09:13:28]] [SUCCESS] Screenshot refreshed successfully
[[09:13:27]] [INFO] VfTTTtrliQ=running
[[09:13:27]] [INFO] Executing action 555/576: iOS Function: alert_accept
[[09:13:27]] [SUCCESS] Screenshot refreshed
[[09:13:27]] [INFO] Refreshing screenshot...
[[09:13:27]] [INFO] ipT2XD9io6=pass
[[09:13:22]] [SUCCESS] Screenshot refreshed successfully
[[09:13:22]] [SUCCESS] Screenshot refreshed successfully
[[09:13:22]] [INFO] ipT2XD9io6=running
[[09:13:22]] [INFO] Executing action 554/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtJoinTodayButton"]
[[09:13:21]] [SUCCESS] Screenshot refreshed
[[09:13:21]] [INFO] Refreshing screenshot...
[[09:13:21]] [INFO] OKCHAK6HCJ=pass
[[09:13:17]] [SUCCESS] Screenshot refreshed successfully
[[09:13:17]] [SUCCESS] Screenshot refreshed successfully
[[09:13:17]] [INFO] OKCHAK6HCJ=running
[[09:13:17]] [INFO] Executing action 553/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:13:16]] [SUCCESS] Screenshot refreshed
[[09:13:16]] [INFO] Refreshing screenshot...
[[09:13:16]] [INFO] RbD937Xbte=pass
[[09:13:11]] [SUCCESS] Screenshot refreshed successfully
[[09:13:11]] [SUCCESS] Screenshot refreshed successfully
[[09:13:11]] [INFO] RbD937Xbte=running
[[09:13:11]] [INFO] Executing action 552/576: Tap on Text: "out"
[[09:13:11]] [SUCCESS] Screenshot refreshed
[[09:13:11]] [INFO] Refreshing screenshot...
[[09:13:11]] [INFO] ylslyLAYKb=pass
[[09:13:07]] [SUCCESS] Screenshot refreshed successfully
[[09:13:07]] [SUCCESS] Screenshot refreshed successfully
[[09:13:07]] [INFO] ylslyLAYKb=running
[[09:13:07]] [INFO] Executing action 551/576: Swipe from (50%, 70%) to (50%, 30%)
[[09:13:06]] [SUCCESS] Screenshot refreshed
[[09:13:06]] [INFO] Refreshing screenshot...
[[09:13:06]] [INFO] wguGCt7OoB=pass
[[09:13:02]] [SUCCESS] Screenshot refreshed successfully
[[09:13:02]] [SUCCESS] Screenshot refreshed successfully
[[09:13:02]] [INFO] wguGCt7OoB=running
[[09:13:02]] [INFO] Executing action 550/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:13:02]] [SUCCESS] Screenshot refreshed
[[09:13:02]] [INFO] Refreshing screenshot...
[[09:13:02]] [INFO] RDQCFIxjA0=pass
[[09:12:57]] [SUCCESS] Screenshot refreshed successfully
[[09:12:57]] [SUCCESS] Screenshot refreshed successfully
[[09:12:57]] [INFO] RDQCFIxjA0=running
[[09:12:57]] [INFO] Executing action 549/576: Swipe from (90%, 20%) to (30%, 20%)
[[09:12:57]] [SUCCESS] Screenshot refreshed
[[09:12:57]] [INFO] Refreshing screenshot...
[[09:12:57]] [INFO] x4Mid4HQ0Z=pass
[[09:12:52]] [SUCCESS] Screenshot refreshed successfully
[[09:12:52]] [SUCCESS] Screenshot refreshed successfully
[[09:12:52]] [INFO] x4Mid4HQ0Z=running
[[09:12:52]] [INFO] Executing action 548/576: Swipe from (90%, 20%) to (30%, 20%)
[[09:12:52]] [SUCCESS] Screenshot refreshed
[[09:12:52]] [INFO] Refreshing screenshot...
[[09:12:52]] [INFO] OKCHAK6HCJ=pass
[[09:12:47]] [SUCCESS] Screenshot refreshed successfully
[[09:12:47]] [SUCCESS] Screenshot refreshed successfully
[[09:12:47]] [INFO] OKCHAK6HCJ=running
[[09:12:47]] [INFO] Executing action 547/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[09:12:47]] [SUCCESS] Screenshot refreshed
[[09:12:47]] [INFO] Refreshing screenshot...
[[09:12:47]] [INFO] Ef6OumM2eS=pass
[[09:12:42]] [SUCCESS] Screenshot refreshed successfully
[[09:12:42]] [SUCCESS] Screenshot refreshed successfully
[[09:12:42]] [INFO] Ef6OumM2eS=running
[[09:12:42]] [INFO] Executing action 546/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[09:12:42]] [SUCCESS] Screenshot refreshed
[[09:12:42]] [INFO] Refreshing screenshot...
[[09:12:42]] [INFO] QkaF93zxUg=pass
[[09:12:38]] [SUCCESS] Screenshot refreshed successfully
[[09:12:38]] [SUCCESS] Screenshot refreshed successfully
[[09:12:38]] [INFO] QkaF93zxUg=running
[[09:12:38]] [INFO] Executing action 545/576: Check if element with xpath="(//XCUIElementTypeStaticText[@name="Value"])[1]" exists
[[09:12:37]] [SUCCESS] Screenshot refreshed
[[09:12:37]] [INFO] Refreshing screenshot...
[[09:12:37]] [INFO] HZT2s0AzX7=pass
[[09:12:33]] [SUCCESS] Screenshot refreshed successfully
[[09:12:33]] [SUCCESS] Screenshot refreshed successfully
[[09:12:33]] [INFO] HZT2s0AzX7=running
[[09:12:33]] [INFO] Executing action 544/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="("]/following-sibling::*[1]
[[09:12:32]] [SUCCESS] Screenshot refreshed
[[09:12:32]] [INFO] Refreshing screenshot...
[[09:12:32]] [INFO] 0bnBNoqPt8=pass
[[09:12:28]] [SUCCESS] Screenshot refreshed successfully
[[09:12:28]] [SUCCESS] Screenshot refreshed successfully
[[09:12:28]] [INFO] 0bnBNoqPt8=running
[[09:12:28]] [INFO] Executing action 543/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[09:12:27]] [SUCCESS] Screenshot refreshed
[[09:12:27]] [INFO] Refreshing screenshot...
[[09:12:27]] [INFO] xmelRkcdVx=pass
[[09:12:23]] [SUCCESS] Screenshot refreshed successfully
[[09:12:23]] [SUCCESS] Screenshot refreshed successfully
[[09:12:22]] [INFO] xmelRkcdVx=running
[[09:12:22]] [INFO] Executing action 542/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[09:12:22]] [SUCCESS] Screenshot refreshed
[[09:12:22]] [INFO] Refreshing screenshot...
[[09:12:22]] [INFO] ksCBjJiwHZ=pass
[[09:12:18]] [SUCCESS] Screenshot refreshed successfully
[[09:12:18]] [SUCCESS] Screenshot refreshed successfully
[[09:12:18]] [INFO] ksCBjJiwHZ=running
[[09:12:18]] [INFO] Executing action 541/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:12:17]] [SUCCESS] Screenshot refreshed
[[09:12:17]] [INFO] Refreshing screenshot...
[[09:12:17]] [INFO] RuPGkdCdah=pass
[[09:12:13]] [SUCCESS] Screenshot refreshed successfully
[[09:12:13]] [SUCCESS] Screenshot refreshed successfully
[[09:12:13]] [INFO] RuPGkdCdah=running
[[09:12:13]] [INFO] Executing action 540/576: iOS Function: text - Text: "enn[cooker-id]"
[[09:12:13]] [SUCCESS] Screenshot refreshed
[[09:12:13]] [INFO] Refreshing screenshot...
[[09:12:13]] [INFO] ewuLtuqVuo=pass
[[09:12:07]] [SUCCESS] Screenshot refreshed successfully
[[09:12:07]] [SUCCESS] Screenshot refreshed successfully
[[09:12:07]] [INFO] ewuLtuqVuo=running
[[09:12:07]] [INFO] Executing action 539/576: Tap on Text: "Find"
[[09:12:06]] [SUCCESS] Screenshot refreshed
[[09:12:06]] [INFO] Refreshing screenshot...
[[09:12:06]] [INFO] GTXmST3hEA=pass
[[09:12:02]] [SUCCESS] Screenshot refreshed successfully
[[09:12:02]] [SUCCESS] Screenshot refreshed successfully
[[09:12:02]] [INFO] GTXmST3hEA=running
[[09:12:02]] [INFO] Executing action 538/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[09:12:01]] [SUCCESS] Screenshot refreshed
[[09:12:01]] [INFO] Refreshing screenshot...
[[09:12:01]] [INFO] qkZ5KShdEU=pass
[[09:11:56]] [SUCCESS] Screenshot refreshed successfully
[[09:11:56]] [SUCCESS] Screenshot refreshed successfully
[[09:11:56]] [INFO] qkZ5KShdEU=running
[[09:11:56]] [INFO] Executing action 537/576: iOS Function: text - Text: "env[pwd]"
[[09:11:55]] [SUCCESS] Screenshot refreshed
[[09:11:55]] [INFO] Refreshing screenshot...
[[09:11:55]] [INFO] 7g2LmvjtEZ=pass
[[09:11:51]] [SUCCESS] Screenshot refreshed successfully
[[09:11:51]] [SUCCESS] Screenshot refreshed successfully
[[09:11:51]] [INFO] 7g2LmvjtEZ=running
[[09:11:51]] [INFO] Executing action 536/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:11:51]] [SUCCESS] Screenshot refreshed
[[09:11:51]] [INFO] Refreshing screenshot...
[[09:11:51]] [INFO] OUT2ASweb6=pass
[[09:11:46]] [SUCCESS] Screenshot refreshed successfully
[[09:11:46]] [SUCCESS] Screenshot refreshed successfully
[[09:11:46]] [INFO] OUT2ASweb6=running
[[09:11:46]] [INFO] Executing action 535/576: iOS Function: text - Text: "env[uname]"
[[09:11:45]] [SUCCESS] Screenshot refreshed
[[09:11:45]] [INFO] Refreshing screenshot...
[[09:11:45]] [INFO] TV4kJIIV9v=pass
[[09:11:41]] [SUCCESS] Screenshot refreshed successfully
[[09:11:41]] [SUCCESS] Screenshot refreshed successfully
[[09:11:41]] [INFO] TV4kJIIV9v=running
[[09:11:41]] [INFO] Executing action 534/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:11:40]] [SUCCESS] Screenshot refreshed
[[09:11:40]] [INFO] Refreshing screenshot...
[[09:11:40]] [INFO] kQJbqm7uCi=pass
[[09:11:38]] [SUCCESS] Screenshot refreshed successfully
[[09:11:38]] [SUCCESS] Screenshot refreshed successfully
[[09:11:37]] [INFO] kQJbqm7uCi=running
[[09:11:37]] [INFO] Executing action 533/576: iOS Function: alert_accept
[[09:11:37]] [SUCCESS] Screenshot refreshed
[[09:11:37]] [INFO] Refreshing screenshot...
[[09:11:37]] [INFO] SPE01N6pyp=pass
[[09:11:30]] [SUCCESS] Screenshot refreshed successfully
[[09:11:30]] [SUCCESS] Screenshot refreshed successfully
[[09:11:30]] [INFO] SPE01N6pyp=running
[[09:11:30]] [INFO] Executing action 532/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:11:29]] [SUCCESS] Screenshot refreshed
[[09:11:29]] [INFO] Refreshing screenshot...
[[09:11:29]] [INFO] WEB5St2Mb7=pass
[[09:11:25]] [SUCCESS] Screenshot refreshed successfully
[[09:11:25]] [SUCCESS] Screenshot refreshed successfully
[[09:11:25]] [INFO] WEB5St2Mb7=running
[[09:11:25]] [INFO] Executing action 531/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:11:24]] [SUCCESS] Screenshot refreshed
[[09:11:24]] [INFO] Refreshing screenshot...
[[09:11:24]] [INFO] To7bij5MnF=pass
[[09:11:19]] [INFO] To7bij5MnF=running
[[09:11:19]] [INFO] Executing action 530/576: Swipe from (5%, 50%) to (90%, 50%)
[[09:11:19]] [SUCCESS] Screenshot refreshed successfully
[[09:11:19]] [SUCCESS] Screenshot refreshed successfully
[[09:11:19]] [SUCCESS] Screenshot refreshed
[[09:11:19]] [INFO] Refreshing screenshot...
[[09:11:19]] [INFO] NkybTKfs2U=pass
[[09:11:13]] [SUCCESS] Screenshot refreshed successfully
[[09:11:13]] [SUCCESS] Screenshot refreshed successfully
[[09:11:13]] [INFO] NkybTKfs2U=running
[[09:11:13]] [INFO] Executing action 529/576: Swipe from (5%, 50%) to (90%, 50%)
[[09:11:13]] [SUCCESS] Screenshot refreshed
[[09:11:13]] [INFO] Refreshing screenshot...
[[09:11:13]] [INFO] dYEtjrv6lz=pass
[[09:11:08]] [INFO] dYEtjrv6lz=running
[[09:11:08]] [INFO] Executing action 528/576: Tap on Text: "Months"
[[09:11:08]] [SUCCESS] Screenshot refreshed successfully
[[09:11:08]] [SUCCESS] Screenshot refreshed successfully
[[09:11:07]] [SUCCESS] Screenshot refreshed
[[09:11:07]] [INFO] Refreshing screenshot...
[[09:11:07]] [INFO] eGQ7VrKUSo=pass
[[09:11:03]] [SUCCESS] Screenshot refreshed successfully
[[09:11:03]] [SUCCESS] Screenshot refreshed successfully
[[09:11:03]] [INFO] eGQ7VrKUSo=running
[[09:11:03]] [INFO] Executing action 527/576: Tap on Text: "Age"
[[09:11:02]] [SUCCESS] Screenshot refreshed
[[09:11:02]] [INFO] Refreshing screenshot...
[[09:11:02]] [INFO] zNRPvs2cC4=pass
[[09:10:58]] [SUCCESS] Screenshot refreshed successfully
[[09:10:58]] [SUCCESS] Screenshot refreshed successfully
[[09:10:58]] [INFO] zNRPvs2cC4=running
[[09:10:58]] [INFO] Executing action 526/576: Tap on Text: "Toys"
[[09:10:57]] [SUCCESS] Screenshot refreshed
[[09:10:57]] [INFO] Refreshing screenshot...
[[09:10:57]] [INFO] KyyS139agr=pass
[[09:10:53]] [SUCCESS] Screenshot refreshed successfully
[[09:10:53]] [SUCCESS] Screenshot refreshed successfully
[[09:10:52]] [INFO] KyyS139agr=running
[[09:10:52]] [INFO] Executing action 525/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[09:10:52]] [SUCCESS] Screenshot refreshed
[[09:10:52]] [INFO] Refreshing screenshot...
[[09:10:52]] [INFO] 5e4LeoW1YU=pass
[[09:10:48]] [SUCCESS] Screenshot refreshed successfully
[[09:10:48]] [SUCCESS] Screenshot refreshed successfully
[[09:10:46]] [INFO] 5e4LeoW1YU=running
[[09:10:46]] [INFO] Executing action 524/576: Restart app: env[appid]
[[09:10:46]] [SUCCESS] Screenshot refreshed
[[09:10:46]] [INFO] Refreshing screenshot...
[[09:10:46]] [SUCCESS] Screenshot refreshed
[[09:10:46]] [INFO] Refreshing screenshot...
[[09:10:31]] [SUCCESS] Screenshot refreshed successfully
[[09:10:31]] [SUCCESS] Screenshot refreshed successfully
[[09:10:30]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[09:10:30]] [SUCCESS] Screenshot refreshed
[[09:10:30]] [INFO] Refreshing screenshot...
[[09:09:44]] [SUCCESS] Screenshot refreshed successfully
[[09:09:44]] [SUCCESS] Screenshot refreshed successfully
[[09:09:44]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[09:09:43]] [SUCCESS] Screenshot refreshed
[[09:09:43]] [INFO] Refreshing screenshot...
[[09:09:28]] [SUCCESS] Screenshot refreshed successfully
[[09:09:28]] [SUCCESS] Screenshot refreshed successfully
[[09:09:27]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[09:09:27]] [SUCCESS] Screenshot refreshed
[[09:09:27]] [INFO] Refreshing screenshot...
[[09:08:41]] [SUCCESS] Screenshot refreshed successfully
[[09:08:41]] [SUCCESS] Screenshot refreshed successfully
[[09:08:41]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[09:08:40]] [SUCCESS] Screenshot refreshed
[[09:08:40]] [INFO] Refreshing screenshot...
[[09:08:28]] [SUCCESS] Screenshot refreshed successfully
[[09:08:28]] [SUCCESS] Screenshot refreshed successfully
[[09:08:24]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[09:08:23]] [SUCCESS] Screenshot refreshed
[[09:08:23]] [INFO] Refreshing screenshot...
[[09:07:38]] [SUCCESS] Screenshot refreshed successfully
[[09:07:38]] [SUCCESS] Screenshot refreshed successfully
[[09:07:37]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[09:07:37]] [SUCCESS] Screenshot refreshed
[[09:07:37]] [INFO] Refreshing screenshot...
[[09:07:20]] [SUCCESS] Screenshot refreshed successfully
[[09:07:20]] [SUCCESS] Screenshot refreshed successfully
[[09:07:19]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[09:07:19]] [SUCCESS] Screenshot refreshed
[[09:07:19]] [INFO] Refreshing screenshot...
[[09:06:33]] [SUCCESS] Screenshot refreshed successfully
[[09:06:33]] [SUCCESS] Screenshot refreshed successfully
[[09:06:33]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[09:06:32]] [SUCCESS] Screenshot refreshed
[[09:06:32]] [INFO] Refreshing screenshot...
[[09:06:16]] [SUCCESS] Screenshot refreshed successfully
[[09:06:16]] [SUCCESS] Screenshot refreshed successfully
[[09:06:16]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[09:06:15]] [SUCCESS] Screenshot refreshed
[[09:06:15]] [INFO] Refreshing screenshot...
[[09:05:26]] [SUCCESS] Screenshot refreshed successfully
[[09:05:26]] [SUCCESS] Screenshot refreshed successfully
[[09:05:26]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[09:05:26]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[09:05:26]] [INFO] Loading steps for multiStep action: Click_Paginations
[[09:05:26]] [INFO] aqs7O0Yq2p=running
[[09:05:26]] [INFO] Executing action 523/576: Execute Test Case: Click_Paginations (10 steps)
[[09:05:25]] [SUCCESS] Screenshot refreshed
[[09:05:25]] [INFO] Refreshing screenshot...
[[09:05:25]] [INFO] IL6kON0uQ9=pass
[[09:05:21]] [SUCCESS] Screenshot refreshed successfully
[[09:05:21]] [SUCCESS] Screenshot refreshed successfully
[[09:05:21]] [INFO] IL6kON0uQ9=running
[[09:05:21]] [INFO] Executing action 522/576: iOS Function: text - Text: "kids toys"
[[09:05:20]] [SUCCESS] Screenshot refreshed
[[09:05:20]] [INFO] Refreshing screenshot...
[[09:05:20]] [INFO] 6G6P3UE7Uy=pass
[[09:05:15]] [SUCCESS] Screenshot refreshed successfully
[[09:05:15]] [SUCCESS] Screenshot refreshed successfully
[[09:05:14]] [INFO] 6G6P3UE7Uy=running
[[09:05:14]] [INFO] Executing action 521/576: Tap on Text: "Find"
[[09:05:14]] [SUCCESS] Screenshot refreshed
[[09:05:14]] [INFO] Refreshing screenshot...
[[09:05:14]] [INFO] 7xs3GiydGF=pass
[[09:05:09]] [SUCCESS] Screenshot refreshed successfully
[[09:05:09]] [SUCCESS] Screenshot refreshed successfully
[[09:05:09]] [INFO] 7xs3GiydGF=running
[[09:05:09]] [INFO] Executing action 520/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:05:09]] [SUCCESS] Screenshot refreshed
[[09:05:09]] [INFO] Refreshing screenshot...
[[09:05:09]] [INFO] VqSa9z9R2Q=pass
[[09:05:07]] [INFO] VqSa9z9R2Q=running
[[09:05:07]] [INFO] Executing action 519/576: Launch app: env[appid]
[[09:05:07]] [SUCCESS] Screenshot refreshed successfully
[[09:05:07]] [SUCCESS] Screenshot refreshed successfully
[[09:05:06]] [SUCCESS] Screenshot refreshed
[[09:05:06]] [INFO] Refreshing screenshot...
[[09:05:06]] [INFO] RHEU77LRMw=pass
[[09:05:02]] [SUCCESS] Screenshot refreshed successfully
[[09:05:02]] [SUCCESS] Screenshot refreshed successfully
[[09:05:02]] [INFO] RHEU77LRMw=running
[[09:05:02]] [INFO] Executing action 518/576: Tap on Text: "+61"
[[09:05:02]] [SUCCESS] Screenshot refreshed
[[09:05:02]] [INFO] Refreshing screenshot...
[[09:05:02]] [INFO] MTRbUlaRvI=pass
[[09:04:57]] [SUCCESS] Screenshot refreshed successfully
[[09:04:57]] [SUCCESS] Screenshot refreshed successfully
[[09:04:57]] [INFO] MTRbUlaRvI=running
[[09:04:57]] [INFO] Executing action 517/576: Tap on Text: "1800"
[[09:04:56]] [SUCCESS] Screenshot refreshed
[[09:04:56]] [INFO] Refreshing screenshot...
[[09:04:56]] [INFO] I0tM87Yjhc=pass
[[09:04:52]] [SUCCESS] Screenshot refreshed successfully
[[09:04:52]] [SUCCESS] Screenshot refreshed successfully
[[09:04:51]] [INFO] I0tM87Yjhc=running
[[09:04:51]] [INFO] Executing action 516/576: Tap on Text: "click"
[[09:04:51]] [SUCCESS] Screenshot refreshed
[[09:04:51]] [INFO] Refreshing screenshot...
[[09:04:51]] [INFO] t6L5vWfBYM=pass
[[09:04:19]] [SUCCESS] Screenshot refreshed successfully
[[09:04:19]] [SUCCESS] Screenshot refreshed successfully
[[09:04:19]] [INFO] t6L5vWfBYM=running
[[09:04:19]] [INFO] Executing action 515/576: Swipe from (50%, 70%) to (50%, 30%)
[[09:04:18]] [SUCCESS] Screenshot refreshed
[[09:04:18]] [INFO] Refreshing screenshot...
[[09:04:18]] [INFO] DhFJzlme9K=pass
[[09:04:14]] [SUCCESS] Screenshot refreshed successfully
[[09:04:14]] [SUCCESS] Screenshot refreshed successfully
[[09:04:14]] [INFO] DhFJzlme9K=running
[[09:04:14]] [INFO] Executing action 514/576: Tap on Text: "FAQ"
[[09:04:13]] [SUCCESS] Screenshot refreshed
[[09:04:13]] [INFO] Refreshing screenshot...
[[09:04:13]] [INFO] g17Boaefhg=pass
[[09:04:09]] [SUCCESS] Screenshot refreshed successfully
[[09:04:09]] [SUCCESS] Screenshot refreshed successfully
[[09:04:09]] [INFO] g17Boaefhg=running
[[09:04:09]] [INFO] Executing action 513/576: Tap on Text: "Help"
[[09:04:08]] [SUCCESS] Screenshot refreshed
[[09:04:08]] [INFO] Refreshing screenshot...
[[09:04:08]] [INFO] SqDiBhmyOG=pass
[[09:04:04]] [SUCCESS] Screenshot refreshed successfully
[[09:04:04]] [SUCCESS] Screenshot refreshed successfully
[[09:04:04]] [INFO] SqDiBhmyOG=running
[[09:04:04]] [INFO] Executing action 512/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:04:03]] [SUCCESS] Screenshot refreshed
[[09:04:03]] [INFO] Refreshing screenshot...
[[09:04:03]] [INFO] OR0SKKnFxy=pass
[[09:03:50]] [SUCCESS] Screenshot refreshed successfully
[[09:03:50]] [SUCCESS] Screenshot refreshed successfully
[[09:03:49]] [INFO] OR0SKKnFxy=running
[[09:03:49]] [INFO] Executing action 511/576: Restart app: env[appid]
[[09:03:49]] [SUCCESS] Screenshot refreshed
[[09:03:49]] [INFO] Refreshing screenshot...
[[09:03:49]] [SUCCESS] Screenshot refreshed
[[09:03:49]] [INFO] Refreshing screenshot...
[[09:03:46]] [SUCCESS] Screenshot refreshed successfully
[[09:03:46]] [SUCCESS] Screenshot refreshed successfully
[[09:03:45]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[09:03:45]] [SUCCESS] Screenshot refreshed
[[09:03:45]] [INFO] Refreshing screenshot...
[[09:03:33]] [SUCCESS] Screenshot refreshed successfully
[[09:03:33]] [SUCCESS] Screenshot refreshed successfully
[[09:03:33]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[09:03:32]] [SUCCESS] Screenshot refreshed
[[09:03:32]] [INFO] Refreshing screenshot...
[[09:03:28]] [SUCCESS] Screenshot refreshed successfully
[[09:03:28]] [SUCCESS] Screenshot refreshed successfully
[[09:03:28]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[09:03:28]] [SUCCESS] Screenshot refreshed
[[09:03:28]] [INFO] Refreshing screenshot...
[[09:03:23]] [SUCCESS] Screenshot refreshed successfully
[[09:03:23]] [SUCCESS] Screenshot refreshed successfully
[[09:03:23]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:03:22]] [SUCCESS] Screenshot refreshed
[[09:03:22]] [INFO] Refreshing screenshot...
[[09:03:16]] [SUCCESS] Screenshot refreshed successfully
[[09:03:16]] [SUCCESS] Screenshot refreshed successfully
[[09:03:15]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[09:03:15]] [SUCCESS] Screenshot refreshed
[[09:03:15]] [INFO] Refreshing screenshot...
[[09:03:08]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[09:03:08]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[09:03:07]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[09:03:07]] [INFO] kPdSiomhwu=running
[[09:03:07]] [INFO] Executing action 510/576: cleanupSteps action
[[09:03:07]] [INFO] Skipping remaining steps in failed test case (moving from action 494 to 509), but preserving cleanup steps
[[09:03:07]] [INFO] Iab9zCfpqO=fail
[[09:03:07]] [ERROR] Action 494 failed: Element not found or not tappable: accessibility_id='Add to bag'
[[09:02:53]] [SUCCESS] Screenshot refreshed successfully
[[09:02:53]] [SUCCESS] Screenshot refreshed successfully
[[09:02:53]] [INFO] Iab9zCfpqO=running
[[09:02:53]] [INFO] Executing action 494/576: Tap on element with accessibility_id: Add to bag
[[09:02:53]] [SUCCESS] Screenshot refreshed
[[09:02:53]] [INFO] Refreshing screenshot...
[[09:02:53]] [INFO] Qy0Y0uJchm=pass
[[09:02:49]] [SUCCESS] Screenshot refreshed successfully
[[09:02:49]] [SUCCESS] Screenshot refreshed successfully
[[09:02:49]] [INFO] Qy0Y0uJchm=running
[[09:02:49]] [INFO] Executing action 493/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[09:02:48]] [SUCCESS] Screenshot refreshed
[[09:02:48]] [INFO] Refreshing screenshot...
[[09:02:48]] [INFO] YHaMIjULRf=pass
[[09:02:43]] [SUCCESS] Screenshot refreshed successfully
[[09:02:43]] [SUCCESS] Screenshot refreshed successfully
[[09:02:42]] [INFO] YHaMIjULRf=running
[[09:02:42]] [INFO] Executing action 492/576: Tap on Text: "List"
[[09:02:41]] [SUCCESS] Screenshot refreshed
[[09:02:41]] [INFO] Refreshing screenshot...
[[09:02:41]] [INFO] igReeDqips=pass
[[09:02:37]] [SUCCESS] Screenshot refreshed successfully
[[09:02:37]] [SUCCESS] Screenshot refreshed successfully
[[09:02:36]] [INFO] igReeDqips=running
[[09:02:36]] [INFO] Executing action 491/576: Tap on image: env[catalogue-menu-img]
[[09:02:36]] [SUCCESS] Screenshot refreshed
[[09:02:36]] [INFO] Refreshing screenshot...
[[09:02:36]] [INFO] Xqj9EIVE7g=pass
[[09:02:14]] [SUCCESS] Screenshot refreshed successfully
[[09:02:14]] [SUCCESS] Screenshot refreshed successfully
[[09:02:13]] [INFO] Xqj9EIVE7g=running
[[09:02:13]] [INFO] Executing action 490/576: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[09:02:12]] [SUCCESS] Screenshot refreshed
[[09:02:12]] [INFO] Refreshing screenshot...
[[09:02:12]] [INFO] gkkQzTCmma=pass
[[09:02:06]] [SUCCESS] Screenshot refreshed successfully
[[09:02:06]] [SUCCESS] Screenshot refreshed successfully
[[09:02:06]] [INFO] gkkQzTCmma=running
[[09:02:06]] [INFO] Executing action 489/576: Tap on Text: "Catalogue"
[[09:02:06]] [SUCCESS] Screenshot refreshed
[[09:02:06]] [INFO] Refreshing screenshot...
[[09:02:06]] [INFO] UpUSVInizv=pass
[[09:02:02]] [SUCCESS] Screenshot refreshed successfully
[[09:02:02]] [SUCCESS] Screenshot refreshed successfully
[[09:02:01]] [INFO] UpUSVInizv=running
[[09:02:01]] [INFO] Executing action 488/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[09:02:01]] [SUCCESS] Screenshot refreshed
[[09:02:01]] [INFO] Refreshing screenshot...
[[09:02:01]] [INFO] Cmvm82hiAa=pass
[[09:01:53]] [SUCCESS] Screenshot refreshed successfully
[[09:01:53]] [SUCCESS] Screenshot refreshed successfully
[[09:01:53]] [INFO] Cmvm82hiAa=running
[[09:01:53]] [INFO] Executing action 487/576: Tap on element with accessibility_id: Add to bag
[[09:01:53]] [SUCCESS] Screenshot refreshed
[[09:01:53]] [INFO] Refreshing screenshot...
[[09:01:53]] [INFO] JcAR0JctQ6=pass
[[09:01:49]] [SUCCESS] Screenshot refreshed successfully
[[09:01:49]] [SUCCESS] Screenshot refreshed successfully
[[09:01:49]] [INFO] JcAR0JctQ6=running
[[09:01:49]] [INFO] Executing action 486/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[09:01:48]] [SUCCESS] Screenshot refreshed
[[09:01:48]] [INFO] Refreshing screenshot...
[[09:01:48]] [INFO] Pd7cReoJM6=pass
[[09:01:43]] [SUCCESS] Screenshot refreshed successfully
[[09:01:43]] [SUCCESS] Screenshot refreshed successfully
[[09:01:42]] [INFO] Pd7cReoJM6=running
[[09:01:42]] [INFO] Executing action 485/576: Tap on Text: "List"
[[09:01:41]] [SUCCESS] Screenshot refreshed
[[09:01:41]] [INFO] Refreshing screenshot...
[[09:01:41]] [INFO] igReeDqips=pass
[[09:01:37]] [SUCCESS] Screenshot refreshed successfully
[[09:01:37]] [SUCCESS] Screenshot refreshed successfully
[[09:01:36]] [INFO] igReeDqips=running
[[09:01:36]] [INFO] Executing action 484/576: Tap on image: env[catalogue-menu-img]
[[09:01:36]] [SUCCESS] Screenshot refreshed
[[09:01:36]] [INFO] Refreshing screenshot...
[[09:01:36]] [INFO] Xqj9EIVE7g=pass
[[09:01:13]] [SUCCESS] Screenshot refreshed successfully
[[09:01:13]] [SUCCESS] Screenshot refreshed successfully
[[09:01:11]] [INFO] Xqj9EIVE7g=running
[[09:01:11]] [INFO] Executing action 483/576: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[09:01:11]] [SUCCESS] Screenshot refreshed
[[09:01:11]] [INFO] Refreshing screenshot...
[[09:01:11]] [INFO] gkkQzTCmma=pass
[[09:01:05]] [SUCCESS] Screenshot refreshed successfully
[[09:01:05]] [SUCCESS] Screenshot refreshed successfully
[[09:01:05]] [INFO] gkkQzTCmma=running
[[09:01:05]] [INFO] Executing action 482/576: Tap on Text: "Catalogue"
[[09:01:05]] [SUCCESS] Screenshot refreshed
[[09:01:05]] [INFO] Refreshing screenshot...
[[09:01:05]] [INFO] UpUSVInizv=pass
[[09:01:00]] [SUCCESS] Screenshot refreshed successfully
[[09:01:00]] [SUCCESS] Screenshot refreshed successfully
[[09:01:00]] [INFO] UpUSVInizv=running
[[09:01:00]] [INFO] Executing action 481/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[09:00:59]] [SUCCESS] Screenshot refreshed
[[09:00:59]] [INFO] Refreshing screenshot...
[[09:00:59]] [INFO] 0QtNHB5WEK=pass
[[09:00:56]] [SUCCESS] Screenshot refreshed successfully
[[09:00:56]] [SUCCESS] Screenshot refreshed successfully
[[09:00:56]] [INFO] 0QtNHB5WEK=running
[[09:00:56]] [INFO] Executing action 480/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[09:00:55]] [SUCCESS] Screenshot refreshed
[[09:00:55]] [INFO] Refreshing screenshot...
[[09:00:55]] [INFO] fTdGMJ3NH3=pass
[[09:00:52]] [SUCCESS] Screenshot refreshed successfully
[[09:00:52]] [SUCCESS] Screenshot refreshed successfully
[[09:00:52]] [INFO] fTdGMJ3NH3=running
[[09:00:52]] [INFO] Executing action 479/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[09:00:52]] [SUCCESS] Screenshot refreshed
[[09:00:52]] [INFO] Refreshing screenshot...
[[09:00:52]] [INFO] rYJcLPh8Aq=pass
[[09:00:48]] [INFO] rYJcLPh8Aq=running
[[09:00:48]] [INFO] Executing action 478/576: iOS Function: text - Text: "kmart au"
[[09:00:48]] [SUCCESS] Screenshot refreshed successfully
[[09:00:48]] [SUCCESS] Screenshot refreshed successfully
[[09:00:47]] [SUCCESS] Screenshot refreshed
[[09:00:47]] [INFO] Refreshing screenshot...
[[09:00:47]] [INFO] 0Q0fm6OTij=pass
[[09:00:44]] [SUCCESS] Screenshot refreshed successfully
[[09:00:44]] [SUCCESS] Screenshot refreshed successfully
[[09:00:44]] [INFO] 0Q0fm6OTij=running
[[09:00:44]] [INFO] Executing action 477/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[09:00:43]] [SUCCESS] Screenshot refreshed
[[09:00:43]] [INFO] Refreshing screenshot...
[[09:00:43]] [INFO] xVuuejtCFA=pass
[[09:00:40]] [SUCCESS] Screenshot refreshed successfully
[[09:00:40]] [SUCCESS] Screenshot refreshed successfully
[[09:00:39]] [INFO] xVuuejtCFA=running
[[09:00:39]] [INFO] Executing action 476/576: Restart app: com.apple.mobilesafari
[[09:00:39]] [SUCCESS] Screenshot refreshed
[[09:00:39]] [INFO] Refreshing screenshot...
[[09:00:39]] [INFO] LcYLwUffqj=pass
[[09:00:34]] [SUCCESS] Screenshot refreshed successfully
[[09:00:34]] [SUCCESS] Screenshot refreshed successfully
[[09:00:34]] [INFO] LcYLwUffqj=running
[[09:00:34]] [INFO] Executing action 475/576: Tap on Text: "out"
[[09:00:33]] [SUCCESS] Screenshot refreshed
[[09:00:33]] [INFO] Refreshing screenshot...
[[09:00:33]] [INFO] ZZPNqTJ65s=pass
[[09:00:29]] [SUCCESS] Screenshot refreshed successfully
[[09:00:29]] [SUCCESS] Screenshot refreshed successfully
[[09:00:29]] [INFO] ZZPNqTJ65s=running
[[09:00:29]] [INFO] Executing action 474/576: Swipe from (50%, 70%) to (50%, 30%)
[[09:00:28]] [SUCCESS] Screenshot refreshed
[[09:00:28]] [INFO] Refreshing screenshot...
[[09:00:28]] [INFO] UpUSVInizv=pass
[[09:00:25]] [SUCCESS] Screenshot refreshed successfully
[[09:00:25]] [SUCCESS] Screenshot refreshed successfully
[[09:00:24]] [INFO] UpUSVInizv=running
[[09:00:24]] [INFO] Executing action 473/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[09:00:24]] [SUCCESS] Screenshot refreshed
[[09:00:24]] [INFO] Refreshing screenshot...
[[09:00:24]] [INFO] hCCEvRtj1A=pass
[[09:00:19]] [INFO] hCCEvRtj1A=running
[[09:00:19]] [INFO] Executing action 472/576: Restart app: env[appid]
[[09:00:18]] [SUCCESS] Screenshot refreshed successfully
[[09:00:18]] [SUCCESS] Screenshot refreshed successfully
[[09:00:18]] [SUCCESS] Screenshot refreshed
[[09:00:18]] [INFO] Refreshing screenshot...
[[09:00:18]] [INFO] V42eHtTRYW=pass
[[09:00:11]] [INFO] V42eHtTRYW=running
[[09:00:11]] [INFO] Executing action 471/576: Wait for 5 ms
[[09:00:11]] [SUCCESS] Screenshot refreshed successfully
[[09:00:11]] [SUCCESS] Screenshot refreshed successfully
[[09:00:11]] [SUCCESS] Screenshot refreshed
[[09:00:11]] [INFO] Refreshing screenshot...
[[09:00:11]] [INFO] GRwHMVK4sA=pass
[[09:00:09]] [INFO] GRwHMVK4sA=running
[[09:00:09]] [INFO] Executing action 470/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[09:00:09]] [SUCCESS] Screenshot refreshed successfully
[[09:00:09]] [SUCCESS] Screenshot refreshed successfully
[[09:00:08]] [SUCCESS] Screenshot refreshed
[[09:00:08]] [INFO] Refreshing screenshot...
[[09:00:08]] [INFO] V42eHtTRYW=pass
[[09:00:02]] [INFO] V42eHtTRYW=running
[[09:00:02]] [INFO] Executing action 469/576: Wait for 5 ms
[[09:00:01]] [SUCCESS] Screenshot refreshed successfully
[[09:00:01]] [SUCCESS] Screenshot refreshed successfully
[[09:00:01]] [SUCCESS] Screenshot refreshed
[[09:00:01]] [INFO] Refreshing screenshot...
[[09:00:01]] [INFO] LfyQctrEJn=pass
[[08:59:59]] [SUCCESS] Screenshot refreshed successfully
[[08:59:59]] [SUCCESS] Screenshot refreshed successfully
[[08:59:59]] [INFO] LfyQctrEJn=running
[[08:59:59]] [INFO] Executing action 468/576: Launch app: com.apple.Preferences
[[08:59:59]] [SUCCESS] Screenshot refreshed
[[08:59:59]] [INFO] Refreshing screenshot...
[[08:59:59]] [INFO] seQcUKjkSU=pass
[[08:59:57]] [SUCCESS] Screenshot refreshed successfully
[[08:59:57]] [SUCCESS] Screenshot refreshed successfully
[[08:59:57]] [INFO] seQcUKjkSU=running
[[08:59:57]] [INFO] Executing action 467/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[08:59:56]] [SUCCESS] Screenshot refreshed
[[08:59:56]] [INFO] Refreshing screenshot...
[[08:59:56]] [INFO] UpUSVInizv=pass
[[08:59:54]] [SUCCESS] Screenshot refreshed successfully
[[08:59:54]] [SUCCESS] Screenshot refreshed successfully
[[08:59:54]] [INFO] UpUSVInizv=running
[[08:59:54]] [INFO] Executing action 466/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[08:59:54]] [SUCCESS] Screenshot refreshed
[[08:59:54]] [INFO] Refreshing screenshot...
[[08:59:54]] [INFO] WoymrHdtrO=pass
[[08:59:52]] [SUCCESS] Screenshot refreshed successfully
[[08:59:52]] [SUCCESS] Screenshot refreshed successfully
[[08:59:52]] [INFO] WoymrHdtrO=running
[[08:59:52]] [INFO] Executing action 465/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[08:59:51]] [SUCCESS] Screenshot refreshed
[[08:59:51]] [INFO] Refreshing screenshot...
[[08:59:51]] [INFO] 6xgrAWyfZ4=pass
[[08:59:49]] [SUCCESS] Screenshot refreshed successfully
[[08:59:49]] [SUCCESS] Screenshot refreshed successfully
[[08:59:49]] [INFO] 6xgrAWyfZ4=running
[[08:59:49]] [INFO] Executing action 464/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[08:59:48]] [SUCCESS] Screenshot refreshed
[[08:59:48]] [INFO] Refreshing screenshot...
[[08:59:48]] [INFO] eSr9EFlJek=pass
[[08:59:46]] [SUCCESS] Screenshot refreshed successfully
[[08:59:46]] [SUCCESS] Screenshot refreshed successfully
[[08:59:46]] [INFO] eSr9EFlJek=running
[[08:59:46]] [INFO] Executing action 463/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[08:59:46]] [SUCCESS] Screenshot refreshed
[[08:59:46]] [INFO] Refreshing screenshot...
[[08:59:46]] [INFO] 3KNqlNy6Bj=pass
[[08:59:43]] [SUCCESS] Screenshot refreshed successfully
[[08:59:43]] [SUCCESS] Screenshot refreshed successfully
[[08:59:43]] [INFO] 3KNqlNy6Bj=running
[[08:59:43]] [INFO] Executing action 462/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[08:59:43]] [SUCCESS] Screenshot refreshed
[[08:59:43]] [INFO] Refreshing screenshot...
[[08:59:43]] [INFO] cokvFXhj4c=pass
[[08:59:40]] [SUCCESS] Screenshot refreshed successfully
[[08:59:40]] [SUCCESS] Screenshot refreshed successfully
[[08:59:40]] [INFO] cokvFXhj4c=running
[[08:59:40]] [INFO] Executing action 461/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[08:59:40]] [SUCCESS] Screenshot refreshed
[[08:59:40]] [INFO] Refreshing screenshot...
[[08:59:40]] [INFO] oSQ8sPdVOJ=pass
[[08:59:35]] [INFO] oSQ8sPdVOJ=running
[[08:59:35]] [INFO] Executing action 460/576: Restart app: env[appid]
[[08:59:34]] [SUCCESS] Screenshot refreshed successfully
[[08:59:34]] [SUCCESS] Screenshot refreshed successfully
[[08:59:34]] [SUCCESS] Screenshot refreshed
[[08:59:34]] [INFO] Refreshing screenshot...
[[08:59:34]] [INFO] V42eHtTRYW=pass
[[08:59:28]] [INFO] V42eHtTRYW=running
[[08:59:28]] [INFO] Executing action 459/576: Wait for 5 ms
[[08:59:27]] [SUCCESS] Screenshot refreshed successfully
[[08:59:27]] [SUCCESS] Screenshot refreshed successfully
[[08:59:27]] [SUCCESS] Screenshot refreshed
[[08:59:27]] [INFO] Refreshing screenshot...
[[08:59:27]] [INFO] jUCAk6GJc4=pass
[[08:59:25]] [INFO] jUCAk6GJc4=running
[[08:59:25]] [INFO] Executing action 458/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[08:59:24]] [SUCCESS] Screenshot refreshed successfully
[[08:59:24]] [SUCCESS] Screenshot refreshed successfully
[[08:59:24]] [SUCCESS] Screenshot refreshed
[[08:59:24]] [INFO] Refreshing screenshot...
[[08:59:24]] [INFO] V42eHtTRYW=pass
[[08:59:17]] [INFO] V42eHtTRYW=running
[[08:59:17]] [INFO] Executing action 457/576: Wait for 5 ms
[[08:59:17]] [SUCCESS] Screenshot refreshed successfully
[[08:59:17]] [SUCCESS] Screenshot refreshed successfully
[[08:59:17]] [SUCCESS] Screenshot refreshed
[[08:59:17]] [INFO] Refreshing screenshot...
[[08:59:17]] [INFO] w1RV76df9x=pass
[[08:59:12]] [INFO] w1RV76df9x=running
[[08:59:12]] [INFO] Executing action 456/576: Tap on Text: "Wi-Fi"
[[08:59:12]] [SUCCESS] Screenshot refreshed successfully
[[08:59:12]] [SUCCESS] Screenshot refreshed successfully
[[08:59:12]] [SUCCESS] Screenshot refreshed
[[08:59:12]] [INFO] Refreshing screenshot...
[[08:59:12]] [INFO] LfyQctrEJn=pass
[[08:59:10]] [SUCCESS] Screenshot refreshed successfully
[[08:59:10]] [SUCCESS] Screenshot refreshed successfully
[[08:59:09]] [INFO] LfyQctrEJn=running
[[08:59:09]] [INFO] Executing action 455/576: Launch app: com.apple.Preferences
[[08:59:09]] [SUCCESS] Screenshot refreshed
[[08:59:09]] [INFO] Refreshing screenshot...
[[08:59:09]] [INFO] mIKA85kXaW=pass
[[08:59:07]] [SUCCESS] Screenshot refreshed successfully
[[08:59:07]] [SUCCESS] Screenshot refreshed successfully
[[08:59:06]] [INFO] mIKA85kXaW=running
[[08:59:06]] [INFO] Executing action 454/576: Terminate app: com.apple.Preferences
[[08:59:06]] [SUCCESS] Screenshot refreshed
[[08:59:06]] [INFO] Refreshing screenshot...
[[08:59:06]] [SUCCESS] Screenshot refreshed
[[08:59:06]] [INFO] Refreshing screenshot...
[[08:59:00]] [SUCCESS] Screenshot refreshed successfully
[[08:59:00]] [SUCCESS] Screenshot refreshed successfully
[[08:59:00]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:59:00]] [SUCCESS] Screenshot refreshed
[[08:59:00]] [INFO] Refreshing screenshot...
[[08:58:56]] [SUCCESS] Screenshot refreshed successfully
[[08:58:56]] [SUCCESS] Screenshot refreshed successfully
[[08:58:55]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:58:55]] [SUCCESS] Screenshot refreshed
[[08:58:55]] [INFO] Refreshing screenshot...
[[08:58:50]] [SUCCESS] Screenshot refreshed successfully
[[08:58:50]] [SUCCESS] Screenshot refreshed successfully
[[08:58:50]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:58:50]] [SUCCESS] Screenshot refreshed
[[08:58:50]] [INFO] Refreshing screenshot...
[[08:58:46]] [SUCCESS] Screenshot refreshed successfully
[[08:58:46]] [SUCCESS] Screenshot refreshed successfully
[[08:58:45]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:58:45]] [SUCCESS] Screenshot refreshed
[[08:58:45]] [INFO] Refreshing screenshot...
[[08:58:39]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:58:39]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:58:39]] [SUCCESS] Screenshot refreshed successfully
[[08:58:39]] [SUCCESS] Screenshot refreshed successfully
[[08:58:39]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:58:39]] [INFO] x6vffndoRV=running
[[08:58:39]] [INFO] Executing action 453/576: Execute Test Case: Kmart-Signin (6 steps)
[[08:58:39]] [SUCCESS] Screenshot refreshed
[[08:58:39]] [INFO] Refreshing screenshot...
[[08:58:39]] [INFO] rJ86z4njuR=pass
[[08:58:36]] [SUCCESS] Screenshot refreshed successfully
[[08:58:36]] [SUCCESS] Screenshot refreshed successfully
[[08:58:36]] [INFO] rJ86z4njuR=running
[[08:58:36]] [INFO] Executing action 452/576: iOS Function: alert_accept
[[08:58:35]] [SUCCESS] Screenshot refreshed
[[08:58:35]] [INFO] Refreshing screenshot...
[[08:58:35]] [INFO] veukWo4573=pass
[[08:58:31]] [SUCCESS] Screenshot refreshed successfully
[[08:58:31]] [SUCCESS] Screenshot refreshed successfully
[[08:58:30]] [INFO] veukWo4573=running
[[08:58:30]] [INFO] Executing action 451/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[08:58:30]] [SUCCESS] Screenshot refreshed
[[08:58:30]] [INFO] Refreshing screenshot...
[[08:58:30]] [INFO] XEbZHdi0GT=pass
[[08:58:17]] [SUCCESS] Screenshot refreshed successfully
[[08:58:17]] [SUCCESS] Screenshot refreshed successfully
[[08:58:15]] [INFO] XEbZHdi0GT=running
[[08:58:15]] [INFO] Executing action 450/576: Restart app: env[appid]
[[08:58:15]] [SUCCESS] Screenshot refreshed
[[08:58:15]] [INFO] Refreshing screenshot...
[[08:58:15]] [SUCCESS] Screenshot refreshed
[[08:58:15]] [INFO] Refreshing screenshot...
[[08:58:12]] [SUCCESS] Screenshot refreshed successfully
[[08:58:12]] [SUCCESS] Screenshot refreshed successfully
[[08:58:12]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:58:11]] [SUCCESS] Screenshot refreshed
[[08:58:11]] [INFO] Refreshing screenshot...
[[08:57:59]] [SUCCESS] Screenshot refreshed successfully
[[08:57:59]] [SUCCESS] Screenshot refreshed successfully
[[08:57:59]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:57:59]] [SUCCESS] Screenshot refreshed
[[08:57:59]] [INFO] Refreshing screenshot...
[[08:57:55]] [SUCCESS] Screenshot refreshed successfully
[[08:57:55]] [SUCCESS] Screenshot refreshed successfully
[[08:57:55]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:57:54]] [SUCCESS] Screenshot refreshed
[[08:57:54]] [INFO] Refreshing screenshot...
[[08:57:51]] [SUCCESS] Screenshot refreshed successfully
[[08:57:51]] [SUCCESS] Screenshot refreshed successfully
[[08:57:50]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:57:50]] [SUCCESS] Screenshot refreshed
[[08:57:50]] [INFO] Refreshing screenshot...
[[08:57:43]] [SUCCESS] Screenshot refreshed successfully
[[08:57:43]] [SUCCESS] Screenshot refreshed successfully
[[08:57:43]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:57:42]] [SUCCESS] Screenshot refreshed
[[08:57:42]] [INFO] Refreshing screenshot...
[[08:57:36]] [SUCCESS] Screenshot refreshed successfully
[[08:57:36]] [SUCCESS] Screenshot refreshed successfully
[[08:57:35]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:57:35]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:57:35]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:57:35]] [INFO] ubySifeF65=running
[[08:57:35]] [INFO] Executing action 449/576: cleanupSteps action
[[08:57:34]] [SUCCESS] Screenshot refreshed
[[08:57:34]] [INFO] Refreshing screenshot...
[[08:57:34]] [INFO] xyHVihJMBi=pass
[[08:57:29]] [SUCCESS] Screenshot refreshed successfully
[[08:57:29]] [SUCCESS] Screenshot refreshed successfully
[[08:57:29]] [INFO] xyHVihJMBi=running
[[08:57:29]] [INFO] Executing action 448/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:57:29]] [SUCCESS] Screenshot refreshed
[[08:57:29]] [INFO] Refreshing screenshot...
[[08:57:29]] [INFO] mWeLQtXiL6=pass
[[08:57:22]] [SUCCESS] Screenshot refreshed successfully
[[08:57:22]] [SUCCESS] Screenshot refreshed successfully
[[08:57:22]] [INFO] mWeLQtXiL6=running
[[08:57:22]] [INFO] Executing action 447/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:57:21]] [SUCCESS] Screenshot refreshed
[[08:57:21]] [INFO] Refreshing screenshot...
[[08:57:21]] [INFO] F4NGh9HrLw=pass
[[08:57:17]] [SUCCESS] Screenshot refreshed successfully
[[08:57:17]] [SUCCESS] Screenshot refreshed successfully
[[08:57:17]] [INFO] F4NGh9HrLw=running
[[08:57:17]] [INFO] Executing action 446/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:57:16]] [SUCCESS] Screenshot refreshed
[[08:57:16]] [INFO] Refreshing screenshot...
[[08:57:16]] [INFO] 0f2FSZYjWq=pass
[[08:56:59]] [SUCCESS] Screenshot refreshed successfully
[[08:56:59]] [SUCCESS] Screenshot refreshed successfully
[[08:56:59]] [INFO] 0f2FSZYjWq=running
[[08:56:59]] [INFO] Executing action 445/576: Check if element with text="Melbourne" exists
[[08:56:58]] [SUCCESS] Screenshot refreshed
[[08:56:58]] [INFO] Refreshing screenshot...
[[08:56:58]] [INFO] Tebej51pT2=pass
[[08:56:54]] [SUCCESS] Screenshot refreshed successfully
[[08:56:54]] [SUCCESS] Screenshot refreshed successfully
[[08:56:54]] [INFO] Tebej51pT2=running
[[08:56:54]] [INFO] Executing action 444/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[08:56:53]] [SUCCESS] Screenshot refreshed
[[08:56:53]] [INFO] Refreshing screenshot...
[[08:56:53]] [INFO] I4gwigwXSj=pass
[[08:56:50]] [SUCCESS] Screenshot refreshed successfully
[[08:56:50]] [SUCCESS] Screenshot refreshed successfully
[[08:56:50]] [INFO] I4gwigwXSj=running
[[08:56:50]] [INFO] Executing action 443/576: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[08:56:49]] [SUCCESS] Screenshot refreshed
[[08:56:49]] [INFO] Refreshing screenshot...
[[08:56:49]] [INFO] eVytJrry9x=pass
[[08:56:45]] [SUCCESS] Screenshot refreshed successfully
[[08:56:45]] [SUCCESS] Screenshot refreshed successfully
[[08:56:45]] [INFO] eVytJrry9x=running
[[08:56:45]] [INFO] Executing action 442/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:56:44]] [SUCCESS] Screenshot refreshed
[[08:56:44]] [INFO] Refreshing screenshot...
[[08:56:44]] [INFO] s8h8VDUIOC=pass
[[08:56:40]] [SUCCESS] Screenshot refreshed successfully
[[08:56:40]] [SUCCESS] Screenshot refreshed successfully
[[08:56:40]] [INFO] s8h8VDUIOC=running
[[08:56:40]] [INFO] Executing action 441/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:56:39]] [SUCCESS] Screenshot refreshed
[[08:56:39]] [INFO] Refreshing screenshot...
[[08:56:39]] [INFO] bkU728TrRF=pass
[[08:56:33]] [SUCCESS] Screenshot refreshed successfully
[[08:56:33]] [SUCCESS] Screenshot refreshed successfully
[[08:56:33]] [INFO] bkU728TrRF=running
[[08:56:33]] [INFO] Executing action 440/576: Tap on element with accessibility_id: Done
[[08:56:32]] [SUCCESS] Screenshot refreshed
[[08:56:32]] [INFO] Refreshing screenshot...
[[08:56:32]] [INFO] ZWpYNcpbFA=pass
[[08:56:27]] [SUCCESS] Screenshot refreshed successfully
[[08:56:27]] [SUCCESS] Screenshot refreshed successfully
[[08:56:27]] [INFO] ZWpYNcpbFA=running
[[08:56:27]] [INFO] Executing action 439/576: Tap on Text: "VIC"
[[08:56:27]] [SUCCESS] Screenshot refreshed
[[08:56:27]] [INFO] Refreshing screenshot...
[[08:56:27]] [INFO] Wld5Urg70o=pass
[[08:56:20]] [SUCCESS] Screenshot refreshed successfully
[[08:56:20]] [SUCCESS] Screenshot refreshed successfully
[[08:56:19]] [INFO] Wld5Urg70o=running
[[08:56:19]] [INFO] Executing action 438/576: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[08:56:19]] [SUCCESS] Screenshot refreshed
[[08:56:19]] [INFO] Refreshing screenshot...
[[08:56:19]] [INFO] QpBLC6BStn=pass
[[08:56:13]] [SUCCESS] Screenshot refreshed successfully
[[08:56:13]] [SUCCESS] Screenshot refreshed successfully
[[08:56:12]] [INFO] QpBLC6BStn=running
[[08:56:12]] [INFO] Executing action 437/576: Tap on element with accessibility_id: delete
[[08:56:12]] [SUCCESS] Screenshot refreshed
[[08:56:12]] [INFO] Refreshing screenshot...
[[08:56:12]] [INFO] G4A3KBlXHq=pass
[[08:56:07]] [SUCCESS] Screenshot refreshed successfully
[[08:56:07]] [SUCCESS] Screenshot refreshed successfully
[[08:56:07]] [INFO] G4A3KBlXHq=running
[[08:56:07]] [INFO] Executing action 436/576: Tap on Text: "Nearby"
[[08:56:06]] [SUCCESS] Screenshot refreshed
[[08:56:06]] [INFO] Refreshing screenshot...
[[08:56:06]] [INFO] uArzgeZYf7=pass
[[08:56:03]] [SUCCESS] Screenshot refreshed successfully
[[08:56:03]] [SUCCESS] Screenshot refreshed successfully
[[08:56:03]] [INFO] uArzgeZYf7=running
[[08:56:03]] [INFO] Executing action 435/576: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[08:56:02]] [SUCCESS] Screenshot refreshed
[[08:56:02]] [INFO] Refreshing screenshot...
[[08:56:02]] [INFO] 3gJsiap2Ds=pass
[[08:55:58]] [SUCCESS] Screenshot refreshed successfully
[[08:55:58]] [SUCCESS] Screenshot refreshed successfully
[[08:55:58]] [INFO] 3gJsiap2Ds=running
[[08:55:58]] [INFO] Executing action 434/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[08:55:57]] [SUCCESS] Screenshot refreshed
[[08:55:57]] [INFO] Refreshing screenshot...
[[08:55:57]] [INFO] dF3hpprg71=pass
[[08:55:55]] [SUCCESS] Screenshot refreshed successfully
[[08:55:55]] [SUCCESS] Screenshot refreshed successfully
[[08:55:54]] [INFO] dF3hpprg71=running
[[08:55:54]] [INFO] Executing action 433/576: Wait till xpath=//XCUIElementTypeOther[@name="Delivery options"]/XCUIElementTypeButton[3]
[[08:55:53]] [SUCCESS] Screenshot refreshed
[[08:55:53]] [INFO] Refreshing screenshot...
[[08:55:53]] [INFO] 94ikwhIEE2=pass
[[08:55:49]] [SUCCESS] Screenshot refreshed successfully
[[08:55:49]] [SUCCESS] Screenshot refreshed successfully
[[08:55:49]] [INFO] 94ikwhIEE2=running
[[08:55:49]] [INFO] Executing action 432/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:55:48]] [SUCCESS] Screenshot refreshed
[[08:55:48]] [INFO] Refreshing screenshot...
[[08:55:48]] [INFO] q8oldD8uZt=pass
[[08:55:45]] [SUCCESS] Screenshot refreshed successfully
[[08:55:45]] [SUCCESS] Screenshot refreshed successfully
[[08:55:44]] [INFO] q8oldD8uZt=running
[[08:55:44]] [INFO] Executing action 431/576: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[08:55:44]] [SUCCESS] Screenshot refreshed
[[08:55:44]] [INFO] Refreshing screenshot...
[[08:55:44]] [INFO] Jf2wJyOphY=pass
[[08:55:37]] [SUCCESS] Screenshot refreshed successfully
[[08:55:37]] [SUCCESS] Screenshot refreshed successfully
[[08:55:36]] [INFO] Jf2wJyOphY=running
[[08:55:36]] [INFO] Executing action 430/576: Tap on element with accessibility_id: Add to bag
[[08:55:36]] [SUCCESS] Screenshot refreshed
[[08:55:36]] [INFO] Refreshing screenshot...
[[08:55:36]] [INFO] eRCmRhc3re=pass
[[08:55:21]] [INFO] eRCmRhc3re=running
[[08:55:21]] [INFO] Executing action 429/576: Check if element with text="Broadway" exists
[[08:55:21]] [SUCCESS] Screenshot refreshed successfully
[[08:55:21]] [SUCCESS] Screenshot refreshed successfully
[[08:55:20]] [SUCCESS] Screenshot refreshed
[[08:55:20]] [INFO] Refreshing screenshot...
[[08:55:20]] [INFO] ORI6ZFMBK1=pass
[[08:55:15]] [SUCCESS] Screenshot refreshed successfully
[[08:55:15]] [SUCCESS] Screenshot refreshed successfully
[[08:55:15]] [INFO] ORI6ZFMBK1=running
[[08:55:15]] [INFO] Executing action 428/576: Tap on Text: "Save"
[[08:55:15]] [SUCCESS] Screenshot refreshed
[[08:55:15]] [INFO] Refreshing screenshot...
[[08:55:15]] [INFO] hr0IVckpYI=pass
[[08:55:10]] [SUCCESS] Screenshot refreshed successfully
[[08:55:10]] [SUCCESS] Screenshot refreshed successfully
[[08:55:10]] [INFO] hr0IVckpYI=running
[[08:55:10]] [INFO] Executing action 427/576: Wait till accessibility_id=btnSaveOrContinue
[[08:55:10]] [SUCCESS] Screenshot refreshed
[[08:55:10]] [INFO] Refreshing screenshot...
[[08:55:10]] [INFO] H0ODFz7sWJ=pass
[[08:55:05]] [SUCCESS] Screenshot refreshed successfully
[[08:55:05]] [SUCCESS] Screenshot refreshed successfully
[[08:55:05]] [INFO] H0ODFz7sWJ=running
[[08:55:05]] [INFO] Executing action 426/576: Tap on Text: "2000"
[[08:55:04]] [SUCCESS] Screenshot refreshed
[[08:55:04]] [INFO] Refreshing screenshot...
[[08:55:04]] [INFO] uZHvvAzVfx=pass
[[08:54:59]] [SUCCESS] Screenshot refreshed successfully
[[08:54:59]] [SUCCESS] Screenshot refreshed successfully
[[08:54:59]] [INFO] uZHvvAzVfx=running
[[08:54:59]] [INFO] Executing action 425/576: textClear action
[[08:54:58]] [SUCCESS] Screenshot refreshed
[[08:54:58]] [INFO] Refreshing screenshot...
[[08:54:58]] [INFO] WmNWcsWVHv=pass
[[08:54:52]] [SUCCESS] Screenshot refreshed successfully
[[08:54:52]] [SUCCESS] Screenshot refreshed successfully
[[08:54:52]] [INFO] WmNWcsWVHv=running
[[08:54:52]] [INFO] Executing action 424/576: Tap on element with accessibility_id: Search suburb or postcode
[[08:54:51]] [SUCCESS] Screenshot refreshed
[[08:54:51]] [INFO] Refreshing screenshot...
[[08:54:51]] [INFO] lnjoz8hHUU=pass
[[08:54:46]] [SUCCESS] Screenshot refreshed successfully
[[08:54:46]] [SUCCESS] Screenshot refreshed successfully
[[08:54:46]] [INFO] lnjoz8hHUU=running
[[08:54:46]] [INFO] Executing action 423/576: Tap on Text: "Edit"
[[08:54:45]] [SUCCESS] Screenshot refreshed
[[08:54:45]] [INFO] Refreshing screenshot...
[[08:54:45]] [INFO] letbbewlnA=pass
[[08:54:41]] [SUCCESS] Screenshot refreshed successfully
[[08:54:41]] [SUCCESS] Screenshot refreshed successfully
[[08:54:41]] [INFO] letbbewlnA=running
[[08:54:41]] [INFO] Executing action 422/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:54:40]] [SUCCESS] Screenshot refreshed
[[08:54:40]] [INFO] Refreshing screenshot...
[[08:54:40]] [INFO] trBISwJ8eZ=pass
[[08:54:36]] [SUCCESS] Screenshot refreshed successfully
[[08:54:36]] [SUCCESS] Screenshot refreshed successfully
[[08:54:36]] [INFO] trBISwJ8eZ=running
[[08:54:36]] [INFO] Executing action 421/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:54:35]] [SUCCESS] Screenshot refreshed
[[08:54:35]] [INFO] Refreshing screenshot...
[[08:54:35]] [INFO] foVGMl9wvu=pass
[[08:54:33]] [SUCCESS] Screenshot refreshed successfully
[[08:54:33]] [SUCCESS] Screenshot refreshed successfully
[[08:54:31]] [INFO] foVGMl9wvu=running
[[08:54:31]] [INFO] Executing action 420/576: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:54:30]] [SUCCESS] Screenshot refreshed
[[08:54:30]] [INFO] Refreshing screenshot...
[[08:54:30]] [INFO] 73NABkfWyY=pass
[[08:54:14]] [SUCCESS] Screenshot refreshed successfully
[[08:54:14]] [SUCCESS] Screenshot refreshed successfully
[[08:54:14]] [INFO] 73NABkfWyY=running
[[08:54:14]] [INFO] Executing action 419/576: Check if element with text="Sanctuary" exists
[[08:54:14]] [SUCCESS] Screenshot refreshed
[[08:54:14]] [INFO] Refreshing screenshot...
[[08:54:14]] [INFO] pKjXoj4mNg=pass
[[08:54:08]] [SUCCESS] Screenshot refreshed successfully
[[08:54:08]] [SUCCESS] Screenshot refreshed successfully
[[08:54:08]] [INFO] pKjXoj4mNg=running
[[08:54:08]] [INFO] Executing action 418/576: Tap on Text: "Save"
[[08:54:08]] [SUCCESS] Screenshot refreshed
[[08:54:08]] [INFO] Refreshing screenshot...
[[08:54:08]] [INFO] M3dXqigqRv=pass
[[08:54:03]] [SUCCESS] Screenshot refreshed successfully
[[08:54:03]] [SUCCESS] Screenshot refreshed successfully
[[08:54:03]] [INFO] M3dXqigqRv=running
[[08:54:03]] [INFO] Executing action 417/576: Wait till accessibility_id=btnSaveOrContinue
[[08:54:02]] [SUCCESS] Screenshot refreshed
[[08:54:02]] [INFO] Refreshing screenshot...
[[08:54:02]] [INFO] GYRHQr7TWx=pass
[[08:53:58]] [SUCCESS] Screenshot refreshed successfully
[[08:53:58]] [SUCCESS] Screenshot refreshed successfully
[[08:53:58]] [INFO] GYRHQr7TWx=running
[[08:53:58]] [INFO] Executing action 416/576: Tap on Text: "current"
[[08:53:57]] [SUCCESS] Screenshot refreshed
[[08:53:57]] [INFO] Refreshing screenshot...
[[08:53:57]] [INFO] kiM0WyWE9I=pass
[[08:53:52]] [SUCCESS] Screenshot refreshed successfully
[[08:53:52]] [SUCCESS] Screenshot refreshed successfully
[[08:53:52]] [INFO] kiM0WyWE9I=running
[[08:53:52]] [INFO] Executing action 415/576: Wait till accessibility_id=btnCurrentLocationButton
[[08:53:51]] [SUCCESS] Screenshot refreshed
[[08:53:51]] [INFO] Refreshing screenshot...
[[08:53:51]] [INFO] VkUKQbf1Qt=pass
[[08:53:46]] [SUCCESS] Screenshot refreshed successfully
[[08:53:46]] [SUCCESS] Screenshot refreshed successfully
[[08:53:46]] [INFO] VkUKQbf1Qt=running
[[08:53:46]] [INFO] Executing action 414/576: Tap on Text: "Edit"
[[08:53:45]] [SUCCESS] Screenshot refreshed
[[08:53:45]] [INFO] Refreshing screenshot...
[[08:53:45]] [INFO] C6JHhLdWTv=pass
[[08:53:42]] [SUCCESS] Screenshot refreshed successfully
[[08:53:42]] [SUCCESS] Screenshot refreshed successfully
[[08:53:41]] [INFO] C6JHhLdWTv=running
[[08:53:41]] [INFO] Executing action 413/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:53:41]] [SUCCESS] Screenshot refreshed
[[08:53:41]] [INFO] Refreshing screenshot...
[[08:53:41]] [INFO] IupxLP2Jsr=pass
[[08:53:36]] [SUCCESS] Screenshot refreshed successfully
[[08:53:36]] [SUCCESS] Screenshot refreshed successfully
[[08:53:36]] [INFO] IupxLP2Jsr=running
[[08:53:36]] [INFO] Executing action 412/576: iOS Function: text - Text: "Uno card"
[[08:53:36]] [SUCCESS] Screenshot refreshed
[[08:53:36]] [INFO] Refreshing screenshot...
[[08:53:36]] [INFO] 70iOOakiG7=pass
[[08:53:30]] [SUCCESS] Screenshot refreshed successfully
[[08:53:30]] [SUCCESS] Screenshot refreshed successfully
[[08:53:30]] [INFO] 70iOOakiG7=running
[[08:53:30]] [INFO] Executing action 411/576: Tap on Text: "Find"
[[08:53:29]] [SUCCESS] Screenshot refreshed
[[08:53:29]] [INFO] Refreshing screenshot...
[[08:53:29]] [INFO] Xqj9EIVEfg=pass
[[08:53:21]] [SUCCESS] Screenshot refreshed successfully
[[08:53:21]] [SUCCESS] Screenshot refreshed successfully
[[08:53:21]] [INFO] Xqj9EIVEfg=running
[[08:53:21]] [INFO] Executing action 410/576: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[08:53:20]] [SUCCESS] Screenshot refreshed
[[08:53:20]] [INFO] Refreshing screenshot...
[[08:53:20]] [INFO] E2jpN7BioW=pass
[[08:53:16]] [SUCCESS] Screenshot refreshed successfully
[[08:53:16]] [SUCCESS] Screenshot refreshed successfully
[[08:53:15]] [INFO] E2jpN7BioW=running
[[08:53:15]] [INFO] Executing action 409/576: Tap on Text: "Save"
[[08:53:15]] [SUCCESS] Screenshot refreshed
[[08:53:15]] [INFO] Refreshing screenshot...
[[08:53:15]] [INFO] Sl6eiqZkRm=pass
[[08:53:10]] [SUCCESS] Screenshot refreshed successfully
[[08:53:10]] [SUCCESS] Screenshot refreshed successfully
[[08:53:10]] [INFO] Sl6eiqZkRm=running
[[08:53:10]] [INFO] Executing action 408/576: Wait till accessibility_id=btnSaveOrContinue
[[08:53:10]] [SUCCESS] Screenshot refreshed
[[08:53:10]] [INFO] Refreshing screenshot...
[[08:53:10]] [INFO] mw9GQ4mzRE=pass
[[08:53:05]] [SUCCESS] Screenshot refreshed successfully
[[08:53:05]] [SUCCESS] Screenshot refreshed successfully
[[08:53:05]] [INFO] mw9GQ4mzRE=running
[[08:53:05]] [INFO] Executing action 407/576: Tap on Text: "2000"
[[08:53:04]] [SUCCESS] Screenshot refreshed
[[08:53:04]] [INFO] Refreshing screenshot...
[[08:53:04]] [INFO] kbdEPCPYod=pass
[[08:52:59]] [SUCCESS] Screenshot refreshed successfully
[[08:52:59]] [SUCCESS] Screenshot refreshed successfully
[[08:52:59]] [INFO] kbdEPCPYod=running
[[08:52:59]] [INFO] Executing action 406/576: textClear action
[[08:52:58]] [SUCCESS] Screenshot refreshed
[[08:52:58]] [INFO] Refreshing screenshot...
[[08:52:58]] [INFO] 8WCusTZ8q9=pass
[[08:52:52]] [SUCCESS] Screenshot refreshed successfully
[[08:52:52]] [SUCCESS] Screenshot refreshed successfully
[[08:52:52]] [INFO] 8WCusTZ8q9=running
[[08:52:52]] [INFO] Executing action 405/576: Tap on element with accessibility_id: Search suburb or postcode
[[08:52:51]] [SUCCESS] Screenshot refreshed
[[08:52:51]] [INFO] Refreshing screenshot...
[[08:52:51]] [INFO] QMXBlswP6H=pass
[[08:52:48]] [SUCCESS] Screenshot refreshed successfully
[[08:52:48]] [SUCCESS] Screenshot refreshed successfully
[[08:52:47]] [INFO] QMXBlswP6H=running
[[08:52:47]] [INFO] Executing action 404/576: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[08:52:47]] [SUCCESS] Screenshot refreshed
[[08:52:47]] [INFO] Refreshing screenshot...
[[08:52:47]] [INFO] m0956RsrdM=pass
[[08:52:45]] [SUCCESS] Screenshot refreshed successfully
[[08:52:45]] [SUCCESS] Screenshot refreshed successfully
[[08:52:43]] [INFO] m0956RsrdM=running
[[08:52:43]] [INFO] Executing action 403/576: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[08:52:43]] [SUCCESS] Screenshot refreshed
[[08:52:43]] [INFO] Refreshing screenshot...
[[08:52:42]] [SUCCESS] Screenshot refreshed
[[08:52:42]] [INFO] Refreshing screenshot...
[[08:52:38]] [SUCCESS] Screenshot refreshed successfully
[[08:52:38]] [SUCCESS] Screenshot refreshed successfully
[[08:52:37]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:52:37]] [SUCCESS] Screenshot refreshed
[[08:52:37]] [INFO] Refreshing screenshot...
[[08:52:33]] [SUCCESS] Screenshot refreshed successfully
[[08:52:33]] [SUCCESS] Screenshot refreshed successfully
[[08:52:33]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:52:32]] [SUCCESS] Screenshot refreshed
[[08:52:32]] [INFO] Refreshing screenshot...
[[08:52:28]] [SUCCESS] Screenshot refreshed successfully
[[08:52:28]] [SUCCESS] Screenshot refreshed successfully
[[08:52:27]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:52:27]] [SUCCESS] Screenshot refreshed
[[08:52:27]] [INFO] Refreshing screenshot...
[[08:52:22]] [SUCCESS] Screenshot refreshed successfully
[[08:52:22]] [SUCCESS] Screenshot refreshed successfully
[[08:52:22]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:52:22]] [SUCCESS] Screenshot refreshed
[[08:52:22]] [INFO] Refreshing screenshot...
[[08:52:16]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:52:16]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:52:16]] [SUCCESS] Screenshot refreshed successfully
[[08:52:16]] [SUCCESS] Screenshot refreshed successfully
[[08:52:16]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:52:16]] [INFO] C3UHsKxa5P=running
[[08:52:16]] [INFO] Executing action 402/576: Execute Test Case: Kmart-Signin (6 steps)
[[08:52:15]] [SUCCESS] Screenshot refreshed
[[08:52:15]] [INFO] Refreshing screenshot...
[[08:52:15]] [INFO] Azb1flbIJJ=pass
[[08:52:11]] [SUCCESS] Screenshot refreshed successfully
[[08:52:11]] [SUCCESS] Screenshot refreshed successfully
[[08:52:11]] [INFO] Azb1flbIJJ=running
[[08:52:11]] [INFO] Executing action 401/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:52:11]] [SUCCESS] Screenshot refreshed
[[08:52:11]] [INFO] Refreshing screenshot...
[[08:52:11]] [INFO] 2xC5fLfLe8=pass
[[08:52:08]] [SUCCESS] Screenshot refreshed successfully
[[08:52:08]] [SUCCESS] Screenshot refreshed successfully
[[08:52:08]] [INFO] 2xC5fLfLe8=running
[[08:52:08]] [INFO] Executing action 400/576: iOS Function: alert_accept
[[08:52:07]] [SUCCESS] Screenshot refreshed
[[08:52:07]] [INFO] Refreshing screenshot...
[[08:52:07]] [INFO] Y8vz7AJD1i=pass
[[08:52:01]] [SUCCESS] Screenshot refreshed successfully
[[08:52:01]] [SUCCESS] Screenshot refreshed successfully
[[08:52:00]] [INFO] Y8vz7AJD1i=running
[[08:52:00]] [INFO] Executing action 399/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:51:59]] [SUCCESS] Screenshot refreshed
[[08:51:59]] [INFO] Refreshing screenshot...
[[08:51:59]] [INFO] H9fy9qcFbZ=pass
[[08:51:46]] [SUCCESS] Screenshot refreshed successfully
[[08:51:46]] [SUCCESS] Screenshot refreshed successfully
[[08:51:45]] [INFO] H9fy9qcFbZ=running
[[08:51:45]] [INFO] Executing action 398/576: Restart app: env[appid]
[[08:51:45]] [SUCCESS] Screenshot refreshed
[[08:51:45]] [INFO] Refreshing screenshot...
[[08:51:45]] [SUCCESS] Screenshot refreshed
[[08:51:45]] [INFO] Refreshing screenshot...
[[08:51:42]] [SUCCESS] Screenshot refreshed successfully
[[08:51:42]] [SUCCESS] Screenshot refreshed successfully
[[08:51:41]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:51:41]] [SUCCESS] Screenshot refreshed
[[08:51:41]] [INFO] Refreshing screenshot...
[[08:51:28]] [SUCCESS] Screenshot refreshed successfully
[[08:51:28]] [SUCCESS] Screenshot refreshed successfully
[[08:51:28]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:51:28]] [SUCCESS] Screenshot refreshed
[[08:51:28]] [INFO] Refreshing screenshot...
[[08:51:24]] [SUCCESS] Screenshot refreshed successfully
[[08:51:24]] [SUCCESS] Screenshot refreshed successfully
[[08:51:24]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:51:24]] [SUCCESS] Screenshot refreshed
[[08:51:24]] [INFO] Refreshing screenshot...
[[08:51:20]] [SUCCESS] Screenshot refreshed successfully
[[08:51:20]] [SUCCESS] Screenshot refreshed successfully
[[08:51:20]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:51:19]] [SUCCESS] Screenshot refreshed
[[08:51:19]] [INFO] Refreshing screenshot...
[[08:51:13]] [SUCCESS] Screenshot refreshed successfully
[[08:51:13]] [SUCCESS] Screenshot refreshed successfully
[[08:51:12]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:51:12]] [SUCCESS] Screenshot refreshed
[[08:51:12]] [INFO] Refreshing screenshot...
[[08:51:06]] [SUCCESS] Screenshot refreshed successfully
[[08:51:06]] [SUCCESS] Screenshot refreshed successfully
[[08:51:05]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:51:05]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:51:05]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:51:05]] [INFO] OMgc2gHHyq=running
[[08:51:05]] [INFO] Executing action 397/576: cleanupSteps action
[[08:51:05]] [SUCCESS] Screenshot refreshed
[[08:51:05]] [INFO] Refreshing screenshot...
[[08:51:05]] [INFO] x4yLCZHaCR=pass
[[08:51:02]] [SUCCESS] Screenshot refreshed successfully
[[08:51:02]] [SUCCESS] Screenshot refreshed successfully
[[08:51:01]] [INFO] x4yLCZHaCR=running
[[08:51:01]] [INFO] Executing action 396/576: Terminate app: env[appid]
[[08:51:01]] [SUCCESS] Screenshot refreshed
[[08:51:01]] [INFO] Refreshing screenshot...
[[08:51:01]] [INFO] 2p13JoJbbA=pass
[[08:50:56]] [SUCCESS] Screenshot refreshed successfully
[[08:50:56]] [SUCCESS] Screenshot refreshed successfully
[[08:50:56]] [INFO] 2p13JoJbbA=running
[[08:50:56]] [INFO] Executing action 395/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:50:56]] [SUCCESS] Screenshot refreshed
[[08:50:56]] [INFO] Refreshing screenshot...
[[08:50:56]] [INFO] qHdMgerbTE=pass
[[08:50:52]] [SUCCESS] Screenshot refreshed successfully
[[08:50:52]] [SUCCESS] Screenshot refreshed successfully
[[08:50:52]] [INFO] qHdMgerbTE=running
[[08:50:52]] [INFO] Executing action 394/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:50:51]] [SUCCESS] Screenshot refreshed
[[08:50:51]] [INFO] Refreshing screenshot...
[[08:50:51]] [INFO] F4NGh9HrLw=pass
[[08:50:49]] [SUCCESS] Screenshot refreshed successfully
[[08:50:49]] [SUCCESS] Screenshot refreshed successfully
[[08:50:46]] [INFO] F4NGh9HrLw=running
[[08:50:46]] [INFO] Executing action 393/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:50:46]] [SUCCESS] Screenshot refreshed
[[08:50:46]] [INFO] Refreshing screenshot...
[[08:50:46]] [SUCCESS] Screenshot refreshed
[[08:50:46]] [INFO] Refreshing screenshot...
[[08:50:42]] [SUCCESS] Screenshot refreshed successfully
[[08:50:42]] [SUCCESS] Screenshot refreshed successfully
[[08:50:42]] [INFO] Executing Multi Step action step 41/41: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[08:50:41]] [SUCCESS] Screenshot refreshed
[[08:50:41]] [INFO] Refreshing screenshot...
[[08:50:37]] [SUCCESS] Screenshot refreshed successfully
[[08:50:37]] [SUCCESS] Screenshot refreshed successfully
[[08:50:37]] [INFO] Executing Multi Step action step 40/41: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:50:37]] [SUCCESS] Screenshot refreshed
[[08:50:37]] [INFO] Refreshing screenshot...
[[08:50:30]] [SUCCESS] Screenshot refreshed successfully
[[08:50:30]] [SUCCESS] Screenshot refreshed successfully
[[08:50:29]] [INFO] Executing Multi Step action step 39/41: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[08:50:29]] [SUCCESS] Screenshot refreshed
[[08:50:29]] [INFO] Refreshing screenshot...
[[08:50:25]] [SUCCESS] Screenshot refreshed successfully
[[08:50:25]] [SUCCESS] Screenshot refreshed successfully
[[08:50:25]] [INFO] Executing Multi Step action step 38/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:50:25]] [SUCCESS] Screenshot refreshed
[[08:50:25]] [INFO] Refreshing screenshot...
[[08:50:21]] [SUCCESS] Screenshot refreshed successfully
[[08:50:21]] [SUCCESS] Screenshot refreshed successfully
[[08:50:20]] [INFO] Executing Multi Step action step 37/41: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:50:20]] [SUCCESS] Screenshot refreshed
[[08:50:20]] [INFO] Refreshing screenshot...
[[08:50:15]] [SUCCESS] Screenshot refreshed successfully
[[08:50:15]] [SUCCESS] Screenshot refreshed successfully
[[08:50:15]] [INFO] Executing Multi Step action step 36/41: Tap on image: banner-close-updated.png
[[08:50:15]] [SUCCESS] Screenshot refreshed
[[08:50:15]] [INFO] Refreshing screenshot...
[[08:50:12]] [INFO] Executing Multi Step action step 35/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[08:50:12]] [SUCCESS] Screenshot refreshed successfully
[[08:50:12]] [SUCCESS] Screenshot refreshed successfully
[[08:50:11]] [SUCCESS] Screenshot refreshed
[[08:50:11]] [INFO] Refreshing screenshot...
[[08:50:07]] [SUCCESS] Screenshot refreshed successfully
[[08:50:07]] [SUCCESS] Screenshot refreshed successfully
[[08:50:07]] [INFO] Executing Multi Step action step 34/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[08:50:07]] [SUCCESS] Screenshot refreshed
[[08:50:07]] [INFO] Refreshing screenshot...
[[08:50:03]] [SUCCESS] Screenshot refreshed successfully
[[08:50:03]] [SUCCESS] Screenshot refreshed successfully
[[08:50:02]] [INFO] Executing Multi Step action step 33/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[08:50:02]] [SUCCESS] Screenshot refreshed
[[08:50:02]] [INFO] Refreshing screenshot...
[[08:49:57]] [SUCCESS] Screenshot refreshed successfully
[[08:49:57]] [SUCCESS] Screenshot refreshed successfully
[[08:49:57]] [INFO] Executing Multi Step action step 32/41: Tap on image: banner-close-updated.png
[[08:49:57]] [SUCCESS] Screenshot refreshed
[[08:49:57]] [INFO] Refreshing screenshot...
[[08:49:51]] [INFO] Executing Multi Step action step 31/41: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[08:49:51]] [SUCCESS] Screenshot refreshed successfully
[[08:49:51]] [SUCCESS] Screenshot refreshed successfully
[[08:49:51]] [SUCCESS] Screenshot refreshed
[[08:49:51]] [INFO] Refreshing screenshot...
[[08:49:47]] [SUCCESS] Screenshot refreshed successfully
[[08:49:47]] [SUCCESS] Screenshot refreshed successfully
[[08:49:47]] [INFO] Executing Multi Step action step 30/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[08:49:46]] [SUCCESS] Screenshot refreshed
[[08:49:46]] [INFO] Refreshing screenshot...
[[08:49:42]] [SUCCESS] Screenshot refreshed successfully
[[08:49:42]] [SUCCESS] Screenshot refreshed successfully
[[08:49:42]] [INFO] Executing Multi Step action step 29/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[08:49:42]] [SUCCESS] Screenshot refreshed
[[08:49:42]] [INFO] Refreshing screenshot...
[[08:49:37]] [SUCCESS] Screenshot refreshed successfully
[[08:49:37]] [SUCCESS] Screenshot refreshed successfully
[[08:49:37]] [INFO] Executing Multi Step action step 28/41: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[08:49:37]] [SUCCESS] Screenshot refreshed
[[08:49:37]] [INFO] Refreshing screenshot...
[[08:49:34]] [SUCCESS] Screenshot refreshed successfully
[[08:49:34]] [SUCCESS] Screenshot refreshed successfully
[[08:49:33]] [INFO] Executing Multi Step action step 27/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[08:49:33]] [SUCCESS] Screenshot refreshed
[[08:49:33]] [INFO] Refreshing screenshot...
[[08:49:29]] [SUCCESS] Screenshot refreshed successfully
[[08:49:29]] [SUCCESS] Screenshot refreshed successfully
[[08:49:29]] [INFO] Executing Multi Step action step 26/41: Tap on element with xpath: //XCUIElementTypeLink[@name="Pay in 4"]
[[08:49:28]] [SUCCESS] Screenshot refreshed
[[08:49:28]] [INFO] Refreshing screenshot...
[[08:49:26]] [SUCCESS] Screenshot refreshed successfully
[[08:49:26]] [SUCCESS] Screenshot refreshed successfully
[[08:49:24]] [INFO] Executing Multi Step action step 25/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[08:49:23]] [SUCCESS] Screenshot refreshed
[[08:49:23]] [INFO] Refreshing screenshot...
[[08:49:19]] [SUCCESS] Screenshot refreshed successfully
[[08:49:19]] [SUCCESS] Screenshot refreshed successfully
[[08:49:19]] [INFO] Executing Multi Step action step 24/41: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[08:49:19]] [SUCCESS] Screenshot refreshed
[[08:49:19]] [INFO] Refreshing screenshot...
[[08:49:15]] [SUCCESS] Screenshot refreshed successfully
[[08:49:15]] [SUCCESS] Screenshot refreshed successfully
[[08:49:15]] [INFO] Executing Multi Step action step 23/41: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[08:49:15]] [SUCCESS] Screenshot refreshed
[[08:49:15]] [INFO] Refreshing screenshot...
[[08:49:11]] [SUCCESS] Screenshot refreshed successfully
[[08:49:11]] [SUCCESS] Screenshot refreshed successfully
[[08:49:11]] [INFO] Executing Multi Step action step 22/41: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[08:49:10]] [SUCCESS] Screenshot refreshed
[[08:49:10]] [INFO] Refreshing screenshot...
[[08:49:02]] [SUCCESS] Screenshot refreshed successfully
[[08:49:02]] [SUCCESS] Screenshot refreshed successfully
[[08:49:02]] [INFO] Executing Multi Step action step 21/41: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[08:49:02]] [SUCCESS] Screenshot refreshed
[[08:49:02]] [INFO] Refreshing screenshot...
[[08:48:57]] [SUCCESS] Screenshot refreshed successfully
[[08:48:57]] [SUCCESS] Screenshot refreshed successfully
[[08:48:57]] [INFO] Executing Multi Step action step 20/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[08:48:56]] [SUCCESS] Screenshot refreshed
[[08:48:56]] [INFO] Refreshing screenshot...
[[08:48:52]] [SUCCESS] Screenshot refreshed successfully
[[08:48:52]] [SUCCESS] Screenshot refreshed successfully
[[08:48:52]] [INFO] Executing Multi Step action step 19/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[08:48:51]] [SUCCESS] Screenshot refreshed
[[08:48:51]] [INFO] Refreshing screenshot...
[[08:48:43]] [SUCCESS] Screenshot refreshed successfully
[[08:48:43]] [SUCCESS] Screenshot refreshed successfully
[[08:48:43]] [INFO] Executing Multi Step action step 18/41: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[08:48:43]] [SUCCESS] Screenshot refreshed
[[08:48:43]] [INFO] Refreshing screenshot...
[[08:48:39]] [SUCCESS] Screenshot refreshed successfully
[[08:48:39]] [SUCCESS] Screenshot refreshed successfully
[[08:48:39]] [INFO] Executing Multi Step action step 17/41: Tap on image: env[delivery-address-img]
[[08:48:38]] [SUCCESS] Screenshot refreshed
[[08:48:38]] [INFO] Refreshing screenshot...
[[08:48:34]] [SUCCESS] Screenshot refreshed successfully
[[08:48:34]] [SUCCESS] Screenshot refreshed successfully
[[08:48:34]] [INFO] Executing Multi Step action step 16/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[08:48:33]] [SUCCESS] Screenshot refreshed
[[08:48:33]] [INFO] Refreshing screenshot...
[[08:48:26]] [SUCCESS] Screenshot refreshed successfully
[[08:48:26]] [SUCCESS] Screenshot refreshed successfully
[[08:48:26]] [INFO] Executing Multi Step action step 15/41: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "env[deliver-address]"
[[08:48:25]] [SUCCESS] Screenshot refreshed
[[08:48:25]] [INFO] Refreshing screenshot...
[[08:48:20]] [SUCCESS] Screenshot refreshed successfully
[[08:48:20]] [SUCCESS] Screenshot refreshed successfully
[[08:48:20]] [INFO] Executing Multi Step action step 14/41: Tap on Text: "address"
[[08:48:19]] [SUCCESS] Screenshot refreshed
[[08:48:19]] [INFO] Refreshing screenshot...
[[08:48:15]] [SUCCESS] Screenshot refreshed successfully
[[08:48:15]] [SUCCESS] Screenshot refreshed successfully
[[08:48:15]] [INFO] Executing Multi Step action step 13/41: iOS Function: text - Text: " "
[[08:48:15]] [SUCCESS] Screenshot refreshed
[[08:48:15]] [INFO] Refreshing screenshot...
[[08:48:08]] [SUCCESS] Screenshot refreshed successfully
[[08:48:08]] [SUCCESS] Screenshot refreshed successfully
[[08:48:08]] [INFO] Executing Multi Step action step 12/41: textClear action
[[08:48:07]] [SUCCESS] Screenshot refreshed
[[08:48:07]] [INFO] Refreshing screenshot...
[[08:48:03]] [SUCCESS] Screenshot refreshed successfully
[[08:48:03]] [SUCCESS] Screenshot refreshed successfully
[[08:48:03]] [INFO] Executing Multi Step action step 11/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[08:48:03]] [SUCCESS] Screenshot refreshed
[[08:48:03]] [INFO] Refreshing screenshot...
[[08:47:56]] [SUCCESS] Screenshot refreshed successfully
[[08:47:56]] [SUCCESS] Screenshot refreshed successfully
[[08:47:56]] [INFO] Executing Multi Step action step 10/41: textClear action
[[08:47:55]] [SUCCESS] Screenshot refreshed
[[08:47:55]] [INFO] Refreshing screenshot...
[[08:47:51]] [SUCCESS] Screenshot refreshed successfully
[[08:47:51]] [SUCCESS] Screenshot refreshed successfully
[[08:47:51]] [INFO] Executing Multi Step action step 9/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:47:51]] [SUCCESS] Screenshot refreshed
[[08:47:51]] [INFO] Refreshing screenshot...
[[08:47:44]] [SUCCESS] Screenshot refreshed successfully
[[08:47:44]] [SUCCESS] Screenshot refreshed successfully
[[08:47:44]] [INFO] Executing Multi Step action step 8/41: textClear action
[[08:47:44]] [SUCCESS] Screenshot refreshed
[[08:47:44]] [INFO] Refreshing screenshot...
[[08:47:40]] [SUCCESS] Screenshot refreshed successfully
[[08:47:40]] [SUCCESS] Screenshot refreshed successfully
[[08:47:40]] [INFO] Executing Multi Step action step 7/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[08:47:39]] [SUCCESS] Screenshot refreshed
[[08:47:39]] [INFO] Refreshing screenshot...
[[08:47:32]] [SUCCESS] Screenshot refreshed successfully
[[08:47:32]] [SUCCESS] Screenshot refreshed successfully
[[08:47:32]] [INFO] Executing Multi Step action step 6/41: textClear action
[[08:47:32]] [SUCCESS] Screenshot refreshed
[[08:47:32]] [INFO] Refreshing screenshot...
[[08:47:28]] [SUCCESS] Screenshot refreshed successfully
[[08:47:28]] [SUCCESS] Screenshot refreshed successfully
[[08:47:28]] [INFO] Executing Multi Step action step 5/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[08:47:27]] [SUCCESS] Screenshot refreshed
[[08:47:27]] [INFO] Refreshing screenshot...
[[08:47:23]] [SUCCESS] Screenshot refreshed successfully
[[08:47:23]] [SUCCESS] Screenshot refreshed successfully
[[08:47:22]] [INFO] Executing Multi Step action step 4/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[08:47:22]] [SUCCESS] Screenshot refreshed
[[08:47:22]] [INFO] Refreshing screenshot...
[[08:47:02]] [SUCCESS] Screenshot refreshed successfully
[[08:47:02]] [SUCCESS] Screenshot refreshed successfully
[[08:47:02]] [INFO] Executing Multi Step action step 3/41: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[08:47:02]] [SUCCESS] Screenshot refreshed
[[08:47:02]] [INFO] Refreshing screenshot...
[[08:46:58]] [SUCCESS] Screenshot refreshed successfully
[[08:46:58]] [SUCCESS] Screenshot refreshed successfully
[[08:46:57]] [INFO] Executing Multi Step action step 2/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:46:57]] [SUCCESS] Screenshot refreshed
[[08:46:57]] [INFO] Refreshing screenshot...
[[08:46:50]] [INFO] Executing Multi Step action step 1/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:46:50]] [INFO] Loaded 41 steps from test case: Delivery Buy Steps
[[08:46:50]] [INFO] Loading steps for multiStep action: Delivery Buy Steps
[[08:46:50]] [INFO] ZxObWodIp8=running
[[08:46:50]] [INFO] Executing action 392/576: Execute Test Case: Delivery Buy Steps (41 steps)
[[08:46:50]] [SUCCESS] Screenshot refreshed successfully
[[08:46:50]] [SUCCESS] Screenshot refreshed successfully
[[08:46:50]] [SUCCESS] Screenshot refreshed
[[08:46:50]] [INFO] Refreshing screenshot...
[[08:46:50]] [INFO] F4NGh9HrLw=pass
[[08:46:46]] [SUCCESS] Screenshot refreshed successfully
[[08:46:46]] [SUCCESS] Screenshot refreshed successfully
[[08:46:45]] [INFO] F4NGh9HrLw=running
[[08:46:45]] [INFO] Executing action 391/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:46:45]] [SUCCESS] Screenshot refreshed
[[08:46:45]] [INFO] Refreshing screenshot...
[[08:46:45]] [INFO] 4eEEGs1x8i=pass
[[08:46:33]] [SUCCESS] Screenshot refreshed successfully
[[08:46:33]] [SUCCESS] Screenshot refreshed successfully
[[08:46:33]] [INFO] 4eEEGs1x8i=running
[[08:46:33]] [INFO] Executing action 390/576: If exists: xpath="//XCUIElementTypeButton[@name="Save my location"]" (timeout: 10s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Save my location"]
[[08:46:32]] [SUCCESS] Screenshot refreshed
[[08:46:32]] [INFO] Refreshing screenshot...
[[08:46:32]] [INFO] O8XvoFFGEB=pass
[[08:46:27]] [SUCCESS] Screenshot refreshed successfully
[[08:46:27]] [SUCCESS] Screenshot refreshed successfully
[[08:46:27]] [INFO] O8XvoFFGEB=running
[[08:46:27]] [INFO] Executing action 389/576: Tap on image: env[atg-pdp]
[[08:46:26]] [SUCCESS] Screenshot refreshed
[[08:46:26]] [INFO] Refreshing screenshot...
[[08:46:26]] [INFO] CcFsA41sKp=pass
[[08:46:22]] [SUCCESS] Screenshot refreshed successfully
[[08:46:22]] [SUCCESS] Screenshot refreshed successfully
[[08:46:21]] [INFO] CcFsA41sKp=running
[[08:46:21]] [INFO] Executing action 388/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:46:21]] [SUCCESS] Screenshot refreshed
[[08:46:21]] [INFO] Refreshing screenshot...
[[08:46:21]] [INFO] 8XWyF2kgwW=pass
[[08:46:18]] [SUCCESS] Screenshot refreshed successfully
[[08:46:18]] [SUCCESS] Screenshot refreshed successfully
[[08:46:17]] [INFO] 8XWyF2kgwW=running
[[08:46:17]] [INFO] Executing action 387/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:46:17]] [SUCCESS] Screenshot refreshed
[[08:46:17]] [INFO] Refreshing screenshot...
[[08:46:17]] [INFO] qG4RkNac30=pass
[[08:46:12]] [SUCCESS] Screenshot refreshed successfully
[[08:46:12]] [SUCCESS] Screenshot refreshed successfully
[[08:46:12]] [INFO] qG4RkNac30=running
[[08:46:12]] [INFO] Executing action 386/576: iOS Function: text - Text: "P_42691341"
[[08:46:12]] [SUCCESS] Screenshot refreshed
[[08:46:12]] [INFO] Refreshing screenshot...
[[08:46:12]] [INFO] Jtn2FK4THX=pass
[[08:46:07]] [SUCCESS] Screenshot refreshed successfully
[[08:46:07]] [SUCCESS] Screenshot refreshed successfully
[[08:46:06]] [INFO] Jtn2FK4THX=running
[[08:46:06]] [INFO] Executing action 385/576: Tap on Text: "Find"
[[08:46:06]] [SUCCESS] Screenshot refreshed
[[08:46:06]] [INFO] Refreshing screenshot...
[[08:46:06]] [INFO] tWq2Qzn22D=pass
[[08:46:02]] [SUCCESS] Screenshot refreshed successfully
[[08:46:02]] [SUCCESS] Screenshot refreshed successfully
[[08:46:01]] [INFO] tWq2Qzn22D=running
[[08:46:01]] [INFO] Executing action 384/576: Tap on image: env[device-back-img]
[[08:46:00]] [SUCCESS] Screenshot refreshed
[[08:46:00]] [INFO] Refreshing screenshot...
[[08:46:00]] [INFO] 5hClb2pKKx=pass
[[08:45:38]] [SUCCESS] Screenshot refreshed successfully
[[08:45:38]] [SUCCESS] Screenshot refreshed successfully
[[08:45:38]] [INFO] 5hClb2pKKx=running
[[08:45:38]] [INFO] Executing action 383/576: If exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="btnUpdate"]
[[08:45:37]] [SUCCESS] Screenshot refreshed
[[08:45:37]] [INFO] Refreshing screenshot...
[[08:45:37]] [INFO] jmKjclMUWT=pass
[[08:45:32]] [SUCCESS] Screenshot refreshed successfully
[[08:45:32]] [SUCCESS] Screenshot refreshed successfully
[[08:45:32]] [INFO] jmKjclMUWT=running
[[08:45:32]] [INFO] Executing action 382/576: Tap on Text: "current"
[[08:45:32]] [SUCCESS] Screenshot refreshed
[[08:45:32]] [INFO] Refreshing screenshot...
[[08:45:32]] [INFO] UoH0wdtcLk=pass
[[08:45:26]] [SUCCESS] Screenshot refreshed successfully
[[08:45:26]] [SUCCESS] Screenshot refreshed successfully
[[08:45:26]] [INFO] UoH0wdtcLk=running
[[08:45:26]] [INFO] Executing action 381/576: Tap on Text: "Edit"
[[08:45:25]] [SUCCESS] Screenshot refreshed
[[08:45:25]] [INFO] Refreshing screenshot...
[[08:45:25]] [INFO] U48qCNydwd=pass
[[08:45:20]] [SUCCESS] Screenshot refreshed successfully
[[08:45:20]] [SUCCESS] Screenshot refreshed successfully
[[08:45:20]] [INFO] U48qCNydwd=running
[[08:45:20]] [INFO] Executing action 380/576: Restart app: env[appid]
[[08:45:20]] [SUCCESS] Screenshot refreshed
[[08:45:20]] [INFO] Refreshing screenshot...
[[08:45:20]] [INFO] XjclKOaCTh=pass
[[08:45:15]] [SUCCESS] Screenshot refreshed successfully
[[08:45:15]] [SUCCESS] Screenshot refreshed successfully
[[08:45:15]] [INFO] XjclKOaCTh=running
[[08:45:15]] [INFO] Executing action 379/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[08:45:14]] [SUCCESS] Screenshot refreshed
[[08:45:14]] [INFO] Refreshing screenshot...
[[08:45:14]] [INFO] q6cKxgMAIn=pass
[[08:45:11]] [SUCCESS] Screenshot refreshed successfully
[[08:45:11]] [SUCCESS] Screenshot refreshed successfully
[[08:45:11]] [INFO] q6cKxgMAIn=running
[[08:45:11]] [INFO] Executing action 378/576: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[08:45:10]] [SUCCESS] Screenshot refreshed
[[08:45:10]] [INFO] Refreshing screenshot...
[[08:45:10]] [INFO] zdh8hKYC1a=pass
[[08:45:06]] [SUCCESS] Screenshot refreshed successfully
[[08:45:06]] [SUCCESS] Screenshot refreshed successfully
[[08:45:06]] [INFO] zdh8hKYC1a=running
[[08:45:06]] [INFO] Executing action 377/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[08:45:06]] [SUCCESS] Screenshot refreshed
[[08:45:06]] [INFO] Refreshing screenshot...
[[08:45:06]] [INFO] P4b2BITpCf=pass
[[08:45:03]] [SUCCESS] Screenshot refreshed successfully
[[08:45:03]] [SUCCESS] Screenshot refreshed successfully
[[08:45:03]] [INFO] P4b2BITpCf=running
[[08:45:03]] [INFO] Executing action 376/576: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[08:45:02]] [SUCCESS] Screenshot refreshed
[[08:45:02]] [INFO] Refreshing screenshot...
[[08:45:02]] [INFO] inrxgdWzXr=pass
[[08:44:57]] [SUCCESS] Screenshot refreshed successfully
[[08:44:57]] [SUCCESS] Screenshot refreshed successfully
[[08:44:57]] [INFO] inrxgdWzXr=running
[[08:44:57]] [INFO] Executing action 375/576: Tap on Text: "Store"
[[08:44:57]] [SUCCESS] Screenshot refreshed
[[08:44:57]] [INFO] Refreshing screenshot...
[[08:44:57]] [INFO] inrxgdWzXr=pass
[[08:44:52]] [SUCCESS] Screenshot refreshed successfully
[[08:44:52]] [SUCCESS] Screenshot refreshed successfully
[[08:44:52]] [INFO] inrxgdWzXr=running
[[08:44:52]] [INFO] Executing action 374/576: Tap on Text: "receipts"
[[08:44:52]] [SUCCESS] Screenshot refreshed
[[08:44:52]] [INFO] Refreshing screenshot...
[[08:44:52]] [INFO] GEMv6goQtW=pass
[[08:44:48]] [SUCCESS] Screenshot refreshed successfully
[[08:44:48]] [SUCCESS] Screenshot refreshed successfully
[[08:44:48]] [INFO] GEMv6goQtW=running
[[08:44:48]] [INFO] Executing action 373/576: Tap on image: env[device-back-img]
[[08:44:47]] [SUCCESS] Screenshot refreshed
[[08:44:47]] [INFO] Refreshing screenshot...
[[08:44:47]] [INFO] DhWa2PCBXE=pass
[[08:44:44]] [SUCCESS] Screenshot refreshed successfully
[[08:44:44]] [SUCCESS] Screenshot refreshed successfully
[[08:44:44]] [INFO] DhWa2PCBXE=running
[[08:44:44]] [INFO] Executing action 372/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[08:44:44]] [SUCCESS] Screenshot refreshed
[[08:44:44]] [INFO] Refreshing screenshot...
[[08:44:44]] [INFO] pk2DLZFBmx=pass
[[08:44:39]] [SUCCESS] Screenshot refreshed successfully
[[08:44:39]] [SUCCESS] Screenshot refreshed successfully
[[08:44:39]] [INFO] pk2DLZFBmx=running
[[08:44:39]] [INFO] Executing action 371/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[08:44:39]] [SUCCESS] Screenshot refreshed
[[08:44:39]] [INFO] Refreshing screenshot...
[[08:44:39]] [INFO] ShJSdXvmVL=pass
[[08:44:36]] [SUCCESS] Screenshot refreshed successfully
[[08:44:36]] [SUCCESS] Screenshot refreshed successfully
[[08:44:36]] [INFO] ShJSdXvmVL=running
[[08:44:36]] [INFO] Executing action 370/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[08:44:35]] [SUCCESS] Screenshot refreshed
[[08:44:35]] [INFO] Refreshing screenshot...
[[08:44:35]] [INFO] kWPRvuo7kk=pass
[[08:44:30]] [SUCCESS] Screenshot refreshed successfully
[[08:44:30]] [SUCCESS] Screenshot refreshed successfully
[[08:44:30]] [INFO] kWPRvuo7kk=running
[[08:44:30]] [INFO] Executing action 369/576: iOS Function: text - Text: "env[pwd-op]"
[[08:44:29]] [SUCCESS] Screenshot refreshed
[[08:44:29]] [INFO] Refreshing screenshot...
[[08:44:29]] [INFO] d6vTfR4Y0D=pass
[[08:44:25]] [SUCCESS] Screenshot refreshed successfully
[[08:44:25]] [SUCCESS] Screenshot refreshed successfully
[[08:44:25]] [INFO] d6vTfR4Y0D=running
[[08:44:25]] [INFO] Executing action 368/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:44:24]] [SUCCESS] Screenshot refreshed
[[08:44:24]] [INFO] Refreshing screenshot...
[[08:44:24]] [INFO] pe9W6tZdXT=pass
[[08:44:19]] [SUCCESS] Screenshot refreshed successfully
[[08:44:19]] [SUCCESS] Screenshot refreshed successfully
[[08:44:19]] [INFO] pe9W6tZdXT=running
[[08:44:19]] [INFO] Executing action 367/576: iOS Function: text - Text: "env[uname-op]"
[[08:44:18]] [SUCCESS] Screenshot refreshed
[[08:44:18]] [INFO] Refreshing screenshot...
[[08:44:18]] [INFO] u928vFzSni=pass
[[08:44:14]] [SUCCESS] Screenshot refreshed successfully
[[08:44:14]] [SUCCESS] Screenshot refreshed successfully
[[08:44:14]] [INFO] u928vFzSni=running
[[08:44:14]] [INFO] Executing action 366/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:44:14]] [SUCCESS] Screenshot refreshed
[[08:44:14]] [INFO] Refreshing screenshot...
[[08:44:14]] [INFO] s0WyiD1w0B=pass
[[08:44:11]] [SUCCESS] Screenshot refreshed successfully
[[08:44:11]] [SUCCESS] Screenshot refreshed successfully
[[08:44:11]] [INFO] s0WyiD1w0B=running
[[08:44:11]] [INFO] Executing action 365/576: iOS Function: alert_accept
[[08:44:10]] [SUCCESS] Screenshot refreshed
[[08:44:10]] [INFO] Refreshing screenshot...
[[08:44:10]] [INFO] gekNSY5O2E=pass
[[08:44:06]] [SUCCESS] Screenshot refreshed successfully
[[08:44:06]] [SUCCESS] Screenshot refreshed successfully
[[08:44:06]] [INFO] gekNSY5O2E=running
[[08:44:06]] [INFO] Executing action 364/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[08:44:05]] [SUCCESS] Screenshot refreshed
[[08:44:05]] [INFO] Refreshing screenshot...
[[08:44:05]] [INFO] VJJ3EXXotU=pass
[[08:44:02]] [SUCCESS] Screenshot refreshed successfully
[[08:44:02]] [SUCCESS] Screenshot refreshed successfully
[[08:44:01]] [INFO] VJJ3EXXotU=running
[[08:44:01]] [INFO] Executing action 363/576: Tap on image: env[device-back-img]
[[08:44:00]] [SUCCESS] Screenshot refreshed
[[08:44:00]] [INFO] Refreshing screenshot...
[[08:44:00]] [INFO] 83tV9A4NOn=pass
[[08:43:57]] [SUCCESS] Screenshot refreshed successfully
[[08:43:57]] [SUCCESS] Screenshot refreshed successfully
[[08:43:57]] [INFO] 83tV9A4NOn=running
[[08:43:57]] [INFO] Executing action 362/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[08:43:57]] [SUCCESS] Screenshot refreshed
[[08:43:57]] [INFO] Refreshing screenshot...
[[08:43:57]] [INFO] aNN0yYFLEd=pass
[[08:43:52]] [SUCCESS] Screenshot refreshed successfully
[[08:43:52]] [SUCCESS] Screenshot refreshed successfully
[[08:43:52]] [INFO] aNN0yYFLEd=running
[[08:43:52]] [INFO] Executing action 361/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[08:43:51]] [SUCCESS] Screenshot refreshed
[[08:43:51]] [INFO] Refreshing screenshot...
[[08:43:51]] [INFO] XJv08Gkucs=pass
[[08:43:48]] [SUCCESS] Screenshot refreshed successfully
[[08:43:48]] [SUCCESS] Screenshot refreshed successfully
[[08:43:48]] [INFO] XJv08Gkucs=running
[[08:43:48]] [INFO] Executing action 360/576: Input text: "env[uname-op]"
[[08:43:47]] [SUCCESS] Screenshot refreshed
[[08:43:47]] [INFO] Refreshing screenshot...
[[08:43:47]] [INFO] kAQ1yIIw3h=pass
[[08:43:43]] [SUCCESS] Screenshot refreshed successfully
[[08:43:43]] [SUCCESS] Screenshot refreshed successfully
[[08:43:43]] [INFO] kAQ1yIIw3h=running
[[08:43:43]] [INFO] Executing action 359/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[08:43:43]] [SUCCESS] Screenshot refreshed
[[08:43:43]] [INFO] Refreshing screenshot...
[[08:43:43]] [INFO] 7YbjwQH1Jc=pass
[[08:43:40]] [SUCCESS] Screenshot refreshed successfully
[[08:43:40]] [SUCCESS] Screenshot refreshed successfully
[[08:43:40]] [INFO] 7YbjwQH1Jc=running
[[08:43:40]] [INFO] Executing action 358/576: Input text: "env[searchorder]"
[[08:43:39]] [SUCCESS] Screenshot refreshed
[[08:43:39]] [INFO] Refreshing screenshot...
[[08:43:39]] [INFO] OmKfD9iBjD=pass
[[08:43:35]] [SUCCESS] Screenshot refreshed successfully
[[08:43:35]] [SUCCESS] Screenshot refreshed successfully
[[08:43:35]] [INFO] OmKfD9iBjD=running
[[08:43:35]] [INFO] Executing action 357/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[08:43:35]] [SUCCESS] Screenshot refreshed
[[08:43:35]] [INFO] Refreshing screenshot...
[[08:43:35]] [INFO] eHLWiRoqqS=pass
[[08:43:30]] [SUCCESS] Screenshot refreshed successfully
[[08:43:30]] [SUCCESS] Screenshot refreshed successfully
[[08:43:30]] [INFO] eHLWiRoqqS=running
[[08:43:30]] [INFO] Executing action 356/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[08:43:30]] [SUCCESS] Screenshot refreshed
[[08:43:30]] [INFO] Refreshing screenshot...
[[08:43:30]] [INFO] F4NGh9HrLw=pass
[[08:43:26]] [SUCCESS] Screenshot refreshed successfully
[[08:43:26]] [SUCCESS] Screenshot refreshed successfully
[[08:43:25]] [INFO] F4NGh9HrLw=running
[[08:43:25]] [INFO] Executing action 355/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:43:25]] [SUCCESS] Screenshot refreshed
[[08:43:25]] [INFO] Refreshing screenshot...
[[08:43:25]] [INFO] 74XW7x54ad=pass
[[08:43:21]] [SUCCESS] Screenshot refreshed successfully
[[08:43:21]] [SUCCESS] Screenshot refreshed successfully
[[08:43:20]] [INFO] 74XW7x54ad=running
[[08:43:20]] [INFO] Executing action 354/576: Tap on image: env[device-back-img]
[[08:43:19]] [SUCCESS] Screenshot refreshed
[[08:43:19]] [INFO] Refreshing screenshot...
[[08:43:19]] [INFO] xUbWFa8Ok2=pass
[[08:43:17]] [SUCCESS] Screenshot refreshed successfully
[[08:43:17]] [SUCCESS] Screenshot refreshed successfully
[[08:43:15]] [INFO] xUbWFa8Ok2=running
[[08:43:15]] [INFO] Executing action 353/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[08:43:14]] [SUCCESS] Screenshot refreshed
[[08:43:14]] [INFO] Refreshing screenshot...
[[08:43:14]] [INFO] RbNtEW6N9T=pass
[[08:43:12]] [SUCCESS] Screenshot refreshed successfully
[[08:43:12]] [SUCCESS] Screenshot refreshed successfully
[[08:43:10]] [INFO] RbNtEW6N9T=running
[[08:43:10]] [INFO] Executing action 352/576: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[08:43:10]] [SUCCESS] Screenshot refreshed
[[08:43:10]] [INFO] Refreshing screenshot...
[[08:43:10]] [INFO] F4NGh9HrLw=pass
[[08:43:07]] [SUCCESS] Screenshot refreshed successfully
[[08:43:07]] [SUCCESS] Screenshot refreshed successfully
[[08:43:05]] [INFO] F4NGh9HrLw=running
[[08:43:05]] [INFO] Executing action 351/576: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[08:43:05]] [SUCCESS] Screenshot refreshed
[[08:43:05]] [INFO] Refreshing screenshot...
[[08:43:05]] [INFO] RlDZFks4Lc=pass
[[08:43:02]] [SUCCESS] Screenshot refreshed successfully
[[08:43:02]] [SUCCESS] Screenshot refreshed successfully
[[08:43:00]] [INFO] RlDZFks4Lc=running
[[08:43:00]] [INFO] Executing action 350/576: iOS Function: alert_accept
[[08:43:00]] [SUCCESS] Screenshot refreshed
[[08:43:00]] [INFO] Refreshing screenshot...
[[08:43:00]] [INFO] Dzn2Q7JTe0=pass
[[08:42:55]] [SUCCESS] Screenshot refreshed successfully
[[08:42:55]] [SUCCESS] Screenshot refreshed successfully
[[08:42:54]] [INFO] Dzn2Q7JTe0=running
[[08:42:54]] [INFO] Executing action 349/576: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[08:42:54]] [SUCCESS] Screenshot refreshed
[[08:42:54]] [INFO] Refreshing screenshot...
[[08:42:54]] [INFO] H9fy9qcFbZ=pass
[[08:42:41]] [SUCCESS] Screenshot refreshed successfully
[[08:42:41]] [SUCCESS] Screenshot refreshed successfully
[[08:42:39]] [INFO] H9fy9qcFbZ=running
[[08:42:39]] [INFO] Executing action 348/576: Restart app: env[appid]
[[08:42:39]] [SUCCESS] Screenshot refreshed
[[08:42:39]] [INFO] Refreshing screenshot...
[[08:42:39]] [SUCCESS] Screenshot refreshed
[[08:42:39]] [INFO] Refreshing screenshot...
[[08:42:35]] [SUCCESS] Screenshot refreshed successfully
[[08:42:35]] [SUCCESS] Screenshot refreshed successfully
[[08:42:35]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:42:35]] [SUCCESS] Screenshot refreshed
[[08:42:35]] [INFO] Refreshing screenshot...
[[08:42:23]] [SUCCESS] Screenshot refreshed successfully
[[08:42:23]] [SUCCESS] Screenshot refreshed successfully
[[08:42:23]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:42:22]] [SUCCESS] Screenshot refreshed
[[08:42:22]] [INFO] Refreshing screenshot...
[[08:42:18]] [SUCCESS] Screenshot refreshed successfully
[[08:42:18]] [SUCCESS] Screenshot refreshed successfully
[[08:42:18]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:42:18]] [SUCCESS] Screenshot refreshed
[[08:42:18]] [INFO] Refreshing screenshot...
[[08:42:14]] [SUCCESS] Screenshot refreshed successfully
[[08:42:14]] [SUCCESS] Screenshot refreshed successfully
[[08:42:14]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:42:13]] [SUCCESS] Screenshot refreshed
[[08:42:13]] [INFO] Refreshing screenshot...
[[08:42:07]] [SUCCESS] Screenshot refreshed successfully
[[08:42:07]] [SUCCESS] Screenshot refreshed successfully
[[08:42:06]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:42:05]] [SUCCESS] Screenshot refreshed
[[08:42:05]] [INFO] Refreshing screenshot...
[[08:41:58]] [SUCCESS] Screenshot refreshed successfully
[[08:41:58]] [SUCCESS] Screenshot refreshed successfully
[[08:41:58]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:41:58]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:41:58]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:41:58]] [INFO] AeQaElnzUN=running
[[08:41:58]] [INFO] Executing action 347/576: cleanupSteps action
[[08:41:57]] [SUCCESS] Screenshot refreshed
[[08:41:57]] [INFO] Refreshing screenshot...
[[08:41:57]] [INFO] BracBsfa3Y=pass
[[08:41:52]] [SUCCESS] Screenshot refreshed successfully
[[08:41:52]] [SUCCESS] Screenshot refreshed successfully
[[08:41:52]] [INFO] BracBsfa3Y=running
[[08:41:52]] [INFO] Executing action 346/576: Tap on Text: "out"
[[08:41:51]] [SUCCESS] Screenshot refreshed
[[08:41:51]] [INFO] Refreshing screenshot...
[[08:41:51]] [INFO] s6tWdQ5URW=pass
[[08:41:44]] [SUCCESS] Screenshot refreshed successfully
[[08:41:44]] [SUCCESS] Screenshot refreshed successfully
[[08:41:44]] [INFO] s6tWdQ5URW=running
[[08:41:44]] [INFO] Executing action 345/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:41:43]] [SUCCESS] Screenshot refreshed
[[08:41:43]] [INFO] Refreshing screenshot...
[[08:41:43]] [INFO] wNGRrfUjpK=pass
[[08:41:39]] [SUCCESS] Screenshot refreshed successfully
[[08:41:39]] [SUCCESS] Screenshot refreshed successfully
[[08:41:39]] [INFO] wNGRrfUjpK=running
[[08:41:39]] [INFO] Executing action 344/576: Tap on image: env[device-back-img]
[[08:41:39]] [SUCCESS] Screenshot refreshed
[[08:41:39]] [INFO] Refreshing screenshot...
[[08:41:39]] [INFO] BracBsfa3Y=pass
[[08:41:34]] [SUCCESS] Screenshot refreshed successfully
[[08:41:34]] [SUCCESS] Screenshot refreshed successfully
[[08:41:34]] [INFO] BracBsfa3Y=running
[[08:41:34]] [INFO] Executing action 343/576: Tap on Text: "Customer"
[[08:41:33]] [SUCCESS] Screenshot refreshed
[[08:41:33]] [INFO] Refreshing screenshot...
[[08:41:33]] [INFO] H4WfwVU8YP=pass
[[08:41:29]] [SUCCESS] Screenshot refreshed successfully
[[08:41:29]] [SUCCESS] Screenshot refreshed successfully
[[08:41:28]] [INFO] H4WfwVU8YP=running
[[08:41:28]] [INFO] Executing action 342/576: Tap on image: banner-close-updated.png
[[08:41:28]] [SUCCESS] Screenshot refreshed
[[08:41:28]] [INFO] Refreshing screenshot...
[[08:41:28]] [INFO] ePyaYpttQA=pass
[[08:41:24]] [SUCCESS] Screenshot refreshed successfully
[[08:41:24]] [SUCCESS] Screenshot refreshed successfully
[[08:41:23]] [INFO] ePyaYpttQA=running
[[08:41:23]] [INFO] Executing action 341/576: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[08:41:23]] [SUCCESS] Screenshot refreshed
[[08:41:23]] [INFO] Refreshing screenshot...
[[08:41:23]] [INFO] BracBsfa3Y=pass
[[08:41:17]] [SUCCESS] Screenshot refreshed successfully
[[08:41:17]] [SUCCESS] Screenshot refreshed successfully
[[08:41:17]] [INFO] BracBsfa3Y=running
[[08:41:17]] [INFO] Executing action 340/576: Tap on Text: "Invite"
[[08:41:17]] [SUCCESS] Screenshot refreshed
[[08:41:17]] [INFO] Refreshing screenshot...
[[08:41:17]] [INFO] xVbCNStsOP=pass
[[08:41:12]] [SUCCESS] Screenshot refreshed successfully
[[08:41:12]] [SUCCESS] Screenshot refreshed successfully
[[08:41:12]] [INFO] xVbCNStsOP=running
[[08:41:12]] [INFO] Executing action 339/576: Tap on image: env[device-back-img]
[[08:41:12]] [SUCCESS] Screenshot refreshed
[[08:41:12]] [INFO] Refreshing screenshot...
[[08:41:12]] [INFO] 8kQkC2FGyZ=pass
[[08:41:09]] [SUCCESS] Screenshot refreshed successfully
[[08:41:09]] [SUCCESS] Screenshot refreshed successfully
[[08:41:08]] [INFO] 8kQkC2FGyZ=running
[[08:41:08]] [INFO] Executing action 338/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[08:41:08]] [SUCCESS] Screenshot refreshed
[[08:41:08]] [INFO] Refreshing screenshot...
[[08:41:08]] [INFO] PgjJCrKFYo=pass
[[08:41:02]] [SUCCESS] Screenshot refreshed successfully
[[08:41:02]] [SUCCESS] Screenshot refreshed successfully
[[08:41:02]] [INFO] PgjJCrKFYo=running
[[08:41:02]] [INFO] Executing action 337/576: Tap on Text: "VIC"
[[08:41:02]] [SUCCESS] Screenshot refreshed
[[08:41:02]] [INFO] Refreshing screenshot...
[[08:41:02]] [INFO] 3Si0csRNaw=pass
[[08:40:55]] [SUCCESS] Screenshot refreshed successfully
[[08:40:55]] [SUCCESS] Screenshot refreshed successfully
[[08:40:54]] [INFO] 3Si0csRNaw=running
[[08:40:54]] [INFO] Executing action 336/576: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[08:40:53]] [SUCCESS] Screenshot refreshed
[[08:40:53]] [INFO] Refreshing screenshot...
[[08:40:53]] [INFO] BracBsfa3Y=pass
[[08:40:48]] [SUCCESS] Screenshot refreshed successfully
[[08:40:48]] [SUCCESS] Screenshot refreshed successfully
[[08:40:48]] [INFO] BracBsfa3Y=running
[[08:40:48]] [INFO] Executing action 335/576: Tap on Text: "Nearby"
[[08:40:47]] [SUCCESS] Screenshot refreshed
[[08:40:47]] [INFO] Refreshing screenshot...
[[08:40:47]] [INFO] BracBsfa3Y=pass
[[08:40:43]] [SUCCESS] Screenshot refreshed successfully
[[08:40:43]] [SUCCESS] Screenshot refreshed successfully
[[08:40:43]] [INFO] BracBsfa3Y=running
[[08:40:43]] [INFO] Executing action 334/576: Tap on Text: "locator"
[[08:40:42]] [SUCCESS] Screenshot refreshed
[[08:40:42]] [INFO] Refreshing screenshot...
[[08:40:42]] [INFO] s6tWdQ5URW=pass
[[08:40:35]] [SUCCESS] Screenshot refreshed successfully
[[08:40:35]] [SUCCESS] Screenshot refreshed successfully
[[08:40:35]] [INFO] s6tWdQ5URW=running
[[08:40:35]] [INFO] Executing action 333/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:40:35]] [SUCCESS] Screenshot refreshed
[[08:40:35]] [INFO] Refreshing screenshot...
[[08:40:35]] [INFO] 2M0KHOVecv=pass
[[08:40:30]] [SUCCESS] Screenshot refreshed successfully
[[08:40:30]] [SUCCESS] Screenshot refreshed successfully
[[08:40:30]] [INFO] 2M0KHOVecv=running
[[08:40:30]] [INFO] Executing action 332/576: Check if element with accessibility_id="txtMy Flybuys card" exists
[[08:40:30]] [SUCCESS] Screenshot refreshed
[[08:40:30]] [INFO] Refreshing screenshot...
[[08:40:30]] [INFO] LBgsj3oLcu=pass
[[08:40:26]] [SUCCESS] Screenshot refreshed successfully
[[08:40:26]] [SUCCESS] Screenshot refreshed successfully
[[08:40:25]] [INFO] LBgsj3oLcu=running
[[08:40:25]] [INFO] Executing action 331/576: Tap on image: env[device-back-img]
[[08:40:25]] [SUCCESS] Screenshot refreshed
[[08:40:25]] [INFO] Refreshing screenshot...
[[08:40:25]] [INFO] biRyWs3nSs=pass
[[08:40:19]] [SUCCESS] Screenshot refreshed successfully
[[08:40:19]] [SUCCESS] Screenshot refreshed successfully
[[08:40:19]] [INFO] biRyWs3nSs=running
[[08:40:19]] [INFO] Executing action 330/576: Tap on element with accessibility_id: btnSaveFlybuysCard
[[08:40:18]] [SUCCESS] Screenshot refreshed
[[08:40:18]] [INFO] Refreshing screenshot...
[[08:40:18]] [INFO] 8cFGh3GD68=pass
[[08:40:13]] [SUCCESS] Screenshot refreshed successfully
[[08:40:13]] [SUCCESS] Screenshot refreshed successfully
[[08:40:12]] [INFO] 8cFGh3GD68=running
[[08:40:12]] [INFO] Executing action 329/576: Tap on element with accessibility_id: Done
[[08:40:12]] [SUCCESS] Screenshot refreshed
[[08:40:12]] [INFO] Refreshing screenshot...
[[08:40:12]] [INFO] sLe0Wurhgm=pass
[[08:40:09]] [SUCCESS] Screenshot refreshed successfully
[[08:40:09]] [SUCCESS] Screenshot refreshed successfully
[[08:40:09]] [INFO] sLe0Wurhgm=running
[[08:40:09]] [INFO] Executing action 328/576: Input text: "2791234567890"
[[08:40:08]] [SUCCESS] Screenshot refreshed
[[08:40:08]] [INFO] Refreshing screenshot...
[[08:40:08]] [INFO] Ey86YRVRzU=pass
[[08:40:02]] [SUCCESS] Screenshot refreshed successfully
[[08:40:02]] [SUCCESS] Screenshot refreshed successfully
[[08:40:02]] [INFO] Ey86YRVRzU=running
[[08:40:02]] [INFO] Executing action 327/576: Tap on element with accessibility_id: Flybuys barcode number
[[08:40:02]] [SUCCESS] Screenshot refreshed
[[08:40:02]] [INFO] Refreshing screenshot...
[[08:40:02]] [INFO] Gxhf3XGc6e=pass
[[08:39:56]] [SUCCESS] Screenshot refreshed successfully
[[08:39:56]] [SUCCESS] Screenshot refreshed successfully
[[08:39:56]] [INFO] Gxhf3XGc6e=running
[[08:39:56]] [INFO] Executing action 326/576: Tap on element with accessibility_id: btnLinkFlyBuys
[[08:39:55]] [SUCCESS] Screenshot refreshed
[[08:39:55]] [INFO] Refreshing screenshot...
[[08:39:55]] [INFO] BracBsfa3Y=pass
[[08:39:50]] [SUCCESS] Screenshot refreshed successfully
[[08:39:50]] [SUCCESS] Screenshot refreshed successfully
[[08:39:50]] [INFO] BracBsfa3Y=running
[[08:39:50]] [INFO] Executing action 325/576: Tap on Text: "Flybuys"
[[08:39:49]] [SUCCESS] Screenshot refreshed
[[08:39:49]] [INFO] Refreshing screenshot...
[[08:39:49]] [INFO] Ds5GfNVb3x=pass
[[08:39:44]] [SUCCESS] Screenshot refreshed successfully
[[08:39:44]] [SUCCESS] Screenshot refreshed successfully
[[08:39:44]] [INFO] Ds5GfNVb3x=running
[[08:39:44]] [INFO] Executing action 324/576: Tap on element with accessibility_id: btnRemove
[[08:39:43]] [SUCCESS] Screenshot refreshed
[[08:39:43]] [INFO] Refreshing screenshot...
[[08:39:43]] [INFO] 3ZFgwFaiXp=pass
[[08:39:38]] [SUCCESS] Screenshot refreshed successfully
[[08:39:38]] [SUCCESS] Screenshot refreshed successfully
[[08:39:37]] [INFO] 3ZFgwFaiXp=running
[[08:39:37]] [INFO] Executing action 323/576: Tap on element with accessibility_id: Remove card
[[08:39:37]] [SUCCESS] Screenshot refreshed
[[08:39:37]] [INFO] Refreshing screenshot...
[[08:39:37]] [INFO] 40hnWPsQ9P=pass
[[08:39:31]] [SUCCESS] Screenshot refreshed successfully
[[08:39:31]] [SUCCESS] Screenshot refreshed successfully
[[08:39:31]] [INFO] 40hnWPsQ9P=running
[[08:39:31]] [INFO] Executing action 322/576: Tap on element with accessibility_id: btneditFlybuysCard
[[08:39:30]] [SUCCESS] Screenshot refreshed
[[08:39:30]] [INFO] Refreshing screenshot...
[[08:39:30]] [INFO] 40hnWPsQ9P=pass
[[08:39:25]] [SUCCESS] Screenshot refreshed successfully
[[08:39:25]] [SUCCESS] Screenshot refreshed successfully
[[08:39:25]] [INFO] 40hnWPsQ9P=running
[[08:39:25]] [INFO] Executing action 321/576: Wait till accessibility_id=btneditFlybuysCard
[[08:39:25]] [SUCCESS] Screenshot refreshed
[[08:39:25]] [INFO] Refreshing screenshot...
[[08:39:25]] [INFO] BracBsfa3Y=pass
[[08:39:19]] [SUCCESS] Screenshot refreshed successfully
[[08:39:19]] [SUCCESS] Screenshot refreshed successfully
[[08:39:19]] [INFO] BracBsfa3Y=running
[[08:39:19]] [INFO] Executing action 320/576: Tap on Text: "Flybuys"
[[08:39:18]] [SUCCESS] Screenshot refreshed
[[08:39:18]] [INFO] Refreshing screenshot...
[[08:39:18]] [INFO] MkTFxfzubv=pass
[[08:39:15]] [SUCCESS] Screenshot refreshed successfully
[[08:39:15]] [SUCCESS] Screenshot refreshed successfully
[[08:39:14]] [INFO] MkTFxfzubv=running
[[08:39:14]] [INFO] Executing action 319/576: Tap on image: env[device-back-img]
[[08:39:14]] [SUCCESS] Screenshot refreshed
[[08:39:14]] [INFO] Refreshing screenshot...
[[08:39:14]] [INFO] EO3cMmdUyM=pass
[[08:39:10]] [SUCCESS] Screenshot refreshed successfully
[[08:39:10]] [SUCCESS] Screenshot refreshed successfully
[[08:39:10]] [INFO] EO3cMmdUyM=running
[[08:39:10]] [INFO] Executing action 318/576: Tap on image: env[device-back-img]
[[08:39:09]] [SUCCESS] Screenshot refreshed
[[08:39:09]] [INFO] Refreshing screenshot...
[[08:39:09]] [INFO] napKDohf3Z=pass
[[08:39:04]] [SUCCESS] Screenshot refreshed successfully
[[08:39:04]] [SUCCESS] Screenshot refreshed successfully
[[08:39:04]] [INFO] napKDohf3Z=running
[[08:39:04]] [INFO] Executing action 317/576: Tap on Text: "payment"
[[08:39:04]] [SUCCESS] Screenshot refreshed
[[08:39:04]] [INFO] Refreshing screenshot...
[[08:39:04]] [INFO] ekqt95ZRol=pass
[[08:38:59]] [SUCCESS] Screenshot refreshed successfully
[[08:38:59]] [SUCCESS] Screenshot refreshed successfully
[[08:38:59]] [INFO] ekqt95ZRol=running
[[08:38:59]] [INFO] Executing action 316/576: Tap on image: env[device-back-img]
[[08:38:59]] [SUCCESS] Screenshot refreshed
[[08:38:59]] [INFO] Refreshing screenshot...
[[08:38:59]] [INFO] 20qUCJgpE9=pass
[[08:38:55]] [SUCCESS] Screenshot refreshed successfully
[[08:38:55]] [SUCCESS] Screenshot refreshed successfully
[[08:38:54]] [INFO] 20qUCJgpE9=running
[[08:38:54]] [INFO] Executing action 315/576: Tap on Text: "address"
[[08:38:53]] [SUCCESS] Screenshot refreshed
[[08:38:53]] [INFO] Refreshing screenshot...
[[08:38:53]] [INFO] 6HR2weiXoT=pass
[[08:38:49]] [SUCCESS] Screenshot refreshed successfully
[[08:38:49]] [SUCCESS] Screenshot refreshed successfully
[[08:38:49]] [INFO] 6HR2weiXoT=running
[[08:38:49]] [INFO] Executing action 314/576: Tap on image: env[device-back-img]
[[08:38:49]] [SUCCESS] Screenshot refreshed
[[08:38:49]] [INFO] Refreshing screenshot...
[[08:38:49]] [INFO] 3hOTINBVMf=pass
[[08:38:44]] [SUCCESS] Screenshot refreshed successfully
[[08:38:44]] [SUCCESS] Screenshot refreshed successfully
[[08:38:44]] [INFO] 3hOTINBVMf=running
[[08:38:44]] [INFO] Executing action 313/576: Tap on Text: "details"
[[08:38:43]] [SUCCESS] Screenshot refreshed
[[08:38:43]] [INFO] Refreshing screenshot...
[[08:38:43]] [INFO] yJi0WxnERj=pass
[[08:38:39]] [SUCCESS] Screenshot refreshed successfully
[[08:38:39]] [SUCCESS] Screenshot refreshed successfully
[[08:38:39]] [INFO] yJi0WxnERj=running
[[08:38:39]] [INFO] Executing action 312/576: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[08:38:39]] [SUCCESS] Screenshot refreshed
[[08:38:39]] [INFO] Refreshing screenshot...
[[08:38:39]] [INFO] PbfHAtFQPP=pass
[[08:38:35]] [SUCCESS] Screenshot refreshed successfully
[[08:38:35]] [SUCCESS] Screenshot refreshed successfully
[[08:38:34]] [INFO] PbfHAtFQPP=running
[[08:38:34]] [INFO] Executing action 311/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:38:34]] [SUCCESS] Screenshot refreshed
[[08:38:34]] [INFO] Refreshing screenshot...
[[08:38:34]] [INFO] 6qZnk86hGg=pass
[[08:38:29]] [SUCCESS] Screenshot refreshed successfully
[[08:38:29]] [SUCCESS] Screenshot refreshed successfully
[[08:38:29]] [INFO] 6qZnk86hGg=running
[[08:38:29]] [INFO] Executing action 310/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:38:28]] [SUCCESS] Screenshot refreshed
[[08:38:28]] [INFO] Refreshing screenshot...
[[08:38:28]] [INFO] FAvQgIuHc1=pass
[[08:38:23]] [SUCCESS] Screenshot refreshed successfully
[[08:38:23]] [SUCCESS] Screenshot refreshed successfully
[[08:38:23]] [INFO] FAvQgIuHc1=running
[[08:38:23]] [INFO] Executing action 309/576: Tap on Text: "Return"
[[08:38:22]] [SUCCESS] Screenshot refreshed
[[08:38:22]] [INFO] Refreshing screenshot...
[[08:38:22]] [INFO] vmc01sHkbr=pass
[[08:38:16]] [SUCCESS] Screenshot refreshed successfully
[[08:38:16]] [SUCCESS] Screenshot refreshed successfully
[[08:38:16]] [INFO] vmc01sHkbr=running
[[08:38:16]] [INFO] Executing action 308/576: Wait for 5 ms
[[08:38:15]] [SUCCESS] Screenshot refreshed
[[08:38:15]] [INFO] Refreshing screenshot...
[[08:38:15]] [INFO] zeu0wd1vcF=pass
[[08:38:02]] [SUCCESS] Screenshot refreshed successfully
[[08:38:02]] [SUCCESS] Screenshot refreshed successfully
[[08:38:02]] [INFO] zeu0wd1vcF=running
[[08:38:02]] [INFO] Executing action 307/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:38:02]] [SUCCESS] Screenshot refreshed
[[08:38:02]] [INFO] Refreshing screenshot...
[[08:38:02]] [INFO] OwWeZes4aT=pass
[[08:37:58]] [SUCCESS] Screenshot refreshed successfully
[[08:37:58]] [SUCCESS] Screenshot refreshed successfully
[[08:37:57]] [INFO] OwWeZes4aT=running
[[08:37:57]] [INFO] Executing action 306/576: Tap on image: env[device-back-img]
[[08:37:57]] [SUCCESS] Screenshot refreshed
[[08:37:57]] [INFO] Refreshing screenshot...
[[08:37:57]] [INFO] aAaTtUE92h=pass
[[08:37:53]] [SUCCESS] Screenshot refreshed successfully
[[08:37:53]] [SUCCESS] Screenshot refreshed successfully
[[08:37:53]] [INFO] aAaTtUE92h=running
[[08:37:53]] [INFO] Executing action 305/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[08:37:53]] [SUCCESS] Screenshot refreshed
[[08:37:53]] [INFO] Refreshing screenshot...
[[08:37:53]] [INFO] 9iOZGMqAZK=pass
[[08:37:49]] [SUCCESS] Screenshot refreshed successfully
[[08:37:49]] [SUCCESS] Screenshot refreshed successfully
[[08:37:48]] [INFO] 9iOZGMqAZK=running
[[08:37:48]] [INFO] Executing action 304/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[08:37:48]] [SUCCESS] Screenshot refreshed
[[08:37:48]] [INFO] Refreshing screenshot...
[[08:37:48]] [INFO] mRTYzOFRRw=pass
[[08:37:45]] [SUCCESS] Screenshot refreshed successfully
[[08:37:45]] [SUCCESS] Screenshot refreshed successfully
[[08:37:45]] [INFO] mRTYzOFRRw=running
[[08:37:45]] [INFO] Executing action 303/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[08:37:44]] [SUCCESS] Screenshot refreshed
[[08:37:44]] [INFO] Refreshing screenshot...
[[08:37:44]] [INFO] 7g6MFJSGIO=pass
[[08:37:40]] [SUCCESS] Screenshot refreshed successfully
[[08:37:40]] [SUCCESS] Screenshot refreshed successfully
[[08:37:40]] [INFO] 7g6MFJSGIO=running
[[08:37:40]] [INFO] Executing action 302/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[08:37:40]] [SUCCESS] Screenshot refreshed
[[08:37:40]] [INFO] Refreshing screenshot...
[[08:37:40]] [INFO] zNwyPagPE1=pass
[[08:37:33]] [SUCCESS] Screenshot refreshed successfully
[[08:37:33]] [SUCCESS] Screenshot refreshed successfully
[[08:37:33]] [INFO] zNwyPagPE1=running
[[08:37:33]] [INFO] Executing action 301/576: Wait for 5 ms
[[08:37:33]] [SUCCESS] Screenshot refreshed
[[08:37:33]] [INFO] Refreshing screenshot...
[[08:37:33]] [INFO] qXsL3wzg6J=pass
[[08:37:29]] [SUCCESS] Screenshot refreshed successfully
[[08:37:29]] [SUCCESS] Screenshot refreshed successfully
[[08:37:29]] [INFO] qXsL3wzg6J=running
[[08:37:29]] [INFO] Executing action 300/576: Tap on image: env[device-back-img]
[[08:37:28]] [SUCCESS] Screenshot refreshed
[[08:37:28]] [INFO] Refreshing screenshot...
[[08:37:28]] [INFO] YuuQe2KupX=pass
[[08:37:23]] [INFO] YuuQe2KupX=running
[[08:37:23]] [INFO] Executing action 299/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[08:37:23]] [SUCCESS] Screenshot refreshed successfully
[[08:37:23]] [SUCCESS] Screenshot refreshed successfully
[[08:37:23]] [SUCCESS] Screenshot refreshed
[[08:37:23]] [INFO] Refreshing screenshot...
[[08:37:23]] [INFO] g0PE7Mofye=pass
[[08:37:17]] [SUCCESS] Screenshot refreshed successfully
[[08:37:17]] [SUCCESS] Screenshot refreshed successfully
[[08:37:17]] [INFO] g0PE7Mofye=running
[[08:37:17]] [INFO] Executing action 298/576: Tap on element with accessibility_id: Print order details
[[08:37:17]] [SUCCESS] Screenshot refreshed
[[08:37:17]] [INFO] Refreshing screenshot...
[[08:37:17]] [INFO] GgQaBLWYkb=pass
[[08:37:13]] [SUCCESS] Screenshot refreshed successfully
[[08:37:13]] [SUCCESS] Screenshot refreshed successfully
[[08:37:13]] [INFO] GgQaBLWYkb=running
[[08:37:13]] [INFO] Executing action 297/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[08:37:12]] [SUCCESS] Screenshot refreshed
[[08:37:12]] [INFO] Refreshing screenshot...
[[08:37:12]] [INFO] f3OrHHzTFN=pass
[[08:36:56]] [SUCCESS] Screenshot refreshed successfully
[[08:36:56]] [SUCCESS] Screenshot refreshed successfully
[[08:36:56]] [INFO] f3OrHHzTFN=running
[[08:36:56]] [INFO] Executing action 296/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[08:36:55]] [SUCCESS] Screenshot refreshed
[[08:36:55]] [INFO] Refreshing screenshot...
[[08:36:55]] [INFO] 7g6MFJSGIO=pass
[[08:36:51]] [SUCCESS] Screenshot refreshed successfully
[[08:36:51]] [SUCCESS] Screenshot refreshed successfully
[[08:36:51]] [INFO] 7g6MFJSGIO=running
[[08:36:51]] [INFO] Executing action 295/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[08:36:51]] [SUCCESS] Screenshot refreshed
[[08:36:51]] [INFO] Refreshing screenshot...
[[08:36:51]] [INFO] Z6g3sGuHTp=pass
[[08:36:44]] [SUCCESS] Screenshot refreshed successfully
[[08:36:44]] [SUCCESS] Screenshot refreshed successfully
[[08:36:44]] [INFO] Z6g3sGuHTp=running
[[08:36:44]] [INFO] Executing action 294/576: Wait for 5 ms
[[08:36:43]] [SUCCESS] Screenshot refreshed
[[08:36:43]] [INFO] Refreshing screenshot...
[[08:36:43]] [INFO] pFlYwTS53v=pass
[[08:36:39]] [SUCCESS] Screenshot refreshed successfully
[[08:36:39]] [SUCCESS] Screenshot refreshed successfully
[[08:36:39]] [INFO] pFlYwTS53v=running
[[08:36:39]] [INFO] Executing action 293/576: Tap on Text: "receipts"
[[08:36:38]] [SUCCESS] Screenshot refreshed
[[08:36:38]] [INFO] Refreshing screenshot...
[[08:36:38]] [INFO] V59u3l1wkM=pass
[[08:36:35]] [SUCCESS] Screenshot refreshed successfully
[[08:36:35]] [SUCCESS] Screenshot refreshed successfully
[[08:36:35]] [INFO] V59u3l1wkM=running
[[08:36:35]] [INFO] Executing action 292/576: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[08:36:34]] [SUCCESS] Screenshot refreshed
[[08:36:34]] [INFO] Refreshing screenshot...
[[08:36:34]] [INFO] sl3Wk1gK8X=pass
[[08:36:32]] [SUCCESS] Screenshot refreshed successfully
[[08:36:32]] [SUCCESS] Screenshot refreshed successfully
[[08:36:30]] [INFO] sl3Wk1gK8X=running
[[08:36:30]] [INFO] Executing action 291/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:36:30]] [SUCCESS] Screenshot refreshed
[[08:36:30]] [INFO] Refreshing screenshot...
[[08:36:29]] [SUCCESS] Screenshot refreshed
[[08:36:29]] [INFO] Refreshing screenshot...
[[08:36:25]] [SUCCESS] Screenshot refreshed successfully
[[08:36:25]] [SUCCESS] Screenshot refreshed successfully
[[08:36:24]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:36:23]] [SUCCESS] Screenshot refreshed
[[08:36:23]] [INFO] Refreshing screenshot...
[[08:36:19]] [SUCCESS] Screenshot refreshed successfully
[[08:36:19]] [SUCCESS] Screenshot refreshed successfully
[[08:36:19]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:36:19]] [SUCCESS] Screenshot refreshed
[[08:36:19]] [INFO] Refreshing screenshot...
[[08:36:14]] [SUCCESS] Screenshot refreshed successfully
[[08:36:14]] [SUCCESS] Screenshot refreshed successfully
[[08:36:14]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:36:13]] [SUCCESS] Screenshot refreshed
[[08:36:13]] [INFO] Refreshing screenshot...
[[08:36:09]] [SUCCESS] Screenshot refreshed successfully
[[08:36:09]] [SUCCESS] Screenshot refreshed successfully
[[08:36:09]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:36:09]] [SUCCESS] Screenshot refreshed
[[08:36:09]] [INFO] Refreshing screenshot...
[[08:36:03]] [SUCCESS] Screenshot refreshed successfully
[[08:36:03]] [SUCCESS] Screenshot refreshed successfully
[[08:36:03]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:36:03]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:36:03]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:36:03]] [INFO] vjK6GqOF3r=running
[[08:36:03]] [INFO] Executing action 290/576: Execute Test Case: Kmart-Signin (8 steps)
[[08:36:02]] [SUCCESS] Screenshot refreshed
[[08:36:02]] [INFO] Refreshing screenshot...
[[08:36:02]] [INFO] ly2oT3zqmf=pass
[[08:36:00]] [SUCCESS] Screenshot refreshed successfully
[[08:36:00]] [SUCCESS] Screenshot refreshed successfully
[[08:35:59]] [INFO] ly2oT3zqmf=running
[[08:35:59]] [INFO] Executing action 289/576: iOS Function: alert_accept
[[08:35:59]] [SUCCESS] Screenshot refreshed
[[08:35:59]] [INFO] Refreshing screenshot...
[[08:35:59]] [INFO] xAPeBnVHrT=pass
[[08:35:51]] [SUCCESS] Screenshot refreshed successfully
[[08:35:51]] [SUCCESS] Screenshot refreshed successfully
[[08:35:51]] [INFO] xAPeBnVHrT=running
[[08:35:51]] [INFO] Executing action 288/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:35:50]] [SUCCESS] Screenshot refreshed
[[08:35:50]] [INFO] Refreshing screenshot...
[[08:35:50]] [INFO] u6bRYZZFAv=pass
[[08:35:44]] [SUCCESS] Screenshot refreshed successfully
[[08:35:44]] [SUCCESS] Screenshot refreshed successfully
[[08:35:43]] [INFO] u6bRYZZFAv=running
[[08:35:43]] [INFO] Executing action 287/576: Wait for 5 ms
[[08:35:43]] [SUCCESS] Screenshot refreshed
[[08:35:43]] [INFO] Refreshing screenshot...
[[08:35:43]] [INFO] pjFNt3w5Fr=pass
[[08:35:30]] [SUCCESS] Screenshot refreshed successfully
[[08:35:30]] [SUCCESS] Screenshot refreshed successfully
[[08:35:29]] [INFO] pjFNt3w5Fr=running
[[08:35:29]] [INFO] Executing action 286/576: Restart app: env[appid]
[[08:35:29]] [SUCCESS] Screenshot refreshed
[[08:35:29]] [INFO] Refreshing screenshot...
[[08:35:28]] [SUCCESS] Screenshot refreshed
[[08:35:28]] [INFO] Refreshing screenshot...
[[08:35:25]] [SUCCESS] Screenshot refreshed successfully
[[08:35:25]] [SUCCESS] Screenshot refreshed successfully
[[08:35:25]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:35:24]] [SUCCESS] Screenshot refreshed
[[08:35:24]] [INFO] Refreshing screenshot...
[[08:35:11]] [SUCCESS] Screenshot refreshed successfully
[[08:35:11]] [SUCCESS] Screenshot refreshed successfully
[[08:35:11]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:35:11]] [SUCCESS] Screenshot refreshed
[[08:35:11]] [INFO] Refreshing screenshot...
[[08:35:07]] [SUCCESS] Screenshot refreshed successfully
[[08:35:07]] [SUCCESS] Screenshot refreshed successfully
[[08:35:07]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:35:07]] [SUCCESS] Screenshot refreshed
[[08:35:07]] [INFO] Refreshing screenshot...
[[08:35:03]] [SUCCESS] Screenshot refreshed successfully
[[08:35:03]] [SUCCESS] Screenshot refreshed successfully
[[08:35:03]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:35:02]] [SUCCESS] Screenshot refreshed
[[08:35:02]] [INFO] Refreshing screenshot...
[[08:34:56]] [SUCCESS] Screenshot refreshed successfully
[[08:34:56]] [SUCCESS] Screenshot refreshed successfully
[[08:34:55]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:34:55]] [SUCCESS] Screenshot refreshed
[[08:34:55]] [INFO] Refreshing screenshot...
[[08:34:49]] [SUCCESS] Screenshot refreshed successfully
[[08:34:49]] [SUCCESS] Screenshot refreshed successfully
[[08:34:48]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:34:48]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:34:48]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:34:48]] [INFO] PGvsG6rpU4=running
[[08:34:48]] [INFO] Executing action 285/576: cleanupSteps action
[[08:34:48]] [SUCCESS] Screenshot refreshed
[[08:34:48]] [INFO] Refreshing screenshot...
[[08:34:48]] [INFO] LzGkAcsQyE=pass
[[08:34:45]] [SUCCESS] Screenshot refreshed successfully
[[08:34:45]] [SUCCESS] Screenshot refreshed successfully
[[08:34:45]] [INFO] LzGkAcsQyE=running
[[08:34:45]] [INFO] Executing action 284/576: Terminate app: env[appid]
[[08:34:44]] [SUCCESS] Screenshot refreshed
[[08:34:44]] [INFO] Refreshing screenshot...
[[08:34:44]] [INFO] Bdhe5AoUlM=pass
[[08:34:40]] [SUCCESS] Screenshot refreshed successfully
[[08:34:40]] [SUCCESS] Screenshot refreshed successfully
[[08:34:40]] [INFO] Bdhe5AoUlM=running
[[08:34:40]] [INFO] Executing action 283/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:34:39]] [SUCCESS] Screenshot refreshed
[[08:34:39]] [INFO] Refreshing screenshot...
[[08:34:39]] [INFO] FciJcOsMsB=pass
[[08:34:32]] [SUCCESS] Screenshot refreshed successfully
[[08:34:32]] [SUCCESS] Screenshot refreshed successfully
[[08:34:32]] [INFO] FciJcOsMsB=running
[[08:34:32]] [INFO] Executing action 282/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:34:32]] [SUCCESS] Screenshot refreshed
[[08:34:32]] [INFO] Refreshing screenshot...
[[08:34:32]] [INFO] FARWZvOj0x=pass
[[08:34:28]] [SUCCESS] Screenshot refreshed successfully
[[08:34:28]] [SUCCESS] Screenshot refreshed successfully
[[08:34:27]] [INFO] FARWZvOj0x=running
[[08:34:27]] [INFO] Executing action 281/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:34:27]] [SUCCESS] Screenshot refreshed
[[08:34:27]] [INFO] Refreshing screenshot...
[[08:34:27]] [INFO] bZCkx4U9Gk=pass
[[08:34:21]] [INFO] bZCkx4U9Gk=running
[[08:34:21]] [INFO] Executing action 280/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:34:21]] [SUCCESS] Screenshot refreshed successfully
[[08:34:21]] [SUCCESS] Screenshot refreshed successfully
[[08:34:21]] [SUCCESS] Screenshot refreshed
[[08:34:21]] [INFO] Refreshing screenshot...
[[08:34:21]] [INFO] vwFwkK6ydQ=pass
[[08:34:17]] [SUCCESS] Screenshot refreshed successfully
[[08:34:17]] [SUCCESS] Screenshot refreshed successfully
[[08:34:16]] [INFO] vwFwkK6ydQ=running
[[08:34:16]] [INFO] Executing action 279/576: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[08:34:16]] [SUCCESS] Screenshot refreshed
[[08:34:16]] [INFO] Refreshing screenshot...
[[08:34:16]] [INFO] xLGm9FefWE=pass
[[08:34:12]] [SUCCESS] Screenshot refreshed successfully
[[08:34:12]] [SUCCESS] Screenshot refreshed successfully
[[08:34:12]] [INFO] xLGm9FefWE=running
[[08:34:12]] [INFO] Executing action 278/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[08:34:11]] [SUCCESS] Screenshot refreshed
[[08:34:11]] [INFO] Refreshing screenshot...
[[08:34:11]] [INFO] UtVRXwa86e=pass
[[08:34:05]] [SUCCESS] Screenshot refreshed successfully
[[08:34:05]] [SUCCESS] Screenshot refreshed successfully
[[08:34:04]] [INFO] UtVRXwa86e=running
[[08:34:04]] [INFO] Executing action 277/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Sign in with Google"]" is visible
[[08:34:04]] [SUCCESS] Screenshot refreshed
[[08:34:04]] [INFO] Refreshing screenshot...
[[08:34:04]] [INFO] SDtskxyVpg=pass
[[08:34:00]] [SUCCESS] Screenshot refreshed successfully
[[08:34:00]] [SUCCESS] Screenshot refreshed successfully
[[08:33:59]] [INFO] SDtskxyVpg=running
[[08:33:59]] [INFO] Executing action 276/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:33:59]] [SUCCESS] Screenshot refreshed
[[08:33:59]] [INFO] Refreshing screenshot...
[[08:33:59]] [INFO] 6HhScBaqQp=pass
[[08:33:57]] [SUCCESS] Screenshot refreshed successfully
[[08:33:57]] [SUCCESS] Screenshot refreshed successfully
[[08:33:56]] [INFO] 6HhScBaqQp=running
[[08:33:56]] [INFO] Executing action 275/576: iOS Function: alert_accept
[[08:33:56]] [SUCCESS] Screenshot refreshed
[[08:33:56]] [INFO] Refreshing screenshot...
[[08:33:56]] [INFO] quzlwPw42x=pass
[[08:33:50]] [SUCCESS] Screenshot refreshed successfully
[[08:33:50]] [SUCCESS] Screenshot refreshed successfully
[[08:33:49]] [INFO] quzlwPw42x=running
[[08:33:49]] [INFO] Executing action 274/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:33:49]] [SUCCESS] Screenshot refreshed
[[08:33:49]] [INFO] Refreshing screenshot...
[[08:33:49]] [INFO] jQYHQIvQ8l=pass
[[08:33:45]] [SUCCESS] Screenshot refreshed successfully
[[08:33:45]] [SUCCESS] Screenshot refreshed successfully
[[08:33:45]] [INFO] jQYHQIvQ8l=running
[[08:33:45]] [INFO] Executing action 273/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[08:33:44]] [SUCCESS] Screenshot refreshed
[[08:33:44]] [INFO] Refreshing screenshot...
[[08:33:44]] [INFO] ts3qyFxyMf=pass
[[08:33:40]] [SUCCESS] Screenshot refreshed successfully
[[08:33:40]] [SUCCESS] Screenshot refreshed successfully
[[08:33:40]] [INFO] ts3qyFxyMf=running
[[08:33:40]] [INFO] Executing action 272/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:33:39]] [SUCCESS] Screenshot refreshed
[[08:33:39]] [INFO] Refreshing screenshot...
[[08:33:39]] [INFO] FciJcOsMsB=pass
[[08:33:32]] [SUCCESS] Screenshot refreshed successfully
[[08:33:32]] [SUCCESS] Screenshot refreshed successfully
[[08:33:32]] [INFO] FciJcOsMsB=running
[[08:33:32]] [INFO] Executing action 271/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:33:32]] [SUCCESS] Screenshot refreshed
[[08:33:32]] [INFO] Refreshing screenshot...
[[08:33:32]] [INFO] CWkqGp5ndO=pass
[[08:33:28]] [SUCCESS] Screenshot refreshed successfully
[[08:33:28]] [SUCCESS] Screenshot refreshed successfully
[[08:33:28]] [INFO] CWkqGp5ndO=running
[[08:33:28]] [INFO] Executing action 270/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:33:27]] [SUCCESS] Screenshot refreshed
[[08:33:27]] [INFO] Refreshing screenshot...
[[08:33:27]] [INFO] KfMHchi8cx=pass
[[08:33:20]] [INFO] KfMHchi8cx=running
[[08:33:20]] [INFO] Executing action 269/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:33:20]] [SUCCESS] Screenshot refreshed successfully
[[08:33:20]] [SUCCESS] Screenshot refreshed successfully
[[08:33:19]] [SUCCESS] Screenshot refreshed
[[08:33:19]] [INFO] Refreshing screenshot...
[[08:33:19]] [INFO] zsVeGHiIgX=pass
[[08:33:16]] [INFO] zsVeGHiIgX=running
[[08:33:16]] [INFO] Executing action 268/576: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[08:33:16]] [SUCCESS] Screenshot refreshed successfully
[[08:33:16]] [SUCCESS] Screenshot refreshed successfully
[[08:33:16]] [SUCCESS] Screenshot refreshed
[[08:33:16]] [INFO] Refreshing screenshot...
[[08:33:16]] [INFO] 5nsUXQ5L7u=pass
[[08:33:13]] [INFO] 5nsUXQ5L7u=running
[[08:33:13]] [INFO] Executing action 267/576: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[08:33:13]] [SUCCESS] Screenshot refreshed successfully
[[08:33:13]] [SUCCESS] Screenshot refreshed successfully
[[08:33:12]] [SUCCESS] Screenshot refreshed
[[08:33:12]] [INFO] Refreshing screenshot...
[[08:33:12]] [INFO] iSckENpXrN=pass
[[08:33:09]] [INFO] iSckENpXrN=running
[[08:33:09]] [INFO] Executing action 266/576: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[08:33:09]] [SUCCESS] Screenshot refreshed successfully
[[08:33:09]] [SUCCESS] Screenshot refreshed successfully
[[08:33:09]] [SUCCESS] Screenshot refreshed
[[08:33:09]] [INFO] Refreshing screenshot...
[[08:33:09]] [INFO] J7BPGVnRJI=pass
[[08:33:06]] [INFO] J7BPGVnRJI=running
[[08:33:06]] [INFO] Executing action 265/576: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[08:33:05]] [SUCCESS] Screenshot refreshed successfully
[[08:33:05]] [SUCCESS] Screenshot refreshed successfully
[[08:33:05]] [SUCCESS] Screenshot refreshed
[[08:33:05]] [INFO] Refreshing screenshot...
[[08:33:05]] [INFO] 0pwZCYAtOv=pass
[[08:33:02]] [INFO] 0pwZCYAtOv=running
[[08:33:02]] [INFO] Executing action 264/576: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[08:33:02]] [SUCCESS] Screenshot refreshed successfully
[[08:33:02]] [SUCCESS] Screenshot refreshed successfully
[[08:33:01]] [SUCCESS] Screenshot refreshed
[[08:33:01]] [INFO] Refreshing screenshot...
[[08:33:01]] [INFO] soKM0KayFJ=pass
[[08:32:58]] [INFO] soKM0KayFJ=running
[[08:32:58]] [INFO] Executing action 263/576: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[08:32:58]] [SUCCESS] Screenshot refreshed successfully
[[08:32:58]] [SUCCESS] Screenshot refreshed successfully
[[08:32:58]] [SUCCESS] Screenshot refreshed
[[08:32:58]] [INFO] Refreshing screenshot...
[[08:32:58]] [INFO] hnH3ayslCh=pass
[[08:32:54]] [INFO] hnH3ayslCh=running
[[08:32:54]] [INFO] Executing action 262/576: Tap on Text: "Passcode"
[[08:32:54]] [SUCCESS] Screenshot refreshed successfully
[[08:32:54]] [SUCCESS] Screenshot refreshed successfully
[[08:32:54]] [SUCCESS] Screenshot refreshed
[[08:32:54]] [INFO] Refreshing screenshot...
[[08:32:54]] [INFO] CzVeOTdAX9=pass
[[08:32:42]] [SUCCESS] Screenshot refreshed successfully
[[08:32:42]] [SUCCESS] Screenshot refreshed successfully
[[08:32:42]] [INFO] CzVeOTdAX9=running
[[08:32:42]] [INFO] Executing action 261/576: Wait for 10 ms
[[08:32:42]] [SUCCESS] Screenshot refreshed
[[08:32:42]] [INFO] Refreshing screenshot...
[[08:32:42]] [INFO] NL2gtj6qIu=pass
[[08:32:37]] [SUCCESS] Screenshot refreshed successfully
[[08:32:37]] [SUCCESS] Screenshot refreshed successfully
[[08:32:37]] [INFO] NL2gtj6qIu=running
[[08:32:37]] [INFO] Executing action 260/576: Tap on Text: "Apple"
[[08:32:36]] [SUCCESS] Screenshot refreshed
[[08:32:36]] [INFO] Refreshing screenshot...
[[08:32:36]] [INFO] VsSlyhXuVD=pass
[[08:32:33]] [SUCCESS] Screenshot refreshed successfully
[[08:32:33]] [SUCCESS] Screenshot refreshed successfully
[[08:32:31]] [INFO] VsSlyhXuVD=running
[[08:32:31]] [INFO] Executing action 259/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:32:31]] [SUCCESS] Screenshot refreshed
[[08:32:31]] [INFO] Refreshing screenshot...
[[08:32:31]] [INFO] CJ88OgjKXp=pass
[[08:32:27]] [SUCCESS] Screenshot refreshed successfully
[[08:32:27]] [SUCCESS] Screenshot refreshed successfully
[[08:32:27]] [INFO] CJ88OgjKXp=running
[[08:32:27]] [INFO] Executing action 258/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:32:26]] [SUCCESS] Screenshot refreshed
[[08:32:26]] [INFO] Refreshing screenshot...
[[08:32:26]] [INFO] AYiwFSLTBD=pass
[[08:32:24]] [SUCCESS] Screenshot refreshed successfully
[[08:32:24]] [SUCCESS] Screenshot refreshed successfully
[[08:32:24]] [INFO] AYiwFSLTBD=running
[[08:32:24]] [INFO] Executing action 257/576: iOS Function: alert_accept
[[08:32:23]] [SUCCESS] Screenshot refreshed
[[08:32:23]] [INFO] Refreshing screenshot...
[[08:32:23]] [INFO] HJzOYZNnGr=pass
[[08:32:17]] [SUCCESS] Screenshot refreshed successfully
[[08:32:17]] [SUCCESS] Screenshot refreshed successfully
[[08:32:17]] [INFO] HJzOYZNnGr=running
[[08:32:17]] [INFO] Executing action 256/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:32:16]] [SUCCESS] Screenshot refreshed
[[08:32:16]] [INFO] Refreshing screenshot...
[[08:32:16]] [INFO] taf19mtrUT=pass
[[08:32:13]] [SUCCESS] Screenshot refreshed successfully
[[08:32:13]] [SUCCESS] Screenshot refreshed successfully
[[08:32:12]] [INFO] taf19mtrUT=running
[[08:32:12]] [INFO] Executing action 255/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[08:32:12]] [SUCCESS] Screenshot refreshed
[[08:32:12]] [INFO] Refreshing screenshot...
[[08:32:12]] [INFO] oiPcknTonJ=pass
[[08:32:07]] [SUCCESS] Screenshot refreshed successfully
[[08:32:07]] [SUCCESS] Screenshot refreshed successfully
[[08:32:07]] [INFO] oiPcknTonJ=running
[[08:32:07]] [INFO] Executing action 254/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:32:07]] [SUCCESS] Screenshot refreshed
[[08:32:07]] [INFO] Refreshing screenshot...
[[08:32:07]] [INFO] FciJcOsMsB=pass
[[08:32:02]] [SUCCESS] Screenshot refreshed successfully
[[08:32:02]] [SUCCESS] Screenshot refreshed successfully
[[08:32:01]] [INFO] FciJcOsMsB=running
[[08:32:01]] [INFO] Executing action 253/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:32:01]] [SUCCESS] Screenshot refreshed
[[08:32:01]] [INFO] Refreshing screenshot...
[[08:32:01]] [INFO] 2qOXZcEmK8=pass
[[08:31:57]] [SUCCESS] Screenshot refreshed successfully
[[08:31:57]] [SUCCESS] Screenshot refreshed successfully
[[08:31:57]] [INFO] 2qOXZcEmK8=running
[[08:31:57]] [INFO] Executing action 252/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:31:56]] [SUCCESS] Screenshot refreshed
[[08:31:56]] [INFO] Refreshing screenshot...
[[08:31:56]] [INFO] M6HdLxu76S=pass
[[08:31:52]] [SUCCESS] Screenshot refreshed successfully
[[08:31:52]] [SUCCESS] Screenshot refreshed successfully
[[08:31:52]] [INFO] M6HdLxu76S=running
[[08:31:52]] [INFO] Executing action 251/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:31:51]] [SUCCESS] Screenshot refreshed
[[08:31:51]] [INFO] Refreshing screenshot...
[[08:31:51]] [INFO] pCPTAtSZbf=pass
[[08:31:47]] [SUCCESS] Screenshot refreshed successfully
[[08:31:47]] [SUCCESS] Screenshot refreshed successfully
[[08:31:47]] [INFO] pCPTAtSZbf=running
[[08:31:47]] [INFO] Executing action 250/576: iOS Function: text - Text: "Wonderbaby@5"
[[08:31:46]] [SUCCESS] Screenshot refreshed
[[08:31:46]] [INFO] Refreshing screenshot...
[[08:31:46]] [INFO] DaVBARRwft=pass
[[08:31:42]] [SUCCESS] Screenshot refreshed successfully
[[08:31:42]] [SUCCESS] Screenshot refreshed successfully
[[08:31:41]] [INFO] DaVBARRwft=running
[[08:31:41]] [INFO] Executing action 249/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[08:31:41]] [SUCCESS] Screenshot refreshed
[[08:31:41]] [INFO] Refreshing screenshot...
[[08:31:41]] [INFO] e1RoZWCZJb=pass
[[08:31:36]] [SUCCESS] Screenshot refreshed successfully
[[08:31:36]] [SUCCESS] Screenshot refreshed successfully
[[08:31:36]] [INFO] e1RoZWCZJb=running
[[08:31:36]] [INFO] Executing action 248/576: iOS Function: text - Text: "<EMAIL>"
[[08:31:36]] [SUCCESS] Screenshot refreshed
[[08:31:36]] [INFO] Refreshing screenshot...
[[08:31:36]] [INFO] y8ZMTkG38M=pass
[[08:31:32]] [SUCCESS] Screenshot refreshed successfully
[[08:31:32]] [SUCCESS] Screenshot refreshed successfully
[[08:31:31]] [INFO] y8ZMTkG38M=running
[[08:31:31]] [INFO] Executing action 247/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[08:31:31]] [SUCCESS] Screenshot refreshed
[[08:31:31]] [INFO] Refreshing screenshot...
[[08:31:31]] [INFO] UUhQjmzfO2=pass
[[08:31:25]] [SUCCESS] Screenshot refreshed successfully
[[08:31:25]] [SUCCESS] Screenshot refreshed successfully
[[08:31:25]] [INFO] UUhQjmzfO2=running
[[08:31:25]] [INFO] Executing action 246/576: Tap on Text: "OnePass"
[[08:31:25]] [SUCCESS] Screenshot refreshed
[[08:31:25]] [INFO] Refreshing screenshot...
[[08:31:25]] [INFO] FciJcOsMsB=pass
[[08:31:20]] [SUCCESS] Screenshot refreshed successfully
[[08:31:20]] [SUCCESS] Screenshot refreshed successfully
[[08:31:20]] [INFO] FciJcOsMsB=running
[[08:31:20]] [INFO] Executing action 245/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:31:19]] [SUCCESS] Screenshot refreshed
[[08:31:19]] [INFO] Refreshing screenshot...
[[08:31:19]] [INFO] NCyuT8W5Xz=pass
[[08:31:16]] [SUCCESS] Screenshot refreshed successfully
[[08:31:16]] [SUCCESS] Screenshot refreshed successfully
[[08:31:16]] [INFO] NCyuT8W5Xz=running
[[08:31:16]] [INFO] Executing action 244/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:31:15]] [SUCCESS] Screenshot refreshed
[[08:31:15]] [INFO] Refreshing screenshot...
[[08:31:15]] [INFO] 2kwu2VBmuZ=pass
[[08:31:13]] [SUCCESS] Screenshot refreshed successfully
[[08:31:13]] [SUCCESS] Screenshot refreshed successfully
[[08:31:12]] [INFO] 2kwu2VBmuZ=running
[[08:31:12]] [INFO] Executing action 243/576: iOS Function: alert_accept
[[08:31:12]] [SUCCESS] Screenshot refreshed
[[08:31:12]] [INFO] Refreshing screenshot...
[[08:31:12]] [INFO] cJDpd7aK3d=pass
[[08:31:06]] [SUCCESS] Screenshot refreshed successfully
[[08:31:06]] [SUCCESS] Screenshot refreshed successfully
[[08:31:06]] [INFO] cJDpd7aK3d=running
[[08:31:06]] [INFO] Executing action 242/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:31:05]] [SUCCESS] Screenshot refreshed
[[08:31:05]] [INFO] Refreshing screenshot...
[[08:31:05]] [INFO] FlEukNkjlS=pass
[[08:31:01]] [SUCCESS] Screenshot refreshed successfully
[[08:31:01]] [SUCCESS] Screenshot refreshed successfully
[[08:31:01]] [INFO] FlEukNkjlS=running
[[08:31:01]] [INFO] Executing action 241/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[08:31:00]] [SUCCESS] Screenshot refreshed
[[08:31:00]] [INFO] Refreshing screenshot...
[[08:31:00]] [INFO] LlRfimKPrn=pass
[[08:30:56]] [SUCCESS] Screenshot refreshed successfully
[[08:30:56]] [SUCCESS] Screenshot refreshed successfully
[[08:30:56]] [INFO] LlRfimKPrn=running
[[08:30:56]] [INFO] Executing action 240/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:30:55]] [SUCCESS] Screenshot refreshed
[[08:30:55]] [INFO] Refreshing screenshot...
[[08:30:55]] [INFO] FciJcOsMsB=pass
[[08:30:48]] [SUCCESS] Screenshot refreshed successfully
[[08:30:48]] [SUCCESS] Screenshot refreshed successfully
[[08:30:48]] [INFO] FciJcOsMsB=running
[[08:30:48]] [INFO] Executing action 239/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:30:48]] [SUCCESS] Screenshot refreshed
[[08:30:48]] [INFO] Refreshing screenshot...
[[08:30:48]] [INFO] 08NzsvhQXK=pass
[[08:30:44]] [SUCCESS] Screenshot refreshed successfully
[[08:30:44]] [SUCCESS] Screenshot refreshed successfully
[[08:30:44]] [INFO] 08NzsvhQXK=running
[[08:30:44]] [INFO] Executing action 238/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:30:43]] [SUCCESS] Screenshot refreshed
[[08:30:43]] [INFO] Refreshing screenshot...
[[08:30:43]] [INFO] IsGWxLFpIn=pass
[[08:30:40]] [SUCCESS] Screenshot refreshed successfully
[[08:30:40]] [SUCCESS] Screenshot refreshed successfully
[[08:30:40]] [INFO] IsGWxLFpIn=running
[[08:30:40]] [INFO] Executing action 237/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:30:39]] [SUCCESS] Screenshot refreshed
[[08:30:39]] [INFO] Refreshing screenshot...
[[08:30:39]] [INFO] dyECdbRifp=pass
[[08:30:34]] [SUCCESS] Screenshot refreshed successfully
[[08:30:34]] [SUCCESS] Screenshot refreshed successfully
[[08:30:34]] [INFO] dyECdbRifp=running
[[08:30:34]] [INFO] Executing action 236/576: iOS Function: text - Text: "Wonderbaby@5"
[[08:30:34]] [SUCCESS] Screenshot refreshed
[[08:30:34]] [INFO] Refreshing screenshot...
[[08:30:34]] [INFO] I5bRbYY1hD=pass
[[08:30:29]] [SUCCESS] Screenshot refreshed successfully
[[08:30:29]] [SUCCESS] Screenshot refreshed successfully
[[08:30:29]] [INFO] I5bRbYY1hD=running
[[08:30:29]] [INFO] Executing action 235/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:30:28]] [SUCCESS] Screenshot refreshed
[[08:30:28]] [INFO] Refreshing screenshot...
[[08:30:28]] [INFO] WMl5g82CCq=pass
[[08:30:23]] [SUCCESS] Screenshot refreshed successfully
[[08:30:23]] [SUCCESS] Screenshot refreshed successfully
[[08:30:23]] [INFO] WMl5g82CCq=running
[[08:30:23]] [INFO] Executing action 234/576: iOS Function: text - Text: "<EMAIL>"
[[08:30:22]] [SUCCESS] Screenshot refreshed
[[08:30:22]] [INFO] Refreshing screenshot...
[[08:30:22]] [INFO] 8OsQmoVYqW=pass
[[08:30:18]] [SUCCESS] Screenshot refreshed successfully
[[08:30:18]] [SUCCESS] Screenshot refreshed successfully
[[08:30:18]] [INFO] 8OsQmoVYqW=running
[[08:30:18]] [INFO] Executing action 233/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:30:17]] [SUCCESS] Screenshot refreshed
[[08:30:17]] [INFO] Refreshing screenshot...
[[08:30:17]] [INFO] ImienLpJEN=pass
[[08:30:14]] [SUCCESS] Screenshot refreshed successfully
[[08:30:14]] [SUCCESS] Screenshot refreshed successfully
[[08:30:14]] [INFO] ImienLpJEN=running
[[08:30:14]] [INFO] Executing action 232/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:30:13]] [SUCCESS] Screenshot refreshed
[[08:30:13]] [INFO] Refreshing screenshot...
[[08:30:13]] [INFO] q4hPXCBtx4=pass
[[08:30:11]] [SUCCESS] Screenshot refreshed successfully
[[08:30:11]] [SUCCESS] Screenshot refreshed successfully
[[08:30:10]] [INFO] q4hPXCBtx4=running
[[08:30:10]] [INFO] Executing action 231/576: iOS Function: alert_accept
[[08:30:10]] [SUCCESS] Screenshot refreshed
[[08:30:10]] [INFO] Refreshing screenshot...
[[08:30:10]] [INFO] 2cTZvK1psn=pass
[[08:30:04]] [SUCCESS] Screenshot refreshed successfully
[[08:30:04]] [SUCCESS] Screenshot refreshed successfully
[[08:30:04]] [INFO] 2cTZvK1psn=running
[[08:30:04]] [INFO] Executing action 230/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:30:03]] [SUCCESS] Screenshot refreshed
[[08:30:03]] [INFO] Refreshing screenshot...
[[08:30:03]] [INFO] Vxt7QOYeDD=pass
[[08:29:50]] [SUCCESS] Screenshot refreshed successfully
[[08:29:50]] [SUCCESS] Screenshot refreshed successfully
[[08:29:49]] [INFO] Vxt7QOYeDD=running
[[08:29:49]] [INFO] Executing action 229/576: Restart app: env[appid]
[[08:29:49]] [SUCCESS] Screenshot refreshed
[[08:29:49]] [INFO] Refreshing screenshot...
[[08:29:49]] [SUCCESS] Screenshot refreshed
[[08:29:49]] [INFO] Refreshing screenshot...
[[08:29:45]] [SUCCESS] Screenshot refreshed successfully
[[08:29:45]] [SUCCESS] Screenshot refreshed successfully
[[08:29:45]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:29:45]] [SUCCESS] Screenshot refreshed
[[08:29:45]] [INFO] Refreshing screenshot...
[[08:29:32]] [SUCCESS] Screenshot refreshed successfully
[[08:29:32]] [SUCCESS] Screenshot refreshed successfully
[[08:29:32]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:29:32]] [SUCCESS] Screenshot refreshed
[[08:29:32]] [INFO] Refreshing screenshot...
[[08:29:28]] [SUCCESS] Screenshot refreshed successfully
[[08:29:28]] [SUCCESS] Screenshot refreshed successfully
[[08:29:28]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:29:28]] [SUCCESS] Screenshot refreshed
[[08:29:28]] [INFO] Refreshing screenshot...
[[08:29:23]] [SUCCESS] Screenshot refreshed successfully
[[08:29:23]] [SUCCESS] Screenshot refreshed successfully
[[08:29:23]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:29:22]] [SUCCESS] Screenshot refreshed
[[08:29:22]] [INFO] Refreshing screenshot...
[[08:29:16]] [SUCCESS] Screenshot refreshed successfully
[[08:29:16]] [SUCCESS] Screenshot refreshed successfully
[[08:29:15]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:29:15]] [SUCCESS] Screenshot refreshed
[[08:29:15]] [INFO] Refreshing screenshot...
[[08:29:08]] [SUCCESS] Screenshot refreshed successfully
[[08:29:08]] [SUCCESS] Screenshot refreshed successfully
[[08:29:08]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:29:08]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:29:08]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:29:08]] [INFO] DYWpUY7xB6=running
[[08:29:08]] [INFO] Executing action 228/576: cleanupSteps action
[[08:29:07]] [SUCCESS] Screenshot refreshed
[[08:29:07]] [INFO] Refreshing screenshot...
[[08:29:07]] [INFO] OyUowAaBzD=pass
[[08:29:03]] [SUCCESS] Screenshot refreshed successfully
[[08:29:03]] [SUCCESS] Screenshot refreshed successfully
[[08:29:03]] [INFO] OyUowAaBzD=running
[[08:29:03]] [INFO] Executing action 227/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:29:02]] [SUCCESS] Screenshot refreshed
[[08:29:02]] [INFO] Refreshing screenshot...
[[08:29:02]] [INFO] Ob26qqcA0p=pass
[[08:28:55]] [SUCCESS] Screenshot refreshed successfully
[[08:28:55]] [SUCCESS] Screenshot refreshed successfully
[[08:28:55]] [INFO] Ob26qqcA0p=running
[[08:28:55]] [INFO] Executing action 226/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:28:55]] [SUCCESS] Screenshot refreshed
[[08:28:55]] [INFO] Refreshing screenshot...
[[08:28:55]] [INFO] k3mu9Mt7Ec=pass
[[08:28:51]] [SUCCESS] Screenshot refreshed successfully
[[08:28:51]] [SUCCESS] Screenshot refreshed successfully
[[08:28:51]] [INFO] k3mu9Mt7Ec=running
[[08:28:51]] [INFO] Executing action 225/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:28:50]] [SUCCESS] Screenshot refreshed
[[08:28:50]] [INFO] Refreshing screenshot...
[[08:28:50]] [INFO] yhmzeynQyu=pass
[[08:28:46]] [SUCCESS] Screenshot refreshed successfully
[[08:28:46]] [SUCCESS] Screenshot refreshed successfully
[[08:28:46]] [INFO] yhmzeynQyu=running
[[08:28:46]] [INFO] Executing action 224/576: Tap on Text: "Remove"
[[08:28:46]] [SUCCESS] Screenshot refreshed
[[08:28:46]] [INFO] Refreshing screenshot...
[[08:28:46]] [INFO] Q0fomJIDoQ=pass
[[08:28:40]] [SUCCESS] Screenshot refreshed successfully
[[08:28:40]] [SUCCESS] Screenshot refreshed successfully
[[08:28:40]] [INFO] Q0fomJIDoQ=running
[[08:28:40]] [INFO] Executing action 223/576: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[08:28:40]] [SUCCESS] Screenshot refreshed
[[08:28:40]] [INFO] Refreshing screenshot...
[[08:28:40]] [INFO] yhmzeynQyu=pass
[[08:28:35]] [SUCCESS] Screenshot refreshed successfully
[[08:28:35]] [SUCCESS] Screenshot refreshed successfully
[[08:28:35]] [INFO] yhmzeynQyu=running
[[08:28:35]] [INFO] Executing action 222/576: Tap on Text: "Remove"
[[08:28:35]] [SUCCESS] Screenshot refreshed
[[08:28:35]] [INFO] Refreshing screenshot...
[[08:28:35]] [INFO] Q0fomJIDoQ=pass
[[08:28:29]] [SUCCESS] Screenshot refreshed successfully
[[08:28:29]] [SUCCESS] Screenshot refreshed successfully
[[08:28:29]] [INFO] Q0fomJIDoQ=running
[[08:28:29]] [INFO] Executing action 221/576: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[08:28:28]] [SUCCESS] Screenshot refreshed
[[08:28:28]] [INFO] Refreshing screenshot...
[[08:28:28]] [INFO] F1olhgKhUt=pass
[[08:28:25]] [SUCCESS] Screenshot refreshed successfully
[[08:28:25]] [SUCCESS] Screenshot refreshed successfully
[[08:28:24]] [INFO] F1olhgKhUt=running
[[08:28:24]] [INFO] Executing action 220/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[08:28:24]] [SUCCESS] Screenshot refreshed
[[08:28:24]] [INFO] Refreshing screenshot...
[[08:28:24]] [INFO] 8umPSX0vrr=pass
[[08:28:19]] [SUCCESS] Screenshot refreshed successfully
[[08:28:19]] [SUCCESS] Screenshot refreshed successfully
[[08:28:19]] [INFO] 8umPSX0vrr=running
[[08:28:19]] [INFO] Executing action 219/576: Tap on image: banner-close-updated.png
[[08:28:19]] [SUCCESS] Screenshot refreshed
[[08:28:19]] [INFO] Refreshing screenshot...
[[08:28:19]] [INFO] pr9o8Zsm5p=pass
[[08:28:15]] [SUCCESS] Screenshot refreshed successfully
[[08:28:15]] [SUCCESS] Screenshot refreshed successfully
[[08:28:15]] [INFO] pr9o8Zsm5p=running
[[08:28:15]] [INFO] Executing action 218/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[08:28:14]] [SUCCESS] Screenshot refreshed
[[08:28:14]] [INFO] Refreshing screenshot...
[[08:28:14]] [INFO] Qbg9bipTGs=pass
[[08:28:11]] [SUCCESS] Screenshot refreshed successfully
[[08:28:11]] [SUCCESS] Screenshot refreshed successfully
[[08:28:11]] [INFO] Qbg9bipTGs=running
[[08:28:11]] [INFO] Executing action 217/576: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[08:28:10]] [SUCCESS] Screenshot refreshed
[[08:28:10]] [INFO] Refreshing screenshot...
[[08:28:10]] [INFO] Ob26qqcA0p=pass
[[08:28:06]] [SUCCESS] Screenshot refreshed successfully
[[08:28:06]] [SUCCESS] Screenshot refreshed successfully
[[08:28:06]] [INFO] Ob26qqcA0p=running
[[08:28:06]] [INFO] Executing action 216/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:28:05]] [SUCCESS] Screenshot refreshed
[[08:28:05]] [INFO] Refreshing screenshot...
[[08:28:05]] [INFO] lWIRxRm6HE=pass
[[08:28:01]] [SUCCESS] Screenshot refreshed successfully
[[08:28:01]] [SUCCESS] Screenshot refreshed successfully
[[08:28:00]] [INFO] lWIRxRm6HE=running
[[08:28:00]] [INFO] Executing action 215/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:28:00]] [SUCCESS] Screenshot refreshed
[[08:28:00]] [INFO] Refreshing screenshot...
[[08:28:00]] [INFO] uOt2cFGhGr=pass
[[08:27:56]] [SUCCESS] Screenshot refreshed successfully
[[08:27:56]] [SUCCESS] Screenshot refreshed successfully
[[08:27:56]] [INFO] uOt2cFGhGr=running
[[08:27:56]] [INFO] Executing action 214/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:27:56]] [SUCCESS] Screenshot refreshed
[[08:27:56]] [INFO] Refreshing screenshot...
[[08:27:56]] [INFO] Q0fomJIDoQ=pass
[[08:27:52]] [SUCCESS] Screenshot refreshed successfully
[[08:27:52]] [SUCCESS] Screenshot refreshed successfully
[[08:27:52]] [INFO] Q0fomJIDoQ=running
[[08:27:52]] [INFO] Executing action 213/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[08:27:51]] [SUCCESS] Screenshot refreshed
[[08:27:51]] [INFO] Refreshing screenshot...
[[08:27:51]] [INFO] yhmzeynQyu=pass
[[08:27:47]] [SUCCESS] Screenshot refreshed successfully
[[08:27:47]] [SUCCESS] Screenshot refreshed successfully
[[08:27:47]] [INFO] yhmzeynQyu=running
[[08:27:47]] [INFO] Executing action 212/576: Tap on Text: "Remove"
[[08:27:46]] [SUCCESS] Screenshot refreshed
[[08:27:46]] [INFO] Refreshing screenshot...
[[08:27:46]] [INFO] Q0fomJIDoQ=pass
[[08:27:42]] [SUCCESS] Screenshot refreshed successfully
[[08:27:42]] [SUCCESS] Screenshot refreshed successfully
[[08:27:42]] [INFO] Q0fomJIDoQ=running
[[08:27:42]] [INFO] Executing action 211/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[08:27:42]] [SUCCESS] Screenshot refreshed
[[08:27:42]] [INFO] Refreshing screenshot...
[[08:27:42]] [INFO] y4i304JeJj=pass
[[08:27:37]] [SUCCESS] Screenshot refreshed successfully
[[08:27:37]] [SUCCESS] Screenshot refreshed successfully
[[08:27:37]] [INFO] y4i304JeJj=running
[[08:27:37]] [INFO] Executing action 210/576: Tap on Text: "Move"
[[08:27:37]] [SUCCESS] Screenshot refreshed
[[08:27:37]] [INFO] Refreshing screenshot...
[[08:27:37]] [INFO] Q0fomJIDoQ=pass
[[08:27:33]] [SUCCESS] Screenshot refreshed successfully
[[08:27:33]] [SUCCESS] Screenshot refreshed successfully
[[08:27:33]] [INFO] Q0fomJIDoQ=running
[[08:27:33]] [INFO] Executing action 209/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[08:27:32]] [SUCCESS] Screenshot refreshed
[[08:27:32]] [INFO] Refreshing screenshot...
[[08:27:32]] [INFO] Q0fomJIDoQ=pass
[[08:27:29]] [SUCCESS] Screenshot refreshed successfully
[[08:27:29]] [SUCCESS] Screenshot refreshed successfully
[[08:27:29]] [INFO] Q0fomJIDoQ=running
[[08:27:29]] [INFO] Executing action 208/576: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[08:27:28]] [SUCCESS] Screenshot refreshed
[[08:27:28]] [INFO] Refreshing screenshot...
[[08:27:28]] [INFO] F1olhgKhUt=pass
[[08:27:24]] [SUCCESS] Screenshot refreshed successfully
[[08:27:24]] [SUCCESS] Screenshot refreshed successfully
[[08:27:23]] [INFO] F1olhgKhUt=running
[[08:27:23]] [INFO] Executing action 207/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[08:27:23]] [SUCCESS] Screenshot refreshed
[[08:27:23]] [INFO] Refreshing screenshot...
[[08:27:23]] [INFO] WbxRVpWtjw=pass
[[08:27:18]] [SUCCESS] Screenshot refreshed successfully
[[08:27:18]] [SUCCESS] Screenshot refreshed successfully
[[08:27:18]] [INFO] WbxRVpWtjw=running
[[08:27:18]] [INFO] Executing action 206/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[08:27:18]] [SUCCESS] Screenshot refreshed
[[08:27:18]] [INFO] Refreshing screenshot...
[[08:27:18]] [INFO] H3IAmq3r3i=pass
[[08:27:11]] [SUCCESS] Screenshot refreshed successfully
[[08:27:11]] [SUCCESS] Screenshot refreshed successfully
[[08:27:10]] [INFO] H3IAmq3r3i=running
[[08:27:10]] [INFO] Executing action 205/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[08:27:10]] [SUCCESS] Screenshot refreshed
[[08:27:10]] [INFO] Refreshing screenshot...
[[08:27:10]] [INFO] uOt2cFGhGr=pass
[[08:27:06]] [SUCCESS] Screenshot refreshed successfully
[[08:27:06]] [SUCCESS] Screenshot refreshed successfully
[[08:27:06]] [INFO] uOt2cFGhGr=running
[[08:27:06]] [INFO] Executing action 204/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:27:05]] [SUCCESS] Screenshot refreshed
[[08:27:05]] [INFO] Refreshing screenshot...
[[08:27:05]] [INFO] eLxHVWKeDQ=pass
[[08:27:02]] [SUCCESS] Screenshot refreshed successfully
[[08:27:02]] [SUCCESS] Screenshot refreshed successfully
[[08:27:01]] [INFO] eLxHVWKeDQ=running
[[08:27:01]] [INFO] Executing action 203/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:27:01]] [SUCCESS] Screenshot refreshed
[[08:27:01]] [INFO] Refreshing screenshot...
[[08:27:01]] [INFO] ghzdMuwrHj=pass
[[08:26:56]] [SUCCESS] Screenshot refreshed successfully
[[08:26:56]] [SUCCESS] Screenshot refreshed successfully
[[08:26:56]] [INFO] ghzdMuwrHj=running
[[08:26:56]] [INFO] Executing action 202/576: iOS Function: text - Text: "P_43386093"
[[08:26:56]] [SUCCESS] Screenshot refreshed
[[08:26:56]] [INFO] Refreshing screenshot...
[[08:26:56]] [INFO] fMzoZJg9I7=pass
[[08:26:51]] [SUCCESS] Screenshot refreshed successfully
[[08:26:51]] [SUCCESS] Screenshot refreshed successfully
[[08:26:50]] [INFO] fMzoZJg9I7=running
[[08:26:50]] [INFO] Executing action 201/576: Tap on Text: "Find"
[[08:26:50]] [SUCCESS] Screenshot refreshed
[[08:26:50]] [INFO] Refreshing screenshot...
[[08:26:50]] [INFO] j1JjmfPRaE=pass
[[08:26:45]] [SUCCESS] Screenshot refreshed successfully
[[08:26:45]] [SUCCESS] Screenshot refreshed successfully
[[08:26:44]] [INFO] j1JjmfPRaE=running
[[08:26:44]] [INFO] Executing action 200/576: Restart app: env[appid]
[[08:26:44]] [SUCCESS] Screenshot refreshed
[[08:26:44]] [INFO] Refreshing screenshot...
[[08:26:44]] [INFO] WbxRVpWtjw=pass
[[08:26:40]] [SUCCESS] Screenshot refreshed successfully
[[08:26:40]] [SUCCESS] Screenshot refreshed successfully
[[08:26:40]] [INFO] WbxRVpWtjw=running
[[08:26:40]] [INFO] Executing action 199/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[08:26:39]] [SUCCESS] Screenshot refreshed
[[08:26:39]] [INFO] Refreshing screenshot...
[[08:26:39]] [INFO] H3IAmq3r3i=pass
[[08:26:33]] [SUCCESS] Screenshot refreshed successfully
[[08:26:33]] [SUCCESS] Screenshot refreshed successfully
[[08:26:33]] [INFO] H3IAmq3r3i=running
[[08:26:33]] [INFO] Executing action 198/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[08:26:32]] [SUCCESS] Screenshot refreshed
[[08:26:32]] [INFO] Refreshing screenshot...
[[08:26:32]] [INFO] ITHvSyXXmu=pass
[[08:26:29]] [SUCCESS] Screenshot refreshed successfully
[[08:26:29]] [SUCCESS] Screenshot refreshed successfully
[[08:26:28]] [INFO] ITHvSyXXmu=running
[[08:26:28]] [INFO] Executing action 197/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:26:28]] [SUCCESS] Screenshot refreshed
[[08:26:28]] [INFO] Refreshing screenshot...
[[08:26:28]] [INFO] eLxHVWKeDQ=pass
[[08:26:12]] [SUCCESS] Screenshot refreshed successfully
[[08:26:12]] [SUCCESS] Screenshot refreshed successfully
[[08:26:12]] [INFO] eLxHVWKeDQ=running
[[08:26:12]] [INFO] Executing action 196/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[08:26:11]] [SUCCESS] Screenshot refreshed
[[08:26:11]] [INFO] Refreshing screenshot...
[[08:26:11]] [INFO] WbxRVpWtjw=pass
[[08:26:07]] [SUCCESS] Screenshot refreshed successfully
[[08:26:07]] [SUCCESS] Screenshot refreshed successfully
[[08:26:07]] [INFO] WbxRVpWtjw=running
[[08:26:07]] [INFO] Executing action 195/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[08:26:06]] [SUCCESS] Screenshot refreshed
[[08:26:06]] [INFO] Refreshing screenshot...
[[08:26:06]] [INFO] H3IAmq3r3i=pass
[[08:25:59]] [SUCCESS] Screenshot refreshed successfully
[[08:25:59]] [SUCCESS] Screenshot refreshed successfully
[[08:25:59]] [INFO] H3IAmq3r3i=running
[[08:25:59]] [INFO] Executing action 194/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[08:25:58]] [SUCCESS] Screenshot refreshed
[[08:25:58]] [INFO] Refreshing screenshot...
[[08:25:58]] [INFO] ITHvSyXXmu=pass
[[08:25:53]] [SUCCESS] Screenshot refreshed successfully
[[08:25:53]] [SUCCESS] Screenshot refreshed successfully
[[08:25:53]] [INFO] ITHvSyXXmu=running
[[08:25:53]] [INFO] Executing action 193/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:25:53]] [SUCCESS] Screenshot refreshed
[[08:25:53]] [INFO] Refreshing screenshot...
[[08:25:53]] [INFO] eLxHVWKeDQ=pass
[[08:25:49]] [SUCCESS] Screenshot refreshed successfully
[[08:25:49]] [SUCCESS] Screenshot refreshed successfully
[[08:25:48]] [INFO] eLxHVWKeDQ=running
[[08:25:48]] [INFO] Executing action 192/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:25:48]] [SUCCESS] Screenshot refreshed
[[08:25:48]] [INFO] Refreshing screenshot...
[[08:25:48]] [INFO] nAB6Q8LAdv=pass
[[08:25:44]] [SUCCESS] Screenshot refreshed successfully
[[08:25:44]] [SUCCESS] Screenshot refreshed successfully
[[08:25:44]] [INFO] nAB6Q8LAdv=running
[[08:25:44]] [INFO] Executing action 191/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:25:43]] [SUCCESS] Screenshot refreshed
[[08:25:43]] [INFO] Refreshing screenshot...
[[08:25:43]] [INFO] sc2KH9bG6H=pass
[[08:25:39]] [SUCCESS] Screenshot refreshed successfully
[[08:25:39]] [SUCCESS] Screenshot refreshed successfully
[[08:25:39]] [INFO] sc2KH9bG6H=running
[[08:25:39]] [INFO] Executing action 190/576: iOS Function: text - Text: "Uno card"
[[08:25:38]] [SUCCESS] Screenshot refreshed
[[08:25:38]] [INFO] Refreshing screenshot...
[[08:25:38]] [INFO] rqLJpAP0mA=pass
[[08:25:33]] [SUCCESS] Screenshot refreshed successfully
[[08:25:33]] [SUCCESS] Screenshot refreshed successfully
[[08:25:33]] [INFO] rqLJpAP0mA=running
[[08:25:33]] [INFO] Executing action 189/576: Tap on Text: "Find"
[[08:25:32]] [SUCCESS] Screenshot refreshed
[[08:25:32]] [INFO] Refreshing screenshot...
[[08:25:32]] [INFO] yiKyF5FJwN=pass
[[08:25:28]] [SUCCESS] Screenshot refreshed successfully
[[08:25:28]] [SUCCESS] Screenshot refreshed successfully
[[08:25:28]] [INFO] yiKyF5FJwN=running
[[08:25:28]] [INFO] Executing action 188/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:25:27]] [SUCCESS] Screenshot refreshed
[[08:25:27]] [INFO] Refreshing screenshot...
[[08:25:27]] [INFO] YqMEb5Jr6o=pass
[[08:25:22]] [SUCCESS] Screenshot refreshed successfully
[[08:25:22]] [SUCCESS] Screenshot refreshed successfully
[[08:25:22]] [INFO] YqMEb5Jr6o=running
[[08:25:22]] [INFO] Executing action 187/576: iOS Function: text - Text: "Wonderbaby@6"
[[08:25:21]] [SUCCESS] Screenshot refreshed
[[08:25:21]] [INFO] Refreshing screenshot...
[[08:25:21]] [INFO] T3MmUw30SF=pass
[[08:25:17]] [SUCCESS] Screenshot refreshed successfully
[[08:25:17]] [SUCCESS] Screenshot refreshed successfully
[[08:25:17]] [INFO] T3MmUw30SF=running
[[08:25:17]] [INFO] Executing action 186/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:25:16]] [SUCCESS] Screenshot refreshed
[[08:25:16]] [INFO] Refreshing screenshot...
[[08:25:16]] [INFO] 3FBGGKUMbh=pass
[[08:25:11]] [SUCCESS] Screenshot refreshed successfully
[[08:25:11]] [SUCCESS] Screenshot refreshed successfully
[[08:25:11]] [INFO] 3FBGGKUMbh=running
[[08:25:11]] [INFO] Executing action 185/576: iOS Function: text - Text: "env[uname-op]"
[[08:25:11]] [SUCCESS] Screenshot refreshed
[[08:25:11]] [INFO] Refreshing screenshot...
[[08:25:11]] [INFO] LDkFLWks00=pass
[[08:25:07]] [SUCCESS] Screenshot refreshed successfully
[[08:25:07]] [SUCCESS] Screenshot refreshed successfully
[[08:25:07]] [INFO] LDkFLWks00=running
[[08:25:07]] [INFO] Executing action 184/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:25:06]] [SUCCESS] Screenshot refreshed
[[08:25:06]] [INFO] Refreshing screenshot...
[[08:25:06]] [INFO] 3caMBvQX7k=pass
[[08:25:03]] [SUCCESS] Screenshot refreshed successfully
[[08:25:03]] [SUCCESS] Screenshot refreshed successfully
[[08:25:02]] [INFO] 3caMBvQX7k=running
[[08:25:02]] [INFO] Executing action 183/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:25:02]] [SUCCESS] Screenshot refreshed
[[08:25:02]] [INFO] Refreshing screenshot...
[[08:25:02]] [INFO] yUJyVO5Wev=pass
[[08:25:00]] [SUCCESS] Screenshot refreshed successfully
[[08:25:00]] [SUCCESS] Screenshot refreshed successfully
[[08:24:59]] [INFO] yUJyVO5Wev=running
[[08:24:59]] [INFO] Executing action 182/576: iOS Function: alert_accept
[[08:24:59]] [SUCCESS] Screenshot refreshed
[[08:24:59]] [INFO] Refreshing screenshot...
[[08:24:59]] [INFO] rkL0oz4kiL=pass
[[08:24:51]] [SUCCESS] Screenshot refreshed successfully
[[08:24:51]] [SUCCESS] Screenshot refreshed successfully
[[08:24:51]] [INFO] rkL0oz4kiL=running
[[08:24:51]] [INFO] Executing action 181/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:24:50]] [SUCCESS] Screenshot refreshed
[[08:24:50]] [INFO] Refreshing screenshot...
[[08:24:50]] [INFO] HotUJOd6oB=pass
[[08:24:37]] [SUCCESS] Screenshot refreshed successfully
[[08:24:37]] [SUCCESS] Screenshot refreshed successfully
[[08:24:36]] [INFO] HotUJOd6oB=running
[[08:24:36]] [INFO] Executing action 180/576: Restart app: env[appid]
[[08:24:36]] [SUCCESS] Screenshot refreshed
[[08:24:36]] [INFO] Refreshing screenshot...
[[08:24:36]] [SUCCESS] Screenshot refreshed
[[08:24:36]] [INFO] Refreshing screenshot...
[[08:24:32]] [SUCCESS] Screenshot refreshed successfully
[[08:24:32]] [SUCCESS] Screenshot refreshed successfully
[[08:24:32]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:24:32]] [SUCCESS] Screenshot refreshed
[[08:24:32]] [INFO] Refreshing screenshot...
[[08:24:18]] [SUCCESS] Screenshot refreshed successfully
[[08:24:18]] [SUCCESS] Screenshot refreshed successfully
[[08:24:18]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:24:18]] [SUCCESS] Screenshot refreshed
[[08:24:18]] [INFO] Refreshing screenshot...
[[08:24:14]] [SUCCESS] Screenshot refreshed successfully
[[08:24:14]] [SUCCESS] Screenshot refreshed successfully
[[08:24:14]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:24:14]] [SUCCESS] Screenshot refreshed
[[08:24:14]] [INFO] Refreshing screenshot...
[[08:24:10]] [SUCCESS] Screenshot refreshed successfully
[[08:24:10]] [SUCCESS] Screenshot refreshed successfully
[[08:24:09]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:24:09]] [SUCCESS] Screenshot refreshed
[[08:24:09]] [INFO] Refreshing screenshot...
[[08:24:02]] [SUCCESS] Screenshot refreshed successfully
[[08:24:02]] [SUCCESS] Screenshot refreshed successfully
[[08:24:02]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:24:01]] [SUCCESS] Screenshot refreshed
[[08:24:01]] [INFO] Refreshing screenshot...
[[08:23:55]] [SUCCESS] Screenshot refreshed successfully
[[08:23:55]] [SUCCESS] Screenshot refreshed successfully
[[08:23:54]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:23:54]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:23:54]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:23:54]] [INFO] IR7wnjW7C8=running
[[08:23:54]] [INFO] Executing action 179/576: cleanupSteps action
[[08:23:54]] [SUCCESS] Screenshot refreshed
[[08:23:54]] [INFO] Refreshing screenshot...
[[08:23:54]] [INFO] 7WYExJTqjp=pass
[[08:23:49]] [SUCCESS] Screenshot refreshed successfully
[[08:23:49]] [SUCCESS] Screenshot refreshed successfully
[[08:23:49]] [INFO] 7WYExJTqjp=running
[[08:23:49]] [INFO] Executing action 178/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:23:49]] [SUCCESS] Screenshot refreshed
[[08:23:49]] [INFO] Refreshing screenshot...
[[08:23:49]] [INFO] 4WfPFN961S=pass
[[08:23:42]] [SUCCESS] Screenshot refreshed successfully
[[08:23:42]] [SUCCESS] Screenshot refreshed successfully
[[08:23:42]] [INFO] 4WfPFN961S=running
[[08:23:42]] [INFO] Executing action 177/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:23:42]] [SUCCESS] Screenshot refreshed
[[08:23:42]] [INFO] Refreshing screenshot...
[[08:23:42]] [INFO] NurQsFoMkE=pass
[[08:23:38]] [SUCCESS] Screenshot refreshed successfully
[[08:23:38]] [SUCCESS] Screenshot refreshed successfully
[[08:23:37]] [INFO] NurQsFoMkE=running
[[08:23:37]] [INFO] Executing action 176/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:23:37]] [SUCCESS] Screenshot refreshed
[[08:23:37]] [INFO] Refreshing screenshot...
[[08:23:37]] [INFO] CkfAScJNq8=pass
[[08:23:33]] [SUCCESS] Screenshot refreshed successfully
[[08:23:33]] [SUCCESS] Screenshot refreshed successfully
[[08:23:33]] [INFO] CkfAScJNq8=running
[[08:23:33]] [INFO] Executing action 175/576: Tap on image: env[closebtnimage]
[[08:23:32]] [SUCCESS] Screenshot refreshed
[[08:23:32]] [INFO] Refreshing screenshot...
[[08:23:32]] [INFO] 1NWfFsDiTQ=pass
[[08:23:28]] [SUCCESS] Screenshot refreshed successfully
[[08:23:28]] [SUCCESS] Screenshot refreshed successfully
[[08:23:28]] [INFO] 1NWfFsDiTQ=running
[[08:23:28]] [INFO] Executing action 174/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:23:28]] [SUCCESS] Screenshot refreshed
[[08:23:28]] [INFO] Refreshing screenshot...
[[08:23:28]] [INFO] tufIibCj03=pass
[[08:23:23]] [SUCCESS] Screenshot refreshed successfully
[[08:23:23]] [SUCCESS] Screenshot refreshed successfully
[[08:23:23]] [INFO] tufIibCj03=running
[[08:23:23]] [INFO] Executing action 173/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:23:22]] [SUCCESS] Screenshot refreshed
[[08:23:22]] [INFO] Refreshing screenshot...
[[08:23:22]] [INFO] XryN8qR1DX=pass
[[08:23:19]] [SUCCESS] Screenshot refreshed successfully
[[08:23:19]] [SUCCESS] Screenshot refreshed successfully
[[08:23:18]] [INFO] XryN8qR1DX=running
[[08:23:18]] [INFO] Executing action 172/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:23:18]] [SUCCESS] Screenshot refreshed
[[08:23:18]] [INFO] Refreshing screenshot...
[[08:23:18]] [INFO] CkfAScJNq8=pass
[[08:23:14]] [SUCCESS] Screenshot refreshed successfully
[[08:23:14]] [SUCCESS] Screenshot refreshed successfully
[[08:23:13]] [INFO] CkfAScJNq8=running
[[08:23:13]] [INFO] Executing action 171/576: Tap on image: env[closebtnimage]
[[08:23:13]] [SUCCESS] Screenshot refreshed
[[08:23:13]] [INFO] Refreshing screenshot...
[[08:23:13]] [SUCCESS] Screenshot refreshed successfully
[[08:23:13]] [SUCCESS] Screenshot refreshed successfully
[[08:23:13]] [SUCCESS] Screenshot refreshed
[[08:23:13]] [INFO] Refreshing screenshot...
[[08:23:08]] [SUCCESS] Screenshot refreshed successfully
[[08:23:08]] [SUCCESS] Screenshot refreshed successfully
[[08:23:08]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:23:08]] [SUCCESS] Screenshot refreshed
[[08:23:08]] [INFO] Refreshing screenshot...
[[08:23:04]] [SUCCESS] Screenshot refreshed successfully
[[08:23:04]] [SUCCESS] Screenshot refreshed successfully
[[08:23:04]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:23:03]] [SUCCESS] Screenshot refreshed
[[08:23:03]] [INFO] Refreshing screenshot...
[[08:22:58]] [SUCCESS] Screenshot refreshed successfully
[[08:22:58]] [SUCCESS] Screenshot refreshed successfully
[[08:22:58]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:22:58]] [SUCCESS] Screenshot refreshed
[[08:22:58]] [INFO] Refreshing screenshot...
[[08:22:53]] [SUCCESS] Screenshot refreshed successfully
[[08:22:53]] [SUCCESS] Screenshot refreshed successfully
[[08:22:53]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:22:52]] [SUCCESS] Screenshot refreshed
[[08:22:52]] [INFO] Refreshing screenshot...
[[08:22:47]] [SUCCESS] Screenshot refreshed successfully
[[08:22:47]] [SUCCESS] Screenshot refreshed successfully
[[08:22:47]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:22:47]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:22:46]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:22:46]] [INFO] mWOCt0aAWW=running
[[08:22:46]] [INFO] Executing action 170/576: Execute Test Case: Kmart-Signin (5 steps)
[[08:22:46]] [SUCCESS] Screenshot refreshed
[[08:22:46]] [INFO] Refreshing screenshot...
[[08:22:46]] [INFO] q9ZiyYoE5B=pass
[[08:22:44]] [SUCCESS] Screenshot refreshed successfully
[[08:22:44]] [SUCCESS] Screenshot refreshed successfully
[[08:22:43]] [INFO] q9ZiyYoE5B=running
[[08:22:43]] [INFO] Executing action 169/576: iOS Function: alert_accept
[[08:22:43]] [SUCCESS] Screenshot refreshed
[[08:22:43]] [INFO] Refreshing screenshot...
[[08:22:43]] [INFO] STEdg5jOU8=pass
[[08:22:38]] [SUCCESS] Screenshot refreshed successfully
[[08:22:38]] [SUCCESS] Screenshot refreshed successfully
[[08:22:38]] [INFO] STEdg5jOU8=running
[[08:22:38]] [INFO] Executing action 168/576: Tap on Text: "in"
[[08:22:37]] [SUCCESS] Screenshot refreshed
[[08:22:37]] [INFO] Refreshing screenshot...
[[08:22:37]] [INFO] LDH2hlTZT9=pass
[[08:22:30]] [SUCCESS] Screenshot refreshed successfully
[[08:22:30]] [SUCCESS] Screenshot refreshed successfully
[[08:22:30]] [INFO] LDH2hlTZT9=running
[[08:22:30]] [INFO] Executing action 167/576: Wait for 5 ms
[[08:22:30]] [SUCCESS] Screenshot refreshed
[[08:22:30]] [INFO] Refreshing screenshot...
[[08:22:30]] [INFO] 5Dk9h5bQWl=pass
[[08:22:23]] [SUCCESS] Screenshot refreshed successfully
[[08:22:23]] [SUCCESS] Screenshot refreshed successfully
[[08:22:23]] [INFO] 5Dk9h5bQWl=running
[[08:22:23]] [INFO] Executing action 166/576: Tap on element with accessibility_id: Continue to details
[[08:22:23]] [SUCCESS] Screenshot refreshed
[[08:22:23]] [INFO] Refreshing screenshot...
[[08:22:23]] [INFO] VMzFZ2uTwl=pass
[[08:22:16]] [SUCCESS] Screenshot refreshed successfully
[[08:22:16]] [SUCCESS] Screenshot refreshed successfully
[[08:22:14]] [INFO] VMzFZ2uTwl=running
[[08:22:14]] [INFO] Executing action 165/576: Swipe up till element accessibilityid: "Continue to details" is visible
[[08:22:14]] [SUCCESS] Screenshot refreshed
[[08:22:14]] [INFO] Refreshing screenshot...
[[08:22:14]] [SUCCESS] Screenshot refreshed
[[08:22:14]] [INFO] Refreshing screenshot...
[[08:22:10]] [INFO] Executing Multi Step action step 6/6: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:22:10]] [SUCCESS] Screenshot refreshed successfully
[[08:22:10]] [SUCCESS] Screenshot refreshed successfully
[[08:22:10]] [SUCCESS] Screenshot refreshed
[[08:22:10]] [INFO] Refreshing screenshot...
[[08:22:06]] [SUCCESS] Screenshot refreshed successfully
[[08:22:06]] [SUCCESS] Screenshot refreshed successfully
[[08:22:06]] [INFO] Executing Multi Step action step 5/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:22:05]] [SUCCESS] Screenshot refreshed
[[08:22:05]] [INFO] Refreshing screenshot...
[[08:22:00]] [SUCCESS] Screenshot refreshed successfully
[[08:22:00]] [SUCCESS] Screenshot refreshed successfully
[[08:22:00]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[08:21:59]] [SUCCESS] Screenshot refreshed
[[08:21:59]] [INFO] Refreshing screenshot...
[[08:21:56]] [SUCCESS] Screenshot refreshed successfully
[[08:21:56]] [SUCCESS] Screenshot refreshed successfully
[[08:21:55]] [INFO] Executing Multi Step action step 3/6: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:21:55]] [SUCCESS] Screenshot refreshed
[[08:21:55]] [INFO] Refreshing screenshot...
[[08:21:50]] [SUCCESS] Screenshot refreshed successfully
[[08:21:50]] [SUCCESS] Screenshot refreshed successfully
[[08:21:50]] [INFO] Executing Multi Step action step 2/6: iOS Function: text - Text: "Notebook"
[[08:21:50]] [SUCCESS] Screenshot refreshed
[[08:21:50]] [INFO] Refreshing screenshot...
[[08:21:42]] [SUCCESS] Screenshot refreshed successfully
[[08:21:42]] [SUCCESS] Screenshot refreshed successfully
[[08:21:42]] [INFO] Executing Multi Step action step 1/6: Tap on Text: "Find"
[[08:21:42]] [INFO] Loaded 6 steps from test case: Search and Add (Notebooks)
[[08:21:42]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[08:21:42]] [INFO] 8HTspxuvVG=running
[[08:21:42]] [INFO] Executing action 164/576: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[08:21:41]] [SUCCESS] Screenshot refreshed
[[08:21:41]] [INFO] Refreshing screenshot...
[[08:21:41]] [INFO] NurQsFoMkE=pass
[[08:21:37]] [SUCCESS] Screenshot refreshed successfully
[[08:21:37]] [SUCCESS] Screenshot refreshed successfully
[[08:21:37]] [INFO] NurQsFoMkE=running
[[08:21:37]] [INFO] Executing action 163/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:21:36]] [SUCCESS] Screenshot refreshed
[[08:21:36]] [INFO] Refreshing screenshot...
[[08:21:36]] [INFO] 7QpmNS6hif=pass
[[08:21:32]] [SUCCESS] Screenshot refreshed successfully
[[08:21:32]] [SUCCESS] Screenshot refreshed successfully
[[08:21:31]] [INFO] 7QpmNS6hif=running
[[08:21:31]] [INFO] Executing action 162/576: Restart app: env[appid]
[[08:21:30]] [SUCCESS] Screenshot refreshed
[[08:21:30]] [INFO] Refreshing screenshot...
[[08:21:30]] [INFO] 7WYExJTqjp=pass
[[08:21:26]] [SUCCESS] Screenshot refreshed successfully
[[08:21:26]] [SUCCESS] Screenshot refreshed successfully
[[08:21:26]] [INFO] 7WYExJTqjp=running
[[08:21:26]] [INFO] Executing action 161/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:21:26]] [SUCCESS] Screenshot refreshed
[[08:21:26]] [INFO] Refreshing screenshot...
[[08:21:26]] [INFO] 4WfPFN961S=pass
[[08:21:19]] [SUCCESS] Screenshot refreshed successfully
[[08:21:19]] [SUCCESS] Screenshot refreshed successfully
[[08:21:19]] [INFO] 4WfPFN961S=running
[[08:21:19]] [INFO] Executing action 160/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:21:18]] [SUCCESS] Screenshot refreshed
[[08:21:18]] [INFO] Refreshing screenshot...
[[08:21:18]] [INFO] NurQsFoMkE=pass
[[08:21:14]] [SUCCESS] Screenshot refreshed successfully
[[08:21:14]] [SUCCESS] Screenshot refreshed successfully
[[08:21:14]] [INFO] NurQsFoMkE=running
[[08:21:14]] [INFO] Executing action 159/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:21:13]] [SUCCESS] Screenshot refreshed
[[08:21:13]] [INFO] Refreshing screenshot...
[[08:21:13]] [INFO] CkfAScJNq8=pass
[[08:21:09]] [SUCCESS] Screenshot refreshed successfully
[[08:21:09]] [SUCCESS] Screenshot refreshed successfully
[[08:21:09]] [INFO] CkfAScJNq8=running
[[08:21:09]] [INFO] Executing action 158/576: Tap on image: env[closebtnimage]
[[08:21:08]] [SUCCESS] Screenshot refreshed
[[08:21:08]] [INFO] Refreshing screenshot...
[[08:21:08]] [INFO] 1NWfFsDiTQ=pass
[[08:21:04]] [SUCCESS] Screenshot refreshed successfully
[[08:21:04]] [SUCCESS] Screenshot refreshed successfully
[[08:21:04]] [INFO] 1NWfFsDiTQ=running
[[08:21:04]] [INFO] Executing action 157/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:21:04]] [SUCCESS] Screenshot refreshed
[[08:21:04]] [INFO] Refreshing screenshot...
[[08:21:04]] [INFO] tufIibCj03=pass
[[08:20:59]] [SUCCESS] Screenshot refreshed successfully
[[08:20:59]] [SUCCESS] Screenshot refreshed successfully
[[08:20:59]] [INFO] tufIibCj03=running
[[08:20:59]] [INFO] Executing action 156/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:20:58]] [SUCCESS] Screenshot refreshed
[[08:20:58]] [INFO] Refreshing screenshot...
[[08:20:58]] [INFO] g8u66qfKkX=pass
[[08:20:55]] [SUCCESS] Screenshot refreshed successfully
[[08:20:55]] [SUCCESS] Screenshot refreshed successfully
[[08:20:55]] [INFO] g8u66qfKkX=running
[[08:20:55]] [INFO] Executing action 155/576: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:20:54]] [SUCCESS] Screenshot refreshed
[[08:20:54]] [INFO] Refreshing screenshot...
[[08:20:54]] [INFO] XryN8qR1DX=pass
[[08:20:51]] [SUCCESS] Screenshot refreshed successfully
[[08:20:51]] [SUCCESS] Screenshot refreshed successfully
[[08:20:50]] [INFO] XryN8qR1DX=running
[[08:20:50]] [INFO] Executing action 154/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:20:50]] [SUCCESS] Screenshot refreshed
[[08:20:50]] [INFO] Refreshing screenshot...
[[08:20:50]] [INFO] CkfAScJNq8=pass
[[08:20:45]] [SUCCESS] Screenshot refreshed successfully
[[08:20:45]] [SUCCESS] Screenshot refreshed successfully
[[08:20:45]] [INFO] CkfAScJNq8=running
[[08:20:45]] [INFO] Executing action 153/576: Tap on image: env[closebtnimage]
[[08:20:45]] [SUCCESS] Screenshot refreshed
[[08:20:45]] [INFO] Refreshing screenshot...
[[08:20:45]] [INFO] g8u66qfKkX=pass
[[08:20:37]] [INFO] g8u66qfKkX=running
[[08:20:37]] [INFO] Executing action 152/576: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:20:37]] [SUCCESS] Screenshot refreshed successfully
[[08:20:37]] [SUCCESS] Screenshot refreshed successfully
[[08:20:37]] [SUCCESS] Screenshot refreshed
[[08:20:37]] [INFO] Refreshing screenshot...
[[08:20:37]] [INFO] pCPTAtSZbf=pass
[[08:20:32]] [SUCCESS] Screenshot refreshed successfully
[[08:20:32]] [SUCCESS] Screenshot refreshed successfully
[[08:20:32]] [INFO] pCPTAtSZbf=running
[[08:20:32]] [INFO] Executing action 151/576: iOS Function: text - Text: "Wonderbaby@5"
[[08:20:32]] [SUCCESS] Screenshot refreshed
[[08:20:32]] [INFO] Refreshing screenshot...
[[08:20:32]] [INFO] DaVBARRwft=pass
[[08:20:27]] [SUCCESS] Screenshot refreshed successfully
[[08:20:27]] [SUCCESS] Screenshot refreshed successfully
[[08:20:27]] [INFO] DaVBARRwft=running
[[08:20:27]] [INFO] Executing action 150/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[08:20:27]] [SUCCESS] Screenshot refreshed
[[08:20:27]] [INFO] Refreshing screenshot...
[[08:20:27]] [INFO] e1RoZWCZJb=pass
[[08:20:21]] [SUCCESS] Screenshot refreshed successfully
[[08:20:21]] [SUCCESS] Screenshot refreshed successfully
[[08:20:21]] [INFO] e1RoZWCZJb=running
[[08:20:21]] [INFO] Executing action 149/576: iOS Function: text - Text: "<EMAIL>"
[[08:20:21]] [SUCCESS] Screenshot refreshed
[[08:20:21]] [INFO] Refreshing screenshot...
[[08:20:21]] [INFO] 50Z2jrodNd=pass
[[08:20:17]] [SUCCESS] Screenshot refreshed successfully
[[08:20:17]] [SUCCESS] Screenshot refreshed successfully
[[08:20:16]] [INFO] 50Z2jrodNd=running
[[08:20:16]] [INFO] Executing action 148/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:20:16]] [SUCCESS] Screenshot refreshed
[[08:20:16]] [INFO] Refreshing screenshot...
[[08:20:16]] [INFO] q9ZiyYoE5B=pass
[[08:20:14]] [SUCCESS] Screenshot refreshed successfully
[[08:20:14]] [SUCCESS] Screenshot refreshed successfully
[[08:20:13]] [INFO] q9ZiyYoE5B=running
[[08:20:13]] [INFO] Executing action 147/576: iOS Function: alert_accept
[[08:20:13]] [SUCCESS] Screenshot refreshed
[[08:20:13]] [INFO] Refreshing screenshot...
[[08:20:13]] [INFO] 6PL8P3rT57=pass
[[08:20:08]] [SUCCESS] Screenshot refreshed successfully
[[08:20:08]] [SUCCESS] Screenshot refreshed successfully
[[08:20:08]] [INFO] 6PL8P3rT57=running
[[08:20:08]] [INFO] Executing action 146/576: Tap on Text: "Sign"
[[08:20:07]] [SUCCESS] Screenshot refreshed
[[08:20:07]] [INFO] Refreshing screenshot...
[[08:20:07]] [INFO] 2YGctqXNED=pass
[[08:20:00]] [SUCCESS] Screenshot refreshed successfully
[[08:20:00]] [SUCCESS] Screenshot refreshed successfully
[[08:20:00]] [INFO] 2YGctqXNED=running
[[08:20:00]] [INFO] Executing action 145/576: Tap on element with accessibility_id: Continue to details
[[08:20:00]] [SUCCESS] Screenshot refreshed
[[08:20:00]] [INFO] Refreshing screenshot...
[[08:20:00]] [INFO] 2YGctqXNED=pass
[[08:19:51]] [SUCCESS] Screenshot refreshed successfully
[[08:19:51]] [SUCCESS] Screenshot refreshed successfully
[[08:19:51]] [INFO] 2YGctqXNED=running
[[08:19:51]] [INFO] Executing action 144/576: Swipe up till element accessibilityid: "Continue to details" is visible
[[08:19:51]] [SUCCESS] Screenshot refreshed
[[08:19:51]] [INFO] Refreshing screenshot...
[[08:19:51]] [INFO] tufIibCj03=pass
[[08:19:47]] [SUCCESS] Screenshot refreshed successfully
[[08:19:47]] [SUCCESS] Screenshot refreshed successfully
[[08:19:47]] [INFO] tufIibCj03=running
[[08:19:47]] [INFO] Executing action 143/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:19:46]] [SUCCESS] Screenshot refreshed
[[08:19:46]] [INFO] Refreshing screenshot...
[[08:19:46]] [INFO] g8u66qfKkX=pass
[[08:19:43]] [INFO] g8u66qfKkX=running
[[08:19:43]] [INFO] Executing action 142/576: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:19:43]] [SUCCESS] Screenshot refreshed successfully
[[08:19:43]] [SUCCESS] Screenshot refreshed successfully
[[08:19:42]] [SUCCESS] Screenshot refreshed
[[08:19:42]] [INFO] Refreshing screenshot...
[[08:19:42]] [INFO] XryN8qR1DX=pass
[[08:19:38]] [SUCCESS] Screenshot refreshed successfully
[[08:19:38]] [SUCCESS] Screenshot refreshed successfully
[[08:19:38]] [INFO] XryN8qR1DX=running
[[08:19:38]] [INFO] Executing action 141/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:19:37]] [SUCCESS] Screenshot refreshed
[[08:19:37]] [INFO] Refreshing screenshot...
[[08:19:37]] [INFO] K2w9XUGwnb=pass
[[08:19:29]] [SUCCESS] Screenshot refreshed successfully
[[08:19:29]] [SUCCESS] Screenshot refreshed successfully
[[08:19:29]] [INFO] K2w9XUGwnb=running
[[08:19:29]] [INFO] Executing action 140/576: Tap on element with accessibility_id: Add to bag
[[08:19:28]] [SUCCESS] Screenshot refreshed
[[08:19:28]] [INFO] Refreshing screenshot...
[[08:19:28]] [INFO] BTYxjEaZEk=pass
[[08:19:23]] [SUCCESS] Screenshot refreshed successfully
[[08:19:23]] [SUCCESS] Screenshot refreshed successfully
[[08:19:23]] [INFO] BTYxjEaZEk=running
[[08:19:23]] [INFO] Executing action 139/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:19:23]] [SUCCESS] Screenshot refreshed
[[08:19:23]] [INFO] Refreshing screenshot...
[[08:19:23]] [INFO] YC6bBrKQgq=pass
[[08:19:19]] [SUCCESS] Screenshot refreshed successfully
[[08:19:19]] [SUCCESS] Screenshot refreshed successfully
[[08:19:19]] [INFO] YC6bBrKQgq=running
[[08:19:19]] [INFO] Executing action 138/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:19:18]] [SUCCESS] Screenshot refreshed
[[08:19:18]] [INFO] Refreshing screenshot...
[[08:19:18]] [INFO] aRgHcQcLDP=pass
[[08:19:14]] [SUCCESS] Screenshot refreshed successfully
[[08:19:14]] [SUCCESS] Screenshot refreshed successfully
[[08:19:14]] [INFO] aRgHcQcLDP=running
[[08:19:14]] [INFO] Executing action 137/576: iOS Function: text - Text: "uno card"
[[08:19:13]] [SUCCESS] Screenshot refreshed
[[08:19:13]] [INFO] Refreshing screenshot...
[[08:19:13]] [INFO] 4PZC1vVWJW=pass
[[08:19:08]] [SUCCESS] Screenshot refreshed successfully
[[08:19:08]] [SUCCESS] Screenshot refreshed successfully
[[08:19:08]] [INFO] 4PZC1vVWJW=running
[[08:19:08]] [INFO] Executing action 136/576: Tap on Text: "Find"
[[08:19:07]] [SUCCESS] Screenshot refreshed
[[08:19:07]] [INFO] Refreshing screenshot...
[[08:19:07]] [INFO] XryN8qR1DX=pass
[[08:19:03]] [SUCCESS] Screenshot refreshed successfully
[[08:19:03]] [SUCCESS] Screenshot refreshed successfully
[[08:19:03]] [INFO] XryN8qR1DX=running
[[08:19:03]] [INFO] Executing action 135/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:19:02]] [SUCCESS] Screenshot refreshed
[[08:19:02]] [INFO] Refreshing screenshot...
[[08:19:02]] [INFO] 7WYExJTqjp=pass
[[08:18:58]] [SUCCESS] Screenshot refreshed successfully
[[08:18:58]] [SUCCESS] Screenshot refreshed successfully
[[08:18:58]] [INFO] 7WYExJTqjp=running
[[08:18:58]] [INFO] Executing action 134/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:18:57]] [SUCCESS] Screenshot refreshed
[[08:18:57]] [INFO] Refreshing screenshot...
[[08:18:57]] [INFO] 4WfPFN961S=pass
[[08:18:50]] [SUCCESS] Screenshot refreshed successfully
[[08:18:50]] [SUCCESS] Screenshot refreshed successfully
[[08:18:50]] [INFO] 4WfPFN961S=running
[[08:18:50]] [INFO] Executing action 133/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:18:50]] [SUCCESS] Screenshot refreshed
[[08:18:50]] [INFO] Refreshing screenshot...
[[08:18:50]] [INFO] NurQsFoMkE=pass
[[08:18:48]] [SUCCESS] Screenshot refreshed successfully
[[08:18:48]] [SUCCESS] Screenshot refreshed successfully
[[08:18:46]] [INFO] NurQsFoMkE=running
[[08:18:46]] [INFO] Executing action 132/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:18:46]] [SUCCESS] Screenshot refreshed
[[08:18:46]] [INFO] Refreshing screenshot...
[[08:18:45]] [SUCCESS] Screenshot refreshed
[[08:18:45]] [INFO] Refreshing screenshot...
[[08:18:41]] [SUCCESS] Screenshot refreshed successfully
[[08:18:41]] [SUCCESS] Screenshot refreshed successfully
[[08:18:41]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:18:40]] [SUCCESS] Screenshot refreshed
[[08:18:40]] [INFO] Refreshing screenshot...
[[08:18:36]] [SUCCESS] Screenshot refreshed successfully
[[08:18:36]] [SUCCESS] Screenshot refreshed successfully
[[08:18:36]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:18:36]] [SUCCESS] Screenshot refreshed
[[08:18:36]] [INFO] Refreshing screenshot...
[[08:18:30]] [SUCCESS] Screenshot refreshed successfully
[[08:18:30]] [SUCCESS] Screenshot refreshed successfully
[[08:18:30]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:18:30]] [SUCCESS] Screenshot refreshed
[[08:18:30]] [INFO] Refreshing screenshot...
[[08:18:26]] [SUCCESS] Screenshot refreshed successfully
[[08:18:26]] [SUCCESS] Screenshot refreshed successfully
[[08:18:25]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:18:25]] [SUCCESS] Screenshot refreshed
[[08:18:25]] [INFO] Refreshing screenshot...
[[08:18:19]] [SUCCESS] Screenshot refreshed successfully
[[08:18:19]] [SUCCESS] Screenshot refreshed successfully
[[08:18:19]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:18:19]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:18:19]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:18:19]] [INFO] mWOCt0aAWW=running
[[08:18:19]] [INFO] Executing action 131/576: Execute Test Case: Kmart-Signin (5 steps)
[[08:18:19]] [SUCCESS] Screenshot refreshed
[[08:18:19]] [INFO] Refreshing screenshot...
[[08:18:19]] [INFO] byEe7qbCpq=pass
[[08:18:16]] [SUCCESS] Screenshot refreshed successfully
[[08:18:16]] [SUCCESS] Screenshot refreshed successfully
[[08:18:16]] [INFO] byEe7qbCpq=running
[[08:18:16]] [INFO] Executing action 130/576: iOS Function: alert_accept
[[08:18:15]] [SUCCESS] Screenshot refreshed
[[08:18:15]] [INFO] Refreshing screenshot...
[[08:18:15]] [INFO] L6wTorOX8B=pass
[[08:18:11]] [SUCCESS] Screenshot refreshed successfully
[[08:18:11]] [SUCCESS] Screenshot refreshed successfully
[[08:18:11]] [INFO] L6wTorOX8B=running
[[08:18:11]] [INFO] Executing action 129/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[08:18:11]] [SUCCESS] Screenshot refreshed
[[08:18:11]] [INFO] Refreshing screenshot...
[[08:18:11]] [INFO] XryN8qR1DX=pass
[[08:18:07]] [SUCCESS] Screenshot refreshed successfully
[[08:18:07]] [SUCCESS] Screenshot refreshed successfully
[[08:18:07]] [INFO] XryN8qR1DX=running
[[08:18:07]] [INFO] Executing action 128/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:18:06]] [SUCCESS] Screenshot refreshed
[[08:18:06]] [INFO] Refreshing screenshot...
[[08:18:06]] [INFO] lCSewtjn1z=pass
[[08:18:01]] [SUCCESS] Screenshot refreshed successfully
[[08:18:01]] [SUCCESS] Screenshot refreshed successfully
[[08:18:01]] [INFO] lCSewtjn1z=running
[[08:18:01]] [INFO] Executing action 127/576: Restart app: env[appid]
[[08:18:00]] [SUCCESS] Screenshot refreshed
[[08:18:00]] [INFO] Refreshing screenshot...
[[08:18:00]] [INFO] IJh702cxG0=pass
[[08:17:56]] [SUCCESS] Screenshot refreshed successfully
[[08:17:56]] [SUCCESS] Screenshot refreshed successfully
[[08:17:56]] [INFO] IJh702cxG0=running
[[08:17:56]] [INFO] Executing action 126/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:17:55]] [SUCCESS] Screenshot refreshed
[[08:17:55]] [INFO] Refreshing screenshot...
[[08:17:55]] [INFO] 4WfPFN961S=pass
[[08:17:49]] [SUCCESS] Screenshot refreshed successfully
[[08:17:49]] [SUCCESS] Screenshot refreshed successfully
[[08:17:49]] [INFO] 4WfPFN961S=running
[[08:17:49]] [INFO] Executing action 125/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:17:48]] [SUCCESS] Screenshot refreshed
[[08:17:48]] [INFO] Refreshing screenshot...
[[08:17:48]] [INFO] AOcOOSuOsB=pass
[[08:17:44]] [SUCCESS] Screenshot refreshed successfully
[[08:17:44]] [SUCCESS] Screenshot refreshed successfully
[[08:17:44]] [INFO] AOcOOSuOsB=running
[[08:17:44]] [INFO] Executing action 124/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:17:43]] [SUCCESS] Screenshot refreshed
[[08:17:43]] [INFO] Refreshing screenshot...
[[08:17:43]] [INFO] AOcOOSuOsB=pass
[[08:17:38]] [SUCCESS] Screenshot refreshed successfully
[[08:17:38]] [SUCCESS] Screenshot refreshed successfully
[[08:17:37]] [INFO] AOcOOSuOsB=running
[[08:17:37]] [INFO] Executing action 123/576: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:17:37]] [SUCCESS] Screenshot refreshed
[[08:17:37]] [INFO] Refreshing screenshot...
[[08:17:37]] [INFO] N2yjynioko=pass
[[08:17:32]] [SUCCESS] Screenshot refreshed successfully
[[08:17:32]] [SUCCESS] Screenshot refreshed successfully
[[08:17:32]] [INFO] N2yjynioko=running
[[08:17:32]] [INFO] Executing action 122/576: iOS Function: text - Text: "Wonderbaby@5"
[[08:17:32]] [SUCCESS] Screenshot refreshed
[[08:17:32]] [INFO] Refreshing screenshot...
[[08:17:32]] [INFO] SHaIduBnay=pass
[[08:17:27]] [SUCCESS] Screenshot refreshed successfully
[[08:17:27]] [SUCCESS] Screenshot refreshed successfully
[[08:17:27]] [INFO] SHaIduBnay=running
[[08:17:27]] [INFO] Executing action 121/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[08:17:27]] [SUCCESS] Screenshot refreshed
[[08:17:27]] [INFO] Refreshing screenshot...
[[08:17:27]] [INFO] wuIMlAwYVA=pass
[[08:17:21]] [SUCCESS] Screenshot refreshed successfully
[[08:17:21]] [SUCCESS] Screenshot refreshed successfully
[[08:17:21]] [INFO] wuIMlAwYVA=running
[[08:17:21]] [INFO] Executing action 120/576: iOS Function: text - Text: "env[uname1]"
[[08:17:21]] [SUCCESS] Screenshot refreshed
[[08:17:21]] [INFO] Refreshing screenshot...
[[08:17:21]] [INFO] 50Z2jrodNd=pass
[[08:17:16]] [SUCCESS] Screenshot refreshed successfully
[[08:17:16]] [SUCCESS] Screenshot refreshed successfully
[[08:17:16]] [INFO] 50Z2jrodNd=running
[[08:17:16]] [INFO] Executing action 119/576: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[08:17:16]] [SUCCESS] Screenshot refreshed
[[08:17:16]] [INFO] Refreshing screenshot...
[[08:17:16]] [INFO] VK2oI6mXSB=pass
[[08:17:12]] [SUCCESS] Screenshot refreshed successfully
[[08:17:12]] [SUCCESS] Screenshot refreshed successfully
[[08:17:12]] [INFO] VK2oI6mXSB=running
[[08:17:12]] [INFO] Executing action 118/576: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[08:17:11]] [SUCCESS] Screenshot refreshed
[[08:17:11]] [INFO] Refreshing screenshot...
[[08:17:11]] [INFO] q9ZiyYoE5B=pass
[[08:17:09]] [SUCCESS] Screenshot refreshed successfully
[[08:17:09]] [SUCCESS] Screenshot refreshed successfully
[[08:17:09]] [INFO] q9ZiyYoE5B=running
[[08:17:09]] [INFO] Executing action 117/576: iOS Function: alert_accept
[[08:17:08]] [SUCCESS] Screenshot refreshed
[[08:17:08]] [INFO] Refreshing screenshot...
[[08:17:08]] [INFO] 4PZC1vVWJW=pass
[[08:17:03]] [SUCCESS] Screenshot refreshed successfully
[[08:17:03]] [SUCCESS] Screenshot refreshed successfully
[[08:17:03]] [INFO] 4PZC1vVWJW=running
[[08:17:03]] [INFO] Executing action 116/576: Tap on Text: "Sign"
[[08:17:02]] [SUCCESS] Screenshot refreshed
[[08:17:02]] [INFO] Refreshing screenshot...
[[08:17:02]] [INFO] 2YGctqXNED=pass
[[08:16:52]] [SUCCESS] Screenshot refreshed successfully
[[08:16:52]] [SUCCESS] Screenshot refreshed successfully
[[08:16:52]] [INFO] 2YGctqXNED=running
[[08:16:52]] [INFO] Executing action 115/576: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[08:16:51]] [SUCCESS] Screenshot refreshed
[[08:16:51]] [INFO] Refreshing screenshot...
[[08:16:51]] [INFO] 6zUBxjSFym=pass
[[08:16:48]] [SUCCESS] Screenshot refreshed successfully
[[08:16:48]] [SUCCESS] Screenshot refreshed successfully
[[08:16:47]] [INFO] 6zUBxjSFym=running
[[08:16:47]] [INFO] Executing action 114/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:16:47]] [SUCCESS] Screenshot refreshed
[[08:16:47]] [INFO] Refreshing screenshot...
[[08:16:47]] [INFO] BTYxjEaZEk=pass
[[08:16:43]] [SUCCESS] Screenshot refreshed successfully
[[08:16:43]] [SUCCESS] Screenshot refreshed successfully
[[08:16:42]] [INFO] BTYxjEaZEk=running
[[08:16:42]] [INFO] Executing action 113/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:16:42]] [SUCCESS] Screenshot refreshed
[[08:16:42]] [INFO] Refreshing screenshot...
[[08:16:42]] [INFO] YC6bBrKQgq=pass
[[08:16:38]] [SUCCESS] Screenshot refreshed successfully
[[08:16:38]] [SUCCESS] Screenshot refreshed successfully
[[08:16:38]] [INFO] YC6bBrKQgq=running
[[08:16:38]] [INFO] Executing action 112/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:16:37]] [SUCCESS] Screenshot refreshed
[[08:16:37]] [INFO] Refreshing screenshot...
[[08:16:37]] [INFO] aRgHcQcLDP=pass
[[08:16:33]] [SUCCESS] Screenshot refreshed successfully
[[08:16:33]] [SUCCESS] Screenshot refreshed successfully
[[08:16:33]] [INFO] aRgHcQcLDP=running
[[08:16:33]] [INFO] Executing action 111/576: iOS Function: text - Text: "uno card"
[[08:16:32]] [SUCCESS] Screenshot refreshed
[[08:16:32]] [INFO] Refreshing screenshot...
[[08:16:32]] [INFO] 4PZC1vVWJW=pass
[[08:16:27]] [SUCCESS] Screenshot refreshed successfully
[[08:16:27]] [SUCCESS] Screenshot refreshed successfully
[[08:16:27]] [INFO] 4PZC1vVWJW=running
[[08:16:27]] [INFO] Executing action 110/576: Tap on Text: "Find"
[[08:16:26]] [SUCCESS] Screenshot refreshed
[[08:16:26]] [INFO] Refreshing screenshot...
[[08:16:26]] [INFO] lCSewtjn1z=pass
[[08:16:21]] [SUCCESS] Screenshot refreshed successfully
[[08:16:21]] [SUCCESS] Screenshot refreshed successfully
[[08:16:21]] [INFO] lCSewtjn1z=running
[[08:16:21]] [INFO] Executing action 109/576: Restart app: env[appid]
[[08:16:20]] [SUCCESS] Screenshot refreshed
[[08:16:20]] [INFO] Refreshing screenshot...
[[08:16:20]] [INFO] A1Wz7p1iVG=pass
[[08:16:16]] [SUCCESS] Screenshot refreshed successfully
[[08:16:16]] [SUCCESS] Screenshot refreshed successfully
[[08:16:16]] [INFO] A1Wz7p1iVG=running
[[08:16:16]] [INFO] Executing action 108/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:16:15]] [SUCCESS] Screenshot refreshed
[[08:16:15]] [INFO] Refreshing screenshot...
[[08:16:15]] [INFO] ehyLmdZWP2=pass
[[08:16:08]] [SUCCESS] Screenshot refreshed successfully
[[08:16:08]] [SUCCESS] Screenshot refreshed successfully
[[08:16:08]] [INFO] ehyLmdZWP2=running
[[08:16:08]] [INFO] Executing action 107/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:16:08]] [SUCCESS] Screenshot refreshed
[[08:16:08]] [INFO] Refreshing screenshot...
[[08:16:08]] [INFO] ydRnBBO1vR=pass
[[08:16:05]] [SUCCESS] Screenshot refreshed successfully
[[08:16:05]] [SUCCESS] Screenshot refreshed successfully
[[08:16:04]] [INFO] ydRnBBO1vR=running
[[08:16:04]] [INFO] Executing action 106/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:16:03]] [SUCCESS] Screenshot refreshed
[[08:16:03]] [INFO] Refreshing screenshot...
[[08:16:03]] [INFO] quZwUwj3a8=pass
[[08:15:59]] [SUCCESS] Screenshot refreshed successfully
[[08:15:59]] [SUCCESS] Screenshot refreshed successfully
[[08:15:59]] [INFO] quZwUwj3a8=running
[[08:15:59]] [INFO] Executing action 105/576: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[08:15:58]] [SUCCESS] Screenshot refreshed
[[08:15:58]] [INFO] Refreshing screenshot...
[[08:15:58]] [INFO] FHRlQXe58T=pass
[[08:15:54]] [SUCCESS] Screenshot refreshed successfully
[[08:15:54]] [SUCCESS] Screenshot refreshed successfully
[[08:15:53]] [INFO] FHRlQXe58T=running
[[08:15:53]] [INFO] Executing action 104/576: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:15:53]] [SUCCESS] Screenshot refreshed
[[08:15:53]] [INFO] Refreshing screenshot...
[[08:15:53]] [INFO] FHRlQXe58T=pass
[[08:15:48]] [SUCCESS] Screenshot refreshed successfully
[[08:15:48]] [SUCCESS] Screenshot refreshed successfully
[[08:15:48]] [INFO] FHRlQXe58T=running
[[08:15:48]] [INFO] Executing action 103/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtStart Shopping"]
[[08:15:48]] [SUCCESS] Screenshot refreshed
[[08:15:48]] [INFO] Refreshing screenshot...
[[08:15:48]] [INFO] N9sXy9WltY=pass
[[08:15:44]] [SUCCESS] Screenshot refreshed successfully
[[08:15:44]] [SUCCESS] Screenshot refreshed successfully
[[08:15:44]] [INFO] N9sXy9WltY=running
[[08:15:44]] [INFO] Executing action 102/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtStart Shopping"]" exists
[[08:15:43]] [SUCCESS] Screenshot refreshed
[[08:15:43]] [INFO] Refreshing screenshot...
[[08:15:43]] [INFO] 8uojw2klHA=pass
[[08:15:39]] [SUCCESS] Screenshot refreshed successfully
[[08:15:39]] [SUCCESS] Screenshot refreshed successfully
[[08:15:38]] [INFO] 8uojw2klHA=running
[[08:15:38]] [INFO] Executing action 101/576: iOS Function: text - Text: "env[pwd]"
[[08:15:38]] [SUCCESS] Screenshot refreshed
[[08:15:38]] [INFO] Refreshing screenshot...
[[08:15:38]] [INFO] SHaIduBnay=pass
[[08:15:34]] [SUCCESS] Screenshot refreshed successfully
[[08:15:34]] [SUCCESS] Screenshot refreshed successfully
[[08:15:34]] [INFO] SHaIduBnay=running
[[08:15:34]] [INFO] Executing action 100/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:15:33]] [SUCCESS] Screenshot refreshed
[[08:15:33]] [INFO] Refreshing screenshot...
[[08:15:33]] [INFO] TGoXyeQtB7=pass
[[08:15:28]] [SUCCESS] Screenshot refreshed successfully
[[08:15:28]] [SUCCESS] Screenshot refreshed successfully
[[08:15:28]] [INFO] TGoXyeQtB7=running
[[08:15:28]] [INFO] Executing action 99/576: iOS Function: text - Text: "env[uname]"
[[08:15:28]] [SUCCESS] Screenshot refreshed
[[08:15:28]] [INFO] Refreshing screenshot...
[[08:15:28]] [INFO] rLCI6NVxSc=pass
[[08:15:23]] [SUCCESS] Screenshot refreshed successfully
[[08:15:23]] [SUCCESS] Screenshot refreshed successfully
[[08:15:23]] [INFO] rLCI6NVxSc=running
[[08:15:23]] [INFO] Executing action 98/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:15:22]] [SUCCESS] Screenshot refreshed
[[08:15:22]] [INFO] Refreshing screenshot...
[[08:15:22]] [INFO] 6mHVWI3j5e=pass
[[08:15:19]] [SUCCESS] Screenshot refreshed successfully
[[08:15:19]] [SUCCESS] Screenshot refreshed successfully
[[08:15:19]] [INFO] 6mHVWI3j5e=running
[[08:15:19]] [INFO] Executing action 97/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:15:18]] [SUCCESS] Screenshot refreshed
[[08:15:18]] [INFO] Refreshing screenshot...
[[08:15:18]] [INFO] rJVGLpLWM3=pass
[[08:15:15]] [SUCCESS] Screenshot refreshed successfully
[[08:15:15]] [SUCCESS] Screenshot refreshed successfully
[[08:15:15]] [INFO] rJVGLpLWM3=running
[[08:15:15]] [INFO] Executing action 96/576: iOS Function: alert_accept
[[08:15:15]] [SUCCESS] Screenshot refreshed
[[08:15:15]] [INFO] Refreshing screenshot...
[[08:15:15]] [INFO] WlISsMf9QA=pass
[[08:15:11]] [SUCCESS] Screenshot refreshed successfully
[[08:15:11]] [SUCCESS] Screenshot refreshed successfully
[[08:15:11]] [INFO] WlISsMf9QA=running
[[08:15:11]] [INFO] Executing action 95/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[08:15:10]] [SUCCESS] Screenshot refreshed
[[08:15:10]] [INFO] Refreshing screenshot...
[[08:15:10]] [INFO] IvqPpScAJa=pass
[[08:15:07]] [SUCCESS] Screenshot refreshed successfully
[[08:15:07]] [SUCCESS] Screenshot refreshed successfully
[[08:15:06]] [INFO] IvqPpScAJa=running
[[08:15:06]] [INFO] Executing action 94/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[08:15:06]] [SUCCESS] Screenshot refreshed
[[08:15:06]] [INFO] Refreshing screenshot...
[[08:15:06]] [INFO] bGo3feCwBQ=pass
[[08:15:02]] [SUCCESS] Screenshot refreshed successfully
[[08:15:02]] [SUCCESS] Screenshot refreshed successfully
[[08:15:01]] [INFO] bGo3feCwBQ=running
[[08:15:01]] [INFO] Executing action 93/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:15:01]] [SUCCESS] Screenshot refreshed
[[08:15:01]] [INFO] Refreshing screenshot...
[[08:15:01]] [INFO] 4WfPFN961S=pass
[[08:14:55]] [SUCCESS] Screenshot refreshed successfully
[[08:14:55]] [SUCCESS] Screenshot refreshed successfully
[[08:14:54]] [INFO] 4WfPFN961S=running
[[08:14:54]] [INFO] Executing action 92/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:14:53]] [SUCCESS] Screenshot refreshed
[[08:14:53]] [INFO] Refreshing screenshot...
[[08:14:53]] [INFO] F0gZF1jEnT=pass
[[08:14:50]] [SUCCESS] Screenshot refreshed successfully
[[08:14:50]] [SUCCESS] Screenshot refreshed successfully
[[08:14:49]] [INFO] F0gZF1jEnT=running
[[08:14:49]] [INFO] Executing action 91/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:14:49]] [SUCCESS] Screenshot refreshed
[[08:14:49]] [INFO] Refreshing screenshot...
[[08:14:49]] [INFO] EDHl0X27Wi=pass
[[08:14:45]] [INFO] EDHl0X27Wi=running
[[08:14:45]] [INFO] Executing action 90/576: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[08:14:45]] [SUCCESS] Screenshot refreshed successfully
[[08:14:45]] [SUCCESS] Screenshot refreshed successfully
[[08:14:44]] [SUCCESS] Screenshot refreshed
[[08:14:44]] [INFO] Refreshing screenshot...
[[08:14:44]] [INFO] j8NXU87gV3=pass
[[08:14:39]] [INFO] j8NXU87gV3=running
[[08:14:39]] [INFO] Executing action 89/576: iOS Function: text - Text: "env[pwd]"
[[08:14:39]] [SUCCESS] Screenshot refreshed successfully
[[08:14:38]] [SUCCESS] Screenshot refreshed successfully
[[08:14:37]] [SUCCESS] Screenshot refreshed
[[08:14:37]] [INFO] Refreshing screenshot...
[[08:14:37]] [INFO] dpVaKL19uc=pass
[[08:14:31]] [INFO] dpVaKL19uc=running
[[08:14:31]] [INFO] Executing action 88/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:14:21]] [SUCCESS] Screenshot refreshed successfully
[[08:14:21]] [SUCCESS] Screenshot refreshed successfully
[[08:14:20]] [SUCCESS] Screenshot refreshed
[[08:14:20]] [INFO] Refreshing screenshot...
[[08:14:20]] [INFO] eOm1WExcrK=pass
[[08:14:15]] [INFO] eOm1WExcrK=running
[[08:14:15]] [INFO] Executing action 87/576: iOS Function: text - Text: "env[uname]"
[[08:14:12]] [SUCCESS] Screenshot refreshed successfully
[[08:14:12]] [SUCCESS] Screenshot refreshed successfully
[[08:14:12]] [SUCCESS] Screenshot refreshed
[[08:14:12]] [INFO] Refreshing screenshot...
[[08:14:12]] [INFO] 50Z2jrodNd=pass
[[08:14:08]] [SUCCESS] Screenshot refreshed successfully
[[08:14:08]] [SUCCESS] Screenshot refreshed successfully
[[08:14:08]] [INFO] 50Z2jrodNd=running
[[08:14:08]] [INFO] Executing action 86/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:14:07]] [SUCCESS] Screenshot refreshed
[[08:14:07]] [INFO] Refreshing screenshot...
[[08:14:07]] [INFO] eJnHS9n9VL=pass
[[08:14:04]] [SUCCESS] Screenshot refreshed successfully
[[08:14:04]] [SUCCESS] Screenshot refreshed successfully
[[08:14:03]] [INFO] eJnHS9n9VL=running
[[08:14:03]] [INFO] Executing action 85/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:14:03]] [SUCCESS] Screenshot refreshed
[[08:14:03]] [INFO] Refreshing screenshot...
[[08:14:03]] [INFO] XuLgjNG74w=pass
[[08:14:01]] [SUCCESS] Screenshot refreshed successfully
[[08:14:01]] [SUCCESS] Screenshot refreshed successfully
[[08:14:00]] [INFO] XuLgjNG74w=running
[[08:14:00]] [INFO] Executing action 84/576: iOS Function: alert_accept
[[08:14:00]] [SUCCESS] Screenshot refreshed
[[08:14:00]] [INFO] Refreshing screenshot...
[[08:14:00]] [INFO] qA1ap4n1m4=pass
[[08:13:52]] [SUCCESS] Screenshot refreshed successfully
[[08:13:52]] [SUCCESS] Screenshot refreshed successfully
[[08:13:52]] [INFO] qA1ap4n1m4=running
[[08:13:52]] [INFO] Executing action 83/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:13:51]] [SUCCESS] Screenshot refreshed
[[08:13:51]] [INFO] Refreshing screenshot...
[[08:13:51]] [INFO] JXFxYCr98V=pass
[[08:13:38]] [SUCCESS] Screenshot refreshed successfully
[[08:13:38]] [SUCCESS] Screenshot refreshed successfully
[[08:13:37]] [INFO] JXFxYCr98V=running
[[08:13:37]] [INFO] Executing action 82/576: Restart app: env[appid]
[[08:13:37]] [SUCCESS] Screenshot refreshed
[[08:13:37]] [INFO] Refreshing screenshot...
[[08:13:36]] [SUCCESS] Screenshot refreshed
[[08:13:36]] [INFO] Refreshing screenshot...
[[08:13:33]] [SUCCESS] Screenshot refreshed successfully
[[08:13:33]] [SUCCESS] Screenshot refreshed successfully
[[08:13:33]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:13:33]] [SUCCESS] Screenshot refreshed
[[08:13:33]] [INFO] Refreshing screenshot...
[[08:13:20]] [SUCCESS] Screenshot refreshed successfully
[[08:13:20]] [SUCCESS] Screenshot refreshed successfully
[[08:13:20]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:13:20]] [SUCCESS] Screenshot refreshed
[[08:13:20]] [INFO] Refreshing screenshot...
[[08:13:16]] [SUCCESS] Screenshot refreshed successfully
[[08:13:16]] [SUCCESS] Screenshot refreshed successfully
[[08:13:16]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:13:15]] [SUCCESS] Screenshot refreshed
[[08:13:15]] [INFO] Refreshing screenshot...
[[08:13:12]] [SUCCESS] Screenshot refreshed successfully
[[08:13:12]] [SUCCESS] Screenshot refreshed successfully
[[08:13:11]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:13:11]] [SUCCESS] Screenshot refreshed
[[08:13:11]] [INFO] Refreshing screenshot...
[[08:13:04]] [SUCCESS] Screenshot refreshed successfully
[[08:13:04]] [SUCCESS] Screenshot refreshed successfully
[[08:13:04]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:13:03]] [SUCCESS] Screenshot refreshed
[[08:13:03]] [INFO] Refreshing screenshot...
[[08:12:57]] [SUCCESS] Screenshot refreshed successfully
[[08:12:57]] [SUCCESS] Screenshot refreshed successfully
[[08:12:56]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:12:56]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:12:56]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:12:56]] [INFO] hbIlJIWlVN=running
[[08:12:56]] [INFO] Executing action 81/576: cleanupSteps action
[[08:12:56]] [SUCCESS] Screenshot refreshed
[[08:12:56]] [INFO] Refreshing screenshot...
[[08:12:56]] [SUCCESS] Screenshot refreshed
[[08:12:56]] [INFO] Refreshing screenshot...
[[08:12:51]] [SUCCESS] Screenshot refreshed successfully
[[08:12:51]] [SUCCESS] Screenshot refreshed successfully
[[08:12:51]] [INFO] Executing Multi Step action step 34/34: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[08:12:51]] [SUCCESS] Screenshot refreshed
[[08:12:51]] [INFO] Refreshing screenshot...
[[08:12:47]] [INFO] Executing Multi Step action step 33/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:12:47]] [SUCCESS] Screenshot refreshed successfully
[[08:12:47]] [SUCCESS] Screenshot refreshed successfully
[[08:12:46]] [SUCCESS] Screenshot refreshed
[[08:12:46]] [INFO] Refreshing screenshot...
[[08:12:43]] [SUCCESS] Screenshot refreshed successfully
[[08:12:43]] [SUCCESS] Screenshot refreshed successfully
[[08:12:42]] [INFO] Executing Multi Step action step 32/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:12:42]] [SUCCESS] Screenshot refreshed
[[08:12:42]] [INFO] Refreshing screenshot...
[[08:12:37]] [SUCCESS] Screenshot refreshed successfully
[[08:12:37]] [SUCCESS] Screenshot refreshed successfully
[[08:12:37]] [INFO] Executing Multi Step action step 31/34: Tap on image: banner-close-updated.png
[[08:12:36]] [SUCCESS] Screenshot refreshed
[[08:12:36]] [INFO] Refreshing screenshot...
[[08:12:26]] [SUCCESS] Screenshot refreshed successfully
[[08:12:26]] [SUCCESS] Screenshot refreshed successfully
[[08:12:26]] [INFO] Executing Multi Step action step 30/34: Swipe from (50%, 70%) to (50%, 30%)
[[08:12:26]] [SUCCESS] Screenshot refreshed
[[08:12:26]] [INFO] Refreshing screenshot...
[[08:12:22]] [SUCCESS] Screenshot refreshed successfully
[[08:12:22]] [SUCCESS] Screenshot refreshed successfully
[[08:12:22]] [INFO] Executing Multi Step action step 29/34: Tap on image: env[delivery-address-img]
[[08:12:21]] [SUCCESS] Screenshot refreshed
[[08:12:21]] [INFO] Refreshing screenshot...
[[08:12:17]] [SUCCESS] Screenshot refreshed successfully
[[08:12:17]] [SUCCESS] Screenshot refreshed successfully
[[08:12:17]] [INFO] Executing Multi Step action step 28/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[08:12:16]] [SUCCESS] Screenshot refreshed
[[08:12:16]] [INFO] Refreshing screenshot...
[[08:12:09]] [SUCCESS] Screenshot refreshed successfully
[[08:12:09]] [SUCCESS] Screenshot refreshed successfully
[[08:12:09]] [INFO] Executing Multi Step action step 27/34: Tap and Type at (54, 314): "305 238 Flinders"
[[08:12:09]] [SUCCESS] Screenshot refreshed
[[08:12:09]] [INFO] Refreshing screenshot...
[[08:12:04]] [SUCCESS] Screenshot refreshed successfully
[[08:12:04]] [SUCCESS] Screenshot refreshed successfully
[[08:12:04]] [INFO] Executing Multi Step action step 26/34: Tap on Text: "address"
[[08:12:03]] [SUCCESS] Screenshot refreshed
[[08:12:03]] [INFO] Refreshing screenshot...
[[08:11:59]] [SUCCESS] Screenshot refreshed successfully
[[08:11:59]] [SUCCESS] Screenshot refreshed successfully
[[08:11:59]] [INFO] Executing Multi Step action step 25/34: iOS Function: text - Text: " "
[[08:11:58]] [SUCCESS] Screenshot refreshed
[[08:11:58]] [INFO] Refreshing screenshot...
[[08:11:55]] [SUCCESS] Screenshot refreshed successfully
[[08:11:55]] [SUCCESS] Screenshot refreshed successfully
[[08:11:54]] [INFO] Executing Multi Step action step 24/34: textClear action
[[08:11:53]] [SUCCESS] Screenshot refreshed
[[08:11:53]] [INFO] Refreshing screenshot...
[[08:11:49]] [SUCCESS] Screenshot refreshed successfully
[[08:11:49]] [SUCCESS] Screenshot refreshed successfully
[[08:11:49]] [INFO] Executing Multi Step action step 23/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[08:11:49]] [SUCCESS] Screenshot refreshed
[[08:11:49]] [INFO] Refreshing screenshot...
[[08:11:44]] [SUCCESS] Screenshot refreshed successfully
[[08:11:44]] [SUCCESS] Screenshot refreshed successfully
[[08:11:44]] [INFO] Executing Multi Step action step 22/34: textClear action
[[08:11:44]] [SUCCESS] Screenshot refreshed
[[08:11:44]] [INFO] Refreshing screenshot...
[[08:11:40]] [SUCCESS] Screenshot refreshed successfully
[[08:11:40]] [SUCCESS] Screenshot refreshed successfully
[[08:11:40]] [INFO] Executing Multi Step action step 21/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:11:39]] [SUCCESS] Screenshot refreshed
[[08:11:39]] [INFO] Refreshing screenshot...
[[08:11:35]] [SUCCESS] Screenshot refreshed successfully
[[08:11:35]] [SUCCESS] Screenshot refreshed successfully
[[08:11:35]] [INFO] Executing Multi Step action step 20/34: textClear action
[[08:11:35]] [SUCCESS] Screenshot refreshed
[[08:11:35]] [INFO] Refreshing screenshot...
[[08:11:30]] [SUCCESS] Screenshot refreshed successfully
[[08:11:30]] [SUCCESS] Screenshot refreshed successfully
[[08:11:30]] [INFO] Executing Multi Step action step 19/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[08:11:29]] [SUCCESS] Screenshot refreshed
[[08:11:29]] [INFO] Refreshing screenshot...
[[08:11:25]] [SUCCESS] Screenshot refreshed successfully
[[08:11:25]] [SUCCESS] Screenshot refreshed successfully
[[08:11:25]] [INFO] Executing Multi Step action step 18/34: textClear action
[[08:11:24]] [SUCCESS] Screenshot refreshed
[[08:11:24]] [INFO] Refreshing screenshot...
[[08:11:20]] [SUCCESS] Screenshot refreshed successfully
[[08:11:20]] [SUCCESS] Screenshot refreshed successfully
[[08:11:20]] [INFO] Executing Multi Step action step 17/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[08:11:20]] [SUCCESS] Screenshot refreshed
[[08:11:20]] [INFO] Refreshing screenshot...
[[08:11:16]] [SUCCESS] Screenshot refreshed successfully
[[08:11:16]] [SUCCESS] Screenshot refreshed successfully
[[08:11:16]] [INFO] Executing Multi Step action step 16/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[08:11:15]] [SUCCESS] Screenshot refreshed
[[08:11:15]] [INFO] Refreshing screenshot...
[[08:10:56]] [SUCCESS] Screenshot refreshed successfully
[[08:10:56]] [SUCCESS] Screenshot refreshed successfully
[[08:10:55]] [INFO] Executing Multi Step action step 15/34: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[08:10:55]] [SUCCESS] Screenshot refreshed
[[08:10:55]] [INFO] Refreshing screenshot...
[[08:10:51]] [SUCCESS] Screenshot refreshed successfully
[[08:10:51]] [SUCCESS] Screenshot refreshed successfully
[[08:10:51]] [INFO] Executing Multi Step action step 14/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:10:50]] [SUCCESS] Screenshot refreshed
[[08:10:50]] [INFO] Refreshing screenshot...
[[08:10:47]] [INFO] Executing Multi Step action step 13/34: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:10:47]] [SUCCESS] Screenshot refreshed successfully
[[08:10:47]] [SUCCESS] Screenshot refreshed successfully
[[08:10:46]] [SUCCESS] Screenshot refreshed
[[08:10:46]] [INFO] Refreshing screenshot...
[[08:10:43]] [SUCCESS] Screenshot refreshed successfully
[[08:10:43]] [SUCCESS] Screenshot refreshed successfully
[[08:10:42]] [INFO] Executing Multi Step action step 12/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:10:42]] [SUCCESS] Screenshot refreshed
[[08:10:42]] [INFO] Refreshing screenshot...
[[08:10:35]] [SUCCESS] Screenshot refreshed successfully
[[08:10:35]] [SUCCESS] Screenshot refreshed successfully
[[08:10:35]] [INFO] Executing Multi Step action step 11/34: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[08:10:34]] [SUCCESS] Screenshot refreshed
[[08:10:34]] [INFO] Refreshing screenshot...
[[08:10:30]] [SUCCESS] Screenshot refreshed successfully
[[08:10:30]] [SUCCESS] Screenshot refreshed successfully
[[08:10:30]] [INFO] Executing Multi Step action step 10/34: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:10:29]] [SUCCESS] Screenshot refreshed
[[08:10:29]] [INFO] Refreshing screenshot...
[[08:10:25]] [SUCCESS] Screenshot refreshed successfully
[[08:10:25]] [SUCCESS] Screenshot refreshed successfully
[[08:10:25]] [INFO] Executing Multi Step action step 9/34: iOS Function: text - Text: "Uno card"
[[08:10:25]] [SUCCESS] Screenshot refreshed
[[08:10:25]] [INFO] Refreshing screenshot...
[[08:10:19]] [SUCCESS] Screenshot refreshed successfully
[[08:10:19]] [SUCCESS] Screenshot refreshed successfully
[[08:10:19]] [INFO] Executing Multi Step action step 8/34: Tap on Text: "Find"
[[08:10:18]] [SUCCESS] Screenshot refreshed
[[08:10:18]] [INFO] Refreshing screenshot...
[[08:09:56]] [SUCCESS] Screenshot refreshed successfully
[[08:09:56]] [SUCCESS] Screenshot refreshed successfully
[[08:09:55]] [INFO] Executing Multi Step action step 7/34: If exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]" (timeout: 20s) → Then tap at (0, 0)
[[08:09:55]] [SUCCESS] Screenshot refreshed
[[08:09:55]] [INFO] Refreshing screenshot...
[[08:09:42]] [SUCCESS] Screenshot refreshed successfully
[[08:09:42]] [SUCCESS] Screenshot refreshed successfully
[[08:09:42]] [INFO] Executing Multi Step action step 6/34: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[08:09:41]] [SUCCESS] Screenshot refreshed
[[08:09:41]] [INFO] Refreshing screenshot...
[[08:09:14]] [SUCCESS] Screenshot refreshed successfully
[[08:09:14]] [SUCCESS] Screenshot refreshed successfully
[[08:09:13]] [INFO] Executing Multi Step action step 5/34: If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 25s) → Then click element: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[08:09:13]] [SUCCESS] Screenshot refreshed
[[08:09:13]] [INFO] Refreshing screenshot...
[[08:09:08]] [SUCCESS] Screenshot refreshed successfully
[[08:09:08]] [SUCCESS] Screenshot refreshed successfully
[[08:09:07]] [INFO] Executing Multi Step action step 4/34: Tap on Text: "Save"
[[08:09:07]] [SUCCESS] Screenshot refreshed
[[08:09:07]] [INFO] Refreshing screenshot...
[[08:09:03]] [SUCCESS] Screenshot refreshed successfully
[[08:09:03]] [SUCCESS] Screenshot refreshed successfully
[[08:09:01]] [INFO] Executing Multi Step action step 3/34: Tap on element with accessibility_id: btnCurrentLocationButton
[[08:09:00]] [SUCCESS] Screenshot refreshed
[[08:09:00]] [INFO] Refreshing screenshot...
[[08:08:56]] [SUCCESS] Screenshot refreshed successfully
[[08:08:56]] [SUCCESS] Screenshot refreshed successfully
[[08:08:56]] [INFO] Executing Multi Step action step 2/34: Wait till accessibility_id=btnCurrentLocationButton
[[08:08:55]] [SUCCESS] Screenshot refreshed
[[08:08:55]] [INFO] Refreshing screenshot...
[[08:08:48]] [SUCCESS] Screenshot refreshed successfully
[[08:08:48]] [SUCCESS] Screenshot refreshed successfully
[[08:08:48]] [INFO] Executing Multi Step action step 1/34: Tap on Text: "Edit"
[[08:08:48]] [INFO] Loaded 34 steps from test case: Delivery  Buy
[[08:08:48]] [INFO] Loading steps for multiStep action: Delivery  Buy
[[08:08:48]] [INFO] aI4Cfo88Pv=running
[[08:08:48]] [INFO] Executing action 80/576: Execute Test Case: Delivery  Buy (34 steps)
[[08:08:47]] [SUCCESS] Screenshot refreshed
[[08:08:47]] [INFO] Refreshing screenshot...
[[08:08:47]] [INFO] cKNu2QoRC1=pass
[[08:08:43]] [SUCCESS] Screenshot refreshed successfully
[[08:08:43]] [SUCCESS] Screenshot refreshed successfully
[[08:08:43]] [INFO] cKNu2QoRC1=running
[[08:08:43]] [INFO] Executing action 79/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:08:42]] [SUCCESS] Screenshot refreshed
[[08:08:42]] [INFO] Refreshing screenshot...
[[08:08:42]] [INFO] OyUowAaBzD=pass
[[08:08:38]] [SUCCESS] Screenshot refreshed successfully
[[08:08:38]] [SUCCESS] Screenshot refreshed successfully
[[08:08:38]] [INFO] OyUowAaBzD=running
[[08:08:38]] [INFO] Executing action 78/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:08:38]] [SUCCESS] Screenshot refreshed
[[08:08:38]] [INFO] Refreshing screenshot...
[[08:08:38]] [INFO] Ob26qqcA0p=pass
[[08:08:30]] [SUCCESS] Screenshot refreshed successfully
[[08:08:30]] [SUCCESS] Screenshot refreshed successfully
[[08:08:30]] [INFO] Ob26qqcA0p=running
[[08:08:30]] [INFO] Executing action 77/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:08:30]] [SUCCESS] Screenshot refreshed
[[08:08:30]] [INFO] Refreshing screenshot...
[[08:08:30]] [INFO] k3mu9Mt7Ec=pass
[[08:08:26]] [SUCCESS] Screenshot refreshed successfully
[[08:08:26]] [SUCCESS] Screenshot refreshed successfully
[[08:08:25]] [INFO] k3mu9Mt7Ec=running
[[08:08:25]] [INFO] Executing action 76/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:08:25]] [SUCCESS] Screenshot refreshed
[[08:08:25]] [INFO] Refreshing screenshot...
[[08:08:25]] [INFO] 8umPSX0vrr=pass
[[08:08:20]] [SUCCESS] Screenshot refreshed successfully
[[08:08:20]] [SUCCESS] Screenshot refreshed successfully
[[08:08:20]] [INFO] 8umPSX0vrr=running
[[08:08:20]] [INFO] Executing action 75/576: Tap on image: banner-close-updated.png
[[08:08:19]] [SUCCESS] Screenshot refreshed
[[08:08:19]] [INFO] Refreshing screenshot...
[[08:08:19]] [INFO] pr9o8Zsm5p=pass
[[08:08:15]] [SUCCESS] Screenshot refreshed successfully
[[08:08:15]] [SUCCESS] Screenshot refreshed successfully
[[08:08:15]] [INFO] pr9o8Zsm5p=running
[[08:08:15]] [INFO] Executing action 74/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:08:15]] [SUCCESS] Screenshot refreshed
[[08:08:15]] [INFO] Refreshing screenshot...
[[08:08:15]] [INFO] Qbg9bipTGs=pass
[[08:08:10]] [SUCCESS] Screenshot refreshed successfully
[[08:08:10]] [SUCCESS] Screenshot refreshed successfully
[[08:08:10]] [INFO] Qbg9bipTGs=running
[[08:08:10]] [INFO] Executing action 73/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:08:09]] [SUCCESS] Screenshot refreshed
[[08:08:09]] [INFO] Refreshing screenshot...
[[08:08:09]] [INFO] qjj0i3rcUh=pass
[[08:08:05]] [SUCCESS] Screenshot refreshed successfully
[[08:08:05]] [SUCCESS] Screenshot refreshed successfully
[[08:08:05]] [INFO] qjj0i3rcUh=running
[[08:08:05]] [INFO] Executing action 72/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[08:08:05]] [SUCCESS] Screenshot refreshed
[[08:08:05]] [INFO] Refreshing screenshot...
[[08:08:05]] [INFO] lWIRxRm6HE=pass
[[08:08:00]] [SUCCESS] Screenshot refreshed successfully
[[08:08:00]] [SUCCESS] Screenshot refreshed successfully
[[08:08:00]] [INFO] lWIRxRm6HE=running
[[08:08:00]] [INFO] Executing action 71/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:08:00]] [SUCCESS] Screenshot refreshed
[[08:08:00]] [INFO] Refreshing screenshot...
[[08:08:00]] [INFO] Q0fomJIDoQ=pass
[[08:07:55]] [SUCCESS] Screenshot refreshed successfully
[[08:07:55]] [SUCCESS] Screenshot refreshed successfully
[[08:07:55]] [INFO] Q0fomJIDoQ=running
[[08:07:55]] [INFO] Executing action 70/576: Tap on image: banner-close-updated.png
[[08:07:55]] [SUCCESS] Screenshot refreshed
[[08:07:55]] [INFO] Refreshing screenshot...
[[08:07:55]] [INFO] 7SpDO20tS2=pass
[[08:07:43]] [SUCCESS] Screenshot refreshed successfully
[[08:07:43]] [SUCCESS] Screenshot refreshed successfully
[[08:07:43]] [INFO] 7SpDO20tS2=running
[[08:07:43]] [INFO] Executing action 69/576: Wait for 10 ms
[[08:07:42]] [SUCCESS] Screenshot refreshed
[[08:07:42]] [INFO] Refreshing screenshot...
[[08:07:42]] [INFO] FKZs2qCWoU=pass
[[08:07:37]] [SUCCESS] Screenshot refreshed successfully
[[08:07:37]] [SUCCESS] Screenshot refreshed successfully
[[08:07:37]] [INFO] FKZs2qCWoU=running
[[08:07:37]] [INFO] Executing action 68/576: Tap on Text: "Brunswick"
[[08:07:37]] [SUCCESS] Screenshot refreshed
[[08:07:37]] [INFO] Refreshing screenshot...
[[08:07:37]] [INFO] Qbg9bipTGs=pass
[[08:07:32]] [SUCCESS] Screenshot refreshed successfully
[[08:07:32]] [SUCCESS] Screenshot refreshed successfully
[[08:07:32]] [INFO] Qbg9bipTGs=running
[[08:07:32]] [INFO] Executing action 67/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:07:32]] [SUCCESS] Screenshot refreshed
[[08:07:32]] [INFO] Refreshing screenshot...
[[08:07:32]] [INFO] qjj0i3rcUh=pass
[[08:07:28]] [SUCCESS] Screenshot refreshed successfully
[[08:07:28]] [SUCCESS] Screenshot refreshed successfully
[[08:07:27]] [INFO] qjj0i3rcUh=running
[[08:07:27]] [INFO] Executing action 66/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[08:07:27]] [SUCCESS] Screenshot refreshed
[[08:07:27]] [INFO] Refreshing screenshot...
[[08:07:27]] [INFO] uM5FOSrU5U=pass
[[08:07:23]] [INFO] uM5FOSrU5U=running
[[08:07:23]] [INFO] Executing action 65/576: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[08:07:23]] [SUCCESS] Screenshot refreshed successfully
[[08:07:23]] [SUCCESS] Screenshot refreshed successfully
[[08:07:22]] [SUCCESS] Screenshot refreshed
[[08:07:22]] [INFO] Refreshing screenshot...
[[08:07:22]] [INFO] F1olhgKhUt=pass
[[08:07:19]] [SUCCESS] Screenshot refreshed successfully
[[08:07:19]] [SUCCESS] Screenshot refreshed successfully
[[08:07:18]] [INFO] F1olhgKhUt=running
[[08:07:18]] [INFO] Executing action 64/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:07:18]] [SUCCESS] Screenshot refreshed
[[08:07:18]] [INFO] Refreshing screenshot...
[[08:07:18]] [INFO] jY0oPjKbuS=pass
[[08:07:15]] [SUCCESS] Screenshot refreshed successfully
[[08:07:15]] [SUCCESS] Screenshot refreshed successfully
[[08:07:14]] [INFO] jY0oPjKbuS=running
[[08:07:14]] [INFO] Executing action 63/576: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[08:07:14]] [SUCCESS] Screenshot refreshed
[[08:07:14]] [INFO] Refreshing screenshot...
[[08:07:14]] [INFO] FnrbyHq7bU=pass
[[08:07:08]] [SUCCESS] Screenshot refreshed successfully
[[08:07:08]] [SUCCESS] Screenshot refreshed successfully
[[08:07:07]] [INFO] FnrbyHq7bU=running
[[08:07:07]] [INFO] Executing action 62/576: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[08:07:07]] [SUCCESS] Screenshot refreshed
[[08:07:07]] [INFO] Refreshing screenshot...
[[08:07:07]] [INFO] nAB6Q8LAdv=pass
[[08:07:03]] [SUCCESS] Screenshot refreshed successfully
[[08:07:03]] [SUCCESS] Screenshot refreshed successfully
[[08:07:03]] [INFO] nAB6Q8LAdv=running
[[08:07:03]] [INFO] Executing action 61/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:07:02]] [SUCCESS] Screenshot refreshed
[[08:07:02]] [INFO] Refreshing screenshot...
[[08:07:02]] [INFO] sc2KH9bG6H=pass
[[08:06:57]] [SUCCESS] Screenshot refreshed successfully
[[08:06:57]] [SUCCESS] Screenshot refreshed successfully
[[08:06:57]] [INFO] sc2KH9bG6H=running
[[08:06:57]] [INFO] Executing action 60/576: iOS Function: text - Text: "Uno card"
[[08:06:57]] [SUCCESS] Screenshot refreshed
[[08:06:57]] [INFO] Refreshing screenshot...
[[08:06:57]] [INFO] ZBXCQNlT8z=pass
[[08:06:52]] [SUCCESS] Screenshot refreshed successfully
[[08:06:52]] [SUCCESS] Screenshot refreshed successfully
[[08:06:51]] [INFO] ZBXCQNlT8z=running
[[08:06:51]] [INFO] Executing action 59/576: Tap on Text: "Find"
[[08:06:51]] [SUCCESS] Screenshot refreshed
[[08:06:51]] [INFO] Refreshing screenshot...
[[08:06:50]] [SUCCESS] Screenshot refreshed
[[08:06:50]] [INFO] Refreshing screenshot...
[[08:06:45]] [SUCCESS] Screenshot refreshed successfully
[[08:06:45]] [SUCCESS] Screenshot refreshed successfully
[[08:06:45]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:06:45]] [SUCCESS] Screenshot refreshed
[[08:06:45]] [INFO] Refreshing screenshot...
[[08:06:41]] [SUCCESS] Screenshot refreshed successfully
[[08:06:41]] [SUCCESS] Screenshot refreshed successfully
[[08:06:41]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:06:40]] [SUCCESS] Screenshot refreshed
[[08:06:40]] [INFO] Refreshing screenshot...
[[08:06:35]] [SUCCESS] Screenshot refreshed successfully
[[08:06:35]] [SUCCESS] Screenshot refreshed successfully
[[08:06:35]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:06:35]] [SUCCESS] Screenshot refreshed
[[08:06:35]] [INFO] Refreshing screenshot...
[[08:06:30]] [SUCCESS] Screenshot refreshed successfully
[[08:06:30]] [SUCCESS] Screenshot refreshed successfully
[[08:06:30]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:06:29]] [SUCCESS] Screenshot refreshed
[[08:06:29]] [INFO] Refreshing screenshot...
[[08:06:25]] [SUCCESS] Screenshot refreshed successfully
[[08:06:25]] [SUCCESS] Screenshot refreshed successfully
[[08:06:24]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:06:24]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:06:24]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:06:24]] [INFO] El6k4IPZly=running
[[08:06:24]] [INFO] Executing action 58/576: Execute Test Case: Kmart-Signin (8 steps)
[[08:06:23]] [SUCCESS] Screenshot refreshed
[[08:06:23]] [INFO] Refreshing screenshot...
[[08:06:23]] [INFO] 3caMBvQX7k=pass
[[08:06:20]] [SUCCESS] Screenshot refreshed successfully
[[08:06:20]] [SUCCESS] Screenshot refreshed successfully
[[08:06:20]] [INFO] 3caMBvQX7k=running
[[08:06:20]] [INFO] Executing action 57/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:06:19]] [SUCCESS] Screenshot refreshed
[[08:06:19]] [INFO] Refreshing screenshot...
[[08:06:19]] [INFO] yUJyVO5Wev=pass
[[08:06:17]] [SUCCESS] Screenshot refreshed successfully
[[08:06:17]] [SUCCESS] Screenshot refreshed successfully
[[08:06:16]] [INFO] yUJyVO5Wev=running
[[08:06:16]] [INFO] Executing action 56/576: iOS Function: alert_accept
[[08:06:16]] [SUCCESS] Screenshot refreshed
[[08:06:16]] [INFO] Refreshing screenshot...
[[08:06:16]] [INFO] rkL0oz4kiL=pass
[[08:06:09]] [SUCCESS] Screenshot refreshed successfully
[[08:06:09]] [SUCCESS] Screenshot refreshed successfully
[[08:06:08]] [INFO] rkL0oz4kiL=running
[[08:06:08]] [INFO] Executing action 55/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:06:08]] [SUCCESS] Screenshot refreshed
[[08:06:08]] [INFO] Refreshing screenshot...
[[08:06:08]] [INFO] HotUJOd6oB=pass
[[08:05:55]] [SUCCESS] Screenshot refreshed successfully
[[08:05:55]] [SUCCESS] Screenshot refreshed successfully
[[08:05:53]] [INFO] HotUJOd6oB=running
[[08:05:53]] [INFO] Executing action 54/576: Restart app: env[appid]
[[08:05:53]] [SUCCESS] Screenshot refreshed
[[08:05:53]] [INFO] Refreshing screenshot...
[[08:05:53]] [SUCCESS] Screenshot refreshed
[[08:05:53]] [INFO] Refreshing screenshot...
[[08:05:50]] [SUCCESS] Screenshot refreshed successfully
[[08:05:50]] [SUCCESS] Screenshot refreshed successfully
[[08:05:50]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:05:49]] [SUCCESS] Screenshot refreshed
[[08:05:49]] [INFO] Refreshing screenshot...
[[08:05:44]] [SUCCESS] Screenshot refreshed successfully
[[08:05:44]] [SUCCESS] Screenshot refreshed successfully
[[08:05:44]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:05:43]] [SUCCESS] Screenshot refreshed
[[08:05:43]] [INFO] Refreshing screenshot...
[[08:05:40]] [SUCCESS] Screenshot refreshed successfully
[[08:05:40]] [SUCCESS] Screenshot refreshed successfully
[[08:05:39]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:05:39]] [SUCCESS] Screenshot refreshed
[[08:05:39]] [INFO] Refreshing screenshot...
[[08:05:36]] [SUCCESS] Screenshot refreshed successfully
[[08:05:36]] [SUCCESS] Screenshot refreshed successfully
[[08:05:35]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:05:35]] [SUCCESS] Screenshot refreshed
[[08:05:35]] [INFO] Refreshing screenshot...
[[08:05:28]] [SUCCESS] Screenshot refreshed successfully
[[08:05:28]] [SUCCESS] Screenshot refreshed successfully
[[08:05:28]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:05:27]] [SUCCESS] Screenshot refreshed
[[08:05:27]] [INFO] Refreshing screenshot...
[[08:05:22]] [SUCCESS] Screenshot refreshed successfully
[[08:05:22]] [SUCCESS] Screenshot refreshed successfully
[[08:05:21]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:05:21]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:05:21]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:05:21]] [INFO] vKo6Ox3YrP=running
[[08:05:21]] [INFO] Executing action 53/576: cleanupSteps action
[[08:05:20]] [SUCCESS] Screenshot refreshed
[[08:05:20]] [INFO] Refreshing screenshot...
[[08:05:20]] [INFO] x4yLCZHaCR=pass
[[08:05:17]] [SUCCESS] Screenshot refreshed successfully
[[08:05:17]] [SUCCESS] Screenshot refreshed successfully
[[08:05:17]] [INFO] x4yLCZHaCR=running
[[08:05:17]] [INFO] Executing action 52/576: Terminate app: env[appid]
[[08:05:17]] [SUCCESS] Screenshot refreshed
[[08:05:17]] [INFO] Refreshing screenshot...
[[08:05:17]] [INFO] 2p13JoJbbA=pass
[[08:05:13]] [SUCCESS] Screenshot refreshed successfully
[[08:05:13]] [SUCCESS] Screenshot refreshed successfully
[[08:05:13]] [INFO] 2p13JoJbbA=running
[[08:05:13]] [INFO] Executing action 51/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:05:12]] [SUCCESS] Screenshot refreshed
[[08:05:12]] [INFO] Refreshing screenshot...
[[08:05:12]] [INFO] 2p13JoJbbA=pass
[[08:05:08]] [SUCCESS] Screenshot refreshed successfully
[[08:05:08]] [SUCCESS] Screenshot refreshed successfully
[[08:05:08]] [INFO] 2p13JoJbbA=running
[[08:05:08]] [INFO] Executing action 50/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:05:07]] [SUCCESS] Screenshot refreshed
[[08:05:07]] [INFO] Refreshing screenshot...
[[08:05:07]] [INFO] nyBidG0kHp=pass
[[08:05:00]] [INFO] nyBidG0kHp=running
[[08:05:00]] [INFO] Executing action 49/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[08:05:00]] [SUCCESS] Screenshot refreshed successfully
[[08:05:00]] [SUCCESS] Screenshot refreshed successfully
[[08:05:00]] [SUCCESS] Screenshot refreshed
[[08:05:00]] [INFO] Refreshing screenshot...
[[08:05:00]] [INFO] F4NGh9HrLw=pass
[[08:04:55]] [SUCCESS] Screenshot refreshed successfully
[[08:04:55]] [SUCCESS] Screenshot refreshed successfully
[[08:04:55]] [INFO] F4NGh9HrLw=running
[[08:04:55]] [INFO] Executing action 48/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:04:55]] [SUCCESS] Screenshot refreshed
[[08:04:55]] [INFO] Refreshing screenshot...
[[08:04:55]] [INFO] VtMfqK1V9t=pass
[[08:04:47]] [SUCCESS] Screenshot refreshed successfully
[[08:04:47]] [SUCCESS] Screenshot refreshed successfully
[[08:04:47]] [INFO] VtMfqK1V9t=running
[[08:04:47]] [INFO] Executing action 47/576: Tap on element with accessibility_id: Add to bag
[[08:04:46]] [SUCCESS] Screenshot refreshed
[[08:04:46]] [INFO] Refreshing screenshot...
[[08:04:46]] [INFO] NOnuFzXy63=pass
[[08:04:42]] [SUCCESS] Screenshot refreshed successfully
[[08:04:42]] [SUCCESS] Screenshot refreshed successfully
[[08:04:42]] [INFO] NOnuFzXy63=running
[[08:04:42]] [INFO] Executing action 46/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:04:41]] [SUCCESS] Screenshot refreshed
[[08:04:41]] [INFO] Refreshing screenshot...
[[08:04:41]] [INFO] kz9lnCdwoH=pass
[[08:04:37]] [SUCCESS] Screenshot refreshed successfully
[[08:04:37]] [SUCCESS] Screenshot refreshed successfully
[[08:04:37]] [INFO] kz9lnCdwoH=running
[[08:04:37]] [INFO] Executing action 45/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[08:04:37]] [SUCCESS] Screenshot refreshed
[[08:04:37]] [INFO] Refreshing screenshot...
[[08:04:37]] [INFO] kz9lnCdwoH=pass
[[08:04:33]] [SUCCESS] Screenshot refreshed successfully
[[08:04:33]] [SUCCESS] Screenshot refreshed successfully
[[08:04:33]] [INFO] kz9lnCdwoH=running
[[08:04:33]] [INFO] Executing action 44/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:04:32]] [SUCCESS] Screenshot refreshed
[[08:04:32]] [INFO] Refreshing screenshot...
[[08:04:32]] [INFO] qIF9CVPc56=pass
[[08:04:28]] [SUCCESS] Screenshot refreshed successfully
[[08:04:28]] [SUCCESS] Screenshot refreshed successfully
[[08:04:28]] [INFO] qIF9CVPc56=running
[[08:04:28]] [INFO] Executing action 43/576: iOS Function: text - Text: "mat"
[[08:04:28]] [SUCCESS] Screenshot refreshed
[[08:04:28]] [INFO] Refreshing screenshot...
[[08:04:28]] [INFO] yEga5MkcRe=pass
[[08:04:23]] [SUCCESS] Screenshot refreshed successfully
[[08:04:23]] [SUCCESS] Screenshot refreshed successfully
[[08:04:23]] [INFO] yEga5MkcRe=running
[[08:04:23]] [INFO] Executing action 42/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[08:04:23]] [SUCCESS] Screenshot refreshed
[[08:04:23]] [INFO] Refreshing screenshot...
[[08:04:23]] [INFO] F4NGh9HrLw=pass
[[08:04:19]] [SUCCESS] Screenshot refreshed successfully
[[08:04:19]] [SUCCESS] Screenshot refreshed successfully
[[08:04:18]] [INFO] F4NGh9HrLw=running
[[08:04:18]] [INFO] Executing action 41/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[08:04:18]] [SUCCESS] Screenshot refreshed
[[08:04:18]] [INFO] Refreshing screenshot...
[[08:04:18]] [INFO] kz9lnCdwoH=pass
[[08:04:14]] [SUCCESS] Screenshot refreshed successfully
[[08:04:14]] [SUCCESS] Screenshot refreshed successfully
[[08:04:13]] [INFO] kz9lnCdwoH=running
[[08:04:13]] [INFO] Executing action 40/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[08:04:13]] [SUCCESS] Screenshot refreshed
[[08:04:13]] [INFO] Refreshing screenshot...
[[08:04:13]] [INFO] kz9lnCdwoH=pass
[[08:04:09]] [SUCCESS] Screenshot refreshed successfully
[[08:04:09]] [SUCCESS] Screenshot refreshed successfully
[[08:04:09]] [INFO] kz9lnCdwoH=running
[[08:04:09]] [INFO] Executing action 39/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:04:09]] [SUCCESS] Screenshot refreshed
[[08:04:09]] [INFO] Refreshing screenshot...
[[08:04:09]] [INFO] JRheDTvpJf=pass
[[08:04:05]] [SUCCESS] Screenshot refreshed successfully
[[08:04:05]] [SUCCESS] Screenshot refreshed successfully
[[08:04:04]] [INFO] JRheDTvpJf=running
[[08:04:04]] [INFO] Executing action 38/576: iOS Function: text - Text: "Kid toy"
[[08:04:04]] [SUCCESS] Screenshot refreshed
[[08:04:04]] [INFO] Refreshing screenshot...
[[08:04:04]] [INFO] yEga5MkcRe=pass
[[08:04:00]] [SUCCESS] Screenshot refreshed successfully
[[08:04:00]] [SUCCESS] Screenshot refreshed successfully
[[08:04:00]] [INFO] yEga5MkcRe=running
[[08:04:00]] [INFO] Executing action 37/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[08:03:59]] [SUCCESS] Screenshot refreshed
[[08:03:59]] [INFO] Refreshing screenshot...
[[08:03:59]] [INFO] F4NGh9HrLw=pass
[[08:03:56]] [SUCCESS] Screenshot refreshed successfully
[[08:03:56]] [SUCCESS] Screenshot refreshed successfully
[[08:03:55]] [INFO] F4NGh9HrLw=running
[[08:03:55]] [INFO] Executing action 36/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[08:03:55]] [SUCCESS] Screenshot refreshed
[[08:03:55]] [INFO] Refreshing screenshot...
[[08:03:55]] [INFO] XPEr3w6Zof=pass
[[08:03:49]] [SUCCESS] Screenshot refreshed successfully
[[08:03:49]] [SUCCESS] Screenshot refreshed successfully
[[08:03:49]] [INFO] XPEr3w6Zof=running
[[08:03:49]] [INFO] Executing action 35/576: Restart app: env[appid]
[[08:03:48]] [SUCCESS] Screenshot refreshed
[[08:03:48]] [INFO] Refreshing screenshot...
[[08:03:48]] [INFO] PiQRBWBe3E=pass
[[08:03:44]] [SUCCESS] Screenshot refreshed successfully
[[08:03:44]] [SUCCESS] Screenshot refreshed successfully
[[08:03:44]] [INFO] PiQRBWBe3E=running
[[08:03:44]] [INFO] Executing action 34/576: Tap on image: env[device-back-img]
[[08:03:44]] [SUCCESS] Screenshot refreshed
[[08:03:44]] [INFO] Refreshing screenshot...
[[08:03:44]] [INFO] GWoppouz1l=pass
[[08:03:41]] [SUCCESS] Screenshot refreshed successfully
[[08:03:41]] [SUCCESS] Screenshot refreshed successfully
[[08:03:41]] [INFO] GWoppouz1l=running
[[08:03:41]] [INFO] Executing action 33/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[08:03:40]] [SUCCESS] Screenshot refreshed
[[08:03:40]] [INFO] Refreshing screenshot...
[[08:03:40]] [INFO] B6GDXWAmWp=pass
[[08:03:36]] [SUCCESS] Screenshot refreshed successfully
[[08:03:36]] [SUCCESS] Screenshot refreshed successfully
[[08:03:36]] [INFO] B6GDXWAmWp=running
[[08:03:36]] [INFO] Executing action 32/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[08:03:35]] [SUCCESS] Screenshot refreshed
[[08:03:35]] [INFO] Refreshing screenshot...
[[08:03:35]] [INFO] mtYqeDttRc=pass
[[08:03:32]] [SUCCESS] Screenshot refreshed successfully
[[08:03:32]] [SUCCESS] Screenshot refreshed successfully
[[08:03:31]] [INFO] mtYqeDttRc=running
[[08:03:31]] [INFO] Executing action 31/576: Tap on image: env[paypal-close-img]
[[08:03:30]] [SUCCESS] Screenshot refreshed
[[08:03:30]] [INFO] Refreshing screenshot...
[[08:03:30]] [INFO] q6cKxgMAIn=pass
[[08:03:23]] [SUCCESS] Screenshot refreshed successfully
[[08:03:23]] [SUCCESS] Screenshot refreshed successfully
[[08:03:23]] [INFO] q6cKxgMAIn=running
[[08:03:23]] [INFO] Executing action 30/576: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[08:03:23]] [SUCCESS] Screenshot refreshed
[[08:03:23]] [INFO] Refreshing screenshot...
[[08:03:23]] [INFO] KRQDBv2D3A=pass
[[08:03:19]] [SUCCESS] Screenshot refreshed successfully
[[08:03:19]] [SUCCESS] Screenshot refreshed successfully
[[08:03:19]] [INFO] KRQDBv2D3A=running
[[08:03:19]] [INFO] Executing action 29/576: Tap on image: env[device-back-img]
[[08:03:18]] [SUCCESS] Screenshot refreshed
[[08:03:18]] [INFO] Refreshing screenshot...
[[08:03:18]] [INFO] P4b2BITpCf=pass
[[08:03:15]] [SUCCESS] Screenshot refreshed successfully
[[08:03:15]] [SUCCESS] Screenshot refreshed successfully
[[08:03:15]] [INFO] P4b2BITpCf=running
[[08:03:15]] [INFO] Executing action 28/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[08:03:14]] [SUCCESS] Screenshot refreshed
[[08:03:14]] [INFO] Refreshing screenshot...
[[08:03:14]] [INFO] inrxgdWzXr=pass
[[08:03:08]] [SUCCESS] Screenshot refreshed successfully
[[08:03:08]] [SUCCESS] Screenshot refreshed successfully
[[08:03:08]] [INFO] inrxgdWzXr=running
[[08:03:08]] [INFO] Executing action 27/576: Tap on element with accessibility_id: Learn more about Zip
[[08:03:08]] [SUCCESS] Screenshot refreshed
[[08:03:08]] [INFO] Refreshing screenshot...
[[08:03:08]] [INFO] Et3kvnFdxh=pass
[[08:03:04]] [SUCCESS] Screenshot refreshed successfully
[[08:03:04]] [SUCCESS] Screenshot refreshed successfully
[[08:03:04]] [INFO] Et3kvnFdxh=running
[[08:03:04]] [INFO] Executing action 26/576: Tap on image: env[device-back-img]
[[08:03:04]] [INFO] Skipping disabled action 25/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[08:03:03]] [SUCCESS] Screenshot refreshed
[[08:03:03]] [INFO] Refreshing screenshot...
[[08:03:03]] [INFO] pk2DLZFBmx=pass
[[08:02:57]] [SUCCESS] Screenshot refreshed successfully
[[08:02:57]] [SUCCESS] Screenshot refreshed successfully
[[08:02:57]] [INFO] pk2DLZFBmx=running
[[08:02:57]] [INFO] Executing action 24/576: Tap on element with accessibility_id: Learn more about AfterPay
[[08:02:56]] [SUCCESS] Screenshot refreshed
[[08:02:56]] [INFO] Refreshing screenshot...
[[08:02:56]] [INFO] ShJSdXvmVL=pass
[[08:02:49]] [SUCCESS] Screenshot refreshed successfully
[[08:02:49]] [SUCCESS] Screenshot refreshed successfully
[[08:02:48]] [INFO] ShJSdXvmVL=running
[[08:02:48]] [INFO] Executing action 23/576: Swipe up till element accessibilityid: "Learn more about AfterPay" is visible
[[08:02:48]] [SUCCESS] Screenshot refreshed
[[08:02:48]] [INFO] Refreshing screenshot...
[[08:02:48]] [INFO] sHQtYzpI4s=pass
[[08:02:43]] [SUCCESS] Screenshot refreshed successfully
[[08:02:43]] [SUCCESS] Screenshot refreshed successfully
[[08:02:42]] [INFO] sHQtYzpI4s=running
[[08:02:42]] [INFO] Executing action 22/576: Tap on image: env[closebtnimage]
[[08:02:42]] [SUCCESS] Screenshot refreshed
[[08:02:42]] [INFO] Refreshing screenshot...
[[08:02:42]] [INFO] 83tV9A4NOn=pass
[[08:02:38]] [SUCCESS] Screenshot refreshed successfully
[[08:02:38]] [SUCCESS] Screenshot refreshed successfully
[[08:02:38]] [INFO] 83tV9A4NOn=running
[[08:02:38]] [INFO] Executing action 21/576: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[08:02:37]] [SUCCESS] Screenshot refreshed
[[08:02:37]] [INFO] Refreshing screenshot...
[[08:02:37]] [INFO] dCqKBG3e7u=pass
[[08:02:32]] [SUCCESS] Screenshot refreshed successfully
[[08:02:32]] [SUCCESS] Screenshot refreshed successfully
[[08:02:32]] [INFO] dCqKBG3e7u=running
[[08:02:32]] [INFO] Executing action 20/576: Tap on image: env[product-share-img]
[[08:02:32]] [SUCCESS] Screenshot refreshed
[[08:02:32]] [INFO] Refreshing screenshot...
[[08:02:32]] [INFO] kAQ1yIIw3h=pass
[[08:01:56]] [SUCCESS] Screenshot refreshed successfully
[[08:01:56]] [SUCCESS] Screenshot refreshed successfully
[[08:01:55]] [INFO] kAQ1yIIw3h=running
[[08:01:55]] [INFO] Executing action 19/576: Tap on element with xpath:  (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::* with fallback: Coordinates (98, 308)
[[08:01:55]] [SUCCESS] Screenshot refreshed
[[08:01:55]] [INFO] Refreshing screenshot...
[[08:01:55]] [INFO] OmKfD9iBjD=pass
[[08:01:51]] [SUCCESS] Screenshot refreshed successfully
[[08:01:51]] [SUCCESS] Screenshot refreshed successfully
[[08:01:51]] [INFO] OmKfD9iBjD=running
[[08:01:51]] [INFO] Executing action 18/576: Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::*
[[08:01:50]] [SUCCESS] Screenshot refreshed
[[08:01:50]] [INFO] Refreshing screenshot...
[[08:01:50]] [INFO] dMl1PH9Dlc=pass
[[08:01:39]] [SUCCESS] Screenshot refreshed successfully
[[08:01:39]] [SUCCESS] Screenshot refreshed successfully
[[08:01:38]] [INFO] dMl1PH9Dlc=running
[[08:01:38]] [INFO] Executing action 17/576: Wait for 10 ms
[[08:01:38]] [SUCCESS] Screenshot refreshed
[[08:01:38]] [INFO] Refreshing screenshot...
[[08:01:38]] [INFO] eHLWiRoqqS=pass
[[08:01:33]] [SUCCESS] Screenshot refreshed successfully
[[08:01:33]] [SUCCESS] Screenshot refreshed successfully
[[08:01:33]] [INFO] eHLWiRoqqS=running
[[08:01:33]] [INFO] Executing action 16/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:01:32]] [SUCCESS] Screenshot refreshed
[[08:01:32]] [INFO] Refreshing screenshot...
[[08:01:32]] [INFO] huUnpMMjVR=pass
[[08:01:28]] [SUCCESS] Screenshot refreshed successfully
[[08:01:28]] [SUCCESS] Screenshot refreshed successfully
[[08:01:27]] [INFO] huUnpMMjVR=running
[[08:01:27]] [INFO] Executing action 15/576: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[08:01:27]] [SUCCESS] Screenshot refreshed
[[08:01:27]] [INFO] Refreshing screenshot...
[[08:01:27]] [INFO] XmAxcBtFI0=pass
[[08:01:23]] [SUCCESS] Screenshot refreshed successfully
[[08:01:23]] [SUCCESS] Screenshot refreshed successfully
[[08:01:23]] [INFO] XmAxcBtFI0=running
[[08:01:23]] [INFO] Executing action 14/576: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[08:01:22]] [SUCCESS] Screenshot refreshed
[[08:01:22]] [INFO] Refreshing screenshot...
[[08:01:22]] [INFO] ktAufkDJnF=pass
[[08:01:18]] [SUCCESS] Screenshot refreshed successfully
[[08:01:18]] [SUCCESS] Screenshot refreshed successfully
[[08:01:18]] [INFO] ktAufkDJnF=running
[[08:01:18]] [INFO] Executing action 13/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show")]
[[08:01:18]] [SUCCESS] Screenshot refreshed
[[08:01:18]] [INFO] Refreshing screenshot...
[[08:01:18]] [INFO] a50JhCx0ir=pass
[[08:01:14]] [SUCCESS] Screenshot refreshed successfully
[[08:01:14]] [SUCCESS] Screenshot refreshed successfully
[[08:01:14]] [INFO] a50JhCx0ir=running
[[08:01:14]] [INFO] Executing action 12/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[08:01:13]] [SUCCESS] Screenshot refreshed
[[08:01:13]] [INFO] Refreshing screenshot...
[[08:01:13]] [INFO] Y1O1clhMSJ=pass
[[08:01:09]] [SUCCESS] Screenshot refreshed successfully
[[08:01:09]] [SUCCESS] Screenshot refreshed successfully
[[08:01:09]] [INFO] Y1O1clhMSJ=running
[[08:01:09]] [INFO] Executing action 11/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[08:01:09]] [SUCCESS] Screenshot refreshed
[[08:01:09]] [INFO] Refreshing screenshot...
[[08:01:09]] [INFO] lYPskZt0Ya=pass
[[08:01:05]] [SUCCESS] Screenshot refreshed successfully
[[08:01:05]] [SUCCESS] Screenshot refreshed successfully
[[08:01:05]] [INFO] lYPskZt0Ya=running
[[08:01:05]] [INFO] Executing action 10/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:01:04]] [SUCCESS] Screenshot refreshed
[[08:01:04]] [INFO] Refreshing screenshot...
[[08:01:04]] [INFO] xUbWFa8Ok2=pass
[[08:01:00]] [SUCCESS] Screenshot refreshed successfully
[[08:01:00]] [SUCCESS] Screenshot refreshed successfully
[[08:01:00]] [INFO] xUbWFa8Ok2=running
[[08:01:00]] [INFO] Executing action 9/576: Tap on Text: "Latest"
[[08:00:59]] [SUCCESS] Screenshot refreshed
[[08:00:59]] [INFO] Refreshing screenshot...
[[08:00:59]] [INFO] RbNtEW6N9T=pass
[[08:00:55]] [SUCCESS] Screenshot refreshed successfully
[[08:00:55]] [SUCCESS] Screenshot refreshed successfully
[[08:00:55]] [INFO] RbNtEW6N9T=running
[[08:00:55]] [INFO] Executing action 8/576: Tap on Text: "Toys"
[[08:00:54]] [SUCCESS] Screenshot refreshed
[[08:00:54]] [INFO] Refreshing screenshot...
[[08:00:54]] [INFO] ltDXyWvtEz=pass
[[08:00:50]] [SUCCESS] Screenshot refreshed successfully
[[08:00:50]] [SUCCESS] Screenshot refreshed successfully
[[08:00:49]] [INFO] ltDXyWvtEz=running
[[08:00:49]] [INFO] Executing action 7/576: Tap on image: env[device-back-img]
[[08:00:49]] [SUCCESS] Screenshot refreshed
[[08:00:49]] [INFO] Refreshing screenshot...
[[08:00:49]] [INFO] QPKR6jUF9O=pass
[[08:00:46]] [SUCCESS] Screenshot refreshed successfully
[[08:00:46]] [SUCCESS] Screenshot refreshed successfully
[[08:00:46]] [INFO] QPKR6jUF9O=running
[[08:00:46]] [INFO] Executing action 6/576: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[08:00:46]] [SUCCESS] Screenshot refreshed
[[08:00:46]] [INFO] Refreshing screenshot...
[[08:00:46]] [INFO] vfwUVEyq6X=pass
[[08:00:43]] [SUCCESS] Screenshot refreshed successfully
[[08:00:43]] [SUCCESS] Screenshot refreshed successfully
[[08:00:43]] [INFO] vfwUVEyq6X=running
[[08:00:43]] [INFO] Executing action 5/576: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[08:00:42]] [SUCCESS] Screenshot refreshed
[[08:00:42]] [INFO] Refreshing screenshot...
[[08:00:42]] [INFO] Xr6F8gdd8q=pass
[[08:00:38]] [SUCCESS] Screenshot refreshed successfully
[[08:00:38]] [SUCCESS] Screenshot refreshed successfully
[[08:00:38]] [INFO] Xr6F8gdd8q=running
[[08:00:38]] [INFO] Executing action 4/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[08:00:38]] [SUCCESS] Screenshot refreshed
[[08:00:38]] [INFO] Refreshing screenshot...
[[08:00:38]] [INFO] Xr6F8gdd8q=pass
[[08:00:35]] [INFO] Xr6F8gdd8q=running
[[08:00:35]] [INFO] Executing action 3/576: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[08:00:35]] [SUCCESS] Screenshot refreshed successfully
[[08:00:35]] [SUCCESS] Screenshot refreshed successfully
[[08:00:34]] [SUCCESS] Screenshot refreshed
[[08:00:34]] [INFO] Refreshing screenshot...
[[08:00:34]] [INFO] F4NGh9HrLw=pass
[[08:00:32]] [INFO] Collapsed all test cases
[[08:00:29]] [SUCCESS] Screenshot refreshed successfully
[[08:00:29]] [SUCCESS] Screenshot refreshed successfully
[[08:00:29]] [INFO] F4NGh9HrLw=running
[[08:00:29]] [INFO] Executing action 2/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[08:00:28]] [SUCCESS] Screenshot refreshed
[[08:00:28]] [INFO] Refreshing screenshot...
[[08:00:28]] [INFO] H9fy9qcFbZ=pass
[[08:00:23]] [INFO] H9fy9qcFbZ=running
[[08:00:23]] [INFO] Executing action 1/576: Restart app: env[appid]
[[08:00:23]] [INFO] ExecutionManager: Starting execution of 576 actions...
[[08:00:23]] [SUCCESS] Cleared 1 screenshots from database
[[08:00:23]] [INFO] Clearing screenshots from database before execution...
[[08:00:23]] [SUCCESS] All screenshots deleted successfully
[[08:00:23]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[08:00:23]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250701_080023/screenshots
[[08:00:23]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250701_080023
[[08:00:23]] [SUCCESS] Report directory initialized successfully
[[08:00:23]] [INFO] Initializing report directory and screenshots folder for test suite...
[[08:00:08]] [SUCCESS] All screenshots deleted successfully
[[08:00:08]] [INFO] All actions cleared
[[08:00:08]] [INFO] Cleaning up screenshots...
[[08:00:03]] [SUCCESS] Screenshot refreshed successfully
[[08:00:01]] [SUCCESS] Screenshot refreshed
[[08:00:01]] [INFO] Refreshing screenshot...
[[08:00:00]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[08:00:00]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[07:59:54]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[07:58:58]] [SUCCESS] Found 1 device(s)
[[07:58:58]] [INFO] Refreshing device list...
