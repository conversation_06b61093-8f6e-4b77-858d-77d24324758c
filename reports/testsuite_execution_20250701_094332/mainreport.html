<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 7/1/2025, 10:26:56 AM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">8</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 01/07/2025, 10:26:56
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="53 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #1 Postcode Flow_NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            53 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">2664ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="failed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4846ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1158ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1923ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1736ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2053ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Search suburb or postcode <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3668ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                textClear action <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">3240ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-0-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "8053" <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">3333ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till accessibility_id=btnSaveOrContinue <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2794ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-0-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Save" <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">3260ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate" <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4655ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-0-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with text="Papanui" exists <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">10619ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-0-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">3999ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-0-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "Uno card" <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">2562ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1682ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-0-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Edit" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">3670ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Search suburb or postcode <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3673ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-0-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                textClear action <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">3063ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-0-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "0616" <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">3114ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till accessibility_id=btnSaveOrContinue <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2790ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-0-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Save" <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">3099ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="cKNu2QoRC1.png" data-action-id="cKNu2QoRC1" onclick="showStepDetails('step-0-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with text="Henderson" exists <span class="action-id-badge" title="Action ID: cKNu2QoRC1">cKNu2QoRC1</span>
                            </div>
                            <span class="test-step-duration">30144ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2195ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2041ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-0-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Edit" <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">3783ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Search suburb or postcode <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3632ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="0pwZCYAtOv.png" data-action-id="0pwZCYAtOv" onclick="showStepDetails('step-0-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                textClear action <span class="action-id-badge" title="Action ID: 0pwZCYAtOv">0pwZCYAtOv</span>
                            </div>
                            <span class="test-step-duration">3082ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="fDgFGQYpCw.png" data-action-id="fDgFGQYpCw" onclick="showStepDetails('step-0-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "8053" <span class="action-id-badge" title="Action ID: fDgFGQYpCw">fDgFGQYpCw</span>
                            </div>
                            <span class="test-step-duration">3142ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till accessibility_id=btnSaveOrContinue <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2770ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="Iab9zCfpqO.png" data-action-id="Iab9zCfpqO" onclick="showStepDetails('step-0-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Save" <span class="action-id-badge" title="Action ID: Iab9zCfpqO">Iab9zCfpqO</span>
                            </div>
                            <span class="test-step-duration">3083ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="9Pwdq32eUk.png" data-action-id="9Pwdq32eUk" onclick="showStepDetails('step-0-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with text="Papanui" exists <span class="action-id-badge" title="Action ID: 9Pwdq32eUk">9Pwdq32eUk</span>
                            </div>
                            <span class="test-step-duration">11101ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Add to bag <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4688ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1545ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2441ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Delivery"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1624ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="unknown"
                            data-screenshot="iSckENpXrN.png" data-action-id="iSckENpXrN" onclick="showStepDetails('step-0-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Collect" <span class="action-id-badge" title="Action ID: iSckENpXrN">iSckENpXrN</span>
                            </div>
                            <span class="test-step-duration">3105ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1677ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="unknown"
                            data-screenshot="q6kSH9e0MI.png" data-action-id="q6kSH9e0MI" onclick="showStepDetails('step-0-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Nearby" <span class="action-id-badge" title="Action ID: q6kSH9e0MI">q6kSH9e0MI</span>
                            </div>
                            <span class="test-step-duration">3373ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: delete <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4152ms</span>
                        </li>
                        <li class="test-step" data-step-id="42" data-status="unknown"
                            data-screenshot="kDpsm2D3xt.png" data-action-id="kDpsm2D3xt" onclick="showStepDetails('step-0-41')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "0616" <span class="action-id-badge" title="Action ID: kDpsm2D3xt">kDpsm2D3xt</span>
                            </div>
                            <span class="test-step-duration">5183ms</span>
                        </li>
                        <li class="test-step" data-step-id="43" data-status="unknown"
                            data-screenshot="OKCHAK6HCJ.png" data-action-id="OKCHAK6HCJ" onclick="showStepDetails('step-0-42')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "AUCKLAND" <span class="action-id-badge" title="Action ID: OKCHAK6HCJ">OKCHAK6HCJ</span>
                            </div>
                            <span class="test-step-duration">3338ms</span>
                        </li>
                        <li class="test-step" data-step-id="44" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-43')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Done <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4111ms</span>
                        </li>
                        <li class="test-step" data-step-id="45" data-status="unknown"
                            data-screenshot="3hOTINBVMf.png" data-action-id="3hOTINBVMf" onclick="showStepDetails('step-0-44')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: 3hOTINBVMf">3hOTINBVMf</span>
                            </div>
                            <span class="test-step-duration">2837ms</span>
                        </li>
                        <li class="test-step" data-step-id="46" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-45')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2113ms</span>
                        </li>
                        <li class="test-step" data-step-id="47" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-46')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1501ms</span>
                        </li>
                        <li class="test-step" data-step-id="48" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-47')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1909ms</span>
                        </li>
                        <li class="test-step" data-step-id="49" data-status="unknown"
                            data-screenshot="NcU6aex76k.png" data-action-id="NcU6aex76k" onclick="showStepDetails('step-0-48')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with text="Henderson" exists <span class="action-id-badge" title="Action ID: NcU6aex76k">NcU6aex76k</span>
                            </div>
                            <span class="test-step-duration">30448ms</span>
                        </li>
                        <li class="test-step" data-step-id="50" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-49')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2451ms</span>
                        </li>
                        <li class="test-step" data-step-id="51" data-status="unknown"
                            data-screenshot="OmKfD9iBjD.png" data-action-id="OmKfD9iBjD" onclick="showStepDetails('step-0-50')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: OmKfD9iBjD">OmKfD9iBjD</span>
                            </div>
                            <span class="test-step-duration">2606ms</span>
                        </li>
                        <li class="test-step" data-step-id="52" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-51')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2052ms</span>
                        </li>
                        <li class="test-step" data-step-id="53" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-0-52')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="37 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 Browse & PDP NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            37 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3212ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2116ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeOther[@name="txtShopMenuTitle"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1472ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Toys" <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">2747ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Latest" <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">2786ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">2756ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::* <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1781ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308) <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2293ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2088ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-1-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[product-share-img] <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">2367ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1733ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-1-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if image "product-share-logo.png" exists on screen <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">11445ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-1-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">2940ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Add to bag"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2147ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-1-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 50%) <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">3650ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-1-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "more" <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">3262ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2118ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1950ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-1-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "Kid toy" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">2559ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-1-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if image "search-result-test-se.png" exists on screen <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">21334ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-1-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">2365ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-1-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">2267ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1949ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1314ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1354ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="cKNu2QoRC1.png" data-action-id="cKNu2QoRC1" onclick="showStepDetails('step-1-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: cKNu2QoRC1">cKNu2QoRC1</span>
                            </div>
                            <span class="test-step-duration">2353ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-1-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">3218ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="0pwZCYAtOv.png" data-action-id="0pwZCYAtOv" onclick="showStepDetails('step-1-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: 0pwZCYAtOv">0pwZCYAtOv</span>
                            </div>
                            <span class="test-step-duration">4026ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="fDgFGQYpCw.png" data-action-id="fDgFGQYpCw" onclick="showStepDetails('step-1-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "notebook" <span class="action-id-badge" title="Action ID: fDgFGQYpCw">fDgFGQYpCw</span>
                            </div>
                            <span class="test-step-duration">2513ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1755ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2506ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2302ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1620ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2096ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2110ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="Iab9zCfpqO.png" data-action-id="Iab9zCfpqO" onclick="showStepDetails('step-1-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: env[appid] <span class="action-id-badge" title="Action ID: Iab9zCfpqO">Iab9zCfpqO</span>
                            </div>
                            <span class="test-step-duration">1044ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-1-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="27 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #3 Delivery & CNC- NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            27 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-2-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3194ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-2-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4867ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-2-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1160ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2059ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-2-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-2-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">4015ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-2-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "P_43250042" <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">2413ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1547ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2069ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1291ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2070ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-2-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if image "cnc-tab-se.png" exists on screen <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">11201ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-2-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Collect" <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">3121ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-2-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">2762ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2224ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-2-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">2288ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2050ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-2-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">5069ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2072ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2110ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-2-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">3988ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-2-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "P_43250042" <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">3791ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1612ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2072ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2062ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-2-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Delivery Buy Step NZ (33 steps) <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-2-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="39 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #4 NZ- MyAccount
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            39 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-3-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3205ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-3-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">5017ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-3-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4811ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-3-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">1166ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-3-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3198ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2265ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="txtMy orders"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1595ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy orders"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2073ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-3-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">5027ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="main"]//XCUIElementTypeLink)[4] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2194ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">8538ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2065ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-3-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: Print order details <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3858ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2366ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-3-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">2923ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-3-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Return" <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">3127ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2344ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2073ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1599ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-3-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "details" <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">2864ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-3-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">2279ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-3-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "address" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">2877ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-3-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">2823ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-3-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "payment" <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">2837ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-3-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">2203ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="cKNu2QoRC1.png" data-action-id="cKNu2QoRC1" onclick="showStepDetails('step-3-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: cKNu2QoRC1">cKNu2QoRC1</span>
                            </div>
                            <span class="test-step-duration">2143ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-3-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">2616ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="0pwZCYAtOv.png" data-action-id="0pwZCYAtOv" onclick="showStepDetails('step-3-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "locator" <span class="action-id-badge" title="Action ID: 0pwZCYAtOv">0pwZCYAtOv</span>
                            </div>
                            <span class="test-step-duration">2852ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="fDgFGQYpCw.png" data-action-id="fDgFGQYpCw" onclick="showStepDetails('step-3-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Nearby" <span class="action-id-badge" title="Action ID: fDgFGQYpCw">fDgFGQYpCw</span>
                            </div>
                            <span class="test-step-duration">5896ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Allow While Using App"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">21297ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="Iab9zCfpqO.png" data-action-id="Iab9zCfpqO" onclick="showStepDetails('step-3-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap and Type at (29, 262): "0616" <span class="action-id-badge" title="Action ID: Iab9zCfpqO">Iab9zCfpqO</span>
                            </div>
                            <span class="test-step-duration">5595ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="9Pwdq32eUk.png" data-action-id="9Pwdq32eUk" onclick="showStepDetails('step-3-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "AUCKLAND" <span class="action-id-badge" title="Action ID: 9Pwdq32eUk">9Pwdq32eUk</span>
                            </div>
                            <span class="test-step-duration">3410ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2340ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="iSckENpXrN.png" data-action-id="iSckENpXrN" onclick="showStepDetails('step-3-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: iSckENpXrN">iSckENpXrN</span>
                            </div>
                            <span class="test-step-duration">2342ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="q6kSH9e0MI.png" data-action-id="q6kSH9e0MI" onclick="showStepDetails('step-3-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Customer" <span class="action-id-badge" title="Action ID: q6kSH9e0MI">q6kSH9e0MI</span>
                            </div>
                            <span class="test-step-duration">2787ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="kDpsm2D3xt.png" data-action-id="kDpsm2D3xt" onclick="showStepDetails('step-3-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: kDpsm2D3xt">kDpsm2D3xt</span>
                            </div>
                            <span class="test-step-duration">2801ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="passed"
                            data-screenshot="OKCHAK6HCJ.png" data-action-id="OKCHAK6HCJ" onclick="showStepDetails('step-3-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "out" <span class="action-id-badge" title="Action ID: OKCHAK6HCJ">OKCHAK6HCJ</span>
                            </div>
                            <span class="test-step-duration">2763ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-3-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="28 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #5 All Sign ins NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            28 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-4-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3203ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-4-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3759ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-4-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1158ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1923ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-4-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (5 steps) <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2149ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-4-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">2616ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2079ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2693ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1938ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-4-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">1185ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1920ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-4-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (5 steps) <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1893ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1605ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2065ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-4-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">2642ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2379ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-4-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">3231ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2097ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2007ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-4-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">1197ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-4-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (5 steps) <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2043ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-4-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">2613ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2602ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-4-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">5024ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-4-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="41 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #6 Kmart-Prod Sign in NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            41 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-5-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3313ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4815ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-5-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1178ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1877ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2437ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-5-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "env[uname]" <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">3655ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2436ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-5-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "env[pwd]" <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">2783ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1289ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2073ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">4349ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3385ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">1756ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3787ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-5-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">1151ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1938ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-5-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Apple" <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">3187ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-5-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 10 ms <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">10033ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-5-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Passcode" <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">2004ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="5"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1577ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="9"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1543ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="1"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1558ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="2"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1529ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="3"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1822ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="4"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1532ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">6079ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2069ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">4332ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2739ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">1749ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3754ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-5-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">1170ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1950ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-5-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Google" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">3157ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3433ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2584ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2089ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">4361ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2083ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-5-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">5024ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-5-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="46 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #7 WishList NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            46 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-6-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3225ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-6-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">5927ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-6-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1186ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1959ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-6-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-6-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">3985ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-6-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "Uno card" <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">2402ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1614ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1742ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2330ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1981ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2426ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-6-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">2534ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2228ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2112ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3214ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-6-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">2657ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1725ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2231ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1996ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2420ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3155ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1356ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2078ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-6-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">5019ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-6-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Move" <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">2909ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1963ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-6-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">5026ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-6-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">2835ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1948ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1953ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2437ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="Move to wishlist"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">5241ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1654ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2146ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-6-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">2227ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2470ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3198ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-6-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">3549ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3215ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="passed"
                            data-screenshot="cKNu2QoRC1.png" data-action-id="cKNu2QoRC1" onclick="showStepDetails('step-6-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: cKNu2QoRC1">cKNu2QoRC1</span>
                            </div>
                            <span class="test-step-duration">2686ms</span>
                        </li>
                        <li class="test-step" data-step-id="42" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-41')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1928ms</span>
                        </li>
                        <li class="test-step" data-step-id="43" data-status="passed"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-6-42')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">2632ms</span>
                        </li>
                        <li class="test-step" data-step-id="44" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-43')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2076ms</span>
                        </li>
                        <li class="test-step" data-step-id="45" data-status="passed"
                            data-screenshot="0pwZCYAtOv.png" data-action-id="0pwZCYAtOv" onclick="showStepDetails('step-6-44')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 10%) <span class="action-id-badge" title="Action ID: 0pwZCYAtOv">0pwZCYAtOv</span>
                            </div>
                            <span class="test-step-duration">2387ms</span>
                        </li>
                        <li class="test-step" data-step-id="46" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-6-45')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="39 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #8 App Settings NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            39 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-7-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3223ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2909ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-7-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">1187ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-7-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-7-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">1095ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-7-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">1228ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-7-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Wi-Fi" <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">2929ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1117ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-7-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">3314ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">256ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">735ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">218ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">644ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">201ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">633ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">177ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-7-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">134ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">750ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-7-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">5024ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-7-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">3271ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2067ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-7-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">2554ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-7-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "out" <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">2713ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="mobilesafa.png" data-action-id="mobilesafa" onclick="showStepDetails('step-7-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.apple.mobilesafari <span class="action-id-badge" title="Action ID: mobilesafa">mobilesafa</span>
                            </div>
                            <span class="test-step-duration">3275ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1193ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-7-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "kmart nz" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">1844ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.co.nz"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1619ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2053ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-7-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">4088ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-7-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "notebook" <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">2432ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1839ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3013ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-7-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: Add to bag <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4669ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-7-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">3354ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2086ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2050ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2093ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2744ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-7-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="41 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #9 NZ- Performance
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            41 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="kAQ1yIIw3h.png" data-action-id="kAQ1yIIw3h" onclick="showStepDetails('step-8-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: kAQ1yIIw3h">kAQ1yIIw3h</span>
                            </div>
                            <span class="test-step-duration">3298ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-8-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">4160ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="ly2oT3zqmf.png" data-action-id="ly2oT3zqmf" onclick="showStepDetails('step-8-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "P_43515028" <span class="action-id-badge" title="Action ID: ly2oT3zqmf">ly2oT3zqmf</span>
                            </div>
                            <span class="test-step-duration">2393ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2034ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XLpUP3Wr93.png" data-action-id="XLpUP3Wr93" onclick="showStepDetails('step-8-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: XLpUP3Wr93">XLpUP3Wr93</span>
                            </div>
                            <span class="test-step-duration">4326ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">12226ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="H9fy9qcFbZ.png" data-action-id="H9fy9qcFbZ" onclick="showStepDetails('step-8-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Done" <span class="action-id-badge" title="Action ID: H9fy9qcFbZ">H9fy9qcFbZ</span>
                            </div>
                            <span class="test-step-duration">4512ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="2mhi7GOrlS.png" data-action-id="2mhi7GOrlS" onclick="showStepDetails('step-8-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: 2mhi7GOrlS">2mhi7GOrlS</span>
                            </div>
                            <span class="test-step-duration">3220ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2082ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="LcYLwUffqj.png" data-action-id="LcYLwUffqj" onclick="showStepDetails('step-8-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: LcYLwUffqj">LcYLwUffqj</span>
                            </div>
                            <span class="test-step-duration">4026ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-8-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "kids toys" <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">3112ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="Pagination.png" data-action-id="Pagination" onclick="showStepDetails('step-8-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Click_Paginations (10 steps) <span class="action-id-badge" title="Action ID: Pagination">Pagination</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="OQ1fr8NUlV.png" data-action-id="OQ1fr8NUlV" onclick="showStepDetails('step-8-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: OQ1fr8NUlV">OQ1fr8NUlV</span>
                            </div>
                            <span class="test-step-duration">3245ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2015ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="xLGm9FefWE.png" data-action-id="xLGm9FefWE" onclick="showStepDetails('step-8-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Toys" <span class="action-id-badge" title="Action ID: xLGm9FefWE">xLGm9FefWE</span>
                            </div>
                            <span class="test-step-duration">3263ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-8-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Age" <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">2783ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="eHvkAVake5.png" data-action-id="eHvkAVake5" onclick="showStepDetails('step-8-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Months" <span class="action-id-badge" title="Action ID: eHvkAVake5">eHvkAVake5</span>
                            </div>
                            <span class="test-step-duration">2681ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-8-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (5%, 50%) to (90%, 50%) <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">3705ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="cKNu2QoRC1.png" data-action-id="cKNu2QoRC1" onclick="showStepDetails('step-8-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (5%, 50%) to (90%, 50%) <span class="action-id-badge" title="Action ID: cKNu2QoRC1">cKNu2QoRC1</span>
                            </div>
                            <span class="test-step-duration">3575ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3127ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-8-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3744ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-8-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">1177ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2434ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="0pwZCYAtOv.png" data-action-id="0pwZCYAtOv" onclick="showStepDetails('step-8-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "env[uname]" <span class="action-id-badge" title="Action ID: 0pwZCYAtOv">0pwZCYAtOv</span>
                            </div>
                            <span class="test-step-duration">3077ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2462ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="fDgFGQYpCw.png" data-action-id="fDgFGQYpCw" onclick="showStepDetails('step-8-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "env[pwd]" <span class="action-id-badge" title="Action ID: fDgFGQYpCw">fDgFGQYpCw</span>
                            </div>
                            <span class="test-step-duration">3174ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1259ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="Iab9zCfpqO.png" data-action-id="Iab9zCfpqO" onclick="showStepDetails('step-8-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: Iab9zCfpqO">Iab9zCfpqO</span>
                            </div>
                            <span class="test-step-duration">3906ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="9Pwdq32eUk.png" data-action-id="9Pwdq32eUk" onclick="showStepDetails('step-8-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text - Text: "enn[cooker-id]" <span class="action-id-badge" title="Action ID: 9Pwdq32eUk">9Pwdq32eUk</span>
                            </div>
                            <span class="test-step-duration">2484ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1590ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2200ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2701ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2568ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2549ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="iSckENpXrN.png" data-action-id="iSckENpXrN" onclick="showStepDetails('step-8-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (90%, 20%) to (30%, 20%) <span class="action-id-badge" title="Action ID: iSckENpXrN">iSckENpXrN</span>
                            </div>
                            <span class="test-step-duration">1822ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="q6kSH9e0MI.png" data-action-id="q6kSH9e0MI" onclick="showStepDetails('step-8-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (90%, 20%) to (30%, 20%) <span class="action-id-badge" title="Action ID: q6kSH9e0MI">q6kSH9e0MI</span>
                            </div>
                            <span class="test-step-duration">1925ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1884ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="passed"
                            data-screenshot="kDpsm2D3xt.png" data-action-id="kDpsm2D3xt" onclick="showStepDetails('step-8-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: kDpsm2D3xt">kDpsm2D3xt</span>
                            </div>
                            <span class="test-step-duration">2422ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="OKCHAK6HCJ.png" data-action-id="OKCHAK6HCJ" onclick="showStepDetails('step-8-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "out" <span class="action-id-badge" title="Action ID: OKCHAK6HCJ">OKCHAK6HCJ</span>
                            </div>
                            <span class="test-step-duration">2752ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-8-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till accessibility_id=txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2885ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="passed"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-8-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 01/07/2025, 10:26:56","testCases":[{"name":"Postcode Flow_NZ\n                            \n                            \n                                    nztest\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            53 actions","status":"failed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"2664ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"failed","duration":"4846ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1158ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"1923ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"unknown","duration":"0ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Wait till xpath=//XCUIElementTypeOther[contains(@name,\"Deliver\")]","status":"unknown","duration":"1736ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeOther[contains(@name,\"Deliver\")]","status":"unknown","duration":"2053ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Search suburb or postcode","status":"unknown","duration":"3668ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"textClear action","status":"unknown","duration":"3240ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Tap on Text: \"8053\"","status":"unknown","duration":"3333ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Wait till accessibility_id=btnSaveOrContinue","status":"unknown","duration":"2794ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on Text: \"Save\"","status":"unknown","duration":"3260ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"If exists: accessibility_id=\"btnUpdate\" (timeout: 10s) → Then click element: accessibility_id=\"btnUpdate\"","status":"unknown","duration":"4655ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Check if element with text=\"Papanui\" exists","status":"unknown","duration":"10619ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"3999ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"iOS Function: text - Text: \"Uno card\"","status":"unknown","duration":"2562ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"unknown","duration":"1682ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Edit\"","status":"unknown","duration":"3670ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on element with accessibility_id: Search suburb or postcode","status":"unknown","duration":"3673ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"textClear action","status":"unknown","duration":"3063ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on Text: \"0616\"","status":"unknown","duration":"3114ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Wait till accessibility_id=btnSaveOrContinue","status":"unknown","duration":"2790ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on Text: \"Save\"","status":"unknown","duration":"3099ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Check if element with text=\"Henderson\" exists","status":"unknown","duration":"30144ms","action_id":"cKNu2QoRC1","screenshot_filename":"cKNu2QoRC1.png","report_screenshot":"cKNu2QoRC1.png","resolved_screenshot":"screenshots/cKNu2QoRC1.png","clean_action_id":"cKNu2QoRC1","prefixed_action_id":"al_cKNu2QoRC1","action_id_screenshot":"screenshots/cKNu2QoRC1.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"2195ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"unknown","duration":"2041ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Edit\"","status":"unknown","duration":"3783ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"Tap on element with accessibility_id: Search suburb or postcode","status":"unknown","duration":"3632ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"textClear action","status":"unknown","duration":"3082ms","action_id":"0pwZCYAtOv","screenshot_filename":"0pwZCYAtOv.png","report_screenshot":"0pwZCYAtOv.png","resolved_screenshot":"screenshots/0pwZCYAtOv.png","clean_action_id":"0pwZCYAtOv","prefixed_action_id":"al_0pwZCYAtOv","action_id_screenshot":"screenshots/0pwZCYAtOv.png"},{"name":"Tap on Text: \"8053\"","status":"unknown","duration":"3142ms","action_id":"fDgFGQYpCw","screenshot_filename":"fDgFGQYpCw.png","report_screenshot":"fDgFGQYpCw.png","resolved_screenshot":"screenshots/fDgFGQYpCw.png","clean_action_id":"fDgFGQYpCw","prefixed_action_id":"al_fDgFGQYpCw","action_id_screenshot":"screenshots/fDgFGQYpCw.png"},{"name":"Wait till accessibility_id=btnSaveOrContinue","status":"unknown","duration":"2770ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on Text: \"Save\"","status":"unknown","duration":"3083ms","action_id":"Iab9zCfpqO","screenshot_filename":"Iab9zCfpqO.png","report_screenshot":"Iab9zCfpqO.png","resolved_screenshot":"screenshots/Iab9zCfpqO.png","clean_action_id":"Iab9zCfpqO","prefixed_action_id":"al_Iab9zCfpqO","action_id_screenshot":"screenshots/Iab9zCfpqO.png"},{"name":"Check if element with text=\"Papanui\" exists","status":"unknown","duration":"11101ms","action_id":"9Pwdq32eUk","screenshot_filename":"9Pwdq32eUk.png","report_screenshot":"9Pwdq32eUk.png","resolved_screenshot":"screenshots/9Pwdq32eUk.png","clean_action_id":"9Pwdq32eUk","prefixed_action_id":"al_9Pwdq32eUk","action_id_screenshot":"screenshots/9Pwdq32eUk.png"},{"name":"Tap on element with accessibility_id: Add to bag","status":"unknown","duration":"4688ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists","status":"unknown","duration":"1545ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"unknown","duration":"2441ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]","status":"unknown","duration":"1624ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Collect\"","status":"unknown","duration":"3105ms","action_id":"iSckENpXrN","screenshot_filename":"iSckENpXrN.png","report_screenshot":"iSckENpXrN.png","resolved_screenshot":"screenshots/iSckENpXrN.png","clean_action_id":"iSckENpXrN","prefixed_action_id":"al_iSckENpXrN","action_id_screenshot":"screenshots/iSckENpXrN.png"},{"name":"Wait till xpath=//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]","status":"unknown","duration":"1677ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Nearby\"","status":"unknown","duration":"3373ms","action_id":"q6kSH9e0MI","screenshot_filename":"q6kSH9e0MI.png","report_screenshot":"q6kSH9e0MI.png","resolved_screenshot":"screenshots/q6kSH9e0MI.png","clean_action_id":"q6kSH9e0MI","prefixed_action_id":"al_q6kSH9e0MI","action_id_screenshot":"screenshots/q6kSH9e0MI.png"},{"name":"Tap on element with accessibility_id: delete","status":"unknown","duration":"4152ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): \"0616\"","status":"unknown","duration":"5183ms","action_id":"kDpsm2D3xt","screenshot_filename":"kDpsm2D3xt.png","report_screenshot":"kDpsm2D3xt.png","resolved_screenshot":"screenshots/kDpsm2D3xt.png","clean_action_id":"kDpsm2D3xt","prefixed_action_id":"al_kDpsm2D3xt","action_id_screenshot":"screenshots/kDpsm2D3xt.png"},{"name":"Tap on Text: \"AUCKLAND\"","status":"unknown","duration":"3338ms","action_id":"OKCHAK6HCJ","screenshot_filename":"OKCHAK6HCJ.png","report_screenshot":"OKCHAK6HCJ.png","resolved_screenshot":"screenshots/OKCHAK6HCJ.png","clean_action_id":"OKCHAK6HCJ","prefixed_action_id":"al_OKCHAK6HCJ","action_id_screenshot":"screenshots/OKCHAK6HCJ.png"},{"name":"Tap on element with accessibility_id: Done","status":"unknown","duration":"4111ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2837ms","action_id":"3hOTINBVMf","screenshot_filename":"3hOTINBVMf.png","report_screenshot":"3hOTINBVMf.png","resolved_screenshot":"screenshots/3hOTINBVMf.png","clean_action_id":"3hOTINBVMf","prefixed_action_id":"al_3hOTINBVMf","action_id_screenshot":"screenshots/3hOTINBVMf.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"unknown","duration":"2113ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"Continue shopping\"]","status":"unknown","duration":"1501ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]","status":"unknown","duration":"1909ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with text=\"Henderson\" exists","status":"unknown","duration":"30448ms","action_id":"NcU6aex76k","screenshot_filename":"NcU6aex76k.png","report_screenshot":"NcU6aex76k.png","resolved_screenshot":"screenshots/NcU6aex76k.png","clean_action_id":"NcU6aex76k","prefixed_action_id":"al_NcU6aex76k","action_id_screenshot":"screenshots/NcU6aex76k.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"2451ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2606ms","action_id":"OmKfD9iBjD","screenshot_filename":"OmKfD9iBjD.png","report_screenshot":"OmKfD9iBjD.png","resolved_screenshot":"screenshots/OmKfD9iBjD.png","clean_action_id":"OmKfD9iBjD","prefixed_action_id":"al_OmKfD9iBjD","action_id_screenshot":"screenshots/OmKfD9iBjD.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"2052ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Browse & PDP NZ\n                            \n                            \n                                    nztest\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            37 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3212ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]","status":"passed","duration":"2116ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeOther[@name=\"txtShopMenuTitle\"]","status":"passed","duration":"1472ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Toys\"","status":"passed","duration":"2747ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Tap on Text: \"Latest\"","status":"passed","duration":"2786ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2756ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Wait till xpath= (//XCUIElementTypeButton[contains(@name,\"bag Add\")])[1]/parent::*","status":"passed","duration":"1781ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)","status":"passed","duration":"2293ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"2088ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[product-share-img]","status":"passed","duration":"2367ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"Check out \")]\" exists","status":"passed","duration":"1733ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if image \"product-share-logo.png\" exists on screen","status":"passed","duration":"11445ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on image: banner-close-updated.png","status":"passed","duration":"2940ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Add to bag\"]","status":"passed","duration":"2147ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 50%)","status":"passed","duration":"3650ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on Text: \"more\"","status":"passed","duration":"3262ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]","status":"passed","duration":"2118ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]","status":"passed","duration":"1950ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"Kid toy\"","status":"passed","duration":"2559ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Check if image \"search-result-test-se.png\" exists on screen","status":"passed","duration":"21334ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2365ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2267ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]","status":"passed","duration":"1949ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeImage[@name=\"More\"]\" exists","status":"passed","duration":"1314ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Scan barcode\"]\" exists","status":"passed","duration":"1354ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2353ms","action_id":"cKNu2QoRC1","screenshot_filename":"cKNu2QoRC1.png","report_screenshot":"cKNu2QoRC1.png","resolved_screenshot":"screenshots/cKNu2QoRC1.png","clean_action_id":"cKNu2QoRC1","prefixed_action_id":"al_cKNu2QoRC1","action_id_screenshot":"screenshots/cKNu2QoRC1.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3218ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"4026ms","action_id":"0pwZCYAtOv","screenshot_filename":"0pwZCYAtOv.png","report_screenshot":"0pwZCYAtOv.png","resolved_screenshot":"screenshots/0pwZCYAtOv.png","clean_action_id":"0pwZCYAtOv","prefixed_action_id":"al_0pwZCYAtOv","action_id_screenshot":"screenshots/0pwZCYAtOv.png"},{"name":"iOS Function: text - Text: \"notebook\"","status":"passed","duration":"2513ms","action_id":"fDgFGQYpCw","screenshot_filename":"fDgFGQYpCw.png","report_screenshot":"fDgFGQYpCw.png","resolved_screenshot":"screenshots/fDgFGQYpCw.png","clean_action_id":"fDgFGQYpCw","prefixed_action_id":"al_fDgFGQYpCw","action_id_screenshot":"screenshots/fDgFGQYpCw.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1755ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]","status":"passed","duration":"2506ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"passed","duration":"2302ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"passed","duration":"1620ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"passed","duration":"2096ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"passed","duration":"2110ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: env[appid]","status":"passed","duration":"1044ms","action_id":"Iab9zCfpqO","screenshot_filename":"Iab9zCfpqO.png","report_screenshot":"Iab9zCfpqO.png","resolved_screenshot":"screenshots/Iab9zCfpqO.png","clean_action_id":"Iab9zCfpqO","prefixed_action_id":"al_Iab9zCfpqO","action_id_screenshot":"screenshots/Iab9zCfpqO.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Delivery & CNC- NZ\n                            \n                            \n                                    nztest\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            27 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3194ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"4867ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1160ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"2059ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"passed","duration":"0ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"4015ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"iOS Function: text - Text: \"P_43250042\"","status":"passed","duration":"2413ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1547ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]","status":"passed","duration":"2069ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists","status":"passed","duration":"1291ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"passed","duration":"2070ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if image \"cnc-tab-se.png\" exists on screen","status":"passed","duration":"11201ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on Text: \"Collect\"","status":"passed","duration":"3121ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2762ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"passed","duration":"2224ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: banner-close-updated.png","status":"passed","duration":"2288ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2050ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"5069ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2072ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"2110ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"3988ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"iOS Function: text - Text: \"P_43250042\"","status":"passed","duration":"3791ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1612ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]","status":"passed","duration":"2072ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"passed","duration":"2062ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Delivery Buy Step NZ (33 steps)","status":"passed","duration":"0ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"NZ- MyAccount\n                            \n                            \n                                    nztest\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            39 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3205ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5017ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"4811ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1166ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"passed","duration":"0ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"3198ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]","status":"passed","duration":"2265ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"txtMy orders\"]","status":"passed","duration":"1595ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMy orders\"]","status":"passed","duration":"2073ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5027ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"main\"]//XCUIElementTypeLink)[4]","status":"passed","duration":"2194ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Email tax invoice\"]\" is visible","status":"passed","duration":"8538ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Email tax invoice\"]","status":"passed","duration":"2065ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Print order details","status":"passed","duration":"3858ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Cancel\"]","status":"passed","duration":"2366ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2923ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on Text: \"Return\"","status":"passed","duration":"3127ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"2344ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2073ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]","status":"passed","duration":"1599ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"details\"","status":"passed","duration":"2864ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2279ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Tap on Text: \"address\"","status":"passed","duration":"2877ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2823ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on Text: \"payment\"","status":"passed","duration":"2837ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2203ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2143ms","action_id":"cKNu2QoRC1","screenshot_filename":"cKNu2QoRC1.png","report_screenshot":"cKNu2QoRC1.png","resolved_screenshot":"screenshots/cKNu2QoRC1.png","clean_action_id":"cKNu2QoRC1","prefixed_action_id":"al_cKNu2QoRC1","action_id_screenshot":"screenshots/cKNu2QoRC1.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2616ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"Tap on Text: \"locator\"","status":"passed","duration":"2852ms","action_id":"0pwZCYAtOv","screenshot_filename":"0pwZCYAtOv.png","report_screenshot":"0pwZCYAtOv.png","resolved_screenshot":"screenshots/0pwZCYAtOv.png","clean_action_id":"0pwZCYAtOv","prefixed_action_id":"al_0pwZCYAtOv","action_id_screenshot":"screenshots/0pwZCYAtOv.png"},{"name":"Tap on Text: \"Nearby\"","status":"passed","duration":"5896ms","action_id":"fDgFGQYpCw","screenshot_filename":"fDgFGQYpCw.png","report_screenshot":"fDgFGQYpCw.png","resolved_screenshot":"screenshots/fDgFGQYpCw.png","clean_action_id":"fDgFGQYpCw","prefixed_action_id":"al_fDgFGQYpCw","action_id_screenshot":"screenshots/fDgFGQYpCw.png"},{"name":"If exists: xpath=\"//XCUIElementTypeButton[@name=\"Allow While Using App\"]\" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name=\"Allow While Using App\"]","status":"passed","duration":"21297ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap and Type at (29, 262): \"0616\"","status":"passed","duration":"5595ms","action_id":"Iab9zCfpqO","screenshot_filename":"Iab9zCfpqO.png","report_screenshot":"Iab9zCfpqO.png","resolved_screenshot":"screenshots/Iab9zCfpqO.png","clean_action_id":"Iab9zCfpqO","prefixed_action_id":"al_Iab9zCfpqO","action_id_screenshot":"screenshots/Iab9zCfpqO.png"},{"name":"Tap on Text: \"AUCKLAND\"","status":"passed","duration":"3410ms","action_id":"9Pwdq32eUk","screenshot_filename":"9Pwdq32eUk.png","report_screenshot":"9Pwdq32eUk.png","resolved_screenshot":"screenshots/9Pwdq32eUk.png","clean_action_id":"9Pwdq32eUk","prefixed_action_id":"al_9Pwdq32eUk","action_id_screenshot":"screenshots/9Pwdq32eUk.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"2340ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2342ms","action_id":"iSckENpXrN","screenshot_filename":"iSckENpXrN.png","report_screenshot":"iSckENpXrN.png","resolved_screenshot":"screenshots/iSckENpXrN.png","clean_action_id":"iSckENpXrN","prefixed_action_id":"al_iSckENpXrN","action_id_screenshot":"screenshots/iSckENpXrN.png"},{"name":"Tap on Text: \"Customer\"","status":"passed","duration":"2787ms","action_id":"q6kSH9e0MI","screenshot_filename":"q6kSH9e0MI.png","report_screenshot":"q6kSH9e0MI.png","resolved_screenshot":"screenshots/q6kSH9e0MI.png","clean_action_id":"q6kSH9e0MI","prefixed_action_id":"al_q6kSH9e0MI","action_id_screenshot":"screenshots/q6kSH9e0MI.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2801ms","action_id":"kDpsm2D3xt","screenshot_filename":"kDpsm2D3xt.png","report_screenshot":"kDpsm2D3xt.png","resolved_screenshot":"screenshots/kDpsm2D3xt.png","clean_action_id":"kDpsm2D3xt","prefixed_action_id":"al_kDpsm2D3xt","action_id_screenshot":"screenshots/kDpsm2D3xt.png"},{"name":"Tap on Text: \"out\"","status":"passed","duration":"2763ms","action_id":"OKCHAK6HCJ","screenshot_filename":"OKCHAK6HCJ.png","report_screenshot":"OKCHAK6HCJ.png","resolved_screenshot":"screenshots/OKCHAK6HCJ.png","clean_action_id":"OKCHAK6HCJ","prefixed_action_id":"al_OKCHAK6HCJ","action_id_screenshot":"screenshots/OKCHAK6HCJ.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"All Sign ins NZ\n                            \n                            \n                                    nztest\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            28 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3203ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"3759ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1158ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1923ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (5 steps)","status":"passed","duration":"0ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2149ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2616ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2079ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"passed","duration":"2693ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtLog in\"]","status":"passed","duration":"1938ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1185ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1920ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (5 steps)","status":"passed","duration":"0ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"1893ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]","status":"passed","duration":"1605ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2065ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2642ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2379ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3231ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2097ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]","status":"passed","duration":"2007ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1197ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (5 steps)","status":"passed","duration":"0ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2043ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2613ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2602ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5024ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Kmart-Prod Sign in NZ\n                            \n                            \n                                    nztest\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            41 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3313ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"4815ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1178ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1877ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"2437ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[uname]\"","status":"passed","duration":"3655ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]","status":"passed","duration":"2436ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[pwd]\"","status":"passed","duration":"2783ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"passed","duration":"1289ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2073ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible","status":"passed","duration":"4349ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"3385ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists","status":"passed","duration":"1756ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"3787ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1151ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1938ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Apple\"","status":"passed","duration":"3187ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Wait for 10 ms","status":"passed","duration":"10033ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on Text: \"Passcode\"","status":"passed","duration":"2004ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"5\"]","status":"passed","duration":"1577ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"9\"]","status":"passed","duration":"1543ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"1\"]","status":"passed","duration":"1558ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"2\"]","status":"passed","duration":"1529ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"3\"]","status":"passed","duration":"1822ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"4\"]","status":"passed","duration":"1532ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"passed","duration":"6079ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2069ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible","status":"passed","duration":"4332ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2739ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists","status":"passed","duration":"1749ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"3754ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1170ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1950ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Google\"","status":"passed","duration":"3157ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on element with xpath: //XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]","status":"passed","duration":"3433ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"passed","duration":"2584ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"2089ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible","status":"passed","duration":"4361ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2083ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5024ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"WishList NZ\n                            \n                            \n                                    nztest\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            46 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3225ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"5927ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1186ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1959ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"passed","duration":"0ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"3985ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"iOS Function: text - Text: \"Uno card\"","status":"passed","duration":"2402ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1614ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"1742ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"2330ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"1981ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"passed","duration":"2426ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2534ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]","status":"passed","duration":"2228ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"2112ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"passed","duration":"3214ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"passed","duration":"2657ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]","status":"passed","duration":"1725ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]","status":"passed","duration":"2231ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"1996ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"passed","duration":"2420ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"passed","duration":"3155ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"passed","duration":"1356ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"passed","duration":"2078ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5019ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on Text: \"Move\"","status":"passed","duration":"2909ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]","status":"passed","duration":"1963ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5026ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on Text: \"Remove\"","status":"passed","duration":"2835ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]","status":"passed","duration":"1948ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"1953ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"passed","duration":"2437ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Move to wishlist\"]\" is visible","status":"passed","duration":"5241ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]","status":"passed","duration":"1654ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]","status":"passed","duration":"2146ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: banner-close-updated.png","status":"passed","duration":"2227ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"passed","duration":"2470ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"passed","duration":"3198ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Remove\"","status":"passed","duration":"3549ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"passed","duration":"3215ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Remove\"","status":"passed","duration":"2686ms","action_id":"cKNu2QoRC1","screenshot_filename":"cKNu2QoRC1.png","report_screenshot":"cKNu2QoRC1.png","resolved_screenshot":"screenshots/cKNu2QoRC1.png","clean_action_id":"cKNu2QoRC1","prefixed_action_id":"al_cKNu2QoRC1","action_id_screenshot":"screenshots/cKNu2QoRC1.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"1928ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2632ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"2076ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 10%)","status":"passed","duration":"2387ms","action_id":"0pwZCYAtOv","screenshot_filename":"0pwZCYAtOv.png","report_screenshot":"0pwZCYAtOv.png","resolved_screenshot":"screenshots/0pwZCYAtOv.png","clean_action_id":"0pwZCYAtOv","prefixed_action_id":"al_0pwZCYAtOv","action_id_screenshot":"screenshots/0pwZCYAtOv.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"App Settings NZ\n                            \n                            \n                                    nztest\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            39 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3223ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]","status":"passed","duration":"2909ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1187ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"passed","duration":"0ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Terminate app: com.apple.Preferences","status":"passed","duration":"1095ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Launch app: com.apple.Preferences","status":"passed","duration":"1228ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Tap on Text: \"Wi-Fi\"","status":"passed","duration":"2929ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]","status":"passed","duration":"1117ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3314ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"passed","duration":"256ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]","status":"passed","duration":"735ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"passed","duration":"218ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]","status":"passed","duration":"644ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"passed","duration":"201ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]","status":"passed","duration":"633ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"passed","duration":"177ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Launch app: com.apple.Preferences","status":"passed","duration":"134ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]","status":"passed","duration":"750ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5024ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3271ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]","status":"passed","duration":"2067ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2554ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Tap on Text: \"out\"","status":"passed","duration":"2713ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Restart app: com.apple.mobilesafari","status":"passed","duration":"3275ms","action_id":"mobilesafa","screenshot_filename":"mobilesafa.png","report_screenshot":"mobilesafa.png","resolved_screenshot":"screenshots/mobilesafa.png","action_id_screenshot":"screenshots/mobilesafa.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]","status":"passed","duration":"1193ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"kmart nz\"","status":"passed","duration":"1844ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.co.nz\"]","status":"passed","duration":"1619ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]","status":"passed","duration":"2053ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"4088ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"iOS Function: text - Text: \"notebook\"","status":"passed","duration":"2432ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"1839ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"3013ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Add to bag","status":"passed","duration":"4669ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3354ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]","status":"passed","duration":"2086ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]","status":"passed","duration":"2050ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]","status":"passed","duration":"2093ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"passed","duration":"2744ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"NZ- Performance\n                            \n                            \n                                    nztest\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            41 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"3298ms","action_id":"kAQ1yIIw3h","screenshot_filename":"kAQ1yIIw3h.png","report_screenshot":"kAQ1yIIw3h.png","resolved_screenshot":"screenshots/kAQ1yIIw3h.png","clean_action_id":"kAQ1yIIw3h","prefixed_action_id":"al_kAQ1yIIw3h","action_id_screenshot":"screenshots/kAQ1yIIw3h.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"4160ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"iOS Function: text - Text: \"P_43515028\"","status":"passed","duration":"2393ms","action_id":"ly2oT3zqmf","screenshot_filename":"ly2oT3zqmf.png","report_screenshot":"ly2oT3zqmf.png","resolved_screenshot":"screenshots/ly2oT3zqmf.png","clean_action_id":"ly2oT3zqmf","prefixed_action_id":"al_ly2oT3zqmf","action_id_screenshot":"screenshots/ly2oT3zqmf.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"2034ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"4326ms","action_id":"XLpUP3Wr93","screenshot_filename":"XLpUP3Wr93.png","report_screenshot":"XLpUP3Wr93.png","resolved_screenshot":"screenshots/XLpUP3Wr93.png","clean_action_id":"XLpUP3Wr93","prefixed_action_id":"al_XLpUP3Wr93","action_id_screenshot":"screenshots/XLpUP3Wr93.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Instruction Manual\"]","status":"passed","duration":"12226ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Done\"","status":"passed","duration":"4512ms","action_id":"H9fy9qcFbZ","screenshot_filename":"H9fy9qcFbZ.png","report_screenshot":"H9fy9qcFbZ.png","resolved_screenshot":"screenshots/H9fy9qcFbZ.png","clean_action_id":"H9fy9qcFbZ","prefixed_action_id":"al_H9fy9qcFbZ","action_id_screenshot":"screenshots/H9fy9qcFbZ.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3220ms","action_id":"2mhi7GOrlS","screenshot_filename":"2mhi7GOrlS.png","report_screenshot":"2mhi7GOrlS.png","resolved_screenshot":"screenshots/2mhi7GOrlS.png","clean_action_id":"2mhi7GOrlS","prefixed_action_id":"al_2mhi7GOrlS","action_id_screenshot":"screenshots/2mhi7GOrlS.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"2082ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"4026ms","action_id":"LcYLwUffqj","screenshot_filename":"LcYLwUffqj.png","report_screenshot":"LcYLwUffqj.png","resolved_screenshot":"screenshots/LcYLwUffqj.png","clean_action_id":"LcYLwUffqj","prefixed_action_id":"al_LcYLwUffqj","action_id_screenshot":"screenshots/LcYLwUffqj.png"},{"name":"iOS Function: text - Text: \"kids toys\"","status":"passed","duration":"3112ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Execute Test Case: Click_Paginations (10 steps)","status":"passed","duration":"0ms","action_id":"Pagination","screenshot_filename":"Pagination.png","report_screenshot":"Pagination.png","resolved_screenshot":"screenshots/Pagination.png","action_id_screenshot":"screenshots/Pagination.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3245ms","action_id":"OQ1fr8NUlV","screenshot_filename":"OQ1fr8NUlV.png","report_screenshot":"OQ1fr8NUlV.png","resolved_screenshot":"screenshots/OQ1fr8NUlV.png","clean_action_id":"OQ1fr8NUlV","prefixed_action_id":"al_OQ1fr8NUlV","action_id_screenshot":"screenshots/OQ1fr8NUlV.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]","status":"passed","duration":"2015ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Toys\"","status":"passed","duration":"3263ms","action_id":"xLGm9FefWE","screenshot_filename":"xLGm9FefWE.png","report_screenshot":"xLGm9FefWE.png","resolved_screenshot":"screenshots/xLGm9FefWE.png","clean_action_id":"xLGm9FefWE","prefixed_action_id":"al_xLGm9FefWE","action_id_screenshot":"screenshots/xLGm9FefWE.png"},{"name":"Tap on Text: \"Age\"","status":"passed","duration":"2783ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on Text: \"Months\"","status":"passed","duration":"2681ms","action_id":"eHvkAVake5","screenshot_filename":"eHvkAVake5.png","report_screenshot":"eHvkAVake5.png","resolved_screenshot":"screenshots/eHvkAVake5.png","clean_action_id":"eHvkAVake5","prefixed_action_id":"al_eHvkAVake5","action_id_screenshot":"screenshots/eHvkAVake5.png"},{"name":"Swipe from (5%, 50%) to (90%, 50%)","status":"passed","duration":"3705ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Swipe from (5%, 50%) to (90%, 50%)","status":"passed","duration":"3575ms","action_id":"cKNu2QoRC1","screenshot_filename":"cKNu2QoRC1.png","report_screenshot":"cKNu2QoRC1.png","resolved_screenshot":"screenshots/cKNu2QoRC1.png","clean_action_id":"cKNu2QoRC1","prefixed_action_id":"al_cKNu2QoRC1","action_id_screenshot":"screenshots/cKNu2QoRC1.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"3127ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"3744ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1177ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"2434ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[uname]\"","status":"passed","duration":"3077ms","action_id":"0pwZCYAtOv","screenshot_filename":"0pwZCYAtOv.png","report_screenshot":"0pwZCYAtOv.png","resolved_screenshot":"screenshots/0pwZCYAtOv.png","clean_action_id":"0pwZCYAtOv","prefixed_action_id":"al_0pwZCYAtOv","action_id_screenshot":"screenshots/0pwZCYAtOv.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]","status":"passed","duration":"2462ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[pwd]\"","status":"passed","duration":"3174ms","action_id":"fDgFGQYpCw","screenshot_filename":"fDgFGQYpCw.png","report_screenshot":"fDgFGQYpCw.png","resolved_screenshot":"screenshots/fDgFGQYpCw.png","clean_action_id":"fDgFGQYpCw","prefixed_action_id":"al_fDgFGQYpCw","action_id_screenshot":"screenshots/fDgFGQYpCw.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"passed","duration":"1259ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"3906ms","action_id":"Iab9zCfpqO","screenshot_filename":"Iab9zCfpqO.png","report_screenshot":"Iab9zCfpqO.png","resolved_screenshot":"screenshots/Iab9zCfpqO.png","clean_action_id":"Iab9zCfpqO","prefixed_action_id":"al_Iab9zCfpqO","action_id_screenshot":"screenshots/Iab9zCfpqO.png"},{"name":"iOS Function: text - Text: \"enn[cooker-id]\"","status":"passed","duration":"2484ms","action_id":"9Pwdq32eUk","screenshot_filename":"9Pwdq32eUk.png","report_screenshot":"9Pwdq32eUk.png","resolved_screenshot":"screenshots/9Pwdq32eUk.png","clean_action_id":"9Pwdq32eUk","prefixed_action_id":"al_9Pwdq32eUk","action_id_screenshot":"screenshots/9Pwdq32eUk.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1590ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"2200ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"2701ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"passed","duration":"2568ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"passed","duration":"2549ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (90%, 20%) to (30%, 20%)","status":"passed","duration":"1822ms","action_id":"iSckENpXrN","screenshot_filename":"iSckENpXrN.png","report_screenshot":"iSckENpXrN.png","resolved_screenshot":"screenshots/iSckENpXrN.png","clean_action_id":"iSckENpXrN","prefixed_action_id":"al_iSckENpXrN","action_id_screenshot":"screenshots/iSckENpXrN.png"},{"name":"Swipe from (90%, 20%) to (30%, 20%)","status":"passed","duration":"1925ms","action_id":"q6kSH9e0MI","screenshot_filename":"q6kSH9e0MI.png","report_screenshot":"q6kSH9e0MI.png","resolved_screenshot":"screenshots/q6kSH9e0MI.png","clean_action_id":"q6kSH9e0MI","prefixed_action_id":"al_q6kSH9e0MI","action_id_screenshot":"screenshots/q6kSH9e0MI.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"1884ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2422ms","action_id":"kDpsm2D3xt","screenshot_filename":"kDpsm2D3xt.png","report_screenshot":"kDpsm2D3xt.png","resolved_screenshot":"screenshots/kDpsm2D3xt.png","clean_action_id":"kDpsm2D3xt","prefixed_action_id":"al_kDpsm2D3xt","action_id_screenshot":"screenshots/kDpsm2D3xt.png"},{"name":"Tap on Text: \"out\"","status":"passed","duration":"2752ms","action_id":"OKCHAK6HCJ","screenshot_filename":"OKCHAK6HCJ.png","report_screenshot":"OKCHAK6HCJ.png","resolved_screenshot":"screenshots/OKCHAK6HCJ.png","clean_action_id":"OKCHAK6HCJ","prefixed_action_id":"al_OKCHAK6HCJ","action_id_screenshot":"screenshots/OKCHAK6HCJ.png"},{"name":"Wait till accessibility_id=txtHomeAccountCtaSignIn","status":"passed","duration":"2885ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"cleanupSteps action","status":"passed","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]}],"passed":8,"failed":1,"skipped":0,"status":"failed","availableScreenshots":["08NzsvhQXK.png","0962MtId5t.png","0Q0fm6OTij.png","0bnBNoqPt8.png","0pwZCYAtOv.png","13YG4jrM9E.png","1Lirmyxkft.png","20qUCJgpE9.png","2FnAZDskt1.png","2cTZvK1psn.png","2kwu2VBmuZ.png","2mhi7GOrlS.png","2p13JoJbbA.png","3CTsyFe28F.png","3KNqlNy6Bj.png","3caMBvQX7k.png","3hOTINBVMf.png","5Gj5mgIxVu.png","5ZzW1VVSzy.png","5e4LeoW1YU.png","5nsUXQ5L7u.png","6G6P3UE7Uy.png","6HhScBaqQp.png","6LQ5cq0f6N.png","6mHVWI3j5e.png","6pAgesZD43.png","6qZnk86hGg.png","6xgrAWyfZ4.png","7WYExJTqjp.png","7g2LmvjtEZ.png","7g6MFJSGIO.png","7xs3GiydGF.png","83tV9A4NOn.png","88BYVcWtJZ.png","8OsQmoVYqW.png","8umPSX0vrr.png","92tKl3T5N8.png","9B5MQGTmpP.png","9Jhn4eWZwR.png","9MqlsILCgk.png","9Pwdq32eUk.png","9QADAZGNH3.png","A1Wz7p1iVG.png","BCM1sS8SGA.png","Bdhe5AoUlM.png","BracBsfa3Y.png","BzTvnSrykE.png","CBBib3pFkq.png","CJ88OgjKXp.png","CLMmkV1OIM.png","CWkqGp5ndO.png","CtWhaVwbJC.png","CzVeOTdAX9.png","DGzGrOfbSq.png","DY8MfL0wXI.png","DbM0d0m6rU.png","DhWa2PCBXE.png","EELcfo48Sh.png","EJkHvEQccu.png","Ef6OumM2eS.png","Ey8MUB57vM.png","F0gZF1jEnT.png","F1olhgKhUt.png","F4NGh9HrLw.png","F9UfvzyNii.png","FARWZvOj0x.png","FAvQgIuHc1.png","FHRlQXe58T.png","FlEukNkjlS.png","FnrbyHq7bU.png","GHH3xhNGgr.png","GRwHMVK4sA.png","GTXmST3hEA.png","GgQaBLWYkb.png","H9fy9qcFbZ.png","HotUJOd6oB.png","I5bRbYY1hD.png","IL6kON0uQ9.png","IOzaCR1Euv.png","ISfUFUnvFh.png","ISpNHH3V9g.png","ITHvSyXXmu.png","IW6uAwdtiW.png","Iab9zCfpqO.png","ImienLpJEN.png","IsGWxLFpIn.png","IvqPpScAJa.png","J7BPGVnRJI.png","JRheDTvpJf.png","JXFxYCr98V.png","K0c1gL9UK1.png","K7yV3GGsgr.png","KfMHchi8cx.png","KlfYmNjrq8.png","KyyS139agr.png","L6wTorOX8B.png","L8LEfGm9WC.png","LcYLwUffqj.png","LfyQctrEJn.png","LlRfimKPrn.png","M1IXnYddFx.png","NL2gtj6qIu.png","NQGIFb5O7u.png","NcU6aex76k.png","NkybTKfs2U.png","NurQsFoMkE.png","OKCHAK6HCJ.png","OKiI82VdnE.png","OQ1fr8NUlV.png","OR0SKKnFxy.png","OUT2ASweb6.png","Ob26qqcA0p.png","OmKfD9iBjD.png","OyUowAaBzD.png","P2OkZzbCB3.png","PH8FFnzohm.png","PLrRarI0Y9.png","PbfHAtFQPP.png","Q0fomJIDoQ.png","Q5A0cNaJ24.png","QPKR6jUF9O.png","Qbg9bipTGs.png","QvuueoTR8W.png","RCYxT9YD8u.png","RDQCFIxjA0.png","RbD937Xbte.png","RbNtEW6N9T.png","RuPGkdCdah.png","SDtskxyVpg.png","SFj4Aa7RHQ.png","SPE01N6pyp.png","SPgFRgq13M.png","TTpwkHEyuE.png","TV4kJIIV9v.png","To7bij5MnF.png","UZkF5rnoLo.png","UpUSVInizv.png","V42eHtTRYW.png","V59u3l1wkM.png","Vxt7QOYeDD.png","Vy3WZ0LTJF.png","Vyrkv4wK1v.png","WEB5St2Mb7.png","WbxRVpWtjw.png","WlISsMf9QA.png","WoymrHdtrO.png","WwIZzJEW9W.png","XEbZHdi0GT.png","XLpUP3Wr93.png","XRiJwoJD9w.png","Xr6F8gdd8q.png","XuLgjNG74w.png","Y8vz7AJD1i.png","YH6erO83XY.png","YNMjmPGi1h.png","YbamBpASJi.png","YuuQe2KupX.png","Z6g3sGuHTp.png","Z86YU6Mq0g.png","ZCsqeOXrY1.png","ZhH80yndRU.png","a4pJa7EAyI.png","alaozIePOy.png","aqBkqyVhrZ.png","arH1CZCPXh.png","bGo3feCwBQ.png","bQrT7FZsxl.png","bZCkx4U9Gk.png","c4T3INQkzn.png","cJDpd7aK3d.png","cKNu2QoRC1.png","cokvFXhj4c.png","dYEtjrv6lz.png","dkSs61jGvX.png","eGQ7VrKUSo.png","eHLWiRoqqS.png","eHvkAVake5.png","eJnHS9n9VL.png","eLxHVWKeDQ.png","eSr9EFlJek.png","ewuLtuqVuo.png","fDgFGQYpCw.png","fPX582qHkp.png","fTdGMJ3NH3.png","ftA0OJvd0W.png","g0PE7Mofye.png","gPYNwJ0HKo.png","gekNSY5O2E.png","h9trcMrvxt.png","hCCEvRtj1A.png","hnH3ayslCh.png","hwdyCKFAUJ.png","iDtcdR3nSL.png","iExyA0ZirJ.png","iSckENpXrN.png","iwqbVl90WJ.png","j4y8kIvfN6.png","jIeR7BPEPu.png","jQYHQIvQ8l.png","jUCAk6GJc4.png","jY0oPjKbuS.png","k3mu9Mt7Ec.png","k404YWYgSk.png","kAQ1yIIw3h.png","kDpsm2D3xt.png","kQJbqm7uCi.png","ksCBjJiwHZ.png","ky6rfmPv0u.png","kz9lnCdwoH.png","lCSewtjn1z.png","lSG7un0qKK.png","lWIRxRm6HE.png","lWJtKSqlPS.png","latest.png","ly2oT3zqmf.png","mIKA85kXaW.png","mfOWujfRpL.png","n57KEWjTea.png","nAB6Q8LAdv.png","nVWzLauG8N.png","napKDohf3Z.png","oETU9DzLi4.png","oSQ8sPdVOJ.png","oWLIFhrzr1.png","oqTdx3vL0d.png","p8rfQL9ara.png","pFlYwTS53v.png","pjFNt3w5Fr.png","pk2DLZFBmx.png","placeholder.png","pr9o8Zsm5p.png","q4hPXCBtx4.png","q6kSH9e0MI.png","q70JSbqKNk.png","qA1ap4n1m4.png","qjj0i3rcUh.png","qkZ5KShdEU.png","quZwUwj3a8.png","quzlwPw42x.png","rJ86z4njuR.png","rJVGLpLWM3.png","rSxM47lUdy.png","rkL0oz4kiL.png","rqLJpAP0mA.png","s0WyiD1w0B.png","sc2KH9bG6H.png","seQcUKjkSU.png","sl3Wk1gK8X.png","soKM0KayFJ.png","ts3qyFxyMf.png","u6bRYZZFAv.png","uM5FOSrU5U.png","uOt2cFGhGr.png","uuatVQwQFW.png","vAKpEDIzs7.png","vYLhraWpQm.png","veukWo4573.png","vfwUVEyq6X.png","vwFwkK6ydQ.png","w0CWlknXmX.png","w1RV76df9x.png","wguGCt7OoB.png","x4Mid4HQ0Z.png","x4yLCZHaCR.png","xAPeBnVHrT.png","xAa049Qgls.png","xLGm9FefWE.png","xUbWFa8Ok2.png","xVuuejtCFA.png","xmelRkcdVx.png","xqHGFj3tDd.png","y4i304JeJj.png","yEga5MkcRe.png","yNAxs8bgMy.png","yUJyVO5Wev.png","ydRnBBO1vR.png","yhmzeynQyu.png","yi5EsHEFvc.png","ylslyLAYKb.png","zNRPvs2cC4.png","zWrzEgdH3Q.png","zsVeGHiIgX.png","zzd5ufNDih.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>