Action Log - 2025-07-05 15:16:31
================================================================================

[[15:16:30]] [INFO] Generating execution report...
[[15:16:30]] [WARNING] 5 tests failed.
[[15:16:30]] [SUCCESS] Screenshot refreshed
[[15:16:30]] [INFO] Refreshing screenshot...
[[15:16:30]] [SUCCESS] Screenshot refreshed
[[15:16:30]] [INFO] Refreshing screenshot...
[[15:16:26]] [SUCCESS] Screenshot refreshed successfully
[[15:16:26]] [SUCCESS] Screenshot refreshed successfully
[[15:16:26]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:16:26]] [SUCCESS] Screenshot refreshed
[[15:16:26]] [INFO] Refreshing screenshot...
[[15:16:14]] [SUCCESS] Screenshot refreshed successfully
[[15:16:14]] [SUCCESS] Screenshot refreshed successfully
[[15:16:14]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:16:14]] [SUCCESS] Screenshot refreshed
[[15:16:14]] [INFO] Refreshing screenshot...
[[15:16:10]] [SUCCESS] Screenshot refreshed successfully
[[15:16:10]] [SUCCESS] Screenshot refreshed successfully
[[15:16:10]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:16:09]] [SUCCESS] Screenshot refreshed
[[15:16:09]] [INFO] Refreshing screenshot...
[[15:16:05]] [SUCCESS] Screenshot refreshed successfully
[[15:16:05]] [SUCCESS] Screenshot refreshed successfully
[[15:16:05]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:16:04]] [SUCCESS] Screenshot refreshed
[[15:16:04]] [INFO] Refreshing screenshot...
[[15:15:58]] [SUCCESS] Screenshot refreshed successfully
[[15:15:58]] [SUCCESS] Screenshot refreshed successfully
[[15:15:57]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:15:57]] [SUCCESS] Screenshot refreshed
[[15:15:57]] [INFO] Refreshing screenshot...
[[15:15:50]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:15:50]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:15:50]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:15:49]] [INFO] Ll4UlkE3L9=running
[[15:15:49]] [INFO] Executing action 576/576: cleanupSteps action
[[15:15:49]] [INFO] Skipping remaining steps in failed test case (moving from action 514 to 575), but preserving cleanup steps
[[15:15:49]] [INFO] DhFJzlme9K=fail
[[15:15:49]] [ERROR] Action 514 failed: Text 'FAQ' not found within timeout (30s)
[[15:15:14]] [SUCCESS] Screenshot refreshed successfully
[[15:15:14]] [SUCCESS] Screenshot refreshed successfully
[[15:15:14]] [INFO] DhFJzlme9K=running
[[15:15:14]] [INFO] Executing action 514/576: Tap on Text: "FAQ"
[[15:15:14]] [SUCCESS] Screenshot refreshed
[[15:15:14]] [INFO] Refreshing screenshot...
[[15:15:14]] [INFO] g17Boaefhg=pass
[[15:15:09]] [SUCCESS] Screenshot refreshed successfully
[[15:15:09]] [SUCCESS] Screenshot refreshed successfully
[[15:15:09]] [INFO] g17Boaefhg=running
[[15:15:09]] [INFO] Executing action 513/576: Tap on Text: "Help"
[[15:15:08]] [SUCCESS] Screenshot refreshed
[[15:15:08]] [INFO] Refreshing screenshot...
[[15:15:08]] [INFO] SqDiBhmyOG=pass
[[15:15:05]] [SUCCESS] Screenshot refreshed successfully
[[15:15:05]] [SUCCESS] Screenshot refreshed successfully
[[15:15:04]] [INFO] SqDiBhmyOG=running
[[15:15:04]] [INFO] Executing action 512/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:15:04]] [SUCCESS] Screenshot refreshed
[[15:15:04]] [INFO] Refreshing screenshot...
[[15:15:04]] [INFO] OR0SKKnFxy=pass
[[15:14:51]] [SUCCESS] Screenshot refreshed successfully
[[15:14:51]] [SUCCESS] Screenshot refreshed successfully
[[15:14:49]] [INFO] OR0SKKnFxy=running
[[15:14:49]] [INFO] Executing action 511/576: Restart app: env[appid]
[[15:14:49]] [SUCCESS] Screenshot refreshed
[[15:14:49]] [INFO] Refreshing screenshot...
[[15:14:49]] [SUCCESS] Screenshot refreshed
[[15:14:49]] [INFO] Refreshing screenshot...
[[15:14:46]] [SUCCESS] Screenshot refreshed successfully
[[15:14:46]] [SUCCESS] Screenshot refreshed successfully
[[15:14:46]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:14:45]] [SUCCESS] Screenshot refreshed
[[15:14:45]] [INFO] Refreshing screenshot...
[[15:14:33]] [SUCCESS] Screenshot refreshed successfully
[[15:14:33]] [SUCCESS] Screenshot refreshed successfully
[[15:14:33]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:14:32]] [SUCCESS] Screenshot refreshed
[[15:14:32]] [INFO] Refreshing screenshot...
[[15:14:28]] [SUCCESS] Screenshot refreshed successfully
[[15:14:28]] [SUCCESS] Screenshot refreshed successfully
[[15:14:28]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:14:28]] [SUCCESS] Screenshot refreshed
[[15:14:28]] [INFO] Refreshing screenshot...
[[15:14:24]] [SUCCESS] Screenshot refreshed successfully
[[15:14:24]] [SUCCESS] Screenshot refreshed successfully
[[15:14:24]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:14:23]] [SUCCESS] Screenshot refreshed
[[15:14:23]] [INFO] Refreshing screenshot...
[[15:14:17]] [SUCCESS] Screenshot refreshed successfully
[[15:14:17]] [SUCCESS] Screenshot refreshed successfully
[[15:14:16]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:14:16]] [SUCCESS] Screenshot refreshed
[[15:14:16]] [INFO] Refreshing screenshot...
[[15:14:09]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:14:09]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:14:08]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:14:08]] [INFO] kPdSiomhwu=running
[[15:14:08]] [INFO] Executing action 510/576: cleanupSteps action
[[15:14:08]] [INFO] Skipping remaining steps in failed test case (moving from action 482 to 509), but preserving cleanup steps
[[15:14:08]] [INFO] gkkQzTCmma=fail
[[15:14:08]] [ERROR] Action 482 failed: Text 'Catalogue' not found within timeout (30s)
[[15:13:34]] [SUCCESS] Screenshot refreshed successfully
[[15:13:34]] [SUCCESS] Screenshot refreshed successfully
[[15:13:34]] [INFO] gkkQzTCmma=running
[[15:13:34]] [INFO] Executing action 482/576: Tap on Text: "Catalogue"
[[15:13:34]] [SUCCESS] Screenshot refreshed
[[15:13:34]] [INFO] Refreshing screenshot...
[[15:13:34]] [INFO] UpUSVInizv=pass
[[15:13:30]] [SUCCESS] Screenshot refreshed successfully
[[15:13:30]] [SUCCESS] Screenshot refreshed successfully
[[15:13:30]] [INFO] UpUSVInizv=running
[[15:13:30]] [INFO] Executing action 481/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[15:13:29]] [SUCCESS] Screenshot refreshed
[[15:13:29]] [INFO] Refreshing screenshot...
[[15:13:29]] [INFO] 0QtNHB5WEK=pass
[[15:13:26]] [SUCCESS] Screenshot refreshed successfully
[[15:13:26]] [SUCCESS] Screenshot refreshed successfully
[[15:13:26]] [INFO] 0QtNHB5WEK=running
[[15:13:26]] [INFO] Executing action 480/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[15:13:25]] [SUCCESS] Screenshot refreshed
[[15:13:25]] [INFO] Refreshing screenshot...
[[15:13:25]] [INFO] fTdGMJ3NH3=pass
[[15:13:22]] [SUCCESS] Screenshot refreshed successfully
[[15:13:22]] [SUCCESS] Screenshot refreshed successfully
[[15:13:22]] [INFO] fTdGMJ3NH3=running
[[15:13:22]] [INFO] Executing action 479/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[15:13:21]] [SUCCESS] Screenshot refreshed
[[15:13:21]] [INFO] Refreshing screenshot...
[[15:13:21]] [INFO] rYJcLPh8Aq=pass
[[15:13:18]] [SUCCESS] Screenshot refreshed successfully
[[15:13:18]] [SUCCESS] Screenshot refreshed successfully
[[15:13:17]] [INFO] rYJcLPh8Aq=running
[[15:13:17]] [INFO] Executing action 478/576: iOS Function: text - Text: "kmart au"
[[15:13:17]] [SUCCESS] Screenshot refreshed
[[15:13:17]] [INFO] Refreshing screenshot...
[[15:13:17]] [INFO] 0Q0fm6OTij=pass
[[15:13:13]] [SUCCESS] Screenshot refreshed successfully
[[15:13:13]] [SUCCESS] Screenshot refreshed successfully
[[15:13:13]] [INFO] 0Q0fm6OTij=running
[[15:13:13]] [INFO] Executing action 477/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[15:13:13]] [SUCCESS] Screenshot refreshed
[[15:13:13]] [INFO] Refreshing screenshot...
[[15:13:13]] [INFO] xVuuejtCFA=pass
[[15:13:10]] [SUCCESS] Screenshot refreshed successfully
[[15:13:10]] [SUCCESS] Screenshot refreshed successfully
[[15:13:09]] [INFO] xVuuejtCFA=running
[[15:13:09]] [INFO] Executing action 476/576: Restart app: com.apple.mobilesafari
[[15:13:08]] [SUCCESS] Screenshot refreshed
[[15:13:08]] [INFO] Refreshing screenshot...
[[15:13:08]] [INFO] LcYLwUffqj=pass
[[15:13:03]] [SUCCESS] Screenshot refreshed successfully
[[15:13:03]] [SUCCESS] Screenshot refreshed successfully
[[15:13:03]] [INFO] LcYLwUffqj=running
[[15:13:03]] [INFO] Executing action 475/576: Tap on Text: "out"
[[15:13:03]] [SUCCESS] Screenshot refreshed
[[15:13:03]] [INFO] Refreshing screenshot...
[[15:13:03]] [INFO] ZZPNqTJ65s=pass
[[15:12:59]] [SUCCESS] Screenshot refreshed successfully
[[15:12:59]] [SUCCESS] Screenshot refreshed successfully
[[15:12:58]] [INFO] ZZPNqTJ65s=running
[[15:12:58]] [INFO] Executing action 474/576: Swipe from (50%, 70%) to (50%, 30%)
[[15:12:58]] [SUCCESS] Screenshot refreshed
[[15:12:58]] [INFO] Refreshing screenshot...
[[15:12:58]] [INFO] UpUSVInizv=pass
[[15:12:55]] [SUCCESS] Screenshot refreshed successfully
[[15:12:55]] [SUCCESS] Screenshot refreshed successfully
[[15:12:54]] [INFO] UpUSVInizv=running
[[15:12:54]] [INFO] Executing action 473/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[15:12:53]] [SUCCESS] Screenshot refreshed
[[15:12:53]] [INFO] Refreshing screenshot...
[[15:12:53]] [INFO] hCCEvRtj1A=pass
[[15:12:48]] [INFO] hCCEvRtj1A=running
[[15:12:48]] [INFO] Executing action 472/576: Restart app: env[appid]
[[15:12:48]] [SUCCESS] Screenshot refreshed successfully
[[15:12:48]] [SUCCESS] Screenshot refreshed successfully
[[15:12:48]] [SUCCESS] Screenshot refreshed
[[15:12:48]] [INFO] Refreshing screenshot...
[[15:12:48]] [INFO] V42eHtTRYW=pass
[[15:12:41]] [INFO] V42eHtTRYW=running
[[15:12:41]] [INFO] Executing action 471/576: Wait for 5 ms
[[15:12:41]] [SUCCESS] Screenshot refreshed successfully
[[15:12:41]] [SUCCESS] Screenshot refreshed successfully
[[15:12:41]] [SUCCESS] Screenshot refreshed
[[15:12:41]] [INFO] Refreshing screenshot...
[[15:12:41]] [INFO] GRwHMVK4sA=pass
[[15:12:38]] [INFO] GRwHMVK4sA=running
[[15:12:38]] [INFO] Executing action 470/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[15:12:38]] [SUCCESS] Screenshot refreshed successfully
[[15:12:38]] [SUCCESS] Screenshot refreshed successfully
[[15:12:38]] [SUCCESS] Screenshot refreshed
[[15:12:38]] [INFO] Refreshing screenshot...
[[15:12:38]] [INFO] V42eHtTRYW=pass
[[15:12:31]] [INFO] V42eHtTRYW=running
[[15:12:31]] [INFO] Executing action 469/576: Wait for 5 ms
[[15:12:31]] [SUCCESS] Screenshot refreshed successfully
[[15:12:31]] [SUCCESS] Screenshot refreshed successfully
[[15:12:31]] [SUCCESS] Screenshot refreshed
[[15:12:31]] [INFO] Refreshing screenshot...
[[15:12:31]] [INFO] LfyQctrEJn=pass
[[15:12:29]] [SUCCESS] Screenshot refreshed successfully
[[15:12:29]] [SUCCESS] Screenshot refreshed successfully
[[15:12:29]] [INFO] LfyQctrEJn=running
[[15:12:29]] [INFO] Executing action 468/576: Launch app: com.apple.Preferences
[[15:12:29]] [SUCCESS] Screenshot refreshed
[[15:12:29]] [INFO] Refreshing screenshot...
[[15:12:29]] [INFO] seQcUKjkSU=pass
[[15:12:27]] [INFO] seQcUKjkSU=running
[[15:12:27]] [INFO] Executing action 467/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[15:12:27]] [SUCCESS] Screenshot refreshed successfully
[[15:12:27]] [SUCCESS] Screenshot refreshed successfully
[[15:12:26]] [SUCCESS] Screenshot refreshed
[[15:12:26]] [INFO] Refreshing screenshot...
[[15:12:26]] [INFO] UpUSVInizv=pass
[[15:12:24]] [SUCCESS] Screenshot refreshed successfully
[[15:12:24]] [SUCCESS] Screenshot refreshed successfully
[[15:12:24]] [INFO] UpUSVInizv=running
[[15:12:24]] [INFO] Executing action 466/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[15:12:23]] [SUCCESS] Screenshot refreshed
[[15:12:23]] [INFO] Refreshing screenshot...
[[15:12:23]] [INFO] WoymrHdtrO=pass
[[15:12:22]] [SUCCESS] Screenshot refreshed successfully
[[15:12:22]] [SUCCESS] Screenshot refreshed successfully
[[15:12:22]] [INFO] WoymrHdtrO=running
[[15:12:22]] [INFO] Executing action 465/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[15:12:21]] [SUCCESS] Screenshot refreshed
[[15:12:21]] [INFO] Refreshing screenshot...
[[15:12:21]] [INFO] 6xgrAWyfZ4=pass
[[15:12:19]] [SUCCESS] Screenshot refreshed successfully
[[15:12:19]] [SUCCESS] Screenshot refreshed successfully
[[15:12:19]] [INFO] 6xgrAWyfZ4=running
[[15:12:19]] [INFO] Executing action 464/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[15:12:18]] [SUCCESS] Screenshot refreshed
[[15:12:18]] [INFO] Refreshing screenshot...
[[15:12:18]] [INFO] eSr9EFlJek=pass
[[15:12:16]] [SUCCESS] Screenshot refreshed successfully
[[15:12:16]] [SUCCESS] Screenshot refreshed successfully
[[15:12:16]] [INFO] eSr9EFlJek=running
[[15:12:16]] [INFO] Executing action 463/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[15:12:16]] [SUCCESS] Screenshot refreshed
[[15:12:16]] [INFO] Refreshing screenshot...
[[15:12:16]] [INFO] 3KNqlNy6Bj=pass
[[15:12:13]] [SUCCESS] Screenshot refreshed successfully
[[15:12:13]] [SUCCESS] Screenshot refreshed successfully
[[15:12:13]] [INFO] 3KNqlNy6Bj=running
[[15:12:13]] [INFO] Executing action 462/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[15:12:13]] [SUCCESS] Screenshot refreshed
[[15:12:13]] [INFO] Refreshing screenshot...
[[15:12:13]] [INFO] cokvFXhj4c=pass
[[15:12:11]] [SUCCESS] Screenshot refreshed successfully
[[15:12:11]] [SUCCESS] Screenshot refreshed successfully
[[15:12:11]] [INFO] cokvFXhj4c=running
[[15:12:11]] [INFO] Executing action 461/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[15:12:10]] [SUCCESS] Screenshot refreshed
[[15:12:10]] [INFO] Refreshing screenshot...
[[15:12:10]] [INFO] oSQ8sPdVOJ=pass
[[15:12:06]] [INFO] oSQ8sPdVOJ=running
[[15:12:06]] [INFO] Executing action 460/576: Restart app: env[appid]
[[15:12:05]] [SUCCESS] Screenshot refreshed successfully
[[15:12:05]] [SUCCESS] Screenshot refreshed successfully
[[15:12:05]] [SUCCESS] Screenshot refreshed
[[15:12:05]] [INFO] Refreshing screenshot...
[[15:12:05]] [INFO] V42eHtTRYW=pass
[[15:11:58]] [INFO] V42eHtTRYW=running
[[15:11:58]] [INFO] Executing action 459/576: Wait for 5 ms
[[15:11:58]] [SUCCESS] Screenshot refreshed successfully
[[15:11:58]] [SUCCESS] Screenshot refreshed successfully
[[15:11:58]] [SUCCESS] Screenshot refreshed
[[15:11:58]] [INFO] Refreshing screenshot...
[[15:11:58]] [INFO] jUCAk6GJc4=pass
[[15:11:55]] [INFO] jUCAk6GJc4=running
[[15:11:55]] [INFO] Executing action 458/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[15:11:55]] [SUCCESS] Screenshot refreshed successfully
[[15:11:55]] [SUCCESS] Screenshot refreshed successfully
[[15:11:55]] [SUCCESS] Screenshot refreshed
[[15:11:55]] [INFO] Refreshing screenshot...
[[15:11:55]] [INFO] V42eHtTRYW=pass
[[15:11:48]] [INFO] V42eHtTRYW=running
[[15:11:48]] [INFO] Executing action 457/576: Wait for 5 ms
[[15:11:48]] [SUCCESS] Screenshot refreshed successfully
[[15:11:48]] [SUCCESS] Screenshot refreshed successfully
[[15:11:48]] [SUCCESS] Screenshot refreshed
[[15:11:48]] [INFO] Refreshing screenshot...
[[15:11:48]] [INFO] w1RV76df9x=pass
[[15:11:43]] [INFO] w1RV76df9x=running
[[15:11:43]] [INFO] Executing action 456/576: Tap on Text: "Wi-Fi"
[[15:11:43]] [SUCCESS] Screenshot refreshed successfully
[[15:11:43]] [SUCCESS] Screenshot refreshed successfully
[[15:11:43]] [SUCCESS] Screenshot refreshed
[[15:11:43]] [INFO] Refreshing screenshot...
[[15:11:43]] [INFO] LfyQctrEJn=pass
[[15:11:41]] [SUCCESS] Screenshot refreshed successfully
[[15:11:41]] [SUCCESS] Screenshot refreshed successfully
[[15:11:40]] [INFO] LfyQctrEJn=running
[[15:11:40]] [INFO] Executing action 455/576: Launch app: com.apple.Preferences
[[15:11:40]] [SUCCESS] Screenshot refreshed
[[15:11:40]] [INFO] Refreshing screenshot...
[[15:11:40]] [INFO] mIKA85kXaW=pass
[[15:11:38]] [SUCCESS] Screenshot refreshed successfully
[[15:11:38]] [SUCCESS] Screenshot refreshed successfully
[[15:11:37]] [INFO] mIKA85kXaW=running
[[15:11:37]] [INFO] Executing action 454/576: Terminate app: com.apple.Preferences
[[15:11:37]] [SUCCESS] Screenshot refreshed
[[15:11:37]] [INFO] Refreshing screenshot...
[[15:11:37]] [SUCCESS] Screenshot refreshed
[[15:11:37]] [INFO] Refreshing screenshot...
[[15:11:32]] [SUCCESS] Screenshot refreshed successfully
[[15:11:32]] [SUCCESS] Screenshot refreshed successfully
[[15:11:32]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[15:11:31]] [SUCCESS] Screenshot refreshed
[[15:11:31]] [INFO] Refreshing screenshot...
[[15:11:27]] [SUCCESS] Screenshot refreshed successfully
[[15:11:27]] [SUCCESS] Screenshot refreshed successfully
[[15:11:27]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:11:27]] [SUCCESS] Screenshot refreshed
[[15:11:27]] [INFO] Refreshing screenshot...
[[15:11:22]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[15:11:22]] [SUCCESS] Screenshot refreshed successfully
[[15:11:22]] [SUCCESS] Screenshot refreshed successfully
[[15:11:21]] [SUCCESS] Screenshot refreshed
[[15:11:21]] [INFO] Refreshing screenshot...
[[15:11:17]] [SUCCESS] Screenshot refreshed successfully
[[15:11:17]] [SUCCESS] Screenshot refreshed successfully
[[15:11:16]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:11:16]] [SUCCESS] Screenshot refreshed
[[15:11:16]] [INFO] Refreshing screenshot...
[[15:11:10]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:11:10]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[15:11:10]] [SUCCESS] Screenshot refreshed successfully
[[15:11:10]] [SUCCESS] Screenshot refreshed successfully
[[15:11:10]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[15:11:10]] [INFO] x6vffndoRV=running
[[15:11:10]] [INFO] Executing action 453/576: Execute Test Case: Kmart-Signin (6 steps)
[[15:11:10]] [SUCCESS] Screenshot refreshed
[[15:11:10]] [INFO] Refreshing screenshot...
[[15:11:10]] [INFO] rJ86z4njuR=pass
[[15:11:07]] [SUCCESS] Screenshot refreshed successfully
[[15:11:07]] [SUCCESS] Screenshot refreshed successfully
[[15:11:07]] [INFO] rJ86z4njuR=running
[[15:11:07]] [INFO] Executing action 452/576: iOS Function: alert_accept
[[15:11:06]] [SUCCESS] Screenshot refreshed
[[15:11:06]] [INFO] Refreshing screenshot...
[[15:11:06]] [INFO] veukWo4573=pass
[[15:11:02]] [SUCCESS] Screenshot refreshed successfully
[[15:11:02]] [SUCCESS] Screenshot refreshed successfully
[[15:11:02]] [INFO] veukWo4573=running
[[15:11:02]] [INFO] Executing action 451/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[15:11:01]] [SUCCESS] Screenshot refreshed
[[15:11:01]] [INFO] Refreshing screenshot...
[[15:11:01]] [INFO] XEbZHdi0GT=pass
[[15:10:48]] [SUCCESS] Screenshot refreshed successfully
[[15:10:48]] [SUCCESS] Screenshot refreshed successfully
[[15:10:47]] [INFO] XEbZHdi0GT=running
[[15:10:47]] [INFO] Executing action 450/576: Restart app: env[appid]
[[15:10:47]] [SUCCESS] Screenshot refreshed
[[15:10:47]] [INFO] Refreshing screenshot...
[[15:10:46]] [SUCCESS] Screenshot refreshed
[[15:10:46]] [INFO] Refreshing screenshot...
[[15:10:44]] [SUCCESS] Screenshot refreshed successfully
[[15:10:44]] [SUCCESS] Screenshot refreshed successfully
[[15:10:43]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:10:42]] [SUCCESS] Screenshot refreshed
[[15:10:42]] [INFO] Refreshing screenshot...
[[15:10:37]] [SUCCESS] Screenshot refreshed successfully
[[15:10:37]] [SUCCESS] Screenshot refreshed successfully
[[15:10:36]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:10:36]] [SUCCESS] Screenshot refreshed
[[15:10:36]] [INFO] Refreshing screenshot...
[[15:10:32]] [SUCCESS] Screenshot refreshed successfully
[[15:10:32]] [SUCCESS] Screenshot refreshed successfully
[[15:10:32]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:10:32]] [SUCCESS] Screenshot refreshed
[[15:10:32]] [INFO] Refreshing screenshot...
[[15:10:28]] [SUCCESS] Screenshot refreshed successfully
[[15:10:28]] [SUCCESS] Screenshot refreshed successfully
[[15:10:28]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:10:27]] [SUCCESS] Screenshot refreshed
[[15:10:27]] [INFO] Refreshing screenshot...
[[15:10:20]] [SUCCESS] Screenshot refreshed successfully
[[15:10:20]] [SUCCESS] Screenshot refreshed successfully
[[15:10:20]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:10:20]] [SUCCESS] Screenshot refreshed
[[15:10:20]] [INFO] Refreshing screenshot...
[[15:10:12]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:10:12]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:10:12]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:10:12]] [INFO] ubySifeF65=running
[[15:10:12]] [INFO] Executing action 449/576: cleanupSteps action
[[15:10:12]] [INFO] Skipping remaining steps in failed test case (moving from action 419 to 448), but preserving cleanup steps
[[15:10:12]] [INFO] 73NABkfWyY=fail
[[15:10:12]] [ERROR] Action 419 failed: Text not found: "Sanctuary"
[[15:09:46]] [SUCCESS] Screenshot refreshed successfully
[[15:09:46]] [SUCCESS] Screenshot refreshed successfully
[[15:09:46]] [INFO] 73NABkfWyY=running
[[15:09:46]] [INFO] Executing action 419/576: Check if element with text="Sanctuary" exists
[[15:09:45]] [SUCCESS] Screenshot refreshed
[[15:09:45]] [INFO] Refreshing screenshot...
[[15:09:45]] [INFO] pKjXoj4mNg=pass
[[15:09:40]] [SUCCESS] Screenshot refreshed successfully
[[15:09:40]] [SUCCESS] Screenshot refreshed successfully
[[15:09:40]] [INFO] pKjXoj4mNg=running
[[15:09:40]] [INFO] Executing action 418/576: Tap on Text: "Save"
[[15:09:40]] [SUCCESS] Screenshot refreshed
[[15:09:40]] [INFO] Refreshing screenshot...
[[15:09:40]] [INFO] M3dXqigqRv=pass
[[15:09:35]] [SUCCESS] Screenshot refreshed successfully
[[15:09:35]] [SUCCESS] Screenshot refreshed successfully
[[15:09:35]] [INFO] M3dXqigqRv=running
[[15:09:35]] [INFO] Executing action 417/576: Wait till accessibility_id=btnSaveOrContinue
[[15:09:34]] [SUCCESS] Screenshot refreshed
[[15:09:34]] [INFO] Refreshing screenshot...
[[15:09:34]] [INFO] GYRHQr7TWx=pass
[[15:09:29]] [SUCCESS] Screenshot refreshed successfully
[[15:09:29]] [SUCCESS] Screenshot refreshed successfully
[[15:09:29]] [INFO] GYRHQr7TWx=running
[[15:09:29]] [INFO] Executing action 416/576: Tap on Text: "current"
[[15:09:29]] [SUCCESS] Screenshot refreshed
[[15:09:29]] [INFO] Refreshing screenshot...
[[15:09:29]] [INFO] kiM0WyWE9I=pass
[[15:09:24]] [SUCCESS] Screenshot refreshed successfully
[[15:09:24]] [SUCCESS] Screenshot refreshed successfully
[[15:09:24]] [INFO] kiM0WyWE9I=running
[[15:09:24]] [INFO] Executing action 415/576: Wait till accessibility_id=btnCurrentLocationButton
[[15:09:23]] [SUCCESS] Screenshot refreshed
[[15:09:23]] [INFO] Refreshing screenshot...
[[15:09:23]] [INFO] VkUKQbf1Qt=pass
[[15:09:18]] [SUCCESS] Screenshot refreshed successfully
[[15:09:18]] [SUCCESS] Screenshot refreshed successfully
[[15:09:18]] [INFO] VkUKQbf1Qt=running
[[15:09:18]] [INFO] Executing action 414/576: Tap on Text: "Edit"
[[15:09:17]] [SUCCESS] Screenshot refreshed
[[15:09:17]] [INFO] Refreshing screenshot...
[[15:09:17]] [INFO] C6JHhLdWTv=pass
[[15:09:14]] [SUCCESS] Screenshot refreshed successfully
[[15:09:14]] [SUCCESS] Screenshot refreshed successfully
[[15:09:13]] [INFO] C6JHhLdWTv=running
[[15:09:13]] [INFO] Executing action 413/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[15:09:13]] [SUCCESS] Screenshot refreshed
[[15:09:13]] [INFO] Refreshing screenshot...
[[15:09:13]] [INFO] IupxLP2Jsr=pass
[[15:09:09]] [SUCCESS] Screenshot refreshed successfully
[[15:09:09]] [SUCCESS] Screenshot refreshed successfully
[[15:09:08]] [INFO] IupxLP2Jsr=running
[[15:09:08]] [INFO] Executing action 412/576: iOS Function: text - Text: "Uno card"
[[15:09:08]] [SUCCESS] Screenshot refreshed
[[15:09:08]] [INFO] Refreshing screenshot...
[[15:09:08]] [INFO] 70iOOakiG7=pass
[[15:09:03]] [SUCCESS] Screenshot refreshed successfully
[[15:09:03]] [SUCCESS] Screenshot refreshed successfully
[[15:09:02]] [INFO] 70iOOakiG7=running
[[15:09:02]] [INFO] Executing action 411/576: Tap on Text: "Find"
[[15:09:02]] [SUCCESS] Screenshot refreshed
[[15:09:02]] [INFO] Refreshing screenshot...
[[15:09:02]] [INFO] Xqj9EIVEfg=pass
[[15:08:54]] [SUCCESS] Screenshot refreshed successfully
[[15:08:54]] [SUCCESS] Screenshot refreshed successfully
[[15:08:54]] [INFO] Xqj9EIVEfg=running
[[15:08:54]] [INFO] Executing action 410/576: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[15:08:53]] [SUCCESS] Screenshot refreshed
[[15:08:53]] [INFO] Refreshing screenshot...
[[15:08:53]] [INFO] E2jpN7BioW=pass
[[15:08:48]] [SUCCESS] Screenshot refreshed successfully
[[15:08:48]] [SUCCESS] Screenshot refreshed successfully
[[15:08:48]] [INFO] E2jpN7BioW=running
[[15:08:48]] [INFO] Executing action 409/576: Tap on Text: "Save"
[[15:08:48]] [SUCCESS] Screenshot refreshed
[[15:08:48]] [INFO] Refreshing screenshot...
[[15:08:48]] [INFO] Sl6eiqZkRm=pass
[[15:08:42]] [SUCCESS] Screenshot refreshed successfully
[[15:08:42]] [SUCCESS] Screenshot refreshed successfully
[[15:08:42]] [INFO] Sl6eiqZkRm=running
[[15:08:42]] [INFO] Executing action 408/576: Wait till accessibility_id=btnSaveOrContinue
[[15:08:42]] [SUCCESS] Screenshot refreshed
[[15:08:42]] [INFO] Refreshing screenshot...
[[15:08:42]] [INFO] mw9GQ4mzRE=pass
[[15:08:37]] [SUCCESS] Screenshot refreshed successfully
[[15:08:37]] [SUCCESS] Screenshot refreshed successfully
[[15:08:37]] [INFO] mw9GQ4mzRE=running
[[15:08:37]] [INFO] Executing action 407/576: Tap on Text: "2000"
[[15:08:37]] [SUCCESS] Screenshot refreshed
[[15:08:37]] [INFO] Refreshing screenshot...
[[15:08:37]] [INFO] kbdEPCPYod=pass
[[15:08:32]] [SUCCESS] Screenshot refreshed successfully
[[15:08:32]] [SUCCESS] Screenshot refreshed successfully
[[15:08:32]] [INFO] kbdEPCPYod=running
[[15:08:32]] [INFO] Executing action 406/576: textClear action
[[15:08:31]] [SUCCESS] Screenshot refreshed
[[15:08:31]] [INFO] Refreshing screenshot...
[[15:08:31]] [INFO] 8WCusTZ8q9=pass
[[15:08:26]] [SUCCESS] Screenshot refreshed successfully
[[15:08:26]] [SUCCESS] Screenshot refreshed successfully
[[15:08:25]] [INFO] 8WCusTZ8q9=running
[[15:08:25]] [INFO] Executing action 405/576: Tap on element with accessibility_id: Search suburb or postcode
[[15:08:25]] [SUCCESS] Screenshot refreshed
[[15:08:25]] [INFO] Refreshing screenshot...
[[15:08:25]] [INFO] QMXBlswP6H=pass
[[15:08:21]] [SUCCESS] Screenshot refreshed successfully
[[15:08:21]] [SUCCESS] Screenshot refreshed successfully
[[15:08:21]] [INFO] QMXBlswP6H=running
[[15:08:21]] [INFO] Executing action 404/576: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[15:08:20]] [SUCCESS] Screenshot refreshed
[[15:08:20]] [INFO] Refreshing screenshot...
[[15:08:20]] [INFO] m0956RsrdM=pass
[[15:08:18]] [SUCCESS] Screenshot refreshed successfully
[[15:08:18]] [SUCCESS] Screenshot refreshed successfully
[[15:08:15]] [INFO] m0956RsrdM=running
[[15:08:15]] [INFO] Executing action 403/576: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[15:08:15]] [SUCCESS] Screenshot refreshed
[[15:08:15]] [INFO] Refreshing screenshot...
[[15:08:15]] [SUCCESS] Screenshot refreshed
[[15:08:15]] [INFO] Refreshing screenshot...
[[15:08:10]] [SUCCESS] Screenshot refreshed successfully
[[15:08:10]] [SUCCESS] Screenshot refreshed successfully
[[15:08:10]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[15:08:09]] [SUCCESS] Screenshot refreshed
[[15:08:09]] [INFO] Refreshing screenshot...
[[15:08:05]] [SUCCESS] Screenshot refreshed successfully
[[15:08:05]] [SUCCESS] Screenshot refreshed successfully
[[15:08:05]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:08:04]] [SUCCESS] Screenshot refreshed
[[15:08:04]] [INFO] Refreshing screenshot...
[[15:08:00]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[15:08:00]] [SUCCESS] Screenshot refreshed successfully
[[15:08:00]] [SUCCESS] Screenshot refreshed successfully
[[15:07:59]] [SUCCESS] Screenshot refreshed
[[15:07:59]] [INFO] Refreshing screenshot...
[[15:07:55]] [SUCCESS] Screenshot refreshed successfully
[[15:07:55]] [SUCCESS] Screenshot refreshed successfully
[[15:07:55]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:07:54]] [SUCCESS] Screenshot refreshed
[[15:07:54]] [INFO] Refreshing screenshot...
[[15:07:49]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:07:49]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[15:07:49]] [SUCCESS] Screenshot refreshed successfully
[[15:07:49]] [SUCCESS] Screenshot refreshed successfully
[[15:07:49]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[15:07:48]] [INFO] C3UHsKxa5P=running
[[15:07:48]] [INFO] Executing action 402/576: Execute Test Case: Kmart-Signin (6 steps)
[[15:07:48]] [SUCCESS] Screenshot refreshed
[[15:07:48]] [INFO] Refreshing screenshot...
[[15:07:48]] [INFO] Azb1flbIJJ=pass
[[15:07:44]] [SUCCESS] Screenshot refreshed successfully
[[15:07:44]] [SUCCESS] Screenshot refreshed successfully
[[15:07:44]] [INFO] Azb1flbIJJ=running
[[15:07:44]] [INFO] Executing action 401/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[15:07:43]] [SUCCESS] Screenshot refreshed
[[15:07:43]] [INFO] Refreshing screenshot...
[[15:07:43]] [INFO] 2xC5fLfLe8=pass
[[15:07:41]] [SUCCESS] Screenshot refreshed successfully
[[15:07:41]] [SUCCESS] Screenshot refreshed successfully
[[15:07:40]] [INFO] 2xC5fLfLe8=running
[[15:07:40]] [INFO] Executing action 400/576: iOS Function: alert_accept
[[15:07:40]] [SUCCESS] Screenshot refreshed
[[15:07:40]] [INFO] Refreshing screenshot...
[[15:07:40]] [INFO] Y8vz7AJD1i=pass
[[15:07:32]] [SUCCESS] Screenshot refreshed successfully
[[15:07:32]] [SUCCESS] Screenshot refreshed successfully
[[15:07:32]] [INFO] Y8vz7AJD1i=running
[[15:07:32]] [INFO] Executing action 399/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:07:31]] [SUCCESS] Screenshot refreshed
[[15:07:31]] [INFO] Refreshing screenshot...
[[15:07:31]] [INFO] H9fy9qcFbZ=pass
[[15:07:18]] [SUCCESS] Screenshot refreshed successfully
[[15:07:18]] [SUCCESS] Screenshot refreshed successfully
[[15:07:17]] [INFO] H9fy9qcFbZ=running
[[15:07:17]] [INFO] Executing action 398/576: Restart app: env[appid]
[[15:07:17]] [SUCCESS] Screenshot refreshed
[[15:07:17]] [INFO] Refreshing screenshot...
[[15:07:17]] [SUCCESS] Screenshot refreshed
[[15:07:17]] [INFO] Refreshing screenshot...
[[15:07:14]] [SUCCESS] Screenshot refreshed successfully
[[15:07:14]] [SUCCESS] Screenshot refreshed successfully
[[15:07:13]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:07:13]] [SUCCESS] Screenshot refreshed
[[15:07:13]] [INFO] Refreshing screenshot...
[[15:07:07]] [SUCCESS] Screenshot refreshed successfully
[[15:07:07]] [SUCCESS] Screenshot refreshed successfully
[[15:07:07]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:07:06]] [SUCCESS] Screenshot refreshed
[[15:07:06]] [INFO] Refreshing screenshot...
[[15:07:02]] [SUCCESS] Screenshot refreshed successfully
[[15:07:02]] [SUCCESS] Screenshot refreshed successfully
[[15:07:02]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:07:02]] [SUCCESS] Screenshot refreshed
[[15:07:02]] [INFO] Refreshing screenshot...
[[15:06:58]] [SUCCESS] Screenshot refreshed successfully
[[15:06:58]] [SUCCESS] Screenshot refreshed successfully
[[15:06:58]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:06:57]] [SUCCESS] Screenshot refreshed
[[15:06:57]] [INFO] Refreshing screenshot...
[[15:06:51]] [SUCCESS] Screenshot refreshed successfully
[[15:06:51]] [SUCCESS] Screenshot refreshed successfully
[[15:06:50]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:06:50]] [SUCCESS] Screenshot refreshed
[[15:06:50]] [INFO] Refreshing screenshot...
[[15:06:43]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:06:42]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:06:42]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:06:42]] [INFO] OMgc2gHHyq=running
[[15:06:42]] [INFO] Executing action 397/576: cleanupSteps action
[[15:06:42]] [INFO] Skipping remaining steps in failed test case (moving from action 392 to 396), but preserving cleanup steps
[[15:06:42]] [INFO] Moving to the next test case after failure (server will handle retry)
[[15:06:42]] [ERROR] Multi Step action step 1 failed: Element with xpath '//XCUIElementTypeButton[@name="Delivery"]' not found within timeout of 40.0 seconds
[[15:05:58]] [INFO] Executing Multi Step action step 1/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[15:05:58]] [INFO] Loaded 41 steps from test case: Delivery Buy Steps
[[15:05:58]] [INFO] Loading steps for multiStep action: Delivery Buy Steps
[[15:05:58]] [INFO] ZxObWodIp8=running
[[15:05:58]] [INFO] Executing action 392/576: Execute Test Case: Delivery Buy Steps (41 steps)
[[15:05:58]] [SUCCESS] Screenshot refreshed successfully
[[15:05:58]] [SUCCESS] Screenshot refreshed successfully
[[15:05:58]] [SUCCESS] Screenshot refreshed
[[15:05:58]] [INFO] Refreshing screenshot...
[[15:05:58]] [INFO] F4NGh9HrLw=pass
[[15:05:54]] [SUCCESS] Screenshot refreshed successfully
[[15:05:54]] [SUCCESS] Screenshot refreshed successfully
[[15:05:53]] [INFO] F4NGh9HrLw=running
[[15:05:53]] [INFO] Executing action 391/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[15:05:53]] [SUCCESS] Screenshot refreshed
[[15:05:53]] [INFO] Refreshing screenshot...
[[15:05:53]] [INFO] 4eEEGs1x8i=pass
[[15:05:41]] [SUCCESS] Screenshot refreshed successfully
[[15:05:41]] [SUCCESS] Screenshot refreshed successfully
[[15:05:40]] [INFO] 4eEEGs1x8i=running
[[15:05:40]] [INFO] Executing action 390/576: If exists: xpath="//XCUIElementTypeButton[@name="Save my location"]" (timeout: 10s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Save my location"]
[[15:05:40]] [SUCCESS] Screenshot refreshed
[[15:05:40]] [INFO] Refreshing screenshot...
[[15:05:40]] [INFO] O8XvoFFGEB=pass
[[15:05:35]] [SUCCESS] Screenshot refreshed successfully
[[15:05:35]] [SUCCESS] Screenshot refreshed successfully
[[15:05:35]] [INFO] O8XvoFFGEB=running
[[15:05:35]] [INFO] Executing action 389/576: Tap on image: env[atg-pdp]
[[15:05:34]] [SUCCESS] Screenshot refreshed
[[15:05:34]] [INFO] Refreshing screenshot...
[[15:05:34]] [INFO] CcFsA41sKp=pass
[[15:05:30]] [SUCCESS] Screenshot refreshed successfully
[[15:05:30]] [SUCCESS] Screenshot refreshed successfully
[[15:05:30]] [INFO] CcFsA41sKp=running
[[15:05:30]] [INFO] Executing action 388/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[15:05:30]] [SUCCESS] Screenshot refreshed
[[15:05:30]] [INFO] Refreshing screenshot...
[[15:05:30]] [INFO] 8XWyF2kgwW=pass
[[15:05:26]] [SUCCESS] Screenshot refreshed successfully
[[15:05:26]] [SUCCESS] Screenshot refreshed successfully
[[15:05:26]] [INFO] 8XWyF2kgwW=running
[[15:05:26]] [INFO] Executing action 387/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[15:05:26]] [SUCCESS] Screenshot refreshed
[[15:05:26]] [INFO] Refreshing screenshot...
[[15:05:26]] [INFO] qG4RkNac30=pass
[[15:05:22]] [SUCCESS] Screenshot refreshed successfully
[[15:05:22]] [SUCCESS] Screenshot refreshed successfully
[[15:05:21]] [INFO] qG4RkNac30=running
[[15:05:21]] [INFO] Executing action 386/576: iOS Function: text - Text: "P_42691341"
[[15:05:21]] [SUCCESS] Screenshot refreshed
[[15:05:21]] [INFO] Refreshing screenshot...
[[15:05:21]] [INFO] Jtn2FK4THX=pass
[[15:05:16]] [SUCCESS] Screenshot refreshed successfully
[[15:05:16]] [SUCCESS] Screenshot refreshed successfully
[[15:05:15]] [INFO] Jtn2FK4THX=running
[[15:05:15]] [INFO] Executing action 385/576: Tap on Text: "Find"
[[15:05:15]] [SUCCESS] Screenshot refreshed
[[15:05:15]] [INFO] Refreshing screenshot...
[[15:05:15]] [INFO] tWq2Qzn22D=pass
[[15:05:11]] [SUCCESS] Screenshot refreshed successfully
[[15:05:11]] [SUCCESS] Screenshot refreshed successfully
[[15:05:10]] [INFO] tWq2Qzn22D=running
[[15:05:10]] [INFO] Executing action 384/576: Tap on image: env[device-back-img]
[[15:05:10]] [SUCCESS] Screenshot refreshed
[[15:05:10]] [INFO] Refreshing screenshot...
[[15:05:10]] [INFO] 5hClb2pKKx=pass
[[15:04:48]] [SUCCESS] Screenshot refreshed successfully
[[15:04:48]] [SUCCESS] Screenshot refreshed successfully
[[15:04:47]] [INFO] 5hClb2pKKx=running
[[15:04:47]] [INFO] Executing action 383/576: If exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="btnUpdate"]
[[15:04:47]] [SUCCESS] Screenshot refreshed
[[15:04:47]] [INFO] Refreshing screenshot...
[[15:04:47]] [INFO] jmKjclMUWT=pass
[[15:04:42]] [SUCCESS] Screenshot refreshed successfully
[[15:04:42]] [SUCCESS] Screenshot refreshed successfully
[[15:04:42]] [INFO] jmKjclMUWT=running
[[15:04:42]] [INFO] Executing action 382/576: Tap on Text: "current"
[[15:04:41]] [SUCCESS] Screenshot refreshed
[[15:04:41]] [INFO] Refreshing screenshot...
[[15:04:41]] [INFO] UoH0wdtcLk=pass
[[15:04:36]] [SUCCESS] Screenshot refreshed successfully
[[15:04:36]] [SUCCESS] Screenshot refreshed successfully
[[15:04:36]] [INFO] UoH0wdtcLk=running
[[15:04:36]] [INFO] Executing action 381/576: Tap on Text: "Edit"
[[15:04:35]] [SUCCESS] Screenshot refreshed
[[15:04:35]] [INFO] Refreshing screenshot...
[[15:04:35]] [INFO] U48qCNydwd=pass
[[15:04:30]] [SUCCESS] Screenshot refreshed successfully
[[15:04:30]] [SUCCESS] Screenshot refreshed successfully
[[15:04:30]] [INFO] U48qCNydwd=running
[[15:04:30]] [INFO] Executing action 380/576: Restart app: env[appid]
[[15:04:29]] [SUCCESS] Screenshot refreshed
[[15:04:29]] [INFO] Refreshing screenshot...
[[15:04:29]] [INFO] XjclKOaCTh=pass
[[15:04:25]] [SUCCESS] Screenshot refreshed successfully
[[15:04:25]] [SUCCESS] Screenshot refreshed successfully
[[15:04:25]] [INFO] XjclKOaCTh=running
[[15:04:25]] [INFO] Executing action 379/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[15:04:24]] [SUCCESS] Screenshot refreshed
[[15:04:24]] [INFO] Refreshing screenshot...
[[15:04:24]] [INFO] q6cKxgMAIn=pass
[[15:04:21]] [SUCCESS] Screenshot refreshed successfully
[[15:04:21]] [SUCCESS] Screenshot refreshed successfully
[[15:04:21]] [INFO] q6cKxgMAIn=running
[[15:04:21]] [INFO] Executing action 378/576: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[15:04:20]] [SUCCESS] Screenshot refreshed
[[15:04:20]] [INFO] Refreshing screenshot...
[[15:04:20]] [INFO] zdh8hKYC1a=pass
[[15:04:16]] [SUCCESS] Screenshot refreshed successfully
[[15:04:16]] [SUCCESS] Screenshot refreshed successfully
[[15:04:15]] [INFO] zdh8hKYC1a=running
[[15:04:15]] [INFO] Executing action 377/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[15:04:15]] [SUCCESS] Screenshot refreshed
[[15:04:15]] [INFO] Refreshing screenshot...
[[15:04:15]] [INFO] P4b2BITpCf=pass
[[15:04:12]] [SUCCESS] Screenshot refreshed successfully
[[15:04:12]] [SUCCESS] Screenshot refreshed successfully
[[15:04:12]] [INFO] P4b2BITpCf=running
[[15:04:12]] [INFO] Executing action 376/576: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[15:04:11]] [SUCCESS] Screenshot refreshed
[[15:04:11]] [INFO] Refreshing screenshot...
[[15:04:11]] [INFO] inrxgdWzXr=pass
[[15:04:06]] [SUCCESS] Screenshot refreshed successfully
[[15:04:06]] [SUCCESS] Screenshot refreshed successfully
[[15:04:06]] [INFO] inrxgdWzXr=running
[[15:04:06]] [INFO] Executing action 375/576: Tap on Text: "Store"
[[15:04:06]] [SUCCESS] Screenshot refreshed
[[15:04:06]] [INFO] Refreshing screenshot...
[[15:04:06]] [INFO] inrxgdWzXr=pass
[[15:04:01]] [SUCCESS] Screenshot refreshed successfully
[[15:04:01]] [SUCCESS] Screenshot refreshed successfully
[[15:04:01]] [INFO] inrxgdWzXr=running
[[15:04:01]] [INFO] Executing action 374/576: Tap on Text: "receipts"
[[15:04:01]] [SUCCESS] Screenshot refreshed
[[15:04:01]] [INFO] Refreshing screenshot...
[[15:04:01]] [INFO] GEMv6goQtW=pass
[[15:03:57]] [SUCCESS] Screenshot refreshed successfully
[[15:03:57]] [SUCCESS] Screenshot refreshed successfully
[[15:03:57]] [INFO] GEMv6goQtW=running
[[15:03:57]] [INFO] Executing action 373/576: Tap on image: env[device-back-img]
[[15:03:56]] [SUCCESS] Screenshot refreshed
[[15:03:56]] [INFO] Refreshing screenshot...
[[15:03:56]] [INFO] DhWa2PCBXE=pass
[[15:03:53]] [SUCCESS] Screenshot refreshed successfully
[[15:03:53]] [SUCCESS] Screenshot refreshed successfully
[[15:03:53]] [INFO] DhWa2PCBXE=running
[[15:03:53]] [INFO] Executing action 372/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[15:03:53]] [SUCCESS] Screenshot refreshed
[[15:03:53]] [INFO] Refreshing screenshot...
[[15:03:53]] [INFO] pk2DLZFBmx=pass
[[15:03:49]] [SUCCESS] Screenshot refreshed successfully
[[15:03:49]] [SUCCESS] Screenshot refreshed successfully
[[15:03:48]] [INFO] pk2DLZFBmx=running
[[15:03:48]] [INFO] Executing action 371/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[15:03:48]] [SUCCESS] Screenshot refreshed
[[15:03:48]] [INFO] Refreshing screenshot...
[[15:03:48]] [INFO] ShJSdXvmVL=pass
[[15:03:37]] [SUCCESS] Screenshot refreshed successfully
[[15:03:37]] [SUCCESS] Screenshot refreshed successfully
[[15:03:37]] [INFO] ShJSdXvmVL=running
[[15:03:37]] [INFO] Executing action 370/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[15:03:37]] [SUCCESS] Screenshot refreshed
[[15:03:37]] [INFO] Refreshing screenshot...
[[15:03:37]] [INFO] kWPRvuo7kk=pass
[[15:03:32]] [SUCCESS] Screenshot refreshed successfully
[[15:03:32]] [SUCCESS] Screenshot refreshed successfully
[[15:03:32]] [INFO] kWPRvuo7kk=running
[[15:03:32]] [INFO] Executing action 369/576: iOS Function: text - Text: "env[pwd-op]"
[[15:03:32]] [SUCCESS] Screenshot refreshed
[[15:03:32]] [INFO] Refreshing screenshot...
[[15:03:32]] [INFO] d6vTfR4Y0D=pass
[[15:03:28]] [SUCCESS] Screenshot refreshed successfully
[[15:03:28]] [SUCCESS] Screenshot refreshed successfully
[[15:03:27]] [INFO] d6vTfR4Y0D=running
[[15:03:27]] [INFO] Executing action 368/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[15:03:27]] [SUCCESS] Screenshot refreshed
[[15:03:27]] [INFO] Refreshing screenshot...
[[15:03:27]] [INFO] pe9W6tZdXT=pass
[[15:03:22]] [SUCCESS] Screenshot refreshed successfully
[[15:03:22]] [SUCCESS] Screenshot refreshed successfully
[[15:03:22]] [INFO] pe9W6tZdXT=running
[[15:03:22]] [INFO] Executing action 367/576: iOS Function: text - Text: "env[uname-op]"
[[15:03:22]] [SUCCESS] Screenshot refreshed
[[15:03:22]] [INFO] Refreshing screenshot...
[[15:03:22]] [INFO] u928vFzSni=pass
[[15:03:17]] [SUCCESS] Screenshot refreshed successfully
[[15:03:17]] [SUCCESS] Screenshot refreshed successfully
[[15:03:17]] [INFO] u928vFzSni=running
[[15:03:17]] [INFO] Executing action 366/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[15:03:16]] [SUCCESS] Screenshot refreshed
[[15:03:16]] [INFO] Refreshing screenshot...
[[15:03:16]] [INFO] s0WyiD1w0B=pass
[[15:03:13]] [SUCCESS] Screenshot refreshed successfully
[[15:03:13]] [SUCCESS] Screenshot refreshed successfully
[[15:03:13]] [INFO] s0WyiD1w0B=running
[[15:03:13]] [INFO] Executing action 365/576: iOS Function: alert_accept
[[15:03:13]] [SUCCESS] Screenshot refreshed
[[15:03:13]] [INFO] Refreshing screenshot...
[[15:03:13]] [INFO] gekNSY5O2E=pass
[[15:03:10]] [SUCCESS] Screenshot refreshed successfully
[[15:03:10]] [SUCCESS] Screenshot refreshed successfully
[[15:03:09]] [INFO] gekNSY5O2E=running
[[15:03:09]] [INFO] Executing action 364/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[15:03:08]] [SUCCESS] Screenshot refreshed
[[15:03:08]] [INFO] Refreshing screenshot...
[[15:03:08]] [INFO] VJJ3EXXotU=pass
[[15:03:04]] [SUCCESS] Screenshot refreshed successfully
[[15:03:04]] [SUCCESS] Screenshot refreshed successfully
[[15:03:04]] [INFO] VJJ3EXXotU=running
[[15:03:04]] [INFO] Executing action 363/576: Tap on image: env[device-back-img]
[[15:03:04]] [SUCCESS] Screenshot refreshed
[[15:03:04]] [INFO] Refreshing screenshot...
[[15:03:04]] [INFO] 83tV9A4NOn=pass
[[15:03:01]] [SUCCESS] Screenshot refreshed successfully
[[15:03:01]] [SUCCESS] Screenshot refreshed successfully
[[15:03:01]] [INFO] 83tV9A4NOn=running
[[15:03:01]] [INFO] Executing action 362/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[15:03:00]] [SUCCESS] Screenshot refreshed
[[15:03:00]] [INFO] Refreshing screenshot...
[[15:03:00]] [INFO] aNN0yYFLEd=pass
[[15:02:56]] [SUCCESS] Screenshot refreshed successfully
[[15:02:56]] [SUCCESS] Screenshot refreshed successfully
[[15:02:56]] [INFO] aNN0yYFLEd=running
[[15:02:56]] [INFO] Executing action 361/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[15:02:56]] [SUCCESS] Screenshot refreshed
[[15:02:56]] [INFO] Refreshing screenshot...
[[15:02:56]] [INFO] XJv08Gkucs=pass
[[15:02:52]] [SUCCESS] Screenshot refreshed successfully
[[15:02:52]] [SUCCESS] Screenshot refreshed successfully
[[15:02:52]] [INFO] XJv08Gkucs=running
[[15:02:52]] [INFO] Executing action 360/576: Input text: "env[uname-op]"
[[15:02:52]] [SUCCESS] Screenshot refreshed
[[15:02:52]] [INFO] Refreshing screenshot...
[[15:02:52]] [INFO] kAQ1yIIw3h=pass
[[15:02:49]] [SUCCESS] Screenshot refreshed successfully
[[15:02:49]] [SUCCESS] Screenshot refreshed successfully
[[15:02:47]] [INFO] kAQ1yIIw3h=running
[[15:02:47]] [INFO] Executing action 359/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[15:02:47]] [SUCCESS] Screenshot refreshed
[[15:02:47]] [INFO] Refreshing screenshot...
[[15:02:47]] [INFO] 7YbjwQH1Jc=pass
[[15:02:43]] [SUCCESS] Screenshot refreshed successfully
[[15:02:43]] [SUCCESS] Screenshot refreshed successfully
[[15:02:43]] [INFO] 7YbjwQH1Jc=running
[[15:02:43]] [INFO] Executing action 358/576: Input text: "env[searchorder]"
[[15:02:43]] [SUCCESS] Screenshot refreshed
[[15:02:43]] [INFO] Refreshing screenshot...
[[15:02:43]] [INFO] OmKfD9iBjD=pass
[[15:02:38]] [SUCCESS] Screenshot refreshed successfully
[[15:02:38]] [SUCCESS] Screenshot refreshed successfully
[[15:02:38]] [INFO] OmKfD9iBjD=running
[[15:02:38]] [INFO] Executing action 357/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[15:02:38]] [SUCCESS] Screenshot refreshed
[[15:02:38]] [INFO] Refreshing screenshot...
[[15:02:38]] [INFO] eHLWiRoqqS=pass
[[15:02:34]] [SUCCESS] Screenshot refreshed successfully
[[15:02:34]] [SUCCESS] Screenshot refreshed successfully
[[15:02:34]] [INFO] eHLWiRoqqS=running
[[15:02:34]] [INFO] Executing action 356/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[15:02:33]] [SUCCESS] Screenshot refreshed
[[15:02:33]] [INFO] Refreshing screenshot...
[[15:02:33]] [INFO] F4NGh9HrLw=pass
[[15:02:30]] [SUCCESS] Screenshot refreshed successfully
[[15:02:30]] [SUCCESS] Screenshot refreshed successfully
[[15:02:29]] [INFO] F4NGh9HrLw=running
[[15:02:29]] [INFO] Executing action 355/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:02:29]] [SUCCESS] Screenshot refreshed
[[15:02:29]] [INFO] Refreshing screenshot...
[[15:02:29]] [INFO] 74XW7x54ad=pass
[[15:02:25]] [SUCCESS] Screenshot refreshed successfully
[[15:02:25]] [SUCCESS] Screenshot refreshed successfully
[[15:02:24]] [INFO] 74XW7x54ad=running
[[15:02:24]] [INFO] Executing action 354/576: Tap on image: env[device-back-img]
[[15:02:24]] [SUCCESS] Screenshot refreshed
[[15:02:24]] [INFO] Refreshing screenshot...
[[15:02:24]] [INFO] xUbWFa8Ok2=pass
[[15:02:21]] [SUCCESS] Screenshot refreshed successfully
[[15:02:21]] [SUCCESS] Screenshot refreshed successfully
[[15:02:20]] [INFO] xUbWFa8Ok2=running
[[15:02:20]] [INFO] Executing action 353/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[15:02:19]] [SUCCESS] Screenshot refreshed
[[15:02:19]] [INFO] Refreshing screenshot...
[[15:02:19]] [INFO] RbNtEW6N9T=pass
[[15:02:16]] [SUCCESS] Screenshot refreshed successfully
[[15:02:16]] [SUCCESS] Screenshot refreshed successfully
[[15:02:14]] [INFO] RbNtEW6N9T=running
[[15:02:14]] [INFO] Executing action 352/576: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[15:02:14]] [SUCCESS] Screenshot refreshed
[[15:02:14]] [INFO] Refreshing screenshot...
[[15:02:14]] [INFO] F4NGh9HrLw=pass
[[15:02:11]] [SUCCESS] Screenshot refreshed successfully
[[15:02:11]] [SUCCESS] Screenshot refreshed successfully
[[15:02:10]] [INFO] F4NGh9HrLw=running
[[15:02:10]] [INFO] Executing action 351/576: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[15:02:09]] [SUCCESS] Screenshot refreshed
[[15:02:09]] [INFO] Refreshing screenshot...
[[15:02:09]] [INFO] RlDZFks4Lc=pass
[[15:02:07]] [SUCCESS] Screenshot refreshed successfully
[[15:02:07]] [SUCCESS] Screenshot refreshed successfully
[[15:02:06]] [INFO] RlDZFks4Lc=running
[[15:02:06]] [INFO] Executing action 350/576: iOS Function: alert_accept
[[15:02:05]] [SUCCESS] Screenshot refreshed
[[15:02:05]] [INFO] Refreshing screenshot...
[[15:02:05]] [INFO] Dzn2Q7JTe0=pass
[[15:02:01]] [SUCCESS] Screenshot refreshed successfully
[[15:02:01]] [SUCCESS] Screenshot refreshed successfully
[[15:02:00]] [INFO] Dzn2Q7JTe0=running
[[15:02:00]] [INFO] Executing action 349/576: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[15:02:00]] [SUCCESS] Screenshot refreshed
[[15:02:00]] [INFO] Refreshing screenshot...
[[15:02:00]] [INFO] H9fy9qcFbZ=pass
[[15:01:47]] [SUCCESS] Screenshot refreshed successfully
[[15:01:47]] [SUCCESS] Screenshot refreshed successfully
[[15:01:45]] [INFO] H9fy9qcFbZ=running
[[15:01:45]] [INFO] Executing action 348/576: Restart app: env[appid]
[[15:01:45]] [SUCCESS] Screenshot refreshed
[[15:01:45]] [INFO] Refreshing screenshot...
[[15:01:45]] [SUCCESS] Screenshot refreshed
[[15:01:45]] [INFO] Refreshing screenshot...
[[15:01:42]] [SUCCESS] Screenshot refreshed successfully
[[15:01:42]] [SUCCESS] Screenshot refreshed successfully
[[15:01:42]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[15:01:41]] [SUCCESS] Screenshot refreshed
[[15:01:41]] [INFO] Refreshing screenshot...
[[15:01:29]] [SUCCESS] Screenshot refreshed successfully
[[15:01:29]] [SUCCESS] Screenshot refreshed successfully
[[15:01:29]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[15:01:28]] [SUCCESS] Screenshot refreshed
[[15:01:28]] [INFO] Refreshing screenshot...
[[15:01:24]] [SUCCESS] Screenshot refreshed successfully
[[15:01:24]] [SUCCESS] Screenshot refreshed successfully
[[15:01:24]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[15:01:24]] [SUCCESS] Screenshot refreshed
[[15:01:24]] [INFO] Refreshing screenshot...
[[15:01:20]] [SUCCESS] Screenshot refreshed successfully
[[15:01:20]] [SUCCESS] Screenshot refreshed successfully
[[15:01:20]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[15:01:19]] [SUCCESS] Screenshot refreshed
[[15:01:19]] [INFO] Refreshing screenshot...
[[15:01:13]] [SUCCESS] Screenshot refreshed successfully
[[15:01:13]] [SUCCESS] Screenshot refreshed successfully
[[15:01:12]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[15:01:12]] [SUCCESS] Screenshot refreshed
[[15:01:12]] [INFO] Refreshing screenshot...
[[15:01:05]] [SUCCESS] Screenshot refreshed successfully
[[15:01:05]] [SUCCESS] Screenshot refreshed successfully
[[15:01:05]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[15:01:05]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[15:01:05]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[15:01:05]] [INFO] AeQaElnzUN=running
[[15:01:05]] [INFO] Executing action 347/576: cleanupSteps action
[[15:01:04]] [SUCCESS] Screenshot refreshed
[[15:01:04]] [INFO] Refreshing screenshot...
[[15:01:04]] [INFO] BracBsfa3Y=pass
[[15:00:59]] [SUCCESS] Screenshot refreshed successfully
[[15:00:59]] [SUCCESS] Screenshot refreshed successfully
[[15:00:59]] [INFO] BracBsfa3Y=running
[[15:00:59]] [INFO] Executing action 346/576: Tap on Text: "out"
[[15:00:59]] [SUCCESS] Screenshot refreshed
[[15:00:59]] [INFO] Refreshing screenshot...
[[15:00:59]] [INFO] s6tWdQ5URW=pass
[[15:00:52]] [SUCCESS] Screenshot refreshed successfully
[[15:00:52]] [SUCCESS] Screenshot refreshed successfully
[[15:00:52]] [INFO] s6tWdQ5URW=running
[[15:00:52]] [INFO] Executing action 345/576: Swipe from (50%, 70%) to (50%, 30%)
[[15:00:51]] [SUCCESS] Screenshot refreshed
[[15:00:51]] [INFO] Refreshing screenshot...
[[15:00:51]] [INFO] wNGRrfUjpK=pass
[[15:00:48]] [SUCCESS] Screenshot refreshed successfully
[[15:00:48]] [SUCCESS] Screenshot refreshed successfully
[[15:00:47]] [INFO] wNGRrfUjpK=running
[[15:00:47]] [INFO] Executing action 344/576: Tap on image: env[device-back-img]
[[15:00:47]] [SUCCESS] Screenshot refreshed
[[15:00:47]] [INFO] Refreshing screenshot...
[[15:00:47]] [INFO] BracBsfa3Y=pass
[[15:00:42]] [SUCCESS] Screenshot refreshed successfully
[[15:00:42]] [SUCCESS] Screenshot refreshed successfully
[[15:00:42]] [INFO] BracBsfa3Y=running
[[15:00:42]] [INFO] Executing action 343/576: Tap on Text: "Customer"
[[15:00:42]] [SUCCESS] Screenshot refreshed
[[15:00:42]] [INFO] Refreshing screenshot...
[[15:00:42]] [INFO] H4WfwVU8YP=pass
[[15:00:37]] [SUCCESS] Screenshot refreshed successfully
[[15:00:37]] [SUCCESS] Screenshot refreshed successfully
[[15:00:37]] [INFO] H4WfwVU8YP=running
[[15:00:37]] [INFO] Executing action 342/576: Tap on image: banner-close-updated.png
[[15:00:36]] [SUCCESS] Screenshot refreshed
[[15:00:36]] [INFO] Refreshing screenshot...
[[15:00:36]] [INFO] ePyaYpttQA=pass
[[15:00:33]] [SUCCESS] Screenshot refreshed successfully
[[15:00:33]] [SUCCESS] Screenshot refreshed successfully
[[15:00:33]] [INFO] ePyaYpttQA=running
[[15:00:33]] [INFO] Executing action 341/576: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[15:00:32]] [SUCCESS] Screenshot refreshed
[[15:00:32]] [INFO] Refreshing screenshot...
[[15:00:32]] [INFO] BracBsfa3Y=pass
[[15:00:27]] [SUCCESS] Screenshot refreshed successfully
[[15:00:27]] [SUCCESS] Screenshot refreshed successfully
[[15:00:27]] [INFO] BracBsfa3Y=running
[[15:00:27]] [INFO] Executing action 340/576: Tap on Text: "Invite"
[[15:00:27]] [SUCCESS] Screenshot refreshed
[[15:00:27]] [INFO] Refreshing screenshot...
[[15:00:27]] [INFO] xVbCNStsOP=pass
[[15:00:23]] [SUCCESS] Screenshot refreshed successfully
[[15:00:23]] [SUCCESS] Screenshot refreshed successfully
[[15:00:23]] [INFO] xVbCNStsOP=running
[[15:00:23]] [INFO] Executing action 339/576: Tap on image: env[device-back-img]
[[15:00:23]] [SUCCESS] Screenshot refreshed
[[15:00:23]] [INFO] Refreshing screenshot...
[[15:00:23]] [INFO] 8kQkC2FGyZ=pass
[[15:00:19]] [SUCCESS] Screenshot refreshed successfully
[[15:00:19]] [SUCCESS] Screenshot refreshed successfully
[[15:00:19]] [INFO] 8kQkC2FGyZ=running
[[15:00:19]] [INFO] Executing action 338/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[15:00:19]] [SUCCESS] Screenshot refreshed
[[15:00:19]] [INFO] Refreshing screenshot...
[[15:00:19]] [INFO] PgjJCrKFYo=pass
[[15:00:14]] [SUCCESS] Screenshot refreshed successfully
[[15:00:14]] [SUCCESS] Screenshot refreshed successfully
[[15:00:14]] [INFO] PgjJCrKFYo=running
[[15:00:14]] [INFO] Executing action 337/576: Tap on Text: "VIC"
[[15:00:13]] [SUCCESS] Screenshot refreshed
[[15:00:13]] [INFO] Refreshing screenshot...
[[15:00:13]] [INFO] 3Si0csRNaw=pass
[[15:00:06]] [INFO] 3Si0csRNaw=running
[[15:00:06]] [INFO] Executing action 336/576: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[15:00:06]] [SUCCESS] Screenshot refreshed successfully
[[15:00:06]] [SUCCESS] Screenshot refreshed successfully
[[15:00:06]] [SUCCESS] Screenshot refreshed
[[15:00:06]] [INFO] Refreshing screenshot...
[[15:00:06]] [INFO] BracBsfa3Y=pass
[[15:00:00]] [SUCCESS] Screenshot refreshed successfully
[[15:00:00]] [SUCCESS] Screenshot refreshed successfully
[[15:00:00]] [INFO] BracBsfa3Y=running
[[15:00:00]] [INFO] Executing action 335/576: Tap on Text: "Nearby"
[[15:00:00]] [SUCCESS] Screenshot refreshed
[[15:00:00]] [INFO] Refreshing screenshot...
[[15:00:00]] [INFO] BracBsfa3Y=pass
[[14:59:55]] [SUCCESS] Screenshot refreshed successfully
[[14:59:55]] [SUCCESS] Screenshot refreshed successfully
[[14:59:55]] [INFO] BracBsfa3Y=running
[[14:59:55]] [INFO] Executing action 334/576: Tap on Text: "locator"
[[14:59:54]] [SUCCESS] Screenshot refreshed
[[14:59:54]] [INFO] Refreshing screenshot...
[[14:59:54]] [INFO] s6tWdQ5URW=pass
[[14:59:49]] [SUCCESS] Screenshot refreshed successfully
[[14:59:49]] [SUCCESS] Screenshot refreshed successfully
[[14:59:47]] [INFO] s6tWdQ5URW=running
[[14:59:47]] [INFO] Executing action 333/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:59:47]] [SUCCESS] Screenshot refreshed
[[14:59:47]] [INFO] Refreshing screenshot...
[[14:59:47]] [INFO] 2M0KHOVecv=pass
[[14:59:43]] [SUCCESS] Screenshot refreshed successfully
[[14:59:43]] [SUCCESS] Screenshot refreshed successfully
[[14:59:43]] [INFO] 2M0KHOVecv=running
[[14:59:43]] [INFO] Executing action 332/576: Check if element with accessibility_id="txtMy Flybuys card" exists
[[14:59:43]] [SUCCESS] Screenshot refreshed
[[14:59:43]] [INFO] Refreshing screenshot...
[[14:59:43]] [INFO] LBgsj3oLcu=pass
[[14:59:38]] [SUCCESS] Screenshot refreshed successfully
[[14:59:38]] [SUCCESS] Screenshot refreshed successfully
[[14:59:38]] [INFO] LBgsj3oLcu=running
[[14:59:38]] [INFO] Executing action 331/576: Tap on image: env[device-back-img]
[[14:59:38]] [SUCCESS] Screenshot refreshed
[[14:59:38]] [INFO] Refreshing screenshot...
[[14:59:38]] [INFO] biRyWs3nSs=pass
[[14:59:32]] [SUCCESS] Screenshot refreshed successfully
[[14:59:32]] [SUCCESS] Screenshot refreshed successfully
[[14:59:32]] [INFO] biRyWs3nSs=running
[[14:59:32]] [INFO] Executing action 330/576: Tap on element with accessibility_id: btnSaveFlybuysCard
[[14:59:31]] [SUCCESS] Screenshot refreshed
[[14:59:31]] [INFO] Refreshing screenshot...
[[14:59:31]] [INFO] 8cFGh3GD68=pass
[[14:59:26]] [SUCCESS] Screenshot refreshed successfully
[[14:59:26]] [SUCCESS] Screenshot refreshed successfully
[[14:59:25]] [INFO] 8cFGh3GD68=running
[[14:59:25]] [INFO] Executing action 329/576: Tap on element with accessibility_id: Done
[[14:59:25]] [SUCCESS] Screenshot refreshed
[[14:59:25]] [INFO] Refreshing screenshot...
[[14:59:25]] [INFO] sLe0Wurhgm=pass
[[14:59:22]] [SUCCESS] Screenshot refreshed successfully
[[14:59:22]] [SUCCESS] Screenshot refreshed successfully
[[14:59:22]] [INFO] sLe0Wurhgm=running
[[14:59:22]] [INFO] Executing action 328/576: Input text: "2791234567890"
[[14:59:21]] [SUCCESS] Screenshot refreshed
[[14:59:21]] [INFO] Refreshing screenshot...
[[14:59:21]] [INFO] Ey86YRVRzU=pass
[[14:59:15]] [SUCCESS] Screenshot refreshed successfully
[[14:59:15]] [SUCCESS] Screenshot refreshed successfully
[[14:59:15]] [INFO] Ey86YRVRzU=running
[[14:59:15]] [INFO] Executing action 327/576: Tap on element with accessibility_id: Flybuys barcode number
[[14:59:14]] [SUCCESS] Screenshot refreshed
[[14:59:14]] [INFO] Refreshing screenshot...
[[14:59:14]] [INFO] Gxhf3XGc6e=pass
[[14:59:08]] [SUCCESS] Screenshot refreshed successfully
[[14:59:08]] [SUCCESS] Screenshot refreshed successfully
[[14:59:08]] [INFO] Gxhf3XGc6e=running
[[14:59:08]] [INFO] Executing action 326/576: Tap on element with accessibility_id: btnLinkFlyBuys
[[14:59:08]] [SUCCESS] Screenshot refreshed
[[14:59:08]] [INFO] Refreshing screenshot...
[[14:59:08]] [INFO] BracBsfa3Y=pass
[[14:59:03]] [SUCCESS] Screenshot refreshed successfully
[[14:59:03]] [SUCCESS] Screenshot refreshed successfully
[[14:59:03]] [INFO] BracBsfa3Y=running
[[14:59:03]] [INFO] Executing action 325/576: Tap on Text: "Flybuys"
[[14:59:02]] [SUCCESS] Screenshot refreshed
[[14:59:02]] [INFO] Refreshing screenshot...
[[14:59:02]] [INFO] Ds5GfNVb3x=pass
[[14:58:57]] [SUCCESS] Screenshot refreshed successfully
[[14:58:57]] [SUCCESS] Screenshot refreshed successfully
[[14:58:57]] [INFO] Ds5GfNVb3x=running
[[14:58:57]] [INFO] Executing action 324/576: Tap on element with accessibility_id: btnRemove
[[14:58:56]] [SUCCESS] Screenshot refreshed
[[14:58:56]] [INFO] Refreshing screenshot...
[[14:58:56]] [INFO] 3ZFgwFaiXp=pass
[[14:58:50]] [SUCCESS] Screenshot refreshed successfully
[[14:58:50]] [SUCCESS] Screenshot refreshed successfully
[[14:58:50]] [INFO] 3ZFgwFaiXp=running
[[14:58:50]] [INFO] Executing action 323/576: Tap on element with accessibility_id: Remove card
[[14:58:50]] [SUCCESS] Screenshot refreshed
[[14:58:50]] [INFO] Refreshing screenshot...
[[14:58:50]] [INFO] 40hnWPsQ9P=pass
[[14:58:44]] [SUCCESS] Screenshot refreshed successfully
[[14:58:44]] [SUCCESS] Screenshot refreshed successfully
[[14:58:44]] [INFO] 40hnWPsQ9P=running
[[14:58:44]] [INFO] Executing action 322/576: Tap on element with accessibility_id: btneditFlybuysCard
[[14:58:44]] [SUCCESS] Screenshot refreshed
[[14:58:44]] [INFO] Refreshing screenshot...
[[14:58:44]] [INFO] 40hnWPsQ9P=pass
[[14:58:39]] [SUCCESS] Screenshot refreshed successfully
[[14:58:39]] [SUCCESS] Screenshot refreshed successfully
[[14:58:39]] [INFO] 40hnWPsQ9P=running
[[14:58:39]] [INFO] Executing action 321/576: Wait till accessibility_id=btneditFlybuysCard
[[14:58:38]] [SUCCESS] Screenshot refreshed
[[14:58:38]] [INFO] Refreshing screenshot...
[[14:58:38]] [INFO] BracBsfa3Y=pass
[[14:58:33]] [SUCCESS] Screenshot refreshed successfully
[[14:58:33]] [SUCCESS] Screenshot refreshed successfully
[[14:58:33]] [INFO] BracBsfa3Y=running
[[14:58:33]] [INFO] Executing action 320/576: Tap on Text: "Flybuys"
[[14:58:33]] [SUCCESS] Screenshot refreshed
[[14:58:33]] [INFO] Refreshing screenshot...
[[14:58:33]] [INFO] MkTFxfzubv=pass
[[14:58:29]] [SUCCESS] Screenshot refreshed successfully
[[14:58:29]] [SUCCESS] Screenshot refreshed successfully
[[14:58:29]] [INFO] MkTFxfzubv=running
[[14:58:29]] [INFO] Executing action 319/576: Tap on image: env[device-back-img]
[[14:58:29]] [SUCCESS] Screenshot refreshed
[[14:58:29]] [INFO] Refreshing screenshot...
[[14:58:29]] [INFO] EO3cMmdUyM=pass
[[14:58:25]] [SUCCESS] Screenshot refreshed successfully
[[14:58:25]] [SUCCESS] Screenshot refreshed successfully
[[14:58:25]] [INFO] EO3cMmdUyM=running
[[14:58:25]] [INFO] Executing action 318/576: Tap on image: env[device-back-img]
[[14:58:24]] [SUCCESS] Screenshot refreshed
[[14:58:24]] [INFO] Refreshing screenshot...
[[14:58:24]] [INFO] napKDohf3Z=pass
[[14:58:19]] [SUCCESS] Screenshot refreshed successfully
[[14:58:19]] [SUCCESS] Screenshot refreshed successfully
[[14:58:19]] [INFO] napKDohf3Z=running
[[14:58:19]] [INFO] Executing action 317/576: Tap on Text: "payment"
[[14:58:19]] [SUCCESS] Screenshot refreshed
[[14:58:19]] [INFO] Refreshing screenshot...
[[14:58:19]] [INFO] ekqt95ZRol=pass
[[14:58:15]] [SUCCESS] Screenshot refreshed successfully
[[14:58:15]] [SUCCESS] Screenshot refreshed successfully
[[14:58:15]] [INFO] ekqt95ZRol=running
[[14:58:15]] [INFO] Executing action 316/576: Tap on image: env[device-back-img]
[[14:58:14]] [SUCCESS] Screenshot refreshed
[[14:58:14]] [INFO] Refreshing screenshot...
[[14:58:14]] [INFO] 20qUCJgpE9=pass
[[14:58:10]] [SUCCESS] Screenshot refreshed successfully
[[14:58:10]] [SUCCESS] Screenshot refreshed successfully
[[14:58:10]] [INFO] 20qUCJgpE9=running
[[14:58:10]] [INFO] Executing action 315/576: Tap on Text: "address"
[[14:58:09]] [SUCCESS] Screenshot refreshed
[[14:58:09]] [INFO] Refreshing screenshot...
[[14:58:09]] [INFO] 6HR2weiXoT=pass
[[14:58:05]] [SUCCESS] Screenshot refreshed successfully
[[14:58:05]] [SUCCESS] Screenshot refreshed successfully
[[14:58:05]] [INFO] 6HR2weiXoT=running
[[14:58:05]] [INFO] Executing action 314/576: Tap on image: env[device-back-img]
[[14:58:05]] [SUCCESS] Screenshot refreshed
[[14:58:05]] [INFO] Refreshing screenshot...
[[14:58:05]] [INFO] 3hOTINBVMf=pass
[[14:58:00]] [SUCCESS] Screenshot refreshed successfully
[[14:58:00]] [SUCCESS] Screenshot refreshed successfully
[[14:58:00]] [INFO] 3hOTINBVMf=running
[[14:58:00]] [INFO] Executing action 313/576: Tap on Text: "details"
[[14:57:59]] [SUCCESS] Screenshot refreshed
[[14:57:59]] [INFO] Refreshing screenshot...
[[14:57:59]] [INFO] yJi0WxnERj=pass
[[14:57:56]] [SUCCESS] Screenshot refreshed successfully
[[14:57:56]] [SUCCESS] Screenshot refreshed successfully
[[14:57:55]] [INFO] yJi0WxnERj=running
[[14:57:55]] [INFO] Executing action 312/576: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[14:57:55]] [SUCCESS] Screenshot refreshed
[[14:57:55]] [INFO] Refreshing screenshot...
[[14:57:55]] [INFO] PbfHAtFQPP=pass
[[14:57:51]] [SUCCESS] Screenshot refreshed successfully
[[14:57:51]] [SUCCESS] Screenshot refreshed successfully
[[14:57:51]] [INFO] PbfHAtFQPP=running
[[14:57:51]] [INFO] Executing action 311/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:57:50]] [SUCCESS] Screenshot refreshed
[[14:57:50]] [INFO] Refreshing screenshot...
[[14:57:50]] [INFO] 6qZnk86hGg=pass
[[14:57:45]] [SUCCESS] Screenshot refreshed successfully
[[14:57:45]] [SUCCESS] Screenshot refreshed successfully
[[14:57:45]] [INFO] 6qZnk86hGg=running
[[14:57:45]] [INFO] Executing action 310/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[14:57:45]] [SUCCESS] Screenshot refreshed
[[14:57:45]] [INFO] Refreshing screenshot...
[[14:57:45]] [INFO] FAvQgIuHc1=pass
[[14:57:40]] [SUCCESS] Screenshot refreshed successfully
[[14:57:40]] [SUCCESS] Screenshot refreshed successfully
[[14:57:40]] [INFO] FAvQgIuHc1=running
[[14:57:40]] [INFO] Executing action 309/576: Tap on Text: "Return"
[[14:57:39]] [SUCCESS] Screenshot refreshed
[[14:57:39]] [INFO] Refreshing screenshot...
[[14:57:39]] [INFO] vmc01sHkbr=pass
[[14:57:32]] [SUCCESS] Screenshot refreshed successfully
[[14:57:32]] [SUCCESS] Screenshot refreshed successfully
[[14:57:32]] [INFO] vmc01sHkbr=running
[[14:57:32]] [INFO] Executing action 308/576: Wait for 5 ms
[[14:57:31]] [SUCCESS] Screenshot refreshed
[[14:57:31]] [INFO] Refreshing screenshot...
[[14:57:31]] [INFO] zeu0wd1vcF=pass
[[14:57:19]] [SUCCESS] Screenshot refreshed successfully
[[14:57:19]] [SUCCESS] Screenshot refreshed successfully
[[14:57:18]] [INFO] zeu0wd1vcF=running
[[14:57:18]] [INFO] Executing action 307/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:57:18]] [SUCCESS] Screenshot refreshed
[[14:57:18]] [INFO] Refreshing screenshot...
[[14:57:18]] [INFO] OwWeZes4aT=pass
[[14:57:14]] [SUCCESS] Screenshot refreshed successfully
[[14:57:14]] [SUCCESS] Screenshot refreshed successfully
[[14:57:14]] [INFO] OwWeZes4aT=running
[[14:57:14]] [INFO] Executing action 306/576: Tap on image: env[device-back-img]
[[14:57:13]] [SUCCESS] Screenshot refreshed
[[14:57:13]] [INFO] Refreshing screenshot...
[[14:57:13]] [INFO] aAaTtUE92h=pass
[[14:57:10]] [SUCCESS] Screenshot refreshed successfully
[[14:57:10]] [SUCCESS] Screenshot refreshed successfully
[[14:57:10]] [INFO] aAaTtUE92h=running
[[14:57:10]] [INFO] Executing action 305/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[14:57:09]] [SUCCESS] Screenshot refreshed
[[14:57:09]] [INFO] Refreshing screenshot...
[[14:57:09]] [INFO] 9iOZGMqAZK=pass
[[14:57:05]] [SUCCESS] Screenshot refreshed successfully
[[14:57:05]] [SUCCESS] Screenshot refreshed successfully
[[14:57:05]] [INFO] 9iOZGMqAZK=running
[[14:57:05]] [INFO] Executing action 304/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[14:57:04]] [SUCCESS] Screenshot refreshed
[[14:57:04]] [INFO] Refreshing screenshot...
[[14:57:04]] [INFO] mRTYzOFRRw=pass
[[14:57:01]] [SUCCESS] Screenshot refreshed successfully
[[14:57:01]] [SUCCESS] Screenshot refreshed successfully
[[14:57:01]] [INFO] mRTYzOFRRw=running
[[14:57:01]] [INFO] Executing action 303/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[14:57:01]] [SUCCESS] Screenshot refreshed
[[14:57:01]] [INFO] Refreshing screenshot...
[[14:57:01]] [INFO] 7g6MFJSGIO=pass
[[14:56:57]] [SUCCESS] Screenshot refreshed successfully
[[14:56:57]] [SUCCESS] Screenshot refreshed successfully
[[14:56:57]] [INFO] 7g6MFJSGIO=running
[[14:56:57]] [INFO] Executing action 302/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[14:56:56]] [SUCCESS] Screenshot refreshed
[[14:56:56]] [INFO] Refreshing screenshot...
[[14:56:56]] [INFO] zNwyPagPE1=pass
[[14:56:50]] [SUCCESS] Screenshot refreshed successfully
[[14:56:50]] [SUCCESS] Screenshot refreshed successfully
[[14:56:50]] [INFO] zNwyPagPE1=running
[[14:56:50]] [INFO] Executing action 301/576: Wait for 5 ms
[[14:56:49]] [SUCCESS] Screenshot refreshed
[[14:56:49]] [INFO] Refreshing screenshot...
[[14:56:49]] [INFO] qXsL3wzg6J=pass
[[14:56:45]] [SUCCESS] Screenshot refreshed successfully
[[14:56:45]] [SUCCESS] Screenshot refreshed successfully
[[14:56:45]] [INFO] qXsL3wzg6J=running
[[14:56:45]] [INFO] Executing action 300/576: Tap on image: env[device-back-img]
[[14:56:45]] [SUCCESS] Screenshot refreshed
[[14:56:45]] [INFO] Refreshing screenshot...
[[14:56:45]] [INFO] YuuQe2KupX=pass
[[14:56:40]] [INFO] YuuQe2KupX=running
[[14:56:40]] [INFO] Executing action 299/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[14:56:40]] [SUCCESS] Screenshot refreshed successfully
[[14:56:40]] [SUCCESS] Screenshot refreshed successfully
[[14:56:40]] [SUCCESS] Screenshot refreshed
[[14:56:40]] [INFO] Refreshing screenshot...
[[14:56:40]] [INFO] g0PE7Mofye=pass
[[14:56:34]] [SUCCESS] Screenshot refreshed successfully
[[14:56:34]] [SUCCESS] Screenshot refreshed successfully
[[14:56:34]] [INFO] g0PE7Mofye=running
[[14:56:34]] [INFO] Executing action 298/576: Tap on element with accessibility_id: Print order details
[[14:56:34]] [SUCCESS] Screenshot refreshed
[[14:56:34]] [INFO] Refreshing screenshot...
[[14:56:34]] [INFO] GgQaBLWYkb=pass
[[14:56:30]] [SUCCESS] Screenshot refreshed successfully
[[14:56:30]] [SUCCESS] Screenshot refreshed successfully
[[14:56:29]] [INFO] GgQaBLWYkb=running
[[14:56:29]] [INFO] Executing action 297/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[14:56:29]] [SUCCESS] Screenshot refreshed
[[14:56:29]] [INFO] Refreshing screenshot...
[[14:56:29]] [INFO] f3OrHHzTFN=pass
[[14:56:12]] [SUCCESS] Screenshot refreshed successfully
[[14:56:12]] [SUCCESS] Screenshot refreshed successfully
[[14:56:12]] [INFO] f3OrHHzTFN=running
[[14:56:12]] [INFO] Executing action 296/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[14:56:12]] [SUCCESS] Screenshot refreshed
[[14:56:12]] [INFO] Refreshing screenshot...
[[14:56:12]] [INFO] 7g6MFJSGIO=pass
[[14:56:07]] [SUCCESS] Screenshot refreshed successfully
[[14:56:07]] [SUCCESS] Screenshot refreshed successfully
[[14:56:07]] [INFO] 7g6MFJSGIO=running
[[14:56:07]] [INFO] Executing action 295/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[14:56:06]] [SUCCESS] Screenshot refreshed
[[14:56:06]] [INFO] Refreshing screenshot...
[[14:56:06]] [INFO] Z6g3sGuHTp=pass
[[14:56:00]] [SUCCESS] Screenshot refreshed successfully
[[14:56:00]] [SUCCESS] Screenshot refreshed successfully
[[14:56:00]] [INFO] Z6g3sGuHTp=running
[[14:56:00]] [INFO] Executing action 294/576: Wait for 5 ms
[[14:55:59]] [SUCCESS] Screenshot refreshed
[[14:55:59]] [INFO] Refreshing screenshot...
[[14:55:59]] [INFO] pFlYwTS53v=pass
[[14:55:55]] [SUCCESS] Screenshot refreshed successfully
[[14:55:55]] [SUCCESS] Screenshot refreshed successfully
[[14:55:55]] [INFO] pFlYwTS53v=running
[[14:55:55]] [INFO] Executing action 293/576: Tap on Text: "receipts"
[[14:55:54]] [SUCCESS] Screenshot refreshed
[[14:55:54]] [INFO] Refreshing screenshot...
[[14:55:54]] [INFO] V59u3l1wkM=pass
[[14:55:51]] [SUCCESS] Screenshot refreshed successfully
[[14:55:51]] [SUCCESS] Screenshot refreshed successfully
[[14:55:51]] [INFO] V59u3l1wkM=running
[[14:55:51]] [INFO] Executing action 292/576: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[14:55:50]] [SUCCESS] Screenshot refreshed
[[14:55:50]] [INFO] Refreshing screenshot...
[[14:55:50]] [INFO] sl3Wk1gK8X=pass
[[14:55:46]] [SUCCESS] Screenshot refreshed successfully
[[14:55:46]] [SUCCESS] Screenshot refreshed successfully
[[14:55:44]] [INFO] sl3Wk1gK8X=running
[[14:55:44]] [INFO] Executing action 291/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:55:44]] [SUCCESS] Screenshot refreshed
[[14:55:44]] [INFO] Refreshing screenshot...
[[14:55:43]] [SUCCESS] Screenshot refreshed
[[14:55:43]] [INFO] Refreshing screenshot...
[[14:55:38]] [SUCCESS] Screenshot refreshed successfully
[[14:55:38]] [SUCCESS] Screenshot refreshed successfully
[[14:55:38]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[14:55:37]] [SUCCESS] Screenshot refreshed
[[14:55:37]] [INFO] Refreshing screenshot...
[[14:55:33]] [SUCCESS] Screenshot refreshed successfully
[[14:55:33]] [SUCCESS] Screenshot refreshed successfully
[[14:55:33]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:55:33]] [SUCCESS] Screenshot refreshed
[[14:55:33]] [INFO] Refreshing screenshot...
[[14:55:28]] [SUCCESS] Screenshot refreshed successfully
[[14:55:28]] [SUCCESS] Screenshot refreshed successfully
[[14:55:28]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[14:55:27]] [SUCCESS] Screenshot refreshed
[[14:55:27]] [INFO] Refreshing screenshot...
[[14:55:23]] [SUCCESS] Screenshot refreshed successfully
[[14:55:23]] [SUCCESS] Screenshot refreshed successfully
[[14:55:23]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:55:23]] [SUCCESS] Screenshot refreshed
[[14:55:23]] [INFO] Refreshing screenshot...
[[14:55:17]] [SUCCESS] Screenshot refreshed successfully
[[14:55:17]] [SUCCESS] Screenshot refreshed successfully
[[14:55:17]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:55:17]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[14:55:17]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[14:55:17]] [INFO] vjK6GqOF3r=running
[[14:55:17]] [INFO] Executing action 290/576: Execute Test Case: Kmart-Signin (8 steps)
[[14:55:16]] [SUCCESS] Screenshot refreshed
[[14:55:16]] [INFO] Refreshing screenshot...
[[14:55:16]] [INFO] ly2oT3zqmf=pass
[[14:55:14]] [SUCCESS] Screenshot refreshed successfully
[[14:55:14]] [SUCCESS] Screenshot refreshed successfully
[[14:55:13]] [INFO] ly2oT3zqmf=running
[[14:55:13]] [INFO] Executing action 289/576: iOS Function: alert_accept
[[14:55:13]] [SUCCESS] Screenshot refreshed
[[14:55:13]] [INFO] Refreshing screenshot...
[[14:55:13]] [INFO] xAPeBnVHrT=pass
[[14:55:05]] [SUCCESS] Screenshot refreshed successfully
[[14:55:05]] [SUCCESS] Screenshot refreshed successfully
[[14:55:05]] [INFO] xAPeBnVHrT=running
[[14:55:05]] [INFO] Executing action 288/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:55:04]] [SUCCESS] Screenshot refreshed
[[14:55:04]] [INFO] Refreshing screenshot...
[[14:55:04]] [INFO] u6bRYZZFAv=pass
[[14:54:58]] [SUCCESS] Screenshot refreshed successfully
[[14:54:58]] [SUCCESS] Screenshot refreshed successfully
[[14:54:57]] [INFO] u6bRYZZFAv=running
[[14:54:57]] [INFO] Executing action 287/576: Wait for 5 ms
[[14:54:57]] [SUCCESS] Screenshot refreshed
[[14:54:57]] [INFO] Refreshing screenshot...
[[14:54:57]] [INFO] pjFNt3w5Fr=pass
[[14:54:44]] [SUCCESS] Screenshot refreshed successfully
[[14:54:44]] [SUCCESS] Screenshot refreshed successfully
[[14:54:42]] [INFO] pjFNt3w5Fr=running
[[14:54:42]] [INFO] Executing action 286/576: Restart app: env[appid]
[[14:54:42]] [SUCCESS] Screenshot refreshed
[[14:54:42]] [INFO] Refreshing screenshot...
[[14:54:42]] [SUCCESS] Screenshot refreshed
[[14:54:42]] [INFO] Refreshing screenshot...
[[14:54:38]] [SUCCESS] Screenshot refreshed successfully
[[14:54:38]] [SUCCESS] Screenshot refreshed successfully
[[14:54:38]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:54:38]] [SUCCESS] Screenshot refreshed
[[14:54:38]] [INFO] Refreshing screenshot...
[[14:54:25]] [SUCCESS] Screenshot refreshed successfully
[[14:54:25]] [SUCCESS] Screenshot refreshed successfully
[[14:54:25]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:54:25]] [SUCCESS] Screenshot refreshed
[[14:54:25]] [INFO] Refreshing screenshot...
[[14:54:21]] [SUCCESS] Screenshot refreshed successfully
[[14:54:21]] [SUCCESS] Screenshot refreshed successfully
[[14:54:21]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:54:20]] [SUCCESS] Screenshot refreshed
[[14:54:20]] [INFO] Refreshing screenshot...
[[14:54:16]] [SUCCESS] Screenshot refreshed successfully
[[14:54:16]] [SUCCESS] Screenshot refreshed successfully
[[14:54:16]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:54:15]] [SUCCESS] Screenshot refreshed
[[14:54:15]] [INFO] Refreshing screenshot...
[[14:54:08]] [SUCCESS] Screenshot refreshed successfully
[[14:54:08]] [SUCCESS] Screenshot refreshed successfully
[[14:54:08]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:54:07]] [SUCCESS] Screenshot refreshed
[[14:54:07]] [INFO] Refreshing screenshot...
[[14:54:02]] [SUCCESS] Screenshot refreshed successfully
[[14:54:02]] [SUCCESS] Screenshot refreshed successfully
[[14:54:01]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:54:01]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:54:01]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:54:01]] [INFO] PGvsG6rpU4=running
[[14:54:01]] [INFO] Executing action 285/576: cleanupSteps action
[[14:54:01]] [SUCCESS] Screenshot refreshed
[[14:54:01]] [INFO] Refreshing screenshot...
[[14:54:01]] [INFO] LzGkAcsQyE=pass
[[14:53:58]] [SUCCESS] Screenshot refreshed successfully
[[14:53:58]] [SUCCESS] Screenshot refreshed successfully
[[14:53:57]] [INFO] LzGkAcsQyE=running
[[14:53:57]] [INFO] Executing action 284/576: Terminate app: env[appid]
[[14:53:57]] [SUCCESS] Screenshot refreshed
[[14:53:57]] [INFO] Refreshing screenshot...
[[14:53:57]] [INFO] Bdhe5AoUlM=pass
[[14:53:53]] [SUCCESS] Screenshot refreshed successfully
[[14:53:53]] [SUCCESS] Screenshot refreshed successfully
[[14:53:52]] [INFO] Bdhe5AoUlM=running
[[14:53:52]] [INFO] Executing action 283/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:53:52]] [SUCCESS] Screenshot refreshed
[[14:53:52]] [INFO] Refreshing screenshot...
[[14:53:52]] [INFO] FciJcOsMsB=pass
[[14:53:45]] [SUCCESS] Screenshot refreshed successfully
[[14:53:45]] [SUCCESS] Screenshot refreshed successfully
[[14:53:45]] [INFO] FciJcOsMsB=running
[[14:53:45]] [INFO] Executing action 282/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:53:44]] [SUCCESS] Screenshot refreshed
[[14:53:44]] [INFO] Refreshing screenshot...
[[14:53:44]] [INFO] FARWZvOj0x=pass
[[14:53:41]] [SUCCESS] Screenshot refreshed successfully
[[14:53:41]] [SUCCESS] Screenshot refreshed successfully
[[14:53:40]] [INFO] FARWZvOj0x=running
[[14:53:40]] [INFO] Executing action 281/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:53:40]] [SUCCESS] Screenshot refreshed
[[14:53:40]] [INFO] Refreshing screenshot...
[[14:53:40]] [INFO] bZCkx4U9Gk=pass
[[14:53:34]] [INFO] bZCkx4U9Gk=running
[[14:53:34]] [INFO] Executing action 280/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[14:53:34]] [SUCCESS] Screenshot refreshed successfully
[[14:53:34]] [SUCCESS] Screenshot refreshed successfully
[[14:53:34]] [SUCCESS] Screenshot refreshed
[[14:53:34]] [INFO] Refreshing screenshot...
[[14:53:34]] [INFO] vwFwkK6ydQ=pass
[[14:53:29]] [SUCCESS] Screenshot refreshed successfully
[[14:53:29]] [SUCCESS] Screenshot refreshed successfully
[[14:53:29]] [INFO] vwFwkK6ydQ=running
[[14:53:29]] [INFO] Executing action 279/576: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[14:53:29]] [SUCCESS] Screenshot refreshed
[[14:53:29]] [INFO] Refreshing screenshot...
[[14:53:29]] [INFO] xLGm9FefWE=pass
[[14:53:25]] [SUCCESS] Screenshot refreshed successfully
[[14:53:25]] [SUCCESS] Screenshot refreshed successfully
[[14:53:24]] [INFO] xLGm9FefWE=running
[[14:53:24]] [INFO] Executing action 278/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[14:53:24]] [SUCCESS] Screenshot refreshed
[[14:53:24]] [INFO] Refreshing screenshot...
[[14:53:24]] [INFO] UtVRXwa86e=pass
[[14:53:17]] [SUCCESS] Screenshot refreshed successfully
[[14:53:17]] [SUCCESS] Screenshot refreshed successfully
[[14:53:16]] [INFO] UtVRXwa86e=running
[[14:53:16]] [INFO] Executing action 277/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Sign in with Google"]" is visible
[[14:53:16]] [SUCCESS] Screenshot refreshed
[[14:53:16]] [INFO] Refreshing screenshot...
[[14:53:16]] [INFO] SDtskxyVpg=pass
[[14:53:12]] [SUCCESS] Screenshot refreshed successfully
[[14:53:12]] [SUCCESS] Screenshot refreshed successfully
[[14:53:12]] [INFO] SDtskxyVpg=running
[[14:53:12]] [INFO] Executing action 276/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:53:12]] [SUCCESS] Screenshot refreshed
[[14:53:12]] [INFO] Refreshing screenshot...
[[14:53:12]] [INFO] 6HhScBaqQp=pass
[[14:53:09]] [SUCCESS] Screenshot refreshed successfully
[[14:53:09]] [SUCCESS] Screenshot refreshed successfully
[[14:53:09]] [INFO] 6HhScBaqQp=running
[[14:53:09]] [INFO] Executing action 275/576: iOS Function: alert_accept
[[14:53:08]] [SUCCESS] Screenshot refreshed
[[14:53:08]] [INFO] Refreshing screenshot...
[[14:53:08]] [INFO] quzlwPw42x=pass
[[14:53:03]] [SUCCESS] Screenshot refreshed successfully
[[14:53:03]] [SUCCESS] Screenshot refreshed successfully
[[14:53:02]] [INFO] quzlwPw42x=running
[[14:53:02]] [INFO] Executing action 274/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:53:01]] [SUCCESS] Screenshot refreshed
[[14:53:01]] [INFO] Refreshing screenshot...
[[14:53:01]] [INFO] jQYHQIvQ8l=pass
[[14:52:58]] [SUCCESS] Screenshot refreshed successfully
[[14:52:58]] [SUCCESS] Screenshot refreshed successfully
[[14:52:57]] [INFO] jQYHQIvQ8l=running
[[14:52:57]] [INFO] Executing action 273/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[14:52:57]] [SUCCESS] Screenshot refreshed
[[14:52:57]] [INFO] Refreshing screenshot...
[[14:52:57]] [INFO] ts3qyFxyMf=pass
[[14:52:53]] [SUCCESS] Screenshot refreshed successfully
[[14:52:53]] [SUCCESS] Screenshot refreshed successfully
[[14:52:52]] [INFO] ts3qyFxyMf=running
[[14:52:52]] [INFO] Executing action 272/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:52:52]] [SUCCESS] Screenshot refreshed
[[14:52:52]] [INFO] Refreshing screenshot...
[[14:52:52]] [INFO] FciJcOsMsB=pass
[[14:52:45]] [SUCCESS] Screenshot refreshed successfully
[[14:52:45]] [SUCCESS] Screenshot refreshed successfully
[[14:52:45]] [INFO] FciJcOsMsB=running
[[14:52:45]] [INFO] Executing action 271/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:52:44]] [SUCCESS] Screenshot refreshed
[[14:52:44]] [INFO] Refreshing screenshot...
[[14:52:44]] [INFO] CWkqGp5ndO=pass
[[14:52:41]] [SUCCESS] Screenshot refreshed successfully
[[14:52:41]] [SUCCESS] Screenshot refreshed successfully
[[14:52:40]] [INFO] CWkqGp5ndO=running
[[14:52:40]] [INFO] Executing action 270/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:52:40]] [SUCCESS] Screenshot refreshed
[[14:52:40]] [INFO] Refreshing screenshot...
[[14:52:40]] [INFO] KfMHchi8cx=pass
[[14:52:32]] [INFO] KfMHchi8cx=running
[[14:52:32]] [INFO] Executing action 269/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[14:52:32]] [SUCCESS] Screenshot refreshed successfully
[[14:52:32]] [SUCCESS] Screenshot refreshed successfully
[[14:52:32]] [SUCCESS] Screenshot refreshed
[[14:52:32]] [INFO] Refreshing screenshot...
[[14:52:32]] [INFO] zsVeGHiIgX=pass
[[14:52:29]] [INFO] zsVeGHiIgX=running
[[14:52:29]] [INFO] Executing action 268/576: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[14:52:29]] [SUCCESS] Screenshot refreshed successfully
[[14:52:29]] [SUCCESS] Screenshot refreshed successfully
[[14:52:28]] [SUCCESS] Screenshot refreshed
[[14:52:28]] [INFO] Refreshing screenshot...
[[14:52:28]] [INFO] 5nsUXQ5L7u=pass
[[14:52:25]] [INFO] 5nsUXQ5L7u=running
[[14:52:25]] [INFO] Executing action 267/576: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[14:52:25]] [SUCCESS] Screenshot refreshed successfully
[[14:52:25]] [SUCCESS] Screenshot refreshed successfully
[[14:52:25]] [SUCCESS] Screenshot refreshed
[[14:52:25]] [INFO] Refreshing screenshot...
[[14:52:25]] [INFO] iSckENpXrN=pass
[[14:52:22]] [INFO] iSckENpXrN=running
[[14:52:22]] [INFO] Executing action 266/576: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[14:52:21]] [SUCCESS] Screenshot refreshed successfully
[[14:52:21]] [SUCCESS] Screenshot refreshed successfully
[[14:52:21]] [SUCCESS] Screenshot refreshed
[[14:52:21]] [INFO] Refreshing screenshot...
[[14:52:21]] [INFO] J7BPGVnRJI=pass
[[14:52:18]] [INFO] J7BPGVnRJI=running
[[14:52:18]] [INFO] Executing action 265/576: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[14:52:18]] [SUCCESS] Screenshot refreshed successfully
[[14:52:18]] [SUCCESS] Screenshot refreshed successfully
[[14:52:18]] [SUCCESS] Screenshot refreshed
[[14:52:18]] [INFO] Refreshing screenshot...
[[14:52:18]] [INFO] 0pwZCYAtOv=pass
[[14:52:14]] [INFO] 0pwZCYAtOv=running
[[14:52:14]] [INFO] Executing action 264/576: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[14:52:14]] [SUCCESS] Screenshot refreshed successfully
[[14:52:14]] [SUCCESS] Screenshot refreshed successfully
[[14:52:14]] [SUCCESS] Screenshot refreshed
[[14:52:14]] [INFO] Refreshing screenshot...
[[14:52:14]] [INFO] soKM0KayFJ=pass
[[14:52:11]] [INFO] soKM0KayFJ=running
[[14:52:11]] [INFO] Executing action 263/576: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[14:52:11]] [SUCCESS] Screenshot refreshed successfully
[[14:52:11]] [SUCCESS] Screenshot refreshed successfully
[[14:52:10]] [SUCCESS] Screenshot refreshed
[[14:52:10]] [INFO] Refreshing screenshot...
[[14:52:10]] [INFO] hnH3ayslCh=pass
[[14:52:07]] [INFO] hnH3ayslCh=running
[[14:52:07]] [INFO] Executing action 262/576: Tap on Text: "Passcode"
[[14:52:07]] [SUCCESS] Screenshot refreshed successfully
[[14:52:07]] [SUCCESS] Screenshot refreshed successfully
[[14:52:06]] [SUCCESS] Screenshot refreshed
[[14:52:06]] [INFO] Refreshing screenshot...
[[14:52:06]] [INFO] CzVeOTdAX9=pass
[[14:51:55]] [INFO] CzVeOTdAX9=running
[[14:51:55]] [INFO] Executing action 261/576: Wait for 10 ms
[[14:51:55]] [SUCCESS] Screenshot refreshed successfully
[[14:51:55]] [SUCCESS] Screenshot refreshed successfully
[[14:51:54]] [SUCCESS] Screenshot refreshed
[[14:51:54]] [INFO] Refreshing screenshot...
[[14:51:54]] [INFO] NL2gtj6qIu=pass
[[14:51:50]] [SUCCESS] Screenshot refreshed successfully
[[14:51:50]] [SUCCESS] Screenshot refreshed successfully
[[14:51:49]] [INFO] NL2gtj6qIu=running
[[14:51:49]] [INFO] Executing action 260/576: Tap on Text: "Apple"
[[14:51:49]] [SUCCESS] Screenshot refreshed
[[14:51:49]] [INFO] Refreshing screenshot...
[[14:51:49]] [INFO] VsSlyhXuVD=pass
[[14:51:44]] [SUCCESS] Screenshot refreshed successfully
[[14:51:44]] [SUCCESS] Screenshot refreshed successfully
[[14:51:44]] [INFO] VsSlyhXuVD=running
[[14:51:44]] [INFO] Executing action 259/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:51:44]] [SUCCESS] Screenshot refreshed
[[14:51:44]] [INFO] Refreshing screenshot...
[[14:51:44]] [INFO] CJ88OgjKXp=pass
[[14:51:40]] [SUCCESS] Screenshot refreshed successfully
[[14:51:40]] [SUCCESS] Screenshot refreshed successfully
[[14:51:40]] [INFO] CJ88OgjKXp=running
[[14:51:40]] [INFO] Executing action 258/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:51:39]] [SUCCESS] Screenshot refreshed
[[14:51:39]] [INFO] Refreshing screenshot...
[[14:51:39]] [INFO] AYiwFSLTBD=pass
[[14:51:36]] [SUCCESS] Screenshot refreshed successfully
[[14:51:36]] [SUCCESS] Screenshot refreshed successfully
[[14:51:36]] [INFO] AYiwFSLTBD=running
[[14:51:36]] [INFO] Executing action 257/576: iOS Function: alert_accept
[[14:51:35]] [SUCCESS] Screenshot refreshed
[[14:51:35]] [INFO] Refreshing screenshot...
[[14:51:35]] [INFO] HJzOYZNnGr=pass
[[14:51:30]] [SUCCESS] Screenshot refreshed successfully
[[14:51:30]] [SUCCESS] Screenshot refreshed successfully
[[14:51:29]] [INFO] HJzOYZNnGr=running
[[14:51:29]] [INFO] Executing action 256/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:51:29]] [SUCCESS] Screenshot refreshed
[[14:51:29]] [INFO] Refreshing screenshot...
[[14:51:29]] [INFO] taf19mtrUT=pass
[[14:51:25]] [SUCCESS] Screenshot refreshed successfully
[[14:51:25]] [SUCCESS] Screenshot refreshed successfully
[[14:51:24]] [INFO] taf19mtrUT=running
[[14:51:24]] [INFO] Executing action 255/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[14:51:24]] [SUCCESS] Screenshot refreshed
[[14:51:24]] [INFO] Refreshing screenshot...
[[14:51:24]] [INFO] oiPcknTonJ=pass
[[14:51:20]] [SUCCESS] Screenshot refreshed successfully
[[14:51:20]] [SUCCESS] Screenshot refreshed successfully
[[14:51:19]] [INFO] oiPcknTonJ=running
[[14:51:19]] [INFO] Executing action 254/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:51:19]] [SUCCESS] Screenshot refreshed
[[14:51:19]] [INFO] Refreshing screenshot...
[[14:51:19]] [INFO] FciJcOsMsB=pass
[[14:51:13]] [SUCCESS] Screenshot refreshed successfully
[[14:51:13]] [SUCCESS] Screenshot refreshed successfully
[[14:51:13]] [INFO] FciJcOsMsB=running
[[14:51:13]] [INFO] Executing action 253/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:51:13]] [SUCCESS] Screenshot refreshed
[[14:51:13]] [INFO] Refreshing screenshot...
[[14:51:13]] [INFO] 2qOXZcEmK8=pass
[[14:51:11]] [SUCCESS] Screenshot refreshed successfully
[[14:51:11]] [SUCCESS] Screenshot refreshed successfully
[[14:51:09]] [INFO] 2qOXZcEmK8=running
[[14:51:09]] [INFO] Executing action 252/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:51:08]] [SUCCESS] Screenshot refreshed
[[14:51:08]] [INFO] Refreshing screenshot...
[[14:51:08]] [INFO] M6HdLxu76S=pass
[[14:51:02]] [SUCCESS] Screenshot refreshed successfully
[[14:51:02]] [SUCCESS] Screenshot refreshed successfully
[[14:51:02]] [INFO] M6HdLxu76S=running
[[14:51:02]] [INFO] Executing action 251/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[14:51:01]] [SUCCESS] Screenshot refreshed
[[14:51:01]] [INFO] Refreshing screenshot...
[[14:51:01]] [INFO] pCPTAtSZbf=pass
[[14:50:57]] [SUCCESS] Screenshot refreshed successfully
[[14:50:57]] [SUCCESS] Screenshot refreshed successfully
[[14:50:57]] [INFO] pCPTAtSZbf=running
[[14:50:57]] [INFO] Executing action 250/576: iOS Function: text - Text: "Wonderbaby@5"
[[14:50:56]] [SUCCESS] Screenshot refreshed
[[14:50:56]] [INFO] Refreshing screenshot...
[[14:50:56]] [INFO] DaVBARRwft=pass
[[14:50:52]] [SUCCESS] Screenshot refreshed successfully
[[14:50:52]] [SUCCESS] Screenshot refreshed successfully
[[14:50:52]] [INFO] DaVBARRwft=running
[[14:50:52]] [INFO] Executing action 249/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[14:50:51]] [SUCCESS] Screenshot refreshed
[[14:50:51]] [INFO] Refreshing screenshot...
[[14:50:51]] [INFO] e1RoZWCZJb=pass
[[14:50:46]] [SUCCESS] Screenshot refreshed successfully
[[14:50:46]] [SUCCESS] Screenshot refreshed successfully
[[14:50:45]] [INFO] e1RoZWCZJb=running
[[14:50:45]] [INFO] Executing action 248/576: iOS Function: text - Text: "<EMAIL>"
[[14:50:45]] [SUCCESS] Screenshot refreshed
[[14:50:45]] [INFO] Refreshing screenshot...
[[14:50:45]] [INFO] y8ZMTkG38M=pass
[[14:50:41]] [SUCCESS] Screenshot refreshed successfully
[[14:50:41]] [SUCCESS] Screenshot refreshed successfully
[[14:50:41]] [INFO] y8ZMTkG38M=running
[[14:50:41]] [INFO] Executing action 247/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[14:50:40]] [SUCCESS] Screenshot refreshed
[[14:50:40]] [INFO] Refreshing screenshot...
[[14:50:40]] [INFO] UUhQjmzfO2=pass
[[14:50:35]] [SUCCESS] Screenshot refreshed successfully
[[14:50:35]] [SUCCESS] Screenshot refreshed successfully
[[14:50:35]] [INFO] UUhQjmzfO2=running
[[14:50:35]] [INFO] Executing action 246/576: Tap on Text: "OnePass"
[[14:50:35]] [SUCCESS] Screenshot refreshed
[[14:50:35]] [INFO] Refreshing screenshot...
[[14:50:35]] [INFO] FciJcOsMsB=pass
[[14:50:30]] [SUCCESS] Screenshot refreshed successfully
[[14:50:30]] [SUCCESS] Screenshot refreshed successfully
[[14:50:30]] [INFO] FciJcOsMsB=running
[[14:50:30]] [INFO] Executing action 245/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:50:29]] [SUCCESS] Screenshot refreshed
[[14:50:29]] [INFO] Refreshing screenshot...
[[14:50:29]] [INFO] NCyuT8W5Xz=pass
[[14:50:26]] [SUCCESS] Screenshot refreshed successfully
[[14:50:26]] [SUCCESS] Screenshot refreshed successfully
[[14:50:26]] [INFO] NCyuT8W5Xz=running
[[14:50:26]] [INFO] Executing action 244/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:50:25]] [SUCCESS] Screenshot refreshed
[[14:50:25]] [INFO] Refreshing screenshot...
[[14:50:25]] [INFO] 2kwu2VBmuZ=pass
[[14:50:23]] [SUCCESS] Screenshot refreshed successfully
[[14:50:23]] [SUCCESS] Screenshot refreshed successfully
[[14:50:22]] [INFO] 2kwu2VBmuZ=running
[[14:50:22]] [INFO] Executing action 243/576: iOS Function: alert_accept
[[14:50:22]] [SUCCESS] Screenshot refreshed
[[14:50:22]] [INFO] Refreshing screenshot...
[[14:50:22]] [INFO] cJDpd7aK3d=pass
[[14:50:15]] [SUCCESS] Screenshot refreshed successfully
[[14:50:15]] [SUCCESS] Screenshot refreshed successfully
[[14:50:15]] [INFO] cJDpd7aK3d=running
[[14:50:15]] [INFO] Executing action 242/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:50:14]] [SUCCESS] Screenshot refreshed
[[14:50:14]] [INFO] Refreshing screenshot...
[[14:50:14]] [INFO] FlEukNkjlS=pass
[[14:50:11]] [SUCCESS] Screenshot refreshed successfully
[[14:50:11]] [SUCCESS] Screenshot refreshed successfully
[[14:50:10]] [INFO] FlEukNkjlS=running
[[14:50:10]] [INFO] Executing action 241/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[14:50:10]] [SUCCESS] Screenshot refreshed
[[14:50:10]] [INFO] Refreshing screenshot...
[[14:50:10]] [INFO] LlRfimKPrn=pass
[[14:50:05]] [SUCCESS] Screenshot refreshed successfully
[[14:50:05]] [SUCCESS] Screenshot refreshed successfully
[[14:50:05]] [INFO] LlRfimKPrn=running
[[14:50:05]] [INFO] Executing action 240/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:50:04]] [SUCCESS] Screenshot refreshed
[[14:50:04]] [INFO] Refreshing screenshot...
[[14:50:04]] [INFO] FciJcOsMsB=pass
[[14:49:57]] [SUCCESS] Screenshot refreshed successfully
[[14:49:57]] [SUCCESS] Screenshot refreshed successfully
[[14:49:57]] [INFO] FciJcOsMsB=running
[[14:49:57]] [INFO] Executing action 239/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:49:57]] [SUCCESS] Screenshot refreshed
[[14:49:57]] [INFO] Refreshing screenshot...
[[14:49:57]] [INFO] 08NzsvhQXK=pass
[[14:49:53]] [SUCCESS] Screenshot refreshed successfully
[[14:49:53]] [SUCCESS] Screenshot refreshed successfully
[[14:49:53]] [INFO] 08NzsvhQXK=running
[[14:49:53]] [INFO] Executing action 238/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:49:52]] [SUCCESS] Screenshot refreshed
[[14:49:52]] [INFO] Refreshing screenshot...
[[14:49:52]] [INFO] IsGWxLFpIn=pass
[[14:49:49]] [SUCCESS] Screenshot refreshed successfully
[[14:49:49]] [SUCCESS] Screenshot refreshed successfully
[[14:49:49]] [INFO] IsGWxLFpIn=running
[[14:49:49]] [INFO] Executing action 237/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[14:49:48]] [SUCCESS] Screenshot refreshed
[[14:49:48]] [INFO] Refreshing screenshot...
[[14:49:48]] [INFO] dyECdbRifp=pass
[[14:49:43]] [SUCCESS] Screenshot refreshed successfully
[[14:49:43]] [SUCCESS] Screenshot refreshed successfully
[[14:49:43]] [INFO] dyECdbRifp=running
[[14:49:43]] [INFO] Executing action 236/576: iOS Function: text - Text: "Wonderbaby@5"
[[14:49:43]] [SUCCESS] Screenshot refreshed
[[14:49:43]] [INFO] Refreshing screenshot...
[[14:49:43]] [INFO] I5bRbYY1hD=pass
[[14:49:38]] [SUCCESS] Screenshot refreshed successfully
[[14:49:38]] [SUCCESS] Screenshot refreshed successfully
[[14:49:38]] [INFO] I5bRbYY1hD=running
[[14:49:38]] [INFO] Executing action 235/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:49:37]] [SUCCESS] Screenshot refreshed
[[14:49:37]] [INFO] Refreshing screenshot...
[[14:49:37]] [INFO] WMl5g82CCq=pass
[[14:49:32]] [INFO] WMl5g82CCq=running
[[14:49:32]] [INFO] Executing action 234/576: iOS Function: text - Text: "<EMAIL>"
[[14:49:32]] [SUCCESS] Screenshot refreshed successfully
[[14:49:32]] [SUCCESS] Screenshot refreshed successfully
[[14:49:32]] [SUCCESS] Screenshot refreshed
[[14:49:32]] [INFO] Refreshing screenshot...
[[14:49:32]] [INFO] 8OsQmoVYqW=pass
[[14:49:28]] [SUCCESS] Screenshot refreshed successfully
[[14:49:28]] [SUCCESS] Screenshot refreshed successfully
[[14:49:28]] [INFO] 8OsQmoVYqW=running
[[14:49:28]] [INFO] Executing action 233/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:49:27]] [SUCCESS] Screenshot refreshed
[[14:49:27]] [INFO] Refreshing screenshot...
[[14:49:27]] [INFO] ImienLpJEN=pass
[[14:49:24]] [SUCCESS] Screenshot refreshed successfully
[[14:49:24]] [SUCCESS] Screenshot refreshed successfully
[[14:49:23]] [INFO] ImienLpJEN=running
[[14:49:23]] [INFO] Executing action 232/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:49:23]] [SUCCESS] Screenshot refreshed
[[14:49:23]] [INFO] Refreshing screenshot...
[[14:49:23]] [INFO] q4hPXCBtx4=pass
[[14:49:21]] [SUCCESS] Screenshot refreshed successfully
[[14:49:21]] [SUCCESS] Screenshot refreshed successfully
[[14:49:20]] [INFO] q4hPXCBtx4=running
[[14:49:20]] [INFO] Executing action 231/576: iOS Function: alert_accept
[[14:49:20]] [SUCCESS] Screenshot refreshed
[[14:49:20]] [INFO] Refreshing screenshot...
[[14:49:20]] [INFO] 2cTZvK1psn=pass
[[14:49:13]] [SUCCESS] Screenshot refreshed successfully
[[14:49:13]] [SUCCESS] Screenshot refreshed successfully
[[14:49:12]] [INFO] 2cTZvK1psn=running
[[14:49:12]] [INFO] Executing action 230/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:49:12]] [SUCCESS] Screenshot refreshed
[[14:49:12]] [INFO] Refreshing screenshot...
[[14:49:12]] [INFO] Vxt7QOYeDD=pass
[[14:48:59]] [SUCCESS] Screenshot refreshed successfully
[[14:48:59]] [SUCCESS] Screenshot refreshed successfully
[[14:48:58]] [INFO] Vxt7QOYeDD=running
[[14:48:58]] [INFO] Executing action 229/576: Restart app: env[appid]
[[14:48:58]] [SUCCESS] Screenshot refreshed
[[14:48:58]] [INFO] Refreshing screenshot...
[[14:48:57]] [SUCCESS] Screenshot refreshed
[[14:48:57]] [INFO] Refreshing screenshot...
[[14:48:54]] [SUCCESS] Screenshot refreshed successfully
[[14:48:54]] [SUCCESS] Screenshot refreshed successfully
[[14:48:54]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:48:53]] [SUCCESS] Screenshot refreshed
[[14:48:53]] [INFO] Refreshing screenshot...
[[14:48:41]] [SUCCESS] Screenshot refreshed successfully
[[14:48:41]] [SUCCESS] Screenshot refreshed successfully
[[14:48:41]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:48:40]] [SUCCESS] Screenshot refreshed
[[14:48:40]] [INFO] Refreshing screenshot...
[[14:48:36]] [SUCCESS] Screenshot refreshed successfully
[[14:48:36]] [SUCCESS] Screenshot refreshed successfully
[[14:48:36]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:48:36]] [SUCCESS] Screenshot refreshed
[[14:48:36]] [INFO] Refreshing screenshot...
[[14:48:32]] [SUCCESS] Screenshot refreshed successfully
[[14:48:32]] [SUCCESS] Screenshot refreshed successfully
[[14:48:31]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:48:31]] [SUCCESS] Screenshot refreshed
[[14:48:31]] [INFO] Refreshing screenshot...
[[14:48:24]] [SUCCESS] Screenshot refreshed successfully
[[14:48:24]] [SUCCESS] Screenshot refreshed successfully
[[14:48:24]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:48:23]] [SUCCESS] Screenshot refreshed
[[14:48:23]] [INFO] Refreshing screenshot...
[[14:48:17]] [SUCCESS] Screenshot refreshed successfully
[[14:48:17]] [SUCCESS] Screenshot refreshed successfully
[[14:48:16]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:48:16]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:48:16]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:48:16]] [INFO] DYWpUY7xB6=running
[[14:48:16]] [INFO] Executing action 228/576: cleanupSteps action
[[14:48:16]] [SUCCESS] Screenshot refreshed
[[14:48:16]] [INFO] Refreshing screenshot...
[[14:48:16]] [INFO] OyUowAaBzD=pass
[[14:48:11]] [SUCCESS] Screenshot refreshed successfully
[[14:48:11]] [SUCCESS] Screenshot refreshed successfully
[[14:48:11]] [INFO] OyUowAaBzD=running
[[14:48:11]] [INFO] Executing action 227/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:48:11]] [SUCCESS] Screenshot refreshed
[[14:48:11]] [INFO] Refreshing screenshot...
[[14:48:11]] [INFO] Ob26qqcA0p=pass
[[14:48:04]] [SUCCESS] Screenshot refreshed successfully
[[14:48:04]] [SUCCESS] Screenshot refreshed successfully
[[14:48:04]] [INFO] Ob26qqcA0p=running
[[14:48:04]] [INFO] Executing action 226/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:48:03]] [SUCCESS] Screenshot refreshed
[[14:48:03]] [INFO] Refreshing screenshot...
[[14:48:03]] [INFO] k3mu9Mt7Ec=pass
[[14:47:59]] [SUCCESS] Screenshot refreshed successfully
[[14:47:59]] [SUCCESS] Screenshot refreshed successfully
[[14:47:59]] [INFO] k3mu9Mt7Ec=running
[[14:47:59]] [INFO] Executing action 225/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:47:59]] [SUCCESS] Screenshot refreshed
[[14:47:59]] [INFO] Refreshing screenshot...
[[14:47:59]] [INFO] yhmzeynQyu=pass
[[14:47:55]] [SUCCESS] Screenshot refreshed successfully
[[14:47:55]] [SUCCESS] Screenshot refreshed successfully
[[14:47:54]] [INFO] yhmzeynQyu=running
[[14:47:54]] [INFO] Executing action 224/576: Tap on Text: "Remove"
[[14:47:54]] [SUCCESS] Screenshot refreshed
[[14:47:54]] [INFO] Refreshing screenshot...
[[14:47:54]] [INFO] Q0fomJIDoQ=pass
[[14:47:49]] [SUCCESS] Screenshot refreshed successfully
[[14:47:49]] [SUCCESS] Screenshot refreshed successfully
[[14:47:49]] [INFO] Q0fomJIDoQ=running
[[14:47:49]] [INFO] Executing action 223/576: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[14:47:48]] [SUCCESS] Screenshot refreshed
[[14:47:48]] [INFO] Refreshing screenshot...
[[14:47:48]] [INFO] yhmzeynQyu=pass
[[14:47:44]] [SUCCESS] Screenshot refreshed successfully
[[14:47:44]] [SUCCESS] Screenshot refreshed successfully
[[14:47:44]] [INFO] yhmzeynQyu=running
[[14:47:44]] [INFO] Executing action 222/576: Tap on Text: "Remove"
[[14:47:43]] [SUCCESS] Screenshot refreshed
[[14:47:43]] [INFO] Refreshing screenshot...
[[14:47:43]] [INFO] Q0fomJIDoQ=pass
[[14:47:37]] [SUCCESS] Screenshot refreshed successfully
[[14:47:37]] [SUCCESS] Screenshot refreshed successfully
[[14:47:37]] [INFO] Q0fomJIDoQ=running
[[14:47:37]] [INFO] Executing action 221/576: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[14:47:37]] [SUCCESS] Screenshot refreshed
[[14:47:37]] [INFO] Refreshing screenshot...
[[14:47:37]] [INFO] F1olhgKhUt=pass
[[14:47:33]] [SUCCESS] Screenshot refreshed successfully
[[14:47:33]] [SUCCESS] Screenshot refreshed successfully
[[14:47:32]] [INFO] F1olhgKhUt=running
[[14:47:32]] [INFO] Executing action 220/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[14:47:32]] [SUCCESS] Screenshot refreshed
[[14:47:32]] [INFO] Refreshing screenshot...
[[14:47:32]] [INFO] 8umPSX0vrr=pass
[[14:47:27]] [INFO] 8umPSX0vrr=running
[[14:47:27]] [INFO] Executing action 219/576: Tap on image: banner-close-updated.png
[[14:47:27]] [SUCCESS] Screenshot refreshed successfully
[[14:47:27]] [SUCCESS] Screenshot refreshed successfully
[[14:47:27]] [SUCCESS] Screenshot refreshed
[[14:47:27]] [INFO] Refreshing screenshot...
[[14:47:27]] [INFO] pr9o8Zsm5p=pass
[[14:47:23]] [SUCCESS] Screenshot refreshed successfully
[[14:47:23]] [SUCCESS] Screenshot refreshed successfully
[[14:47:23]] [INFO] pr9o8Zsm5p=running
[[14:47:23]] [INFO] Executing action 218/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[14:47:22]] [SUCCESS] Screenshot refreshed
[[14:47:22]] [INFO] Refreshing screenshot...
[[14:47:22]] [INFO] Qbg9bipTGs=pass
[[14:47:19]] [SUCCESS] Screenshot refreshed successfully
[[14:47:19]] [SUCCESS] Screenshot refreshed successfully
[[14:47:19]] [INFO] Qbg9bipTGs=running
[[14:47:19]] [INFO] Executing action 217/576: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[14:47:18]] [SUCCESS] Screenshot refreshed
[[14:47:18]] [INFO] Refreshing screenshot...
[[14:47:18]] [INFO] Ob26qqcA0p=pass
[[14:47:13]] [SUCCESS] Screenshot refreshed successfully
[[14:47:13]] [SUCCESS] Screenshot refreshed successfully
[[14:47:13]] [INFO] Ob26qqcA0p=running
[[14:47:13]] [INFO] Executing action 216/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:47:13]] [SUCCESS] Screenshot refreshed
[[14:47:13]] [INFO] Refreshing screenshot...
[[14:47:13]] [INFO] lWIRxRm6HE=pass
[[14:47:08]] [SUCCESS] Screenshot refreshed successfully
[[14:47:08]] [SUCCESS] Screenshot refreshed successfully
[[14:47:07]] [INFO] lWIRxRm6HE=running
[[14:47:07]] [INFO] Executing action 215/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:47:07]] [SUCCESS] Screenshot refreshed
[[14:47:07]] [INFO] Refreshing screenshot...
[[14:47:07]] [INFO] uOt2cFGhGr=pass
[[14:46:53]] [INFO] uOt2cFGhGr=running
[[14:46:53]] [INFO] Executing action 214/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[14:46:53]] [SUCCESS] Screenshot refreshed successfully
[[14:46:53]] [SUCCESS] Screenshot refreshed successfully
[[14:46:52]] [SUCCESS] Screenshot refreshed
[[14:46:52]] [INFO] Refreshing screenshot...
[[14:46:52]] [INFO] Q0fomJIDoQ=pass
[[14:46:48]] [SUCCESS] Screenshot refreshed successfully
[[14:46:48]] [SUCCESS] Screenshot refreshed successfully
[[14:46:48]] [INFO] Q0fomJIDoQ=running
[[14:46:48]] [INFO] Executing action 213/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[14:46:48]] [SUCCESS] Screenshot refreshed
[[14:46:48]] [INFO] Refreshing screenshot...
[[14:46:48]] [INFO] yhmzeynQyu=pass
[[14:46:43]] [SUCCESS] Screenshot refreshed successfully
[[14:46:43]] [SUCCESS] Screenshot refreshed successfully
[[14:46:43]] [INFO] yhmzeynQyu=running
[[14:46:43]] [INFO] Executing action 212/576: Tap on Text: "Remove"
[[14:46:42]] [SUCCESS] Screenshot refreshed
[[14:46:42]] [INFO] Refreshing screenshot...
[[14:46:42]] [INFO] Q0fomJIDoQ=pass
[[14:46:38]] [SUCCESS] Screenshot refreshed successfully
[[14:46:38]] [SUCCESS] Screenshot refreshed successfully
[[14:46:38]] [INFO] Q0fomJIDoQ=running
[[14:46:38]] [INFO] Executing action 211/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[14:46:38]] [SUCCESS] Screenshot refreshed
[[14:46:38]] [INFO] Refreshing screenshot...
[[14:46:38]] [INFO] y4i304JeJj=pass
[[14:46:33]] [SUCCESS] Screenshot refreshed successfully
[[14:46:33]] [SUCCESS] Screenshot refreshed successfully
[[14:46:33]] [INFO] y4i304JeJj=running
[[14:46:33]] [INFO] Executing action 210/576: Tap on Text: "Move"
[[14:46:32]] [SUCCESS] Screenshot refreshed
[[14:46:32]] [INFO] Refreshing screenshot...
[[14:46:32]] [INFO] Q0fomJIDoQ=pass
[[14:46:28]] [SUCCESS] Screenshot refreshed successfully
[[14:46:28]] [SUCCESS] Screenshot refreshed successfully
[[14:46:28]] [INFO] Q0fomJIDoQ=running
[[14:46:28]] [INFO] Executing action 209/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[14:46:28]] [SUCCESS] Screenshot refreshed
[[14:46:28]] [INFO] Refreshing screenshot...
[[14:46:28]] [INFO] Q0fomJIDoQ=pass
[[14:46:25]] [SUCCESS] Screenshot refreshed successfully
[[14:46:25]] [SUCCESS] Screenshot refreshed successfully
[[14:46:25]] [INFO] Q0fomJIDoQ=running
[[14:46:25]] [INFO] Executing action 208/576: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[14:46:24]] [SUCCESS] Screenshot refreshed
[[14:46:24]] [INFO] Refreshing screenshot...
[[14:46:24]] [INFO] F1olhgKhUt=pass
[[14:46:20]] [SUCCESS] Screenshot refreshed successfully
[[14:46:20]] [SUCCESS] Screenshot refreshed successfully
[[14:46:20]] [INFO] F1olhgKhUt=running
[[14:46:20]] [INFO] Executing action 207/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[14:46:19]] [SUCCESS] Screenshot refreshed
[[14:46:19]] [INFO] Refreshing screenshot...
[[14:46:19]] [INFO] WbxRVpWtjw=pass
[[14:46:15]] [SUCCESS] Screenshot refreshed successfully
[[14:46:15]] [SUCCESS] Screenshot refreshed successfully
[[14:46:15]] [INFO] WbxRVpWtjw=running
[[14:46:15]] [INFO] Executing action 206/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[14:46:14]] [SUCCESS] Screenshot refreshed
[[14:46:14]] [INFO] Refreshing screenshot...
[[14:46:14]] [INFO] H3IAmq3r3i=pass
[[14:46:07]] [SUCCESS] Screenshot refreshed successfully
[[14:46:07]] [SUCCESS] Screenshot refreshed successfully
[[14:46:06]] [INFO] H3IAmq3r3i=running
[[14:46:06]] [INFO] Executing action 205/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[14:46:06]] [SUCCESS] Screenshot refreshed
[[14:46:06]] [INFO] Refreshing screenshot...
[[14:46:06]] [INFO] uOt2cFGhGr=pass
[[14:46:02]] [SUCCESS] Screenshot refreshed successfully
[[14:46:02]] [SUCCESS] Screenshot refreshed successfully
[[14:46:02]] [INFO] uOt2cFGhGr=running
[[14:46:02]] [INFO] Executing action 204/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[14:46:01]] [SUCCESS] Screenshot refreshed
[[14:46:01]] [INFO] Refreshing screenshot...
[[14:46:01]] [INFO] eLxHVWKeDQ=pass
[[14:45:57]] [SUCCESS] Screenshot refreshed successfully
[[14:45:57]] [SUCCESS] Screenshot refreshed successfully
[[14:45:57]] [INFO] eLxHVWKeDQ=running
[[14:45:57]] [INFO] Executing action 203/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:45:57]] [SUCCESS] Screenshot refreshed
[[14:45:57]] [INFO] Refreshing screenshot...
[[14:45:57]] [INFO] ghzdMuwrHj=pass
[[14:45:52]] [SUCCESS] Screenshot refreshed successfully
[[14:45:52]] [SUCCESS] Screenshot refreshed successfully
[[14:45:52]] [INFO] ghzdMuwrHj=running
[[14:45:52]] [INFO] Executing action 202/576: iOS Function: text - Text: "P_43386093"
[[14:45:52]] [SUCCESS] Screenshot refreshed
[[14:45:52]] [INFO] Refreshing screenshot...
[[14:45:52]] [INFO] fMzoZJg9I7=pass
[[14:45:47]] [SUCCESS] Screenshot refreshed successfully
[[14:45:47]] [SUCCESS] Screenshot refreshed successfully
[[14:45:46]] [INFO] fMzoZJg9I7=running
[[14:45:46]] [INFO] Executing action 201/576: Tap on Text: "Find"
[[14:45:46]] [SUCCESS] Screenshot refreshed
[[14:45:46]] [INFO] Refreshing screenshot...
[[14:45:46]] [INFO] j1JjmfPRaE=pass
[[14:45:41]] [SUCCESS] Screenshot refreshed successfully
[[14:45:41]] [SUCCESS] Screenshot refreshed successfully
[[14:45:40]] [INFO] j1JjmfPRaE=running
[[14:45:40]] [INFO] Executing action 200/576: Restart app: env[appid]
[[14:45:40]] [SUCCESS] Screenshot refreshed
[[14:45:40]] [INFO] Refreshing screenshot...
[[14:45:40]] [INFO] WbxRVpWtjw=pass
[[14:45:35]] [SUCCESS] Screenshot refreshed successfully
[[14:45:35]] [SUCCESS] Screenshot refreshed successfully
[[14:45:35]] [INFO] WbxRVpWtjw=running
[[14:45:35]] [INFO] Executing action 199/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[14:45:34]] [SUCCESS] Screenshot refreshed
[[14:45:34]] [INFO] Refreshing screenshot...
[[14:45:34]] [INFO] H3IAmq3r3i=pass
[[14:45:28]] [SUCCESS] Screenshot refreshed successfully
[[14:45:28]] [SUCCESS] Screenshot refreshed successfully
[[14:45:27]] [INFO] H3IAmq3r3i=running
[[14:45:27]] [INFO] Executing action 198/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[14:45:26]] [SUCCESS] Screenshot refreshed
[[14:45:26]] [INFO] Refreshing screenshot...
[[14:45:26]] [INFO] ITHvSyXXmu=pass
[[14:45:23]] [SUCCESS] Screenshot refreshed successfully
[[14:45:23]] [SUCCESS] Screenshot refreshed successfully
[[14:45:22]] [INFO] ITHvSyXXmu=running
[[14:45:22]] [INFO] Executing action 197/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[14:45:21]] [SUCCESS] Screenshot refreshed
[[14:45:21]] [INFO] Refreshing screenshot...
[[14:45:21]] [INFO] eLxHVWKeDQ=pass
[[14:45:05]] [SUCCESS] Screenshot refreshed successfully
[[14:45:05]] [SUCCESS] Screenshot refreshed successfully
[[14:45:04]] [INFO] eLxHVWKeDQ=running
[[14:45:04]] [INFO] Executing action 196/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[14:45:04]] [SUCCESS] Screenshot refreshed
[[14:45:04]] [INFO] Refreshing screenshot...
[[14:45:04]] [INFO] WbxRVpWtjw=pass
[[14:45:00]] [SUCCESS] Screenshot refreshed successfully
[[14:45:00]] [SUCCESS] Screenshot refreshed successfully
[[14:44:59]] [INFO] WbxRVpWtjw=running
[[14:44:59]] [INFO] Executing action 195/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[14:44:59]] [SUCCESS] Screenshot refreshed
[[14:44:59]] [INFO] Refreshing screenshot...
[[14:44:59]] [INFO] H3IAmq3r3i=pass
[[14:44:52]] [SUCCESS] Screenshot refreshed successfully
[[14:44:52]] [SUCCESS] Screenshot refreshed successfully
[[14:44:51]] [INFO] H3IAmq3r3i=running
[[14:44:51]] [INFO] Executing action 194/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[14:44:51]] [SUCCESS] Screenshot refreshed
[[14:44:51]] [INFO] Refreshing screenshot...
[[14:44:51]] [INFO] ITHvSyXXmu=pass
[[14:44:46]] [SUCCESS] Screenshot refreshed successfully
[[14:44:46]] [SUCCESS] Screenshot refreshed successfully
[[14:44:45]] [INFO] ITHvSyXXmu=running
[[14:44:45]] [INFO] Executing action 193/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[14:44:45]] [SUCCESS] Screenshot refreshed
[[14:44:45]] [INFO] Refreshing screenshot...
[[14:44:45]] [INFO] eLxHVWKeDQ=pass
[[14:44:41]] [SUCCESS] Screenshot refreshed successfully
[[14:44:41]] [SUCCESS] Screenshot refreshed successfully
[[14:44:40]] [INFO] eLxHVWKeDQ=running
[[14:44:40]] [INFO] Executing action 192/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:44:40]] [SUCCESS] Screenshot refreshed
[[14:44:40]] [INFO] Refreshing screenshot...
[[14:44:40]] [INFO] nAB6Q8LAdv=pass
[[14:44:36]] [SUCCESS] Screenshot refreshed successfully
[[14:44:36]] [SUCCESS] Screenshot refreshed successfully
[[14:44:35]] [INFO] nAB6Q8LAdv=running
[[14:44:35]] [INFO] Executing action 191/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:44:35]] [SUCCESS] Screenshot refreshed
[[14:44:35]] [INFO] Refreshing screenshot...
[[14:44:35]] [INFO] sc2KH9bG6H=pass
[[14:44:30]] [SUCCESS] Screenshot refreshed successfully
[[14:44:30]] [SUCCESS] Screenshot refreshed successfully
[[14:44:30]] [INFO] sc2KH9bG6H=running
[[14:44:30]] [INFO] Executing action 190/576: iOS Function: text - Text: "Uno card"
[[14:44:30]] [SUCCESS] Screenshot refreshed
[[14:44:30]] [INFO] Refreshing screenshot...
[[14:44:30]] [INFO] rqLJpAP0mA=pass
[[14:44:25]] [SUCCESS] Screenshot refreshed successfully
[[14:44:25]] [SUCCESS] Screenshot refreshed successfully
[[14:44:24]] [INFO] rqLJpAP0mA=running
[[14:44:24]] [INFO] Executing action 189/576: Tap on Text: "Find"
[[14:44:24]] [SUCCESS] Screenshot refreshed
[[14:44:24]] [INFO] Refreshing screenshot...
[[14:44:24]] [INFO] yiKyF5FJwN=pass
[[14:44:21]] [SUCCESS] Screenshot refreshed successfully
[[14:44:21]] [SUCCESS] Screenshot refreshed successfully
[[14:44:20]] [INFO] yiKyF5FJwN=running
[[14:44:20]] [INFO] Executing action 188/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[14:44:20]] [SUCCESS] Screenshot refreshed
[[14:44:20]] [INFO] Refreshing screenshot...
[[14:44:20]] [INFO] YqMEb5Jr6o=pass
[[14:44:15]] [SUCCESS] Screenshot refreshed successfully
[[14:44:15]] [SUCCESS] Screenshot refreshed successfully
[[14:44:15]] [INFO] YqMEb5Jr6o=running
[[14:44:15]] [INFO] Executing action 187/576: iOS Function: text - Text: "Wonderbaby@6"
[[14:44:14]] [SUCCESS] Screenshot refreshed
[[14:44:14]] [INFO] Refreshing screenshot...
[[14:44:14]] [INFO] T3MmUw30SF=pass
[[14:44:10]] [SUCCESS] Screenshot refreshed successfully
[[14:44:10]] [SUCCESS] Screenshot refreshed successfully
[[14:44:10]] [INFO] T3MmUw30SF=running
[[14:44:10]] [INFO] Executing action 186/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:44:09]] [SUCCESS] Screenshot refreshed
[[14:44:09]] [INFO] Refreshing screenshot...
[[14:44:09]] [INFO] 3FBGGKUMbh=pass
[[14:44:05]] [SUCCESS] Screenshot refreshed successfully
[[14:44:05]] [SUCCESS] Screenshot refreshed successfully
[[14:44:05]] [INFO] 3FBGGKUMbh=running
[[14:44:05]] [INFO] Executing action 185/576: iOS Function: text - Text: "env[uname-op]"
[[14:44:04]] [SUCCESS] Screenshot refreshed
[[14:44:04]] [INFO] Refreshing screenshot...
[[14:44:04]] [INFO] LDkFLWks00=pass
[[14:44:00]] [SUCCESS] Screenshot refreshed successfully
[[14:44:00]] [SUCCESS] Screenshot refreshed successfully
[[14:44:00]] [INFO] LDkFLWks00=running
[[14:44:00]] [INFO] Executing action 184/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:43:59]] [SUCCESS] Screenshot refreshed
[[14:43:59]] [INFO] Refreshing screenshot...
[[14:43:59]] [INFO] 3caMBvQX7k=pass
[[14:43:56]] [SUCCESS] Screenshot refreshed successfully
[[14:43:56]] [SUCCESS] Screenshot refreshed successfully
[[14:43:56]] [INFO] 3caMBvQX7k=running
[[14:43:56]] [INFO] Executing action 183/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:43:55]] [SUCCESS] Screenshot refreshed
[[14:43:55]] [INFO] Refreshing screenshot...
[[14:43:55]] [INFO] yUJyVO5Wev=pass
[[14:43:53]] [SUCCESS] Screenshot refreshed successfully
[[14:43:53]] [SUCCESS] Screenshot refreshed successfully
[[14:43:52]] [INFO] yUJyVO5Wev=running
[[14:43:52]] [INFO] Executing action 182/576: iOS Function: alert_accept
[[14:43:52]] [SUCCESS] Screenshot refreshed
[[14:43:52]] [INFO] Refreshing screenshot...
[[14:43:52]] [INFO] rkL0oz4kiL=pass
[[14:43:44]] [SUCCESS] Screenshot refreshed successfully
[[14:43:44]] [SUCCESS] Screenshot refreshed successfully
[[14:43:44]] [INFO] rkL0oz4kiL=running
[[14:43:44]] [INFO] Executing action 181/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:43:43]] [SUCCESS] Screenshot refreshed
[[14:43:43]] [INFO] Refreshing screenshot...
[[14:43:43]] [INFO] HotUJOd6oB=pass
[[14:43:30]] [SUCCESS] Screenshot refreshed successfully
[[14:43:30]] [SUCCESS] Screenshot refreshed successfully
[[14:43:29]] [INFO] HotUJOd6oB=running
[[14:43:29]] [INFO] Executing action 180/576: Restart app: env[appid]
[[14:43:29]] [SUCCESS] Screenshot refreshed
[[14:43:29]] [INFO] Refreshing screenshot...
[[14:43:28]] [SUCCESS] Screenshot refreshed
[[14:43:28]] [INFO] Refreshing screenshot...
[[14:43:25]] [SUCCESS] Screenshot refreshed successfully
[[14:43:25]] [SUCCESS] Screenshot refreshed successfully
[[14:43:25]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:43:25]] [SUCCESS] Screenshot refreshed
[[14:43:25]] [INFO] Refreshing screenshot...
[[14:43:13]] [SUCCESS] Screenshot refreshed successfully
[[14:43:13]] [SUCCESS] Screenshot refreshed successfully
[[14:43:13]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:43:12]] [SUCCESS] Screenshot refreshed
[[14:43:12]] [INFO] Refreshing screenshot...
[[14:43:08]] [SUCCESS] Screenshot refreshed successfully
[[14:43:08]] [SUCCESS] Screenshot refreshed successfully
[[14:43:08]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:43:08]] [SUCCESS] Screenshot refreshed
[[14:43:08]] [INFO] Refreshing screenshot...
[[14:43:04]] [SUCCESS] Screenshot refreshed successfully
[[14:43:04]] [SUCCESS] Screenshot refreshed successfully
[[14:43:03]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:43:03]] [SUCCESS] Screenshot refreshed
[[14:43:03]] [INFO] Refreshing screenshot...
[[14:42:56]] [SUCCESS] Screenshot refreshed successfully
[[14:42:56]] [SUCCESS] Screenshot refreshed successfully
[[14:42:56]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:42:55]] [SUCCESS] Screenshot refreshed
[[14:42:55]] [INFO] Refreshing screenshot...
[[14:42:48]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:42:48]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:42:48]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:42:48]] [INFO] IR7wnjW7C8=running
[[14:42:48]] [INFO] Executing action 179/576: cleanupSteps action
[[14:42:48]] [INFO] Skipping remaining steps in failed test case (moving from action 115 to 178), but preserving cleanup steps
[[14:42:48]] [INFO] 2YGctqXNED=fail
[[14:42:48]] [ERROR] Action 115 failed: Element not visible after 4 swipe(s)
[[14:41:51]] [SUCCESS] Screenshot refreshed successfully
[[14:41:51]] [SUCCESS] Screenshot refreshed successfully
[[14:41:51]] [INFO] 2YGctqXNED=running
[[14:41:51]] [INFO] Executing action 115/576: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[14:41:50]] [SUCCESS] Screenshot refreshed
[[14:41:50]] [INFO] Refreshing screenshot...
[[14:41:50]] [INFO] 6zUBxjSFym=pass
[[14:41:45]] [SUCCESS] Screenshot refreshed successfully
[[14:41:45]] [SUCCESS] Screenshot refreshed successfully
[[14:41:45]] [INFO] 6zUBxjSFym=running
[[14:41:45]] [INFO] Executing action 114/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[14:41:44]] [SUCCESS] Screenshot refreshed
[[14:41:44]] [INFO] Refreshing screenshot...
[[14:41:44]] [INFO] BTYxjEaZEk=pass
[[14:41:41]] [SUCCESS] Screenshot refreshed successfully
[[14:41:41]] [SUCCESS] Screenshot refreshed successfully
[[14:41:40]] [INFO] BTYxjEaZEk=running
[[14:41:40]] [INFO] Executing action 113/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:41:40]] [SUCCESS] Screenshot refreshed
[[14:41:40]] [INFO] Refreshing screenshot...
[[14:41:40]] [INFO] YC6bBrKQgq=pass
[[14:41:36]] [SUCCESS] Screenshot refreshed successfully
[[14:41:36]] [SUCCESS] Screenshot refreshed successfully
[[14:41:35]] [INFO] YC6bBrKQgq=running
[[14:41:35]] [INFO] Executing action 112/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:41:35]] [SUCCESS] Screenshot refreshed
[[14:41:35]] [INFO] Refreshing screenshot...
[[14:41:35]] [INFO] aRgHcQcLDP=pass
[[14:41:31]] [SUCCESS] Screenshot refreshed successfully
[[14:41:31]] [SUCCESS] Screenshot refreshed successfully
[[14:41:31]] [INFO] aRgHcQcLDP=running
[[14:41:31]] [INFO] Executing action 111/576: iOS Function: text - Text: "uno card"
[[14:41:30]] [SUCCESS] Screenshot refreshed
[[14:41:30]] [INFO] Refreshing screenshot...
[[14:41:30]] [INFO] 4PZC1vVWJW=pass
[[14:41:25]] [SUCCESS] Screenshot refreshed successfully
[[14:41:25]] [SUCCESS] Screenshot refreshed successfully
[[14:41:25]] [INFO] 4PZC1vVWJW=running
[[14:41:25]] [INFO] Executing action 110/576: Tap on Text: "Find"
[[14:41:24]] [SUCCESS] Screenshot refreshed
[[14:41:24]] [INFO] Refreshing screenshot...
[[14:41:24]] [INFO] lCSewtjn1z=pass
[[14:41:19]] [SUCCESS] Screenshot refreshed successfully
[[14:41:19]] [SUCCESS] Screenshot refreshed successfully
[[14:41:19]] [INFO] lCSewtjn1z=running
[[14:41:19]] [INFO] Executing action 109/576: Restart app: env[appid]
[[14:41:18]] [SUCCESS] Screenshot refreshed
[[14:41:18]] [INFO] Refreshing screenshot...
[[14:41:18]] [INFO] A1Wz7p1iVG=pass
[[14:41:14]] [SUCCESS] Screenshot refreshed successfully
[[14:41:14]] [SUCCESS] Screenshot refreshed successfully
[[14:41:14]] [INFO] A1Wz7p1iVG=running
[[14:41:14]] [INFO] Executing action 108/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:41:13]] [SUCCESS] Screenshot refreshed
[[14:41:13]] [INFO] Refreshing screenshot...
[[14:41:13]] [INFO] ehyLmdZWP2=pass
[[14:41:06]] [SUCCESS] Screenshot refreshed successfully
[[14:41:06]] [SUCCESS] Screenshot refreshed successfully
[[14:41:06]] [INFO] ehyLmdZWP2=running
[[14:41:06]] [INFO] Executing action 107/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:41:05]] [SUCCESS] Screenshot refreshed
[[14:41:05]] [INFO] Refreshing screenshot...
[[14:41:05]] [INFO] ydRnBBO1vR=pass
[[14:41:02]] [SUCCESS] Screenshot refreshed successfully
[[14:41:02]] [SUCCESS] Screenshot refreshed successfully
[[14:41:01]] [INFO] ydRnBBO1vR=running
[[14:41:01]] [INFO] Executing action 106/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:41:01]] [SUCCESS] Screenshot refreshed
[[14:41:01]] [INFO] Refreshing screenshot...
[[14:41:01]] [INFO] quZwUwj3a8=pass
[[14:40:57]] [SUCCESS] Screenshot refreshed successfully
[[14:40:57]] [SUCCESS] Screenshot refreshed successfully
[[14:40:57]] [INFO] quZwUwj3a8=running
[[14:40:57]] [INFO] Executing action 105/576: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[14:40:56]] [SUCCESS] Screenshot refreshed
[[14:40:56]] [INFO] Refreshing screenshot...
[[14:40:56]] [INFO] FHRlQXe58T=pass
[[14:40:53]] [SUCCESS] Screenshot refreshed successfully
[[14:40:53]] [SUCCESS] Screenshot refreshed successfully
[[14:40:52]] [INFO] FHRlQXe58T=running
[[14:40:52]] [INFO] Executing action 104/576: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[14:40:51]] [SUCCESS] Screenshot refreshed
[[14:40:51]] [INFO] Refreshing screenshot...
[[14:40:51]] [INFO] FHRlQXe58T=pass
[[14:40:47]] [SUCCESS] Screenshot refreshed successfully
[[14:40:47]] [SUCCESS] Screenshot refreshed successfully
[[14:40:47]] [INFO] FHRlQXe58T=running
[[14:40:47]] [INFO] Executing action 103/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtStart Shopping"]
[[14:40:46]] [SUCCESS] Screenshot refreshed
[[14:40:46]] [INFO] Refreshing screenshot...
[[14:40:46]] [INFO] N9sXy9WltY=pass
[[14:40:43]] [SUCCESS] Screenshot refreshed successfully
[[14:40:43]] [SUCCESS] Screenshot refreshed successfully
[[14:40:43]] [INFO] N9sXy9WltY=running
[[14:40:43]] [INFO] Executing action 102/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtStart Shopping"]" exists
[[14:40:43]] [SUCCESS] Screenshot refreshed
[[14:40:43]] [INFO] Refreshing screenshot...
[[14:40:43]] [INFO] 8uojw2klHA=pass
[[14:40:38]] [SUCCESS] Screenshot refreshed successfully
[[14:40:38]] [SUCCESS] Screenshot refreshed successfully
[[14:40:37]] [INFO] 8uojw2klHA=running
[[14:40:37]] [INFO] Executing action 101/576: iOS Function: text - Text: "env[pwd]"
[[14:40:37]] [SUCCESS] Screenshot refreshed
[[14:40:37]] [INFO] Refreshing screenshot...
[[14:40:37]] [INFO] SHaIduBnay=pass
[[14:40:33]] [SUCCESS] Screenshot refreshed successfully
[[14:40:33]] [SUCCESS] Screenshot refreshed successfully
[[14:40:33]] [INFO] SHaIduBnay=running
[[14:40:33]] [INFO] Executing action 100/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:40:32]] [SUCCESS] Screenshot refreshed
[[14:40:32]] [INFO] Refreshing screenshot...
[[14:40:32]] [INFO] TGoXyeQtB7=pass
[[14:40:27]] [SUCCESS] Screenshot refreshed successfully
[[14:40:27]] [SUCCESS] Screenshot refreshed successfully
[[14:40:27]] [INFO] TGoXyeQtB7=running
[[14:40:27]] [INFO] Executing action 99/576: iOS Function: text - Text: "env[uname]"
[[14:40:27]] [SUCCESS] Screenshot refreshed
[[14:40:27]] [INFO] Refreshing screenshot...
[[14:40:27]] [INFO] rLCI6NVxSc=pass
[[14:40:23]] [SUCCESS] Screenshot refreshed successfully
[[14:40:23]] [SUCCESS] Screenshot refreshed successfully
[[14:40:23]] [INFO] rLCI6NVxSc=running
[[14:40:23]] [INFO] Executing action 98/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:40:22]] [SUCCESS] Screenshot refreshed
[[14:40:22]] [INFO] Refreshing screenshot...
[[14:40:22]] [INFO] 6mHVWI3j5e=pass
[[14:40:19]] [SUCCESS] Screenshot refreshed successfully
[[14:40:19]] [SUCCESS] Screenshot refreshed successfully
[[14:40:19]] [INFO] 6mHVWI3j5e=running
[[14:40:19]] [INFO] Executing action 97/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:40:18]] [SUCCESS] Screenshot refreshed
[[14:40:18]] [INFO] Refreshing screenshot...
[[14:40:18]] [INFO] rJVGLpLWM3=pass
[[14:40:15]] [SUCCESS] Screenshot refreshed successfully
[[14:40:15]] [SUCCESS] Screenshot refreshed successfully
[[14:40:15]] [INFO] rJVGLpLWM3=running
[[14:40:15]] [INFO] Executing action 96/576: iOS Function: alert_accept
[[14:40:14]] [SUCCESS] Screenshot refreshed
[[14:40:14]] [INFO] Refreshing screenshot...
[[14:40:14]] [INFO] WlISsMf9QA=pass
[[14:40:10]] [SUCCESS] Screenshot refreshed successfully
[[14:40:10]] [SUCCESS] Screenshot refreshed successfully
[[14:40:10]] [INFO] WlISsMf9QA=running
[[14:40:10]] [INFO] Executing action 95/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[14:40:10]] [SUCCESS] Screenshot refreshed
[[14:40:10]] [INFO] Refreshing screenshot...
[[14:40:10]] [INFO] IvqPpScAJa=pass
[[14:40:06]] [SUCCESS] Screenshot refreshed successfully
[[14:40:06]] [SUCCESS] Screenshot refreshed successfully
[[14:40:06]] [INFO] IvqPpScAJa=running
[[14:40:06]] [INFO] Executing action 94/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[14:40:05]] [SUCCESS] Screenshot refreshed
[[14:40:05]] [INFO] Refreshing screenshot...
[[14:40:05]] [INFO] bGo3feCwBQ=pass
[[14:40:01]] [SUCCESS] Screenshot refreshed successfully
[[14:40:01]] [SUCCESS] Screenshot refreshed successfully
[[14:40:01]] [INFO] bGo3feCwBQ=running
[[14:40:01]] [INFO] Executing action 93/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:40:00]] [SUCCESS] Screenshot refreshed
[[14:40:00]] [INFO] Refreshing screenshot...
[[14:40:00]] [INFO] 4WfPFN961S=pass
[[14:39:53]] [SUCCESS] Screenshot refreshed successfully
[[14:39:53]] [SUCCESS] Screenshot refreshed successfully
[[14:39:53]] [INFO] 4WfPFN961S=running
[[14:39:53]] [INFO] Executing action 92/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:39:53]] [SUCCESS] Screenshot refreshed
[[14:39:53]] [INFO] Refreshing screenshot...
[[14:39:53]] [INFO] F0gZF1jEnT=pass
[[14:39:49]] [SUCCESS] Screenshot refreshed successfully
[[14:39:49]] [SUCCESS] Screenshot refreshed successfully
[[14:39:49]] [INFO] F0gZF1jEnT=running
[[14:39:49]] [INFO] Executing action 91/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:39:48]] [SUCCESS] Screenshot refreshed
[[14:39:48]] [INFO] Refreshing screenshot...
[[14:39:48]] [INFO] EDHl0X27Wi=pass
[[14:39:44]] [SUCCESS] Screenshot refreshed successfully
[[14:39:44]] [SUCCESS] Screenshot refreshed successfully
[[14:39:44]] [INFO] EDHl0X27Wi=running
[[14:39:44]] [INFO] Executing action 90/576: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[14:39:43]] [SUCCESS] Screenshot refreshed
[[14:39:43]] [INFO] Refreshing screenshot...
[[14:39:43]] [INFO] j8NXU87gV3=pass
[[14:39:38]] [SUCCESS] Screenshot refreshed successfully
[[14:39:38]] [SUCCESS] Screenshot refreshed successfully
[[14:39:38]] [INFO] j8NXU87gV3=running
[[14:39:38]] [INFO] Executing action 89/576: iOS Function: text - Text: "env[pwd]"
[[14:39:37]] [SUCCESS] Screenshot refreshed
[[14:39:37]] [INFO] Refreshing screenshot...
[[14:39:37]] [INFO] dpVaKL19uc=pass
[[14:39:33]] [SUCCESS] Screenshot refreshed successfully
[[14:39:33]] [SUCCESS] Screenshot refreshed successfully
[[14:39:33]] [INFO] dpVaKL19uc=running
[[14:39:33]] [INFO] Executing action 88/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:39:32]] [SUCCESS] Screenshot refreshed
[[14:39:32]] [INFO] Refreshing screenshot...
[[14:39:32]] [INFO] eOm1WExcrK=pass
[[14:39:28]] [SUCCESS] Screenshot refreshed successfully
[[14:39:28]] [SUCCESS] Screenshot refreshed successfully
[[14:39:28]] [INFO] eOm1WExcrK=running
[[14:39:28]] [INFO] Executing action 87/576: iOS Function: text - Text: "env[uname]"
[[14:39:27]] [SUCCESS] Screenshot refreshed
[[14:39:27]] [INFO] Refreshing screenshot...
[[14:39:27]] [INFO] 50Z2jrodNd=pass
[[14:39:23]] [SUCCESS] Screenshot refreshed successfully
[[14:39:23]] [SUCCESS] Screenshot refreshed successfully
[[14:39:23]] [INFO] 50Z2jrodNd=running
[[14:39:23]] [INFO] Executing action 86/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:39:22]] [SUCCESS] Screenshot refreshed
[[14:39:22]] [INFO] Refreshing screenshot...
[[14:39:22]] [INFO] eJnHS9n9VL=pass
[[14:39:19]] [SUCCESS] Screenshot refreshed successfully
[[14:39:19]] [SUCCESS] Screenshot refreshed successfully
[[14:39:19]] [INFO] eJnHS9n9VL=running
[[14:39:19]] [INFO] Executing action 85/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:39:18]] [SUCCESS] Screenshot refreshed
[[14:39:18]] [INFO] Refreshing screenshot...
[[14:39:18]] [INFO] XuLgjNG74w=pass
[[14:39:16]] [SUCCESS] Screenshot refreshed successfully
[[14:39:16]] [SUCCESS] Screenshot refreshed successfully
[[14:39:15]] [INFO] XuLgjNG74w=running
[[14:39:15]] [INFO] Executing action 84/576: iOS Function: alert_accept
[[14:39:15]] [SUCCESS] Screenshot refreshed
[[14:39:15]] [INFO] Refreshing screenshot...
[[14:39:15]] [INFO] qA1ap4n1m4=pass
[[14:39:11]] [SUCCESS] Screenshot refreshed successfully
[[14:39:11]] [SUCCESS] Screenshot refreshed successfully
[[14:39:09]] [INFO] qA1ap4n1m4=running
[[14:39:09]] [INFO] Executing action 83/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:39:08]] [SUCCESS] Screenshot refreshed
[[14:39:08]] [INFO] Refreshing screenshot...
[[14:39:08]] [INFO] JXFxYCr98V=pass
[[14:38:55]] [SUCCESS] Screenshot refreshed successfully
[[14:38:55]] [SUCCESS] Screenshot refreshed successfully
[[14:38:54]] [INFO] JXFxYCr98V=running
[[14:38:54]] [INFO] Executing action 82/576: Restart app: env[appid]
[[14:38:54]] [SUCCESS] Screenshot refreshed
[[14:38:54]] [INFO] Refreshing screenshot...
[[14:38:53]] [SUCCESS] Screenshot refreshed
[[14:38:53]] [INFO] Refreshing screenshot...
[[14:38:50]] [SUCCESS] Screenshot refreshed successfully
[[14:38:50]] [SUCCESS] Screenshot refreshed successfully
[[14:38:50]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:38:50]] [SUCCESS] Screenshot refreshed
[[14:38:50]] [INFO] Refreshing screenshot...
[[14:38:38]] [SUCCESS] Screenshot refreshed successfully
[[14:38:38]] [SUCCESS] Screenshot refreshed successfully
[[14:38:38]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:38:37]] [SUCCESS] Screenshot refreshed
[[14:38:37]] [INFO] Refreshing screenshot...
[[14:38:34]] [SUCCESS] Screenshot refreshed successfully
[[14:38:34]] [SUCCESS] Screenshot refreshed successfully
[[14:38:33]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:38:33]] [SUCCESS] Screenshot refreshed
[[14:38:33]] [INFO] Refreshing screenshot...
[[14:38:30]] [SUCCESS] Screenshot refreshed successfully
[[14:38:30]] [SUCCESS] Screenshot refreshed successfully
[[14:38:29]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:38:28]] [SUCCESS] Screenshot refreshed
[[14:38:28]] [INFO] Refreshing screenshot...
[[14:38:22]] [SUCCESS] Screenshot refreshed successfully
[[14:38:22]] [SUCCESS] Screenshot refreshed successfully
[[14:38:21]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:38:21]] [SUCCESS] Screenshot refreshed
[[14:38:21]] [INFO] Refreshing screenshot...
[[14:38:15]] [SUCCESS] Screenshot refreshed successfully
[[14:38:15]] [SUCCESS] Screenshot refreshed successfully
[[14:38:14]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:38:14]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:38:14]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:38:14]] [INFO] hbIlJIWlVN=running
[[14:38:14]] [INFO] Executing action 81/576: cleanupSteps action
[[14:38:14]] [SUCCESS] Screenshot refreshed
[[14:38:14]] [INFO] Refreshing screenshot...
[[14:38:13]] [SUCCESS] Screenshot refreshed
[[14:38:13]] [INFO] Refreshing screenshot...
[[14:38:09]] [SUCCESS] Screenshot refreshed successfully
[[14:38:09]] [SUCCESS] Screenshot refreshed successfully
[[14:38:09]] [INFO] Executing Multi Step action step 34/34: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[14:38:08]] [SUCCESS] Screenshot refreshed
[[14:38:08]] [INFO] Refreshing screenshot...
[[14:38:04]] [INFO] Executing Multi Step action step 33/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:38:04]] [SUCCESS] Screenshot refreshed successfully
[[14:38:04]] [SUCCESS] Screenshot refreshed successfully
[[14:38:04]] [SUCCESS] Screenshot refreshed
[[14:38:04]] [INFO] Refreshing screenshot...
[[14:38:00]] [SUCCESS] Screenshot refreshed successfully
[[14:38:00]] [SUCCESS] Screenshot refreshed successfully
[[14:38:00]] [INFO] Executing Multi Step action step 32/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:37:59]] [SUCCESS] Screenshot refreshed
[[14:37:59]] [INFO] Refreshing screenshot...
[[14:37:55]] [SUCCESS] Screenshot refreshed successfully
[[14:37:55]] [SUCCESS] Screenshot refreshed successfully
[[14:37:55]] [INFO] Executing Multi Step action step 31/34: Tap on image: banner-close-updated.png
[[14:37:54]] [SUCCESS] Screenshot refreshed
[[14:37:54]] [INFO] Refreshing screenshot...
[[14:37:44]] [SUCCESS] Screenshot refreshed successfully
[[14:37:44]] [SUCCESS] Screenshot refreshed successfully
[[14:37:43]] [INFO] Executing Multi Step action step 30/34: Swipe from (50%, 70%) to (50%, 30%)
[[14:37:43]] [SUCCESS] Screenshot refreshed
[[14:37:43]] [INFO] Refreshing screenshot...
[[14:37:39]] [SUCCESS] Screenshot refreshed successfully
[[14:37:39]] [SUCCESS] Screenshot refreshed successfully
[[14:37:39]] [INFO] Executing Multi Step action step 29/34: Tap on image: env[delivery-address-img]
[[14:37:38]] [SUCCESS] Screenshot refreshed
[[14:37:38]] [INFO] Refreshing screenshot...
[[14:37:34]] [SUCCESS] Screenshot refreshed successfully
[[14:37:34]] [SUCCESS] Screenshot refreshed successfully
[[14:37:34]] [INFO] Executing Multi Step action step 28/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[14:37:34]] [SUCCESS] Screenshot refreshed
[[14:37:34]] [INFO] Refreshing screenshot...
[[14:37:27]] [SUCCESS] Screenshot refreshed successfully
[[14:37:27]] [SUCCESS] Screenshot refreshed successfully
[[14:37:27]] [INFO] Executing Multi Step action step 27/34: Tap and Type at (54, 314): "305 238 Flinders"
[[14:37:26]] [SUCCESS] Screenshot refreshed
[[14:37:26]] [INFO] Refreshing screenshot...
[[14:37:21]] [SUCCESS] Screenshot refreshed successfully
[[14:37:21]] [SUCCESS] Screenshot refreshed successfully
[[14:37:21]] [INFO] Executing Multi Step action step 26/34: Tap on Text: "address"
[[14:37:20]] [SUCCESS] Screenshot refreshed
[[14:37:20]] [INFO] Refreshing screenshot...
[[14:37:16]] [SUCCESS] Screenshot refreshed successfully
[[14:37:16]] [SUCCESS] Screenshot refreshed successfully
[[14:37:16]] [INFO] Executing Multi Step action step 25/34: iOS Function: text - Text: " "
[[14:37:15]] [SUCCESS] Screenshot refreshed
[[14:37:15]] [INFO] Refreshing screenshot...
[[14:37:11]] [INFO] Executing Multi Step action step 24/34: textClear action
[[14:37:11]] [SUCCESS] Screenshot refreshed successfully
[[14:37:11]] [SUCCESS] Screenshot refreshed successfully
[[14:37:10]] [SUCCESS] Screenshot refreshed
[[14:37:10]] [INFO] Refreshing screenshot...
[[14:37:06]] [SUCCESS] Screenshot refreshed successfully
[[14:37:06]] [SUCCESS] Screenshot refreshed successfully
[[14:37:06]] [INFO] Executing Multi Step action step 23/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[14:37:06]] [SUCCESS] Screenshot refreshed
[[14:37:06]] [INFO] Refreshing screenshot...
[[14:37:01]] [SUCCESS] Screenshot refreshed successfully
[[14:37:01]] [SUCCESS] Screenshot refreshed successfully
[[14:37:01]] [INFO] Executing Multi Step action step 22/34: textClear action
[[14:37:01]] [SUCCESS] Screenshot refreshed
[[14:37:01]] [INFO] Refreshing screenshot...
[[14:36:57]] [SUCCESS] Screenshot refreshed successfully
[[14:36:57]] [SUCCESS] Screenshot refreshed successfully
[[14:36:57]] [INFO] Executing Multi Step action step 21/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:36:56]] [SUCCESS] Screenshot refreshed
[[14:36:56]] [INFO] Refreshing screenshot...
[[14:36:52]] [SUCCESS] Screenshot refreshed successfully
[[14:36:52]] [SUCCESS] Screenshot refreshed successfully
[[14:36:52]] [INFO] Executing Multi Step action step 20/34: textClear action
[[14:36:51]] [SUCCESS] Screenshot refreshed
[[14:36:51]] [INFO] Refreshing screenshot...
[[14:36:49]] [SUCCESS] Screenshot refreshed successfully
[[14:36:49]] [SUCCESS] Screenshot refreshed successfully
[[14:36:47]] [INFO] Executing Multi Step action step 19/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[14:36:47]] [SUCCESS] Screenshot refreshed
[[14:36:47]] [INFO] Refreshing screenshot...
[[14:36:42]] [INFO] Executing Multi Step action step 18/34: textClear action
[[14:36:42]] [SUCCESS] Screenshot refreshed successfully
[[14:36:42]] [SUCCESS] Screenshot refreshed successfully
[[14:36:42]] [SUCCESS] Screenshot refreshed
[[14:36:42]] [INFO] Refreshing screenshot...
[[14:36:37]] [SUCCESS] Screenshot refreshed successfully
[[14:36:37]] [SUCCESS] Screenshot refreshed successfully
[[14:36:37]] [INFO] Executing Multi Step action step 17/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[14:36:37]] [SUCCESS] Screenshot refreshed
[[14:36:37]] [INFO] Refreshing screenshot...
[[14:36:33]] [SUCCESS] Screenshot refreshed successfully
[[14:36:33]] [SUCCESS] Screenshot refreshed successfully
[[14:36:33]] [INFO] Executing Multi Step action step 16/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[14:36:32]] [SUCCESS] Screenshot refreshed
[[14:36:32]] [INFO] Refreshing screenshot...
[[14:36:13]] [SUCCESS] Screenshot refreshed successfully
[[14:36:13]] [SUCCESS] Screenshot refreshed successfully
[[14:36:13]] [INFO] Executing Multi Step action step 15/34: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[14:36:12]] [SUCCESS] Screenshot refreshed
[[14:36:12]] [INFO] Refreshing screenshot...
[[14:36:08]] [SUCCESS] Screenshot refreshed successfully
[[14:36:08]] [SUCCESS] Screenshot refreshed successfully
[[14:36:08]] [INFO] Executing Multi Step action step 14/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[14:36:07]] [SUCCESS] Screenshot refreshed
[[14:36:07]] [INFO] Refreshing screenshot...
[[14:36:04]] [SUCCESS] Screenshot refreshed successfully
[[14:36:04]] [SUCCESS] Screenshot refreshed successfully
[[14:36:04]] [INFO] Executing Multi Step action step 13/34: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[14:36:03]] [SUCCESS] Screenshot refreshed
[[14:36:03]] [INFO] Refreshing screenshot...
[[14:36:00]] [SUCCESS] Screenshot refreshed successfully
[[14:36:00]] [SUCCESS] Screenshot refreshed successfully
[[14:35:59]] [INFO] Executing Multi Step action step 12/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:35:59]] [SUCCESS] Screenshot refreshed
[[14:35:59]] [INFO] Refreshing screenshot...
[[14:35:52]] [SUCCESS] Screenshot refreshed successfully
[[14:35:52]] [SUCCESS] Screenshot refreshed successfully
[[14:35:52]] [INFO] Executing Multi Step action step 11/34: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[14:35:51]] [SUCCESS] Screenshot refreshed
[[14:35:51]] [INFO] Refreshing screenshot...
[[14:35:47]] [SUCCESS] Screenshot refreshed successfully
[[14:35:47]] [SUCCESS] Screenshot refreshed successfully
[[14:35:47]] [INFO] Executing Multi Step action step 10/34: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:35:46]] [SUCCESS] Screenshot refreshed
[[14:35:46]] [INFO] Refreshing screenshot...
[[14:35:42]] [SUCCESS] Screenshot refreshed successfully
[[14:35:42]] [SUCCESS] Screenshot refreshed successfully
[[14:35:42]] [INFO] Executing Multi Step action step 9/34: iOS Function: text - Text: "Uno card"
[[14:35:41]] [SUCCESS] Screenshot refreshed
[[14:35:41]] [INFO] Refreshing screenshot...
[[14:35:36]] [SUCCESS] Screenshot refreshed successfully
[[14:35:36]] [SUCCESS] Screenshot refreshed successfully
[[14:35:35]] [INFO] Executing Multi Step action step 8/34: Tap on Text: "Find"
[[14:35:35]] [SUCCESS] Screenshot refreshed
[[14:35:35]] [INFO] Refreshing screenshot...
[[14:35:12]] [SUCCESS] Screenshot refreshed successfully
[[14:35:12]] [SUCCESS] Screenshot refreshed successfully
[[14:35:12]] [INFO] Executing Multi Step action step 7/34: If exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]" (timeout: 20s) → Then tap at (0, 0)
[[14:35:11]] [SUCCESS] Screenshot refreshed
[[14:35:11]] [INFO] Refreshing screenshot...
[[14:34:59]] [SUCCESS] Screenshot refreshed successfully
[[14:34:59]] [SUCCESS] Screenshot refreshed successfully
[[14:34:59]] [INFO] Executing Multi Step action step 6/34: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[14:34:58]] [SUCCESS] Screenshot refreshed
[[14:34:58]] [INFO] Refreshing screenshot...
[[14:34:31]] [SUCCESS] Screenshot refreshed successfully
[[14:34:31]] [SUCCESS] Screenshot refreshed successfully
[[14:34:31]] [INFO] Executing Multi Step action step 5/34: If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 25s) → Then click element: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[14:34:30]] [SUCCESS] Screenshot refreshed
[[14:34:30]] [INFO] Refreshing screenshot...
[[14:34:25]] [SUCCESS] Screenshot refreshed successfully
[[14:34:25]] [SUCCESS] Screenshot refreshed successfully
[[14:34:25]] [INFO] Executing Multi Step action step 4/34: Tap on Text: "Save"
[[14:34:25]] [SUCCESS] Screenshot refreshed
[[14:34:25]] [INFO] Refreshing screenshot...
[[14:34:19]] [SUCCESS] Screenshot refreshed successfully
[[14:34:19]] [SUCCESS] Screenshot refreshed successfully
[[14:34:19]] [INFO] Executing Multi Step action step 3/34: Tap on element with accessibility_id: btnCurrentLocationButton
[[14:34:18]] [SUCCESS] Screenshot refreshed
[[14:34:18]] [INFO] Refreshing screenshot...
[[14:34:14]] [SUCCESS] Screenshot refreshed successfully
[[14:34:14]] [SUCCESS] Screenshot refreshed successfully
[[14:34:14]] [INFO] Executing Multi Step action step 2/34: Wait till accessibility_id=btnCurrentLocationButton
[[14:34:13]] [SUCCESS] Screenshot refreshed
[[14:34:13]] [INFO] Refreshing screenshot...
[[14:34:06]] [SUCCESS] Screenshot refreshed successfully
[[14:34:06]] [SUCCESS] Screenshot refreshed successfully
[[14:34:06]] [INFO] Executing Multi Step action step 1/34: Tap on Text: "Edit"
[[14:34:06]] [INFO] Loaded 34 steps from test case: Delivery  Buy
[[14:34:06]] [INFO] Loading steps for multiStep action: Delivery  Buy
[[14:34:06]] [INFO] aI4Cfo88Pv=running
[[14:34:06]] [INFO] Executing action 80/576: Execute Test Case: Delivery  Buy (34 steps)
[[14:34:05]] [SUCCESS] Screenshot refreshed
[[14:34:05]] [INFO] Refreshing screenshot...
[[14:34:05]] [INFO] cKNu2QoRC1=pass
[[14:34:01]] [SUCCESS] Screenshot refreshed successfully
[[14:34:01]] [SUCCESS] Screenshot refreshed successfully
[[14:34:01]] [INFO] cKNu2QoRC1=running
[[14:34:01]] [INFO] Executing action 79/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[14:34:00]] [SUCCESS] Screenshot refreshed
[[14:34:00]] [INFO] Refreshing screenshot...
[[14:34:00]] [INFO] OyUowAaBzD=pass
[[14:33:56]] [SUCCESS] Screenshot refreshed successfully
[[14:33:56]] [SUCCESS] Screenshot refreshed successfully
[[14:33:56]] [INFO] OyUowAaBzD=running
[[14:33:56]] [INFO] Executing action 78/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[14:33:55]] [SUCCESS] Screenshot refreshed
[[14:33:55]] [INFO] Refreshing screenshot...
[[14:33:55]] [INFO] Ob26qqcA0p=pass
[[14:33:48]] [SUCCESS] Screenshot refreshed successfully
[[14:33:48]] [SUCCESS] Screenshot refreshed successfully
[[14:33:48]] [INFO] Ob26qqcA0p=running
[[14:33:48]] [INFO] Executing action 77/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:33:48]] [SUCCESS] Screenshot refreshed
[[14:33:48]] [INFO] Refreshing screenshot...
[[14:33:48]] [INFO] k3mu9Mt7Ec=pass
[[14:33:44]] [SUCCESS] Screenshot refreshed successfully
[[14:33:44]] [SUCCESS] Screenshot refreshed successfully
[[14:33:43]] [INFO] k3mu9Mt7Ec=running
[[14:33:43]] [INFO] Executing action 76/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:33:43]] [SUCCESS] Screenshot refreshed
[[14:33:43]] [INFO] Refreshing screenshot...
[[14:33:43]] [INFO] 8umPSX0vrr=pass
[[14:33:38]] [INFO] 8umPSX0vrr=running
[[14:33:38]] [INFO] Executing action 75/576: Tap on image: banner-close-updated.png
[[14:33:38]] [SUCCESS] Screenshot refreshed successfully
[[14:33:38]] [SUCCESS] Screenshot refreshed successfully
[[14:33:37]] [SUCCESS] Screenshot refreshed
[[14:33:37]] [INFO] Refreshing screenshot...
[[14:33:37]] [INFO] pr9o8Zsm5p=pass
[[14:33:34]] [SUCCESS] Screenshot refreshed successfully
[[14:33:34]] [SUCCESS] Screenshot refreshed successfully
[[14:33:33]] [INFO] pr9o8Zsm5p=running
[[14:33:33]] [INFO] Executing action 74/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:33:33]] [SUCCESS] Screenshot refreshed
[[14:33:33]] [INFO] Refreshing screenshot...
[[14:33:33]] [INFO] Qbg9bipTGs=pass
[[14:33:28]] [SUCCESS] Screenshot refreshed successfully
[[14:33:28]] [SUCCESS] Screenshot refreshed successfully
[[14:33:28]] [INFO] Qbg9bipTGs=running
[[14:33:28]] [INFO] Executing action 73/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:33:28]] [SUCCESS] Screenshot refreshed
[[14:33:28]] [INFO] Refreshing screenshot...
[[14:33:28]] [INFO] qjj0i3rcUh=pass
[[14:33:24]] [SUCCESS] Screenshot refreshed successfully
[[14:33:24]] [SUCCESS] Screenshot refreshed successfully
[[14:33:24]] [INFO] qjj0i3rcUh=running
[[14:33:24]] [INFO] Executing action 72/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[14:33:23]] [SUCCESS] Screenshot refreshed
[[14:33:23]] [INFO] Refreshing screenshot...
[[14:33:23]] [INFO] lWIRxRm6HE=pass
[[14:33:20]] [SUCCESS] Screenshot refreshed successfully
[[14:33:20]] [SUCCESS] Screenshot refreshed successfully
[[14:33:19]] [INFO] lWIRxRm6HE=running
[[14:33:19]] [INFO] Executing action 71/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:33:19]] [SUCCESS] Screenshot refreshed
[[14:33:19]] [INFO] Refreshing screenshot...
[[14:33:19]] [INFO] Q0fomJIDoQ=pass
[[14:33:15]] [SUCCESS] Screenshot refreshed successfully
[[14:33:15]] [SUCCESS] Screenshot refreshed successfully
[[14:33:14]] [INFO] Q0fomJIDoQ=running
[[14:33:14]] [INFO] Executing action 70/576: Tap on image: banner-close-updated.png
[[14:33:14]] [SUCCESS] Screenshot refreshed
[[14:33:14]] [INFO] Refreshing screenshot...
[[14:33:14]] [INFO] 7SpDO20tS2=pass
[[14:33:02]] [SUCCESS] Screenshot refreshed successfully
[[14:33:02]] [SUCCESS] Screenshot refreshed successfully
[[14:33:02]] [INFO] 7SpDO20tS2=running
[[14:33:02]] [INFO] Executing action 69/576: Wait for 10 ms
[[14:33:02]] [SUCCESS] Screenshot refreshed
[[14:33:02]] [INFO] Refreshing screenshot...
[[14:33:02]] [INFO] FKZs2qCWoU=pass
[[14:32:57]] [SUCCESS] Screenshot refreshed successfully
[[14:32:57]] [SUCCESS] Screenshot refreshed successfully
[[14:32:57]] [INFO] FKZs2qCWoU=running
[[14:32:57]] [INFO] Executing action 68/576: Tap on Text: "Brunswick"
[[14:32:56]] [SUCCESS] Screenshot refreshed
[[14:32:56]] [INFO] Refreshing screenshot...
[[14:32:56]] [INFO] Qbg9bipTGs=pass
[[14:32:52]] [SUCCESS] Screenshot refreshed successfully
[[14:32:52]] [SUCCESS] Screenshot refreshed successfully
[[14:32:52]] [INFO] Qbg9bipTGs=running
[[14:32:52]] [INFO] Executing action 67/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:32:51]] [SUCCESS] Screenshot refreshed
[[14:32:51]] [INFO] Refreshing screenshot...
[[14:32:51]] [INFO] qjj0i3rcUh=pass
[[14:32:47]] [SUCCESS] Screenshot refreshed successfully
[[14:32:47]] [SUCCESS] Screenshot refreshed successfully
[[14:32:47]] [INFO] qjj0i3rcUh=running
[[14:32:47]] [INFO] Executing action 66/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[14:32:46]] [SUCCESS] Screenshot refreshed
[[14:32:46]] [INFO] Refreshing screenshot...
[[14:32:46]] [INFO] uM5FOSrU5U=pass
[[14:32:43]] [INFO] uM5FOSrU5U=running
[[14:32:43]] [INFO] Executing action 65/576: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[14:32:43]] [SUCCESS] Screenshot refreshed successfully
[[14:32:43]] [SUCCESS] Screenshot refreshed successfully
[[14:32:43]] [SUCCESS] Screenshot refreshed
[[14:32:43]] [INFO] Refreshing screenshot...
[[14:32:43]] [INFO] F1olhgKhUt=pass
[[14:32:39]] [SUCCESS] Screenshot refreshed successfully
[[14:32:39]] [SUCCESS] Screenshot refreshed successfully
[[14:32:38]] [INFO] F1olhgKhUt=running
[[14:32:38]] [INFO] Executing action 64/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:32:38]] [SUCCESS] Screenshot refreshed
[[14:32:38]] [INFO] Refreshing screenshot...
[[14:32:38]] [INFO] jY0oPjKbuS=pass
[[14:32:34]] [SUCCESS] Screenshot refreshed successfully
[[14:32:34]] [SUCCESS] Screenshot refreshed successfully
[[14:32:34]] [INFO] jY0oPjKbuS=running
[[14:32:34]] [INFO] Executing action 63/576: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[14:32:33]] [SUCCESS] Screenshot refreshed
[[14:32:33]] [INFO] Refreshing screenshot...
[[14:32:33]] [INFO] FnrbyHq7bU=pass
[[14:32:27]] [SUCCESS] Screenshot refreshed successfully
[[14:32:27]] [SUCCESS] Screenshot refreshed successfully
[[14:32:27]] [INFO] FnrbyHq7bU=running
[[14:32:27]] [INFO] Executing action 62/576: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[14:32:26]] [SUCCESS] Screenshot refreshed
[[14:32:26]] [INFO] Refreshing screenshot...
[[14:32:26]] [INFO] nAB6Q8LAdv=pass
[[14:32:23]] [SUCCESS] Screenshot refreshed successfully
[[14:32:23]] [SUCCESS] Screenshot refreshed successfully
[[14:32:22]] [INFO] nAB6Q8LAdv=running
[[14:32:22]] [INFO] Executing action 61/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:32:22]] [SUCCESS] Screenshot refreshed
[[14:32:22]] [INFO] Refreshing screenshot...
[[14:32:22]] [INFO] sc2KH9bG6H=pass
[[14:32:18]] [SUCCESS] Screenshot refreshed successfully
[[14:32:18]] [SUCCESS] Screenshot refreshed successfully
[[14:32:17]] [INFO] sc2KH9bG6H=running
[[14:32:17]] [INFO] Executing action 60/576: iOS Function: text - Text: "Uno card"
[[14:32:17]] [SUCCESS] Screenshot refreshed
[[14:32:17]] [INFO] Refreshing screenshot...
[[14:32:17]] [INFO] ZBXCQNlT8z=pass
[[14:32:12]] [SUCCESS] Screenshot refreshed successfully
[[14:32:12]] [SUCCESS] Screenshot refreshed successfully
[[14:32:11]] [INFO] ZBXCQNlT8z=running
[[14:32:11]] [INFO] Executing action 59/576: Tap on Text: "Find"
[[14:32:11]] [SUCCESS] Screenshot refreshed
[[14:32:11]] [INFO] Refreshing screenshot...
[[14:32:11]] [SUCCESS] Screenshot refreshed
[[14:32:11]] [INFO] Refreshing screenshot...
[[14:32:06]] [SUCCESS] Screenshot refreshed successfully
[[14:32:06]] [SUCCESS] Screenshot refreshed successfully
[[14:32:06]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[14:32:05]] [SUCCESS] Screenshot refreshed
[[14:32:05]] [INFO] Refreshing screenshot...
[[14:32:01]] [SUCCESS] Screenshot refreshed successfully
[[14:32:01]] [SUCCESS] Screenshot refreshed successfully
[[14:32:01]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[14:32:01]] [SUCCESS] Screenshot refreshed
[[14:32:01]] [INFO] Refreshing screenshot...
[[14:31:56]] [SUCCESS] Screenshot refreshed successfully
[[14:31:56]] [SUCCESS] Screenshot refreshed successfully
[[14:31:56]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[14:31:55]] [SUCCESS] Screenshot refreshed
[[14:31:55]] [INFO] Refreshing screenshot...
[[14:31:51]] [SUCCESS] Screenshot refreshed successfully
[[14:31:51]] [SUCCESS] Screenshot refreshed successfully
[[14:31:51]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[14:31:51]] [SUCCESS] Screenshot refreshed
[[14:31:51]] [INFO] Refreshing screenshot...
[[14:31:44]] [SUCCESS] Screenshot refreshed successfully
[[14:31:44]] [SUCCESS] Screenshot refreshed successfully
[[14:31:44]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:31:44]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[14:31:44]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[14:31:44]] [INFO] El6k4IPZly=running
[[14:31:44]] [INFO] Executing action 58/576: Execute Test Case: Kmart-Signin (8 steps)
[[14:31:44]] [SUCCESS] Screenshot refreshed
[[14:31:44]] [INFO] Refreshing screenshot...
[[14:31:44]] [INFO] 3caMBvQX7k=pass
[[14:31:40]] [SUCCESS] Screenshot refreshed successfully
[[14:31:40]] [SUCCESS] Screenshot refreshed successfully
[[14:31:40]] [INFO] 3caMBvQX7k=running
[[14:31:40]] [INFO] Executing action 57/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[14:31:39]] [SUCCESS] Screenshot refreshed
[[14:31:39]] [INFO] Refreshing screenshot...
[[14:31:39]] [INFO] yUJyVO5Wev=pass
[[14:31:36]] [SUCCESS] Screenshot refreshed successfully
[[14:31:36]] [SUCCESS] Screenshot refreshed successfully
[[14:31:36]] [INFO] yUJyVO5Wev=running
[[14:31:36]] [INFO] Executing action 56/576: iOS Function: alert_accept
[[14:31:35]] [SUCCESS] Screenshot refreshed
[[14:31:35]] [INFO] Refreshing screenshot...
[[14:31:35]] [INFO] rkL0oz4kiL=pass
[[14:31:29]] [SUCCESS] Screenshot refreshed successfully
[[14:31:29]] [SUCCESS] Screenshot refreshed successfully
[[14:31:28]] [INFO] rkL0oz4kiL=running
[[14:31:28]] [INFO] Executing action 55/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[14:31:28]] [SUCCESS] Screenshot refreshed
[[14:31:28]] [INFO] Refreshing screenshot...
[[14:31:28]] [INFO] HotUJOd6oB=pass
[[14:31:15]] [SUCCESS] Screenshot refreshed successfully
[[14:31:15]] [SUCCESS] Screenshot refreshed successfully
[[14:31:13]] [INFO] HotUJOd6oB=running
[[14:31:13]] [INFO] Executing action 54/576: Restart app: env[appid]
[[14:31:13]] [SUCCESS] Screenshot refreshed
[[14:31:13]] [INFO] Refreshing screenshot...
[[14:31:13]] [SUCCESS] Screenshot refreshed
[[14:31:13]] [INFO] Refreshing screenshot...
[[14:31:10]] [SUCCESS] Screenshot refreshed successfully
[[14:31:10]] [SUCCESS] Screenshot refreshed successfully
[[14:31:10]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[14:31:09]] [SUCCESS] Screenshot refreshed
[[14:31:09]] [INFO] Refreshing screenshot...
[[14:30:57]] [SUCCESS] Screenshot refreshed successfully
[[14:30:57]] [SUCCESS] Screenshot refreshed successfully
[[14:30:57]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[14:30:56]] [SUCCESS] Screenshot refreshed
[[14:30:56]] [INFO] Refreshing screenshot...
[[14:30:52]] [SUCCESS] Screenshot refreshed successfully
[[14:30:52]] [SUCCESS] Screenshot refreshed successfully
[[14:30:52]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[14:30:52]] [SUCCESS] Screenshot refreshed
[[14:30:52]] [INFO] Refreshing screenshot...
[[14:30:48]] [SUCCESS] Screenshot refreshed successfully
[[14:30:48]] [SUCCESS] Screenshot refreshed successfully
[[14:30:48]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[14:30:47]] [SUCCESS] Screenshot refreshed
[[14:30:47]] [INFO] Refreshing screenshot...
[[14:30:41]] [SUCCESS] Screenshot refreshed successfully
[[14:30:41]] [SUCCESS] Screenshot refreshed successfully
[[14:30:40]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[14:30:40]] [SUCCESS] Screenshot refreshed
[[14:30:40]] [INFO] Refreshing screenshot...
[[14:30:34]] [SUCCESS] Screenshot refreshed successfully
[[14:30:34]] [SUCCESS] Screenshot refreshed successfully
[[14:30:33]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[14:30:33]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[14:30:33]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[14:30:33]] [INFO] vKo6Ox3YrP=running
[[14:30:33]] [INFO] Executing action 53/576: cleanupSteps action
[[14:30:33]] [SUCCESS] Screenshot refreshed
[[14:30:33]] [INFO] Refreshing screenshot...
[[14:30:33]] [INFO] x4yLCZHaCR=pass
[[14:30:30]] [INFO] x4yLCZHaCR=running
[[14:30:30]] [INFO] Executing action 52/576: Terminate app: env[appid]
[[14:30:30]] [SUCCESS] Screenshot refreshed successfully
[[14:30:30]] [SUCCESS] Screenshot refreshed successfully
[[14:30:29]] [SUCCESS] Screenshot refreshed
[[14:30:29]] [INFO] Refreshing screenshot...
[[14:30:29]] [INFO] 2p13JoJbbA=pass
[[14:30:25]] [SUCCESS] Screenshot refreshed successfully
[[14:30:25]] [SUCCESS] Screenshot refreshed successfully
[[14:30:25]] [INFO] 2p13JoJbbA=running
[[14:30:25]] [INFO] Executing action 51/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:30:25]] [SUCCESS] Screenshot refreshed
[[14:30:25]] [INFO] Refreshing screenshot...
[[14:30:25]] [INFO] 2p13JoJbbA=pass
[[14:30:21]] [SUCCESS] Screenshot refreshed successfully
[[14:30:21]] [SUCCESS] Screenshot refreshed successfully
[[14:30:21]] [INFO] 2p13JoJbbA=running
[[14:30:21]] [INFO] Executing action 50/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[14:30:20]] [SUCCESS] Screenshot refreshed
[[14:30:20]] [INFO] Refreshing screenshot...
[[14:30:20]] [INFO] nyBidG0kHp=pass
[[14:30:13]] [INFO] nyBidG0kHp=running
[[14:30:13]] [INFO] Executing action 49/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[14:30:13]] [SUCCESS] Screenshot refreshed successfully
[[14:30:13]] [SUCCESS] Screenshot refreshed successfully
[[14:30:12]] [SUCCESS] Screenshot refreshed
[[14:30:12]] [INFO] Refreshing screenshot...
[[14:30:12]] [INFO] F4NGh9HrLw=pass
[[14:30:07]] [SUCCESS] Screenshot refreshed successfully
[[14:30:07]] [SUCCESS] Screenshot refreshed successfully
[[14:30:07]] [INFO] F4NGh9HrLw=running
[[14:30:07]] [INFO] Executing action 48/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[14:30:06]] [SUCCESS] Screenshot refreshed
[[14:30:06]] [INFO] Refreshing screenshot...
[[14:30:06]] [INFO] VtMfqK1V9t=pass
[[14:29:50]] [SUCCESS] Screenshot refreshed successfully
[[14:29:50]] [SUCCESS] Screenshot refreshed successfully
[[14:29:47]] [INFO] VtMfqK1V9t=running
[[14:29:47]] [INFO] Executing action 47/576: Tap on element with accessibility_id: Add to bag
[[14:29:47]] [SUCCESS] Screenshot refreshed
[[14:29:47]] [INFO] Refreshing screenshot...
[[14:29:47]] [INFO] NOnuFzXy63=pass
[[14:29:43]] [SUCCESS] Screenshot refreshed successfully
[[14:29:43]] [SUCCESS] Screenshot refreshed successfully
[[14:29:42]] [INFO] NOnuFzXy63=running
[[14:29:42]] [INFO] Executing action 46/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[14:29:42]] [SUCCESS] Screenshot refreshed
[[14:29:42]] [INFO] Refreshing screenshot...
[[14:29:42]] [INFO] kz9lnCdwoH=pass
[[14:29:37]] [SUCCESS] Screenshot refreshed successfully
[[14:29:37]] [SUCCESS] Screenshot refreshed successfully
[[14:29:37]] [INFO] kz9lnCdwoH=running
[[14:29:37]] [INFO] Executing action 45/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:29:36]] [SUCCESS] Screenshot refreshed
[[14:29:36]] [INFO] Refreshing screenshot...
[[14:29:36]] [INFO] kz9lnCdwoH=pass
[[14:29:33]] [SUCCESS] Screenshot refreshed successfully
[[14:29:33]] [SUCCESS] Screenshot refreshed successfully
[[14:29:32]] [INFO] kz9lnCdwoH=running
[[14:29:32]] [INFO] Executing action 44/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:29:32]] [SUCCESS] Screenshot refreshed
[[14:29:32]] [INFO] Refreshing screenshot...
[[14:29:32]] [INFO] qIF9CVPc56=pass
[[14:29:28]] [SUCCESS] Screenshot refreshed successfully
[[14:29:28]] [SUCCESS] Screenshot refreshed successfully
[[14:29:28]] [INFO] qIF9CVPc56=running
[[14:29:28]] [INFO] Executing action 43/576: iOS Function: text - Text: "mat"
[[14:29:27]] [SUCCESS] Screenshot refreshed
[[14:29:27]] [INFO] Refreshing screenshot...
[[14:29:27]] [INFO] yEga5MkcRe=pass
[[14:29:23]] [SUCCESS] Screenshot refreshed successfully
[[14:29:23]] [SUCCESS] Screenshot refreshed successfully
[[14:29:23]] [INFO] yEga5MkcRe=running
[[14:29:23]] [INFO] Executing action 42/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:29:23]] [SUCCESS] Screenshot refreshed
[[14:29:23]] [INFO] Refreshing screenshot...
[[14:29:23]] [INFO] F4NGh9HrLw=pass
[[14:29:19]] [SUCCESS] Screenshot refreshed successfully
[[14:29:19]] [SUCCESS] Screenshot refreshed successfully
[[14:29:19]] [INFO] F4NGh9HrLw=running
[[14:29:19]] [INFO] Executing action 41/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:29:18]] [SUCCESS] Screenshot refreshed
[[14:29:18]] [INFO] Refreshing screenshot...
[[14:29:18]] [INFO] kz9lnCdwoH=pass
[[14:29:13]] [SUCCESS] Screenshot refreshed successfully
[[14:29:13]] [SUCCESS] Screenshot refreshed successfully
[[14:29:13]] [INFO] kz9lnCdwoH=running
[[14:29:13]] [INFO] Executing action 40/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[14:29:12]] [SUCCESS] Screenshot refreshed
[[14:29:12]] [INFO] Refreshing screenshot...
[[14:29:12]] [INFO] kz9lnCdwoH=pass
[[14:29:08]] [SUCCESS] Screenshot refreshed successfully
[[14:29:08]] [SUCCESS] Screenshot refreshed successfully
[[14:29:08]] [INFO] kz9lnCdwoH=running
[[14:29:08]] [INFO] Executing action 39/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:29:07]] [SUCCESS] Screenshot refreshed
[[14:29:07]] [INFO] Refreshing screenshot...
[[14:29:07]] [INFO] JRheDTvpJf=pass
[[14:29:03]] [SUCCESS] Screenshot refreshed successfully
[[14:29:03]] [SUCCESS] Screenshot refreshed successfully
[[14:29:03]] [INFO] JRheDTvpJf=running
[[14:29:03]] [INFO] Executing action 38/576: iOS Function: text - Text: "Kid toy"
[[14:29:02]] [SUCCESS] Screenshot refreshed
[[14:29:02]] [INFO] Refreshing screenshot...
[[14:29:02]] [INFO] yEga5MkcRe=pass
[[14:28:59]] [SUCCESS] Screenshot refreshed successfully
[[14:28:59]] [SUCCESS] Screenshot refreshed successfully
[[14:28:59]] [INFO] yEga5MkcRe=running
[[14:28:59]] [INFO] Executing action 37/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:28:58]] [SUCCESS] Screenshot refreshed
[[14:28:58]] [INFO] Refreshing screenshot...
[[14:28:58]] [INFO] F4NGh9HrLw=pass
[[14:28:55]] [SUCCESS] Screenshot refreshed successfully
[[14:28:55]] [SUCCESS] Screenshot refreshed successfully
[[14:28:54]] [INFO] F4NGh9HrLw=running
[[14:28:54]] [INFO] Executing action 36/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:28:54]] [SUCCESS] Screenshot refreshed
[[14:28:54]] [INFO] Refreshing screenshot...
[[14:28:54]] [INFO] XPEr3w6Zof=pass
[[14:28:49]] [SUCCESS] Screenshot refreshed successfully
[[14:28:49]] [SUCCESS] Screenshot refreshed successfully
[[14:28:49]] [INFO] XPEr3w6Zof=running
[[14:28:49]] [INFO] Executing action 35/576: Restart app: env[appid]
[[14:28:48]] [SUCCESS] Screenshot refreshed
[[14:28:48]] [INFO] Refreshing screenshot...
[[14:28:48]] [INFO] PiQRBWBe3E=pass
[[14:28:44]] [SUCCESS] Screenshot refreshed successfully
[[14:28:44]] [SUCCESS] Screenshot refreshed successfully
[[14:28:44]] [INFO] PiQRBWBe3E=running
[[14:28:44]] [INFO] Executing action 34/576: Tap on image: env[device-back-img]
[[14:28:44]] [SUCCESS] Screenshot refreshed
[[14:28:44]] [INFO] Refreshing screenshot...
[[14:28:44]] [INFO] GWoppouz1l=pass
[[14:28:41]] [SUCCESS] Screenshot refreshed successfully
[[14:28:41]] [SUCCESS] Screenshot refreshed successfully
[[14:28:40]] [INFO] GWoppouz1l=running
[[14:28:40]] [INFO] Executing action 33/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[14:28:40]] [SUCCESS] Screenshot refreshed
[[14:28:40]] [INFO] Refreshing screenshot...
[[14:28:40]] [INFO] B6GDXWAmWp=pass
[[14:28:19]] [SUCCESS] Screenshot refreshed successfully
[[14:28:19]] [SUCCESS] Screenshot refreshed successfully
[[14:28:19]] [INFO] B6GDXWAmWp=running
[[14:28:19]] [INFO] Executing action 32/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[14:28:18]] [SUCCESS] Screenshot refreshed
[[14:28:18]] [INFO] Refreshing screenshot...
[[14:28:18]] [INFO] mtYqeDttRc=pass
[[14:28:14]] [SUCCESS] Screenshot refreshed successfully
[[14:28:14]] [SUCCESS] Screenshot refreshed successfully
[[14:28:14]] [INFO] mtYqeDttRc=running
[[14:28:14]] [INFO] Executing action 31/576: Tap on image: env[paypal-close-img]
[[14:28:13]] [SUCCESS] Screenshot refreshed
[[14:28:13]] [INFO] Refreshing screenshot...
[[14:28:13]] [INFO] q6cKxgMAIn=pass
[[14:28:06]] [SUCCESS] Screenshot refreshed successfully
[[14:28:06]] [SUCCESS] Screenshot refreshed successfully
[[14:28:06]] [INFO] q6cKxgMAIn=running
[[14:28:06]] [INFO] Executing action 30/576: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[14:28:06]] [SUCCESS] Screenshot refreshed
[[14:28:06]] [INFO] Refreshing screenshot...
[[14:28:06]] [INFO] KRQDBv2D3A=pass
[[14:28:01]] [SUCCESS] Screenshot refreshed successfully
[[14:28:01]] [SUCCESS] Screenshot refreshed successfully
[[14:28:01]] [INFO] KRQDBv2D3A=running
[[14:28:01]] [INFO] Executing action 29/576: Tap on image: env[device-back-img]
[[14:28:01]] [SUCCESS] Screenshot refreshed
[[14:28:01]] [INFO] Refreshing screenshot...
[[14:28:01]] [INFO] P4b2BITpCf=pass
[[14:27:58]] [SUCCESS] Screenshot refreshed successfully
[[14:27:58]] [SUCCESS] Screenshot refreshed successfully
[[14:27:58]] [INFO] P4b2BITpCf=running
[[14:27:58]] [INFO] Executing action 28/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[14:27:57]] [SUCCESS] Screenshot refreshed
[[14:27:57]] [INFO] Refreshing screenshot...
[[14:27:57]] [INFO] inrxgdWzXr=pass
[[14:27:51]] [SUCCESS] Screenshot refreshed successfully
[[14:27:51]] [SUCCESS] Screenshot refreshed successfully
[[14:27:51]] [INFO] inrxgdWzXr=running
[[14:27:51]] [INFO] Executing action 27/576: Tap on element with accessibility_id: Learn more about Zip
[[14:27:50]] [SUCCESS] Screenshot refreshed
[[14:27:50]] [INFO] Refreshing screenshot...
[[14:27:50]] [INFO] Et3kvnFdxh=pass
[[14:27:46]] [SUCCESS] Screenshot refreshed successfully
[[14:27:46]] [SUCCESS] Screenshot refreshed successfully
[[14:27:45]] [INFO] Et3kvnFdxh=running
[[14:27:45]] [INFO] Executing action 26/576: Tap on image: env[device-back-img]
[[14:27:45]] [INFO] Skipping disabled action 25/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[14:27:45]] [SUCCESS] Screenshot refreshed
[[14:27:45]] [INFO] Refreshing screenshot...
[[14:27:45]] [INFO] pk2DLZFBmx=pass
[[14:27:38]] [SUCCESS] Screenshot refreshed successfully
[[14:27:38]] [SUCCESS] Screenshot refreshed successfully
[[14:27:38]] [INFO] pk2DLZFBmx=running
[[14:27:38]] [INFO] Executing action 24/576: Tap on element with accessibility_id: Learn more about AfterPay
[[14:27:37]] [SUCCESS] Screenshot refreshed
[[14:27:37]] [INFO] Refreshing screenshot...
[[14:27:37]] [INFO] ShJSdXvmVL=pass
[[14:27:30]] [SUCCESS] Screenshot refreshed successfully
[[14:27:30]] [SUCCESS] Screenshot refreshed successfully
[[14:27:29]] [INFO] ShJSdXvmVL=running
[[14:27:29]] [INFO] Executing action 23/576: Swipe up till element accessibilityid: "Learn more about AfterPay" is visible
[[14:27:29]] [SUCCESS] Screenshot refreshed
[[14:27:29]] [INFO] Refreshing screenshot...
[[14:27:29]] [INFO] sHQtYzpI4s=pass
[[14:27:24]] [SUCCESS] Screenshot refreshed successfully
[[14:27:24]] [SUCCESS] Screenshot refreshed successfully
[[14:27:24]] [INFO] sHQtYzpI4s=running
[[14:27:24]] [INFO] Executing action 22/576: Tap on image: env[closebtnimage]
[[14:27:24]] [SUCCESS] Screenshot refreshed
[[14:27:24]] [INFO] Refreshing screenshot...
[[14:27:24]] [INFO] 83tV9A4NOn=pass
[[14:27:20]] [SUCCESS] Screenshot refreshed successfully
[[14:27:20]] [SUCCESS] Screenshot refreshed successfully
[[14:27:20]] [INFO] 83tV9A4NOn=running
[[14:27:20]] [INFO] Executing action 21/576: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[14:27:19]] [SUCCESS] Screenshot refreshed
[[14:27:19]] [INFO] Refreshing screenshot...
[[14:27:19]] [INFO] dCqKBG3e7u=pass
[[14:27:15]] [SUCCESS] Screenshot refreshed successfully
[[14:27:15]] [SUCCESS] Screenshot refreshed successfully
[[14:27:15]] [INFO] dCqKBG3e7u=running
[[14:27:15]] [INFO] Executing action 20/576: Tap on image: env[product-share-img]
[[14:27:14]] [SUCCESS] Screenshot refreshed
[[14:27:14]] [INFO] Refreshing screenshot...
[[14:27:14]] [INFO] kAQ1yIIw3h=pass
[[14:26:41]] [SUCCESS] Screenshot refreshed successfully
[[14:26:41]] [SUCCESS] Screenshot refreshed successfully
[[14:26:40]] [INFO] kAQ1yIIw3h=running
[[14:26:40]] [INFO] Executing action 19/576: Tap on element with xpath:  (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::* with fallback: Coordinates (98, 308)
[[14:26:40]] [SUCCESS] Screenshot refreshed
[[14:26:40]] [INFO] Refreshing screenshot...
[[14:26:40]] [INFO] OmKfD9iBjD=pass
[[14:26:36]] [SUCCESS] Screenshot refreshed successfully
[[14:26:36]] [SUCCESS] Screenshot refreshed successfully
[[14:26:35]] [INFO] OmKfD9iBjD=running
[[14:26:35]] [INFO] Executing action 18/576: Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::*
[[14:26:35]] [SUCCESS] Screenshot refreshed
[[14:26:35]] [INFO] Refreshing screenshot...
[[14:26:35]] [INFO] dMl1PH9Dlc=pass
[[14:26:23]] [SUCCESS] Screenshot refreshed successfully
[[14:26:23]] [SUCCESS] Screenshot refreshed successfully
[[14:26:23]] [INFO] dMl1PH9Dlc=running
[[14:26:23]] [INFO] Executing action 17/576: Wait for 10 ms
[[14:26:22]] [SUCCESS] Screenshot refreshed
[[14:26:22]] [INFO] Refreshing screenshot...
[[14:26:22]] [INFO] eHLWiRoqqS=pass
[[14:26:17]] [SUCCESS] Screenshot refreshed successfully
[[14:26:17]] [SUCCESS] Screenshot refreshed successfully
[[14:26:16]] [INFO] eHLWiRoqqS=running
[[14:26:16]] [INFO] Executing action 16/576: Swipe from (50%, 70%) to (50%, 30%)
[[14:26:16]] [SUCCESS] Screenshot refreshed
[[14:26:16]] [INFO] Refreshing screenshot...
[[14:26:16]] [INFO] huUnpMMjVR=pass
[[14:26:12]] [SUCCESS] Screenshot refreshed successfully
[[14:26:12]] [SUCCESS] Screenshot refreshed successfully
[[14:26:12]] [INFO] huUnpMMjVR=running
[[14:26:12]] [INFO] Executing action 15/576: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[14:26:11]] [SUCCESS] Screenshot refreshed
[[14:26:11]] [INFO] Refreshing screenshot...
[[14:26:11]] [INFO] XmAxcBtFI0=pass
[[14:26:07]] [SUCCESS] Screenshot refreshed successfully
[[14:26:07]] [SUCCESS] Screenshot refreshed successfully
[[14:26:07]] [INFO] XmAxcBtFI0=running
[[14:26:07]] [INFO] Executing action 14/576: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[14:26:06]] [SUCCESS] Screenshot refreshed
[[14:26:06]] [INFO] Refreshing screenshot...
[[14:26:06]] [INFO] ktAufkDJnF=pass
[[14:26:02]] [SUCCESS] Screenshot refreshed successfully
[[14:26:02]] [SUCCESS] Screenshot refreshed successfully
[[14:26:02]] [INFO] ktAufkDJnF=running
[[14:26:02]] [INFO] Executing action 13/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show")]
[[14:26:02]] [SUCCESS] Screenshot refreshed
[[14:26:02]] [INFO] Refreshing screenshot...
[[14:26:02]] [INFO] a50JhCx0ir=pass
[[14:25:58]] [SUCCESS] Screenshot refreshed successfully
[[14:25:58]] [SUCCESS] Screenshot refreshed successfully
[[14:25:58]] [INFO] a50JhCx0ir=running
[[14:25:58]] [INFO] Executing action 12/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[14:25:57]] [SUCCESS] Screenshot refreshed
[[14:25:57]] [INFO] Refreshing screenshot...
[[14:25:57]] [INFO] Y1O1clhMSJ=pass
[[14:25:53]] [SUCCESS] Screenshot refreshed successfully
[[14:25:53]] [SUCCESS] Screenshot refreshed successfully
[[14:25:53]] [INFO] Y1O1clhMSJ=running
[[14:25:53]] [INFO] Executing action 11/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[14:25:53]] [SUCCESS] Screenshot refreshed
[[14:25:53]] [INFO] Refreshing screenshot...
[[14:25:53]] [INFO] lYPskZt0Ya=pass
[[14:25:49]] [SUCCESS] Screenshot refreshed successfully
[[14:25:49]] [SUCCESS] Screenshot refreshed successfully
[[14:25:49]] [INFO] lYPskZt0Ya=running
[[14:25:49]] [INFO] Executing action 10/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[14:25:48]] [SUCCESS] Screenshot refreshed
[[14:25:48]] [INFO] Refreshing screenshot...
[[14:25:48]] [INFO] xUbWFa8Ok2=pass
[[14:25:44]] [SUCCESS] Screenshot refreshed successfully
[[14:25:44]] [SUCCESS] Screenshot refreshed successfully
[[14:25:44]] [INFO] xUbWFa8Ok2=running
[[14:25:44]] [INFO] Executing action 9/576: Tap on Text: "Latest"
[[14:25:43]] [SUCCESS] Screenshot refreshed
[[14:25:43]] [INFO] Refreshing screenshot...
[[14:25:43]] [INFO] RbNtEW6N9T=pass
[[14:25:39]] [INFO] Collapsed all test cases
[[14:25:39]] [SUCCESS] Screenshot refreshed successfully
[[14:25:39]] [SUCCESS] Screenshot refreshed successfully
[[14:25:38]] [INFO] RbNtEW6N9T=running
[[14:25:38]] [INFO] Executing action 8/576: Tap on Text: "Toys"
[[14:25:38]] [SUCCESS] Screenshot refreshed
[[14:25:38]] [INFO] Refreshing screenshot...
[[14:25:38]] [INFO] ltDXyWvtEz=pass
[[14:25:34]] [INFO] ltDXyWvtEz=running
[[14:25:34]] [INFO] Executing action 7/576: Tap on image: env[device-back-img]
[[14:25:34]] [SUCCESS] Screenshot refreshed successfully
[[14:25:34]] [SUCCESS] Screenshot refreshed successfully
[[14:25:33]] [SUCCESS] Screenshot refreshed
[[14:25:33]] [INFO] Refreshing screenshot...
[[14:25:33]] [INFO] QPKR6jUF9O=pass
[[14:25:30]] [SUCCESS] Screenshot refreshed successfully
[[14:25:30]] [SUCCESS] Screenshot refreshed successfully
[[14:25:30]] [INFO] QPKR6jUF9O=running
[[14:25:30]] [INFO] Executing action 6/576: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[14:25:30]] [SUCCESS] Screenshot refreshed
[[14:25:30]] [INFO] Refreshing screenshot...
[[14:25:30]] [INFO] vfwUVEyq6X=pass
[[14:25:27]] [SUCCESS] Screenshot refreshed successfully
[[14:25:27]] [SUCCESS] Screenshot refreshed successfully
[[14:25:27]] [INFO] vfwUVEyq6X=running
[[14:25:27]] [INFO] Executing action 5/576: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[14:25:26]] [SUCCESS] Screenshot refreshed
[[14:25:26]] [INFO] Refreshing screenshot...
[[14:25:26]] [INFO] Xr6F8gdd8q=pass
[[14:25:23]] [SUCCESS] Screenshot refreshed successfully
[[14:25:23]] [SUCCESS] Screenshot refreshed successfully
[[14:25:23]] [INFO] Xr6F8gdd8q=running
[[14:25:23]] [INFO] Executing action 4/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:25:22]] [SUCCESS] Screenshot refreshed
[[14:25:22]] [INFO] Refreshing screenshot...
[[14:25:22]] [INFO] Xr6F8gdd8q=pass
[[14:25:19]] [SUCCESS] Screenshot refreshed successfully
[[14:25:19]] [SUCCESS] Screenshot refreshed successfully
[[14:25:19]] [INFO] Xr6F8gdd8q=running
[[14:25:19]] [INFO] Executing action 3/576: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[14:25:18]] [SUCCESS] Screenshot refreshed
[[14:25:18]] [INFO] Refreshing screenshot...
[[14:25:18]] [INFO] F4NGh9HrLw=pass
[[14:25:15]] [SUCCESS] Screenshot refreshed successfully
[[14:25:15]] [SUCCESS] Screenshot refreshed successfully
[[14:25:14]] [INFO] F4NGh9HrLw=running
[[14:25:14]] [INFO] Executing action 2/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[14:25:14]] [SUCCESS] Screenshot refreshed
[[14:25:14]] [INFO] Refreshing screenshot...
[[14:25:14]] [INFO] H9fy9qcFbZ=pass
[[14:25:08]] [INFO] H9fy9qcFbZ=running
[[14:25:08]] [INFO] Executing action 1/576: Restart app: env[appid]
[[14:25:08]] [INFO] ExecutionManager: Starting execution of 576 actions...
[[14:25:08]] [SUCCESS] Cleared 1 screenshots from database
[[14:25:08]] [INFO] Clearing screenshots from database before execution...
[[14:25:08]] [SUCCESS] All screenshots deleted successfully
[[14:25:08]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[14:25:08]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250705_142508/screenshots
[[14:25:08]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250705_142508
[[14:25:08]] [SUCCESS] Report directory initialized successfully
[[14:25:08]] [INFO] Initializing report directory and screenshots folder for test suite...
[[14:25:04]] [SUCCESS] All screenshots deleted successfully
[[14:25:04]] [INFO] All actions cleared
[[14:25:04]] [INFO] Cleaning up screenshots...
[[14:24:49]] [SUCCESS] Screenshot refreshed successfully
[[14:24:48]] [SUCCESS] Screenshot refreshed
[[14:24:48]] [INFO] Refreshing screenshot...
[[14:24:47]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[14:24:47]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[14:24:41]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[14:24:39]] [SUCCESS] Found 1 device(s)
[[14:24:39]] [INFO] Refreshing device list...
