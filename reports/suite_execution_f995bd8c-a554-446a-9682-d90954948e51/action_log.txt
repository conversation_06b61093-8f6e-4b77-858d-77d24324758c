Action Log - 2025-07-05 18:31:04
================================================================================

[[18:31:04]] [INFO] Generating execution report...
[[18:31:04]] [SUCCESS] All tests passed successfully!
[[18:31:04]] [SUCCESS] Screenshot refreshed
[[18:31:04]] [INFO] Refreshing screenshot...
[[18:31:04]] [INFO] qPv5C4K0a2=pass
[[18:30:59]] [SUCCESS] Screenshot refreshed successfully
[[18:30:59]] [SUCCESS] Screenshot refreshed successfully
[[18:30:59]] [INFO] qPv5C4K0a2=running
[[18:30:59]] [INFO] Executing action 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:30:58]] [SUCCESS] Screenshot refreshed
[[18:30:58]] [INFO] Refreshing screenshot...
[[18:30:58]] [INFO] K7yV3GGsgr=pass
[[18:30:54]] [SUCCESS] Screenshot refreshed successfully
[[18:30:54]] [SUCCESS] Screenshot refreshed successfully
[[18:30:54]] [INFO] K7yV3GGsgr=running
[[18:30:54]] [INFO] Executing action 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:30:53]] [SUCCESS] Screenshot refreshed
[[18:30:53]] [INFO] Refreshing screenshot...
[[18:30:53]] [INFO] AtwFJEKbDH=pass
[[18:30:49]] [SUCCESS] Screenshot refreshed successfully
[[18:30:49]] [SUCCESS] Screenshot refreshed successfully
[[18:30:49]] [INFO] AtwFJEKbDH=running
[[18:30:49]] [INFO] Executing action 3/5: iOS Function: text - Text: "env[uname]"
[[18:30:48]] [SUCCESS] Screenshot refreshed
[[18:30:48]] [INFO] Refreshing screenshot...
[[18:30:48]] [INFO] RCYxT9YD8u=pass
[[18:30:46]] [SUCCESS] Screenshot refreshed successfully
[[18:30:46]] [SUCCESS] Screenshot refreshed successfully
[[18:30:44]] [INFO] RCYxT9YD8u=running
[[18:30:44]] [INFO] Executing action 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:30:43]] [SUCCESS] Screenshot refreshed
[[18:30:43]] [INFO] Refreshing screenshot...
[[18:30:43]] [INFO] EELcfo48Sh=pass
[[18:30:39]] [INFO] EELcfo48Sh=running
[[18:30:39]] [INFO] Executing action 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:30:39]] [INFO] ExecutionManager: Starting execution of 5 actions...
[[18:30:39]] [SUCCESS] Cleared 1 screenshots from database
[[18:30:39]] [INFO] Clearing screenshots from database before execution...
[[18:30:39]] [SUCCESS] All screenshots deleted successfully
[[18:30:39]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[18:30:39]] [INFO] Skipping report initialization - single test case execution
[[18:30:31]] [SUCCESS] All screenshots deleted successfully
[[18:30:31]] [SUCCESS] Loaded test case "Kmart-Signin" with 5 actions
[[18:30:31]] [SUCCESS] Added action: iosFunctions
[[18:30:31]] [SUCCESS] Added action: tap
[[18:30:31]] [SUCCESS] Added action: iosFunctions
[[18:30:31]] [SUCCESS] Added action: tap
[[18:30:31]] [SUCCESS] Added action: waitTill
[[18:30:31]] [INFO] All actions cleared
[[18:30:31]] [INFO] Cleaning up screenshots...
[[18:30:15]] [SUCCESS] Screenshot refreshed successfully
[[18:30:14]] [SUCCESS] Screenshot refreshed
[[18:30:14]] [INFO] Refreshing screenshot...
[[18:30:13]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[18:30:13]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[18:30:08]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[18:30:03]] [SUCCESS] Found 1 device(s)
[[18:30:02]] [INFO] Refreshing device list...
