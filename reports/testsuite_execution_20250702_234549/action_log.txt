Action Log - 2025-07-03 00:02:54
================================================================================

[[00:02:54]] [INFO] Generating execution report...
[[00:02:54]] [SUCCESS] All tests passed successfully!
[[00:02:54]] [SUCCESS] Screenshot refreshed
[[00:02:54]] [INFO] Refreshing screenshot...
[[00:02:54]] [INFO] xyHVihJMBi=pass
[[00:02:47]] [SUCCESS] Screenshot refreshed successfully
[[00:02:47]] [SUCCESS] Screenshot refreshed successfully
[[00:02:47]] [INFO] xyHVihJMBi=running
[[00:02:47]] [INFO] Executing action 50/50: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[00:02:47]] [SUCCESS] Screenshot refreshed
[[00:02:47]] [INFO] Refreshing screenshot...
[[00:02:47]] [INFO] mWeLQtXiL6=pass
[[00:02:42]] [SUCCESS] Screenshot refreshed successfully
[[00:02:42]] [SUCCESS] Screenshot refreshed successfully
[[00:02:41]] [INFO] mWeLQtXiL6=running
[[00:02:41]] [INFO] Executing action 49/50: Swipe from (50%, 70%) to (50%, 30%)
[[00:02:41]] [SUCCESS] Screenshot refreshed
[[00:02:41]] [INFO] Refreshing screenshot...
[[00:02:41]] [INFO] rkwVoJGZG4=pass
[[00:02:39]] [SUCCESS] Screenshot refreshed successfully
[[00:02:39]] [SUCCESS] Screenshot refreshed successfully
[[00:02:39]] [INFO] rkwVoJGZG4=running
[[00:02:39]] [INFO] Executing action 48/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[00:02:38]] [SUCCESS] Screenshot refreshed
[[00:02:38]] [INFO] Refreshing screenshot...
[[00:02:38]] [INFO] 0f2FSZYjWq=pass
[[00:02:35]] [SUCCESS] Screenshot refreshed successfully
[[00:02:35]] [SUCCESS] Screenshot refreshed successfully
[[00:02:09]] [INFO] 0f2FSZYjWq=running
[[00:02:09]] [INFO] Executing action 47/50: Check if element with text="3000" exists
[[00:02:09]] [SUCCESS] Screenshot refreshed
[[00:02:09]] [INFO] Refreshing screenshot...
[[00:02:09]] [INFO] Tebej51pT2=pass
[[00:02:07]] [SUCCESS] Screenshot refreshed successfully
[[00:02:07]] [SUCCESS] Screenshot refreshed successfully
[[00:02:06]] [INFO] Tebej51pT2=running
[[00:02:06]] [INFO] Executing action 46/50: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[00:02:06]] [SUCCESS] Screenshot refreshed
[[00:02:06]] [INFO] Refreshing screenshot...
[[00:02:06]] [INFO] eVytJrry9x=pass
[[00:02:04]] [SUCCESS] Screenshot refreshed successfully
[[00:02:04]] [SUCCESS] Screenshot refreshed successfully
[[00:02:04]] [INFO] eVytJrry9x=running
[[00:02:04]] [INFO] Executing action 45/50: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[00:02:03]] [SUCCESS] Screenshot refreshed
[[00:02:03]] [INFO] Refreshing screenshot...
[[00:02:03]] [INFO] s8h8VDUIOC=pass
[[00:01:55]] [SUCCESS] Screenshot refreshed successfully
[[00:01:55]] [SUCCESS] Screenshot refreshed successfully
[[00:01:55]] [INFO] s8h8VDUIOC=running
[[00:01:55]] [INFO] Executing action 44/50: Swipe from (50%, 70%) to (50%, 30%)
[[00:01:54]] [SUCCESS] Screenshot refreshed
[[00:01:54]] [INFO] Refreshing screenshot...
[[00:01:54]] [INFO] GYK47u1y3A=pass
[[00:01:52]] [SUCCESS] Screenshot refreshed successfully
[[00:01:52]] [SUCCESS] Screenshot refreshed successfully
[[00:01:52]] [INFO] GYK47u1y3A=running
[[00:01:52]] [INFO] Executing action 43/50: Android Function: send_key_event - Key Event: TAB
[[00:01:51]] [SUCCESS] Screenshot refreshed
[[00:01:51]] [INFO] Refreshing screenshot...
[[00:01:51]] [INFO] ZWpYNcpbFA=pass
[[00:01:47]] [SUCCESS] Screenshot refreshed successfully
[[00:01:47]] [SUCCESS] Screenshot refreshed successfully
[[00:01:46]] [INFO] ZWpYNcpbFA=running
[[00:01:46]] [INFO] Executing action 42/50: Tap on Text: "VIC"
[[00:01:46]] [SUCCESS] Screenshot refreshed
[[00:01:46]] [INFO] Refreshing screenshot...
[[00:01:46]] [INFO] QpBLC6BStn=pass
[[00:01:43]] [SUCCESS] Screenshot refreshed successfully
[[00:01:43]] [SUCCESS] Screenshot refreshed successfully
[[00:01:43]] [INFO] QpBLC6BStn=running
[[00:01:43]] [INFO] Executing action 41/50: textClear action
[[00:01:43]] [SUCCESS] Screenshot refreshed
[[00:01:43]] [INFO] Refreshing screenshot...
[[00:01:43]] [INFO] G4A3KBlXHq=pass
[[00:01:39]] [SUCCESS] Screenshot refreshed successfully
[[00:01:39]] [SUCCESS] Screenshot refreshed successfully
[[00:01:39]] [INFO] G4A3KBlXHq=running
[[00:01:39]] [INFO] Executing action 40/50: Tap on Text: "Nearby"
[[00:01:38]] [SUCCESS] Screenshot refreshed
[[00:01:38]] [INFO] Refreshing screenshot...
[[00:01:38]] [INFO] 3gJsiap2Ds=pass
[[00:01:35]] [SUCCESS] Screenshot refreshed successfully
[[00:01:35]] [SUCCESS] Screenshot refreshed successfully
[[00:01:35]] [INFO] 3gJsiap2Ds=running
[[00:01:35]] [INFO] Executing action 39/50: Tap on Text: "Collect"
[[00:01:34]] [SUCCESS] Screenshot refreshed
[[00:01:34]] [INFO] Refreshing screenshot...
[[00:01:34]] [INFO] qofJDqXBME=pass
[[00:01:28]] [SUCCESS] Screenshot refreshed successfully
[[00:01:28]] [SUCCESS] Screenshot refreshed successfully
[[00:01:28]] [INFO] qofJDqXBME=running
[[00:01:28]] [INFO] Executing action 38/50: Wait till text appears: "Delivery"
[[00:01:27]] [SUCCESS] Screenshot refreshed
[[00:01:27]] [INFO] Refreshing screenshot...
[[00:01:27]] [INFO] rkwVoJGZG4=pass
[[00:01:25]] [SUCCESS] Screenshot refreshed successfully
[[00:01:25]] [SUCCESS] Screenshot refreshed successfully
[[00:01:25]] [INFO] rkwVoJGZG4=running
[[00:01:25]] [INFO] Executing action 37/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[00:01:24]] [SUCCESS] Screenshot refreshed
[[00:01:24]] [INFO] Refreshing screenshot...
[[00:01:24]] [INFO] 94ikwhIEE2=pass
[[00:01:21]] [SUCCESS] Screenshot refreshed successfully
[[00:01:21]] [SUCCESS] Screenshot refreshed successfully
[[00:01:17]] [INFO] 94ikwhIEE2=running
[[00:01:17]] [INFO] Executing action 36/50: Tap on Text: "bag"
[[00:01:17]] [SUCCESS] Screenshot refreshed
[[00:01:17]] [INFO] Refreshing screenshot...
[[00:01:17]] [INFO] DfwaiVZ8Z9=pass
[[00:01:14]] [SUCCESS] Screenshot refreshed successfully
[[00:01:14]] [SUCCESS] Screenshot refreshed successfully
[[00:01:13]] [INFO] DfwaiVZ8Z9=running
[[00:01:13]] [INFO] Executing action 35/50: Swipe from (50%, 70%) to (50%, 50%)
[[00:01:13]] [SUCCESS] Screenshot refreshed
[[00:01:13]] [INFO] Refreshing screenshot...
[[00:01:13]] [INFO] eRCmRhc3re=pass
[[00:01:10]] [SUCCESS] Screenshot refreshed successfully
[[00:01:10]] [SUCCESS] Screenshot refreshed successfully
[[00:01:10]] [INFO] eRCmRhc3re=running
[[00:01:10]] [INFO] Executing action 34/50: Check if element with text="Broadway" exists
[[00:01:09]] [SUCCESS] Screenshot refreshed
[[00:01:09]] [INFO] Refreshing screenshot...
[[00:01:09]] [INFO] E2jpN7BioW=pass
[[00:01:07]] [SUCCESS] Screenshot refreshed successfully
[[00:01:07]] [SUCCESS] Screenshot refreshed successfully
[[00:01:07]] [INFO] E2jpN7BioW=running
[[00:01:07]] [INFO] Executing action 33/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[00:01:06]] [SUCCESS] Screenshot refreshed
[[00:01:06]] [INFO] Refreshing screenshot...
[[00:01:06]] [INFO] IOc0IwmLPQ=pass
[[00:01:05]] [SUCCESS] Screenshot refreshed successfully
[[00:01:05]] [SUCCESS] Screenshot refreshed successfully
[[00:01:04]] [INFO] IOc0IwmLPQ=running
[[00:01:04]] [INFO] Executing action 32/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[00:01:04]] [SUCCESS] Screenshot refreshed
[[00:01:04]] [INFO] Refreshing screenshot...
[[00:01:04]] [INFO] H0ODFz7sWJ=pass
[[00:00:45]] [SUCCESS] Screenshot refreshed successfully
[[00:00:45]] [SUCCESS] Screenshot refreshed successfully
[[00:00:45]] [INFO] H0ODFz7sWJ=running
[[00:00:45]] [INFO] Executing action 31/50: Tap on Text: "2000"
[[00:00:44]] [SUCCESS] Screenshot refreshed
[[00:00:44]] [INFO] Refreshing screenshot...
[[00:00:44]] [INFO] pldheRUBVi=pass
[[00:00:42]] [SUCCESS] Screenshot refreshed successfully
[[00:00:42]] [SUCCESS] Screenshot refreshed successfully
[[00:00:42]] [INFO] pldheRUBVi=running
[[00:00:42]] [INFO] Executing action 30/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[00:00:41]] [SUCCESS] Screenshot refreshed
[[00:00:41]] [INFO] Refreshing screenshot...
[[00:00:41]] [INFO] uZHvvAzVfx=pass
[[00:00:39]] [SUCCESS] Screenshot refreshed successfully
[[00:00:39]] [SUCCESS] Screenshot refreshed successfully
[[00:00:38]] [INFO] uZHvvAzVfx=running
[[00:00:38]] [INFO] Executing action 29/50: textClear action
[[00:00:38]] [SUCCESS] Screenshot refreshed
[[00:00:38]] [INFO] Refreshing screenshot...
[[00:00:38]] [INFO] WmNWcsWVHv=pass
[[00:00:11]] [SUCCESS] Screenshot refreshed successfully
[[00:00:11]] [SUCCESS] Screenshot refreshed successfully
[[00:00:11]] [INFO] WmNWcsWVHv=running
[[00:00:11]] [INFO] Executing action 28/50: Tap on Text: "4000"
[[00:00:10]] [SUCCESS] Screenshot refreshed
[[00:00:10]] [INFO] Refreshing screenshot...
[[00:00:10]] [INFO] lnjoz8hHUU=pass
[[00:00:04]] [SUCCESS] Screenshot refreshed successfully
[[00:00:04]] [SUCCESS] Screenshot refreshed successfully
[[00:00:03]] [INFO] lnjoz8hHUU=running
[[00:00:03]] [INFO] Executing action 27/50: Tap on Text: "Edit"
[[00:00:03]] [SUCCESS] Screenshot refreshed
[[00:00:03]] [INFO] Refreshing screenshot...
[[00:00:03]] [INFO] BQ7Cxm53HQ=pass
[[23:59:58]] [SUCCESS] Screenshot refreshed successfully
[[23:59:58]] [SUCCESS] Screenshot refreshed successfully
[[23:59:58]] [INFO] BQ7Cxm53HQ=running
[[23:59:58]] [INFO] Executing action 26/50: Wait till text appears: "UNO"
[[23:59:57]] [SUCCESS] Screenshot refreshed
[[23:59:57]] [INFO] Refreshing screenshot...
[[23:59:57]] [INFO] VkUKQbf1Qt=pass
[[23:59:52]] [SUCCESS] Screenshot refreshed successfully
[[23:59:52]] [SUCCESS] Screenshot refreshed successfully
[[23:59:52]] [INFO] VkUKQbf1Qt=running
[[23:59:52]] [INFO] Executing action 25/50: Tap on Text: "UNO"
[[23:59:51]] [SUCCESS] Screenshot refreshed
[[23:59:51]] [INFO] Refreshing screenshot...
[[23:59:51]] [INFO] 73NABkfWyY=pass
[[23:59:45]] [SUCCESS] Screenshot refreshed successfully
[[23:59:45]] [SUCCESS] Screenshot refreshed successfully
[[23:59:44]] [INFO] 73NABkfWyY=running
[[23:59:44]] [INFO] Executing action 24/50: Check if element with text="Toowong" exists
[[23:59:44]] [SUCCESS] Screenshot refreshed
[[23:59:44]] [INFO] Refreshing screenshot...
[[23:59:44]] [INFO] E2jpN7BioW=pass
[[23:59:43]] [SUCCESS] Screenshot refreshed successfully
[[23:59:43]] [SUCCESS] Screenshot refreshed successfully
[[23:59:41]] [INFO] E2jpN7BioW=running
[[23:59:41]] [INFO] Executing action 23/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[23:59:41]] [SUCCESS] Screenshot refreshed
[[23:59:41]] [INFO] Refreshing screenshot...
[[23:59:41]] [INFO] IOc0IwmLPQ=pass
[[23:59:39]] [SUCCESS] Screenshot refreshed successfully
[[23:59:39]] [SUCCESS] Screenshot refreshed successfully
[[23:59:39]] [INFO] IOc0IwmLPQ=running
[[23:59:39]] [INFO] Executing action 22/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[23:59:38]] [SUCCESS] Screenshot refreshed
[[23:59:38]] [INFO] Refreshing screenshot...
[[23:59:38]] [INFO] VkUKQbf1Qt=pass
[[23:59:13]] [SUCCESS] Screenshot refreshed successfully
[[23:59:13]] [SUCCESS] Screenshot refreshed successfully
[[23:59:12]] [INFO] VkUKQbf1Qt=running
[[23:59:12]] [INFO] Executing action 21/50: Tap on Text: "CITY"
[[23:59:11]] [SUCCESS] Screenshot refreshed
[[23:59:11]] [INFO] Refreshing screenshot...
[[23:59:11]] [INFO] pldheRUBVi=pass
[[23:59:09]] [SUCCESS] Screenshot refreshed successfully
[[23:59:09]] [SUCCESS] Screenshot refreshed successfully
[[23:59:09]] [INFO] pldheRUBVi=running
[[23:59:09]] [INFO] Executing action 20/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[23:59:09]] [SUCCESS] Screenshot refreshed
[[23:59:09]] [INFO] Refreshing screenshot...
[[23:59:09]] [INFO] kbdEPCPYod=pass
[[23:59:05]] [SUCCESS] Screenshot refreshed successfully
[[23:59:05]] [SUCCESS] Screenshot refreshed successfully
[[23:59:04]] [INFO] kbdEPCPYod=running
[[23:59:04]] [INFO] Executing action 19/50: textClear action
[[23:59:04]] [SUCCESS] Screenshot refreshed
[[23:59:04]] [INFO] Refreshing screenshot...
[[23:59:04]] [INFO] pldheRUBVi=pass
[[23:58:46]] [SUCCESS] Screenshot refreshed successfully
[[23:58:46]] [SUCCESS] Screenshot refreshed successfully
[[23:58:46]] [INFO] pldheRUBVi=running
[[23:58:46]] [INFO] Executing action 18/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[23:58:45]] [SUCCESS] Screenshot refreshed
[[23:58:45]] [INFO] Refreshing screenshot...
[[23:58:45]] [INFO] YhLhTn3Wtm=pass
[[23:58:39]] [SUCCESS] Screenshot refreshed successfully
[[23:58:39]] [SUCCESS] Screenshot refreshed successfully
[[23:58:38]] [INFO] YhLhTn3Wtm=running
[[23:58:38]] [INFO] Executing action 17/50: Wait for 5 ms
[[23:58:38]] [SUCCESS] Screenshot refreshed
[[23:58:38]] [INFO] Refreshing screenshot...
[[23:58:38]] [INFO] VkUKQbf1Qt=pass
[[23:58:25]] [SUCCESS] Screenshot refreshed successfully
[[23:58:25]] [SUCCESS] Screenshot refreshed successfully
[[23:58:24]] [INFO] VkUKQbf1Qt=running
[[23:58:24]] [INFO] Executing action 16/50: Tap on Text: "Edit"
[[23:58:24]] [SUCCESS] Screenshot refreshed
[[23:58:24]] [INFO] Refreshing screenshot...
[[23:58:24]] [INFO] BQ7Cxm53HQ=pass
[[23:58:20]] [SUCCESS] Screenshot refreshed successfully
[[23:58:20]] [SUCCESS] Screenshot refreshed successfully
[[23:58:20]] [INFO] BQ7Cxm53HQ=running
[[23:58:20]] [INFO] Executing action 15/50: Wait till text appears: "UNO"
[[23:58:19]] [SUCCESS] Screenshot refreshed
[[23:58:19]] [INFO] Refreshing screenshot...
[[23:58:19]] [INFO] IupxLP2Jsr=pass
[[23:58:17]] [SUCCESS] Screenshot refreshed successfully
[[23:58:17]] [SUCCESS] Screenshot refreshed successfully
[[23:58:17]] [INFO] IupxLP2Jsr=running
[[23:58:17]] [INFO] Executing action 14/50: Input text: "P_6225544"
[[23:58:17]] [SUCCESS] Screenshot refreshed
[[23:58:17]] [INFO] Refreshing screenshot...
[[23:58:17]] [INFO] 70iOOakiG7=pass
[[23:58:10]] [SUCCESS] Screenshot refreshed successfully
[[23:58:10]] [SUCCESS] Screenshot refreshed successfully
[[23:58:09]] [INFO] 70iOOakiG7=running
[[23:58:09]] [INFO] Executing action 13/50: Tap on Text: "Find"
[[23:58:08]] [SUCCESS] Screenshot refreshed
[[23:58:08]] [INFO] Refreshing screenshot...
[[23:58:08]] [INFO] E2jpN7BioW=pass
[[23:58:06]] [SUCCESS] Screenshot refreshed successfully
[[23:58:06]] [SUCCESS] Screenshot refreshed successfully
[[23:58:06]] [INFO] E2jpN7BioW=running
[[23:58:06]] [INFO] Executing action 12/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[23:58:05]] [SUCCESS] Screenshot refreshed
[[23:58:05]] [INFO] Refreshing screenshot...
[[23:58:05]] [INFO] kDnmoQJG4o=pass
[[23:58:03]] [SUCCESS] Screenshot refreshed successfully
[[23:58:03]] [SUCCESS] Screenshot refreshed successfully
[[23:58:03]] [INFO] kDnmoQJG4o=running
[[23:58:03]] [INFO] Executing action 11/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[23:58:03]] [SUCCESS] Screenshot refreshed
[[23:58:03]] [INFO] Refreshing screenshot...
[[23:58:03]] [INFO] mw9GQ4mzRE=pass
[[23:57:53]] [SUCCESS] Screenshot refreshed successfully
[[23:57:53]] [SUCCESS] Screenshot refreshed successfully
[[23:57:51]] [INFO] mw9GQ4mzRE=running
[[23:57:51]] [INFO] Executing action 10/50: Tap on Text: "BC"
[[23:57:50]] [SUCCESS] Screenshot refreshed
[[23:57:50]] [INFO] Refreshing screenshot...
[[23:57:50]] [INFO] pldheRUBVi=pass
[[23:57:47]] [SUCCESS] Screenshot refreshed successfully
[[23:57:47]] [SUCCESS] Screenshot refreshed successfully
[[23:57:47]] [INFO] pldheRUBVi=running
[[23:57:47]] [INFO] Executing action 9/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[23:57:47]] [SUCCESS] Screenshot refreshed
[[23:57:47]] [INFO] Refreshing screenshot...
[[23:57:47]] [INFO] kbdEPCPYod=pass
[[23:57:43]] [SUCCESS] Screenshot refreshed successfully
[[23:57:43]] [SUCCESS] Screenshot refreshed successfully
[[23:57:43]] [INFO] kbdEPCPYod=running
[[23:57:43]] [INFO] Executing action 8/50: textClear action
[[23:57:42]] [SUCCESS] Screenshot refreshed
[[23:57:42]] [INFO] Refreshing screenshot...
[[23:57:42]] [INFO] pldheRUBVi=pass
[[23:57:39]] [SUCCESS] Screenshot refreshed successfully
[[23:57:39]] [SUCCESS] Screenshot refreshed successfully
[[23:57:39]] [INFO] pldheRUBVi=running
[[23:57:39]] [INFO] Executing action 7/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[23:57:39]] [SUCCESS] Screenshot refreshed
[[23:57:39]] [INFO] Refreshing screenshot...
[[23:57:39]] [INFO] QMXBlswP6H=pass
[[23:57:06]] [SUCCESS] Screenshot refreshed successfully
[[23:57:06]] [SUCCESS] Screenshot refreshed successfully
[[23:57:06]] [INFO] QMXBlswP6H=running
[[23:57:06]] [INFO] Executing action 6/50: Tap on Text: "Edit"
[[23:57:05]] [SUCCESS] Screenshot refreshed
[[23:57:05]] [INFO] Refreshing screenshot...
[[23:57:05]] [INFO] RLz6vQo3ag=pass
[[23:57:02]] [SUCCESS] Screenshot refreshed successfully
[[23:57:02]] [SUCCESS] Screenshot refreshed successfully
[[23:56:49]] [INFO] RLz6vQo3ag=running
[[23:56:49]] [INFO] Executing action 5/50: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[23:56:49]] [SUCCESS] Screenshot refreshed
[[23:56:49]] [INFO] Refreshing screenshot...
[[23:56:48]] [SUCCESS] Screenshot refreshed
[[23:56:48]] [INFO] Refreshing screenshot...
[[23:56:46]] [SUCCESS] Screenshot refreshed successfully
[[23:56:46]] [SUCCESS] Screenshot refreshed successfully
[[23:56:46]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[23:56:46]] [SUCCESS] Screenshot refreshed
[[23:56:46]] [INFO] Refreshing screenshot...
[[23:56:44]] [SUCCESS] Screenshot refreshed successfully
[[23:56:44]] [SUCCESS] Screenshot refreshed successfully
[[23:56:43]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[23:56:43]] [SUCCESS] Screenshot refreshed
[[23:56:43]] [INFO] Refreshing screenshot...
[[23:56:40]] [SUCCESS] Screenshot refreshed successfully
[[23:56:40]] [SUCCESS] Screenshot refreshed successfully
[[23:56:40]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[23:56:40]] [SUCCESS] Screenshot refreshed
[[23:56:40]] [INFO] Refreshing screenshot...
[[23:56:38]] [SUCCESS] Screenshot refreshed successfully
[[23:56:38]] [SUCCESS] Screenshot refreshed successfully
[[23:56:37]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[23:56:37]] [SUCCESS] Screenshot refreshed
[[23:56:37]] [INFO] Refreshing screenshot...
[[23:56:09]] [SUCCESS] Screenshot refreshed successfully
[[23:56:09]] [SUCCESS] Screenshot refreshed successfully
[[23:56:08]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[23:56:08]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[23:56:08]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[23:56:08]] [INFO] xz8njynjpZ=running
[[23:56:08]] [INFO] Executing action 4/50: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[23:56:08]] [SUCCESS] Screenshot refreshed
[[23:56:08]] [INFO] Refreshing screenshot...
[[23:56:08]] [INFO] J9loj6Zl5K=pass
[[23:56:06]] [SUCCESS] Screenshot refreshed successfully
[[23:56:06]] [SUCCESS] Screenshot refreshed successfully
[[23:56:06]] [INFO] J9loj6Zl5K=running
[[23:56:06]] [INFO] Executing action 3/50: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[23:56:05]] [SUCCESS] Screenshot refreshed
[[23:56:05]] [INFO] Refreshing screenshot...
[[23:56:05]] [INFO] Y8vz7AJD1i=pass
[[23:55:59]] [SUCCESS] Screenshot refreshed successfully
[[23:55:59]] [SUCCESS] Screenshot refreshed successfully
[[23:55:59]] [INFO] Y8vz7AJD1i=running
[[23:55:59]] [INFO] Executing action 2/50: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[23:55:58]] [SUCCESS] Screenshot refreshed
[[23:55:58]] [INFO] Refreshing screenshot...
[[23:55:58]] [INFO] H9fy9qcFbZ=pass
[[23:55:55]] [INFO] H9fy9qcFbZ=running
[[23:55:55]] [INFO] Executing action 1/50: Launch app: au.com.kmart
[[23:55:55]] [INFO] ExecutionManager: Starting execution of 50 actions...
[[23:55:55]] [SUCCESS] Cleared 1 screenshots from database
[[23:55:55]] [INFO] Clearing screenshots from database before execution...
[[23:55:55]] [SUCCESS] All screenshots deleted successfully
[[23:55:55]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[23:55:55]] [INFO] Skipping report initialization - single test case execution
[[23:55:47]] [SUCCESS] All screenshots deleted successfully
[[23:55:47]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 50 actions
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: swipe
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: exists
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: swipe
[[23:55:47]] [SUCCESS] Added action: androidFunctions
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: textClear
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: waitTill
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: swipe
[[23:55:47]] [SUCCESS] Added action: exists
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: waitTill
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: textClear
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: waitTill
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: exists
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: waitTill
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: textClear
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: wait
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: waitTill
[[23:55:47]] [SUCCESS] Added action: text
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: waitTill
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: textClear
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: tapOnText
[[23:55:47]] [SUCCESS] Added action: waitTill
[[23:55:47]] [SUCCESS] Added action: multiStep
[[23:55:47]] [SUCCESS] Added action: waitTill
[[23:55:47]] [SUCCESS] Added action: tap
[[23:55:47]] [SUCCESS] Added action: launchApp
[[23:55:47]] [INFO] All actions cleared
[[23:55:47]] [INFO] Cleaning up screenshots...
[[23:55:41]] [SUCCESS] Screenshot refreshed successfully
[[23:55:40]] [SUCCESS] Screenshot refreshed
[[23:55:40]] [INFO] Refreshing screenshot...
[[23:55:39]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[23:55:39]] [INFO] Device info updated: RMX2151
[[23:55:26]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[23:55:24]] [SUCCESS] Found 1 device(s)
[[23:55:24]] [INFO] Refreshing device list...
