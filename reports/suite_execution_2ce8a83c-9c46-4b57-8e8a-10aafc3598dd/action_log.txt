Action Log - 2025-07-05 21:23:37
================================================================================

[[21:23:37]] [INFO] Generating execution report...
[[21:23:37]] [SUCCESS] All tests passed successfully!
[[21:23:37]] [SUCCESS] Screenshot refreshed
[[21:23:37]] [INFO] Refreshing screenshot...
[[21:23:36]] [SUCCESS] Screenshot refreshed
[[21:23:36]] [INFO] Refreshing screenshot...
[[21:23:33]] [SUCCESS] Screenshot refreshed successfully
[[21:23:33]] [SUCCESS] Screenshot refreshed successfully
[[21:23:33]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[21:23:32]] [SUCCESS] Screenshot refreshed
[[21:23:32]] [INFO] Refreshing screenshot...
[[21:23:20]] [SUCCESS] Screenshot refreshed successfully
[[21:23:20]] [SUCCESS] Screenshot refreshed successfully
[[21:23:20]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[21:23:19]] [SUCCESS] Screenshot refreshed
[[21:23:19]] [INFO] Refreshing screenshot...
[[21:23:16]] [SUCCESS] Screenshot refreshed successfully
[[21:23:16]] [SUCCESS] Screenshot refreshed successfully
[[21:23:16]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[21:23:15]] [SUCCESS] Screenshot refreshed
[[21:23:15]] [INFO] Refreshing screenshot...
[[21:23:12]] [SUCCESS] Screenshot refreshed successfully
[[21:23:12]] [SUCCESS] Screenshot refreshed successfully
[[21:23:11]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[21:23:11]] [SUCCESS] Screenshot refreshed
[[21:23:11]] [INFO] Refreshing screenshot...
[[21:23:04]] [SUCCESS] Screenshot refreshed successfully
[[21:23:04]] [SUCCESS] Screenshot refreshed successfully
[[21:23:03]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[21:23:03]] [SUCCESS] Screenshot refreshed
[[21:23:03]] [INFO] Refreshing screenshot...
[[21:22:58]] [SUCCESS] Screenshot refreshed successfully
[[21:22:58]] [SUCCESS] Screenshot refreshed successfully
[[21:22:58]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[21:22:58]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[21:22:58]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[21:22:58]] [INFO] IR7wnjW7C8=running
[[21:22:58]] [INFO] Executing action 98/98: cleanupSteps action
[[21:22:57]] [SUCCESS] Screenshot refreshed
[[21:22:57]] [INFO] Refreshing screenshot...
[[21:22:57]] [INFO] 7WYExJTqjp=pass
[[21:22:53]] [SUCCESS] Screenshot refreshed successfully
[[21:22:53]] [SUCCESS] Screenshot refreshed successfully
[[21:22:53]] [INFO] 7WYExJTqjp=running
[[21:22:53]] [INFO] Executing action 97/98: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[21:22:52]] [SUCCESS] Screenshot refreshed
[[21:22:52]] [INFO] Refreshing screenshot...
[[21:22:52]] [INFO] 4WfPFN961S=pass
[[21:22:45]] [SUCCESS] Screenshot refreshed successfully
[[21:22:45]] [SUCCESS] Screenshot refreshed successfully
[[21:22:44]] [INFO] 4WfPFN961S=running
[[21:22:44]] [INFO] Executing action 96/98: Swipe from (50%, 70%) to (50%, 30%)
[[21:22:44]] [SUCCESS] Screenshot refreshed
[[21:22:44]] [INFO] Refreshing screenshot...
[[21:22:44]] [INFO] NurQsFoMkE=pass
[[21:22:40]] [SUCCESS] Screenshot refreshed successfully
[[21:22:40]] [SUCCESS] Screenshot refreshed successfully
[[21:22:40]] [INFO] NurQsFoMkE=running
[[21:22:40]] [INFO] Executing action 95/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[21:22:39]] [SUCCESS] Screenshot refreshed
[[21:22:39]] [INFO] Refreshing screenshot...
[[21:22:39]] [INFO] CkfAScJNq8=pass
[[21:22:35]] [INFO] CkfAScJNq8=running
[[21:22:35]] [INFO] Executing action 94/98: Tap on image: env[closebtnimage]
[[21:22:35]] [SUCCESS] Screenshot refreshed successfully
[[21:22:35]] [SUCCESS] Screenshot refreshed successfully
[[21:22:35]] [SUCCESS] Screenshot refreshed
[[21:22:35]] [INFO] Refreshing screenshot...
[[21:22:35]] [INFO] 1NWfFsDiTQ=pass
[[21:22:31]] [SUCCESS] Screenshot refreshed successfully
[[21:22:31]] [SUCCESS] Screenshot refreshed successfully
[[21:22:31]] [INFO] 1NWfFsDiTQ=running
[[21:22:31]] [INFO] Executing action 93/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[21:22:30]] [SUCCESS] Screenshot refreshed
[[21:22:30]] [INFO] Refreshing screenshot...
[[21:22:30]] [INFO] tufIibCj03=pass
[[21:22:26]] [SUCCESS] Screenshot refreshed successfully
[[21:22:26]] [SUCCESS] Screenshot refreshed successfully
[[21:22:26]] [INFO] tufIibCj03=running
[[21:22:26]] [INFO] Executing action 92/98: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[21:22:26]] [SUCCESS] Screenshot refreshed
[[21:22:26]] [INFO] Refreshing screenshot...
[[21:22:26]] [INFO] XryN8qR1DX=pass
[[21:22:21]] [SUCCESS] Screenshot refreshed successfully
[[21:22:21]] [SUCCESS] Screenshot refreshed successfully
[[21:22:21]] [INFO] XryN8qR1DX=running
[[21:22:21]] [INFO] Executing action 91/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[21:22:20]] [SUCCESS] Screenshot refreshed
[[21:22:20]] [INFO] Refreshing screenshot...
[[21:22:20]] [INFO] CkfAScJNq8=pass
[[21:22:16]] [SUCCESS] Screenshot refreshed successfully
[[21:22:16]] [SUCCESS] Screenshot refreshed successfully
[[21:22:16]] [INFO] CkfAScJNq8=running
[[21:22:16]] [INFO] Executing action 90/98: Tap on image: env[closebtnimage]
[[21:22:16]] [SUCCESS] Screenshot refreshed
[[21:22:16]] [INFO] Refreshing screenshot...
[[21:22:16]] [SUCCESS] Screenshot refreshed successfully
[[21:22:16]] [SUCCESS] Screenshot refreshed successfully
[[21:22:15]] [SUCCESS] Screenshot refreshed
[[21:22:15]] [INFO] Refreshing screenshot...
[[21:22:11]] [SUCCESS] Screenshot refreshed successfully
[[21:22:11]] [SUCCESS] Screenshot refreshed successfully
[[21:22:11]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[21:22:10]] [SUCCESS] Screenshot refreshed
[[21:22:10]] [INFO] Refreshing screenshot...
[[21:22:06]] [SUCCESS] Screenshot refreshed successfully
[[21:22:06]] [SUCCESS] Screenshot refreshed successfully
[[21:22:06]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[21:22:06]] [SUCCESS] Screenshot refreshed
[[21:22:06]] [INFO] Refreshing screenshot...
[[21:22:01]] [SUCCESS] Screenshot refreshed successfully
[[21:22:01]] [SUCCESS] Screenshot refreshed successfully
[[21:22:01]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[21:22:00]] [SUCCESS] Screenshot refreshed
[[21:22:00]] [INFO] Refreshing screenshot...
[[21:21:56]] [SUCCESS] Screenshot refreshed successfully
[[21:21:56]] [SUCCESS] Screenshot refreshed successfully
[[21:21:56]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[21:21:56]] [SUCCESS] Screenshot refreshed
[[21:21:56]] [INFO] Refreshing screenshot...
[[21:21:51]] [SUCCESS] Screenshot refreshed successfully
[[21:21:51]] [SUCCESS] Screenshot refreshed successfully
[[21:21:51]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[21:21:51]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[21:21:51]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[21:21:51]] [INFO] mWOCt0aAWW=running
[[21:21:51]] [INFO] Executing action 89/98: Execute Test Case: Kmart-Signin (5 steps)
[[21:21:51]] [SUCCESS] Screenshot refreshed
[[21:21:51]] [INFO] Refreshing screenshot...
[[21:21:51]] [INFO] q9ZiyYoE5B=pass
[[21:21:48]] [SUCCESS] Screenshot refreshed successfully
[[21:21:48]] [SUCCESS] Screenshot refreshed successfully
[[21:21:48]] [INFO] q9ZiyYoE5B=running
[[21:21:48]] [INFO] Executing action 88/98: iOS Function: alert_accept
[[21:21:47]] [SUCCESS] Screenshot refreshed
[[21:21:47]] [INFO] Refreshing screenshot...
[[21:21:47]] [INFO] STEdg5jOU8=pass
[[21:21:43]] [SUCCESS] Screenshot refreshed successfully
[[21:21:43]] [SUCCESS] Screenshot refreshed successfully
[[21:21:43]] [INFO] STEdg5jOU8=running
[[21:21:43]] [INFO] Executing action 87/98: Tap on Text: "in"
[[21:21:42]] [SUCCESS] Screenshot refreshed
[[21:21:42]] [INFO] Refreshing screenshot...
[[21:21:42]] [INFO] LDH2hlTZT9=pass
[[21:21:36]] [SUCCESS] Screenshot refreshed successfully
[[21:21:36]] [SUCCESS] Screenshot refreshed successfully
[[21:21:35]] [INFO] LDH2hlTZT9=running
[[21:21:35]] [INFO] Executing action 86/98: Wait for 5 ms
[[21:21:35]] [SUCCESS] Screenshot refreshed
[[21:21:35]] [INFO] Refreshing screenshot...
[[21:21:35]] [INFO] 5Dk9h5bQWl=pass
[[21:21:29]] [SUCCESS] Screenshot refreshed successfully
[[21:21:29]] [SUCCESS] Screenshot refreshed successfully
[[21:21:29]] [INFO] 5Dk9h5bQWl=running
[[21:21:29]] [INFO] Executing action 85/98: Tap on element with accessibility_id: Continue to details
[[21:21:29]] [SUCCESS] Screenshot refreshed
[[21:21:29]] [INFO] Refreshing screenshot...
[[21:21:29]] [INFO] VMzFZ2uTwl=pass
[[21:21:21]] [SUCCESS] Screenshot refreshed successfully
[[21:21:21]] [SUCCESS] Screenshot refreshed successfully
[[21:21:20]] [INFO] VMzFZ2uTwl=running
[[21:21:20]] [INFO] Executing action 84/98: Swipe up till element accessibilityid: "Continue to details" is visible
[[21:21:20]] [SUCCESS] Screenshot refreshed
[[21:21:20]] [INFO] Refreshing screenshot...
[[21:21:19]] [SUCCESS] Screenshot refreshed
[[21:21:19]] [INFO] Refreshing screenshot...
[[21:21:15]] [INFO] Executing Multi Step action step 6/6: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[21:21:15]] [SUCCESS] Screenshot refreshed successfully
[[21:21:15]] [SUCCESS] Screenshot refreshed successfully
[[21:21:15]] [SUCCESS] Screenshot refreshed
[[21:21:15]] [INFO] Refreshing screenshot...
[[21:21:11]] [SUCCESS] Screenshot refreshed successfully
[[21:21:11]] [SUCCESS] Screenshot refreshed successfully
[[21:21:11]] [INFO] Executing Multi Step action step 5/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[21:21:10]] [SUCCESS] Screenshot refreshed
[[21:21:10]] [INFO] Refreshing screenshot...
[[21:21:06]] [SUCCESS] Screenshot refreshed successfully
[[21:21:06]] [SUCCESS] Screenshot refreshed successfully
[[21:21:06]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[21:21:05]] [SUCCESS] Screenshot refreshed
[[21:21:05]] [INFO] Refreshing screenshot...
[[21:21:02]] [SUCCESS] Screenshot refreshed successfully
[[21:21:02]] [SUCCESS] Screenshot refreshed successfully
[[21:21:01]] [INFO] Executing Multi Step action step 3/6: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[21:21:01]] [SUCCESS] Screenshot refreshed
[[21:21:01]] [INFO] Refreshing screenshot...
[[21:20:56]] [SUCCESS] Screenshot refreshed successfully
[[21:20:56]] [SUCCESS] Screenshot refreshed successfully
[[21:20:56]] [INFO] Executing Multi Step action step 2/6: iOS Function: text - Text: "Notebook"
[[21:20:56]] [SUCCESS] Screenshot refreshed
[[21:20:56]] [INFO] Refreshing screenshot...
[[21:20:50]] [SUCCESS] Screenshot refreshed successfully
[[21:20:50]] [SUCCESS] Screenshot refreshed successfully
[[21:20:50]] [INFO] Executing Multi Step action step 1/6: Tap on Text: "Find"
[[21:20:50]] [INFO] Loaded 6 steps from test case: Search and Add (Notebooks)
[[21:20:50]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[21:20:50]] [INFO] 8HTspxuvVG=running
[[21:20:50]] [INFO] Executing action 83/98: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[21:20:49]] [SUCCESS] Screenshot refreshed
[[21:20:49]] [INFO] Refreshing screenshot...
[[21:20:49]] [INFO] NurQsFoMkE=pass
[[21:20:45]] [SUCCESS] Screenshot refreshed successfully
[[21:20:45]] [SUCCESS] Screenshot refreshed successfully
[[21:20:44]] [INFO] NurQsFoMkE=running
[[21:20:44]] [INFO] Executing action 82/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[21:20:44]] [SUCCESS] Screenshot refreshed
[[21:20:44]] [INFO] Refreshing screenshot...
[[21:20:44]] [INFO] 7QpmNS6hif=pass
[[21:20:39]] [SUCCESS] Screenshot refreshed successfully
[[21:20:39]] [SUCCESS] Screenshot refreshed successfully
[[21:20:38]] [INFO] 7QpmNS6hif=running
[[21:20:38]] [INFO] Executing action 81/98: Restart app: env[appid]
[[21:20:38]] [SUCCESS] Screenshot refreshed
[[21:20:38]] [INFO] Refreshing screenshot...
[[21:20:38]] [INFO] 7WYExJTqjp=pass
[[21:20:33]] [SUCCESS] Screenshot refreshed successfully
[[21:20:33]] [SUCCESS] Screenshot refreshed successfully
[[21:20:33]] [INFO] 7WYExJTqjp=running
[[21:20:33]] [INFO] Executing action 80/98: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[21:20:33]] [SUCCESS] Screenshot refreshed
[[21:20:33]] [INFO] Refreshing screenshot...
[[21:20:33]] [INFO] 4WfPFN961S=pass
[[21:20:26]] [SUCCESS] Screenshot refreshed successfully
[[21:20:26]] [SUCCESS] Screenshot refreshed successfully
[[21:20:26]] [INFO] 4WfPFN961S=running
[[21:20:26]] [INFO] Executing action 79/98: Swipe from (50%, 70%) to (50%, 30%)
[[21:20:25]] [SUCCESS] Screenshot refreshed
[[21:20:25]] [INFO] Refreshing screenshot...
[[21:20:25]] [INFO] NurQsFoMkE=pass
[[21:20:22]] [SUCCESS] Screenshot refreshed successfully
[[21:20:22]] [SUCCESS] Screenshot refreshed successfully
[[21:20:21]] [INFO] NurQsFoMkE=running
[[21:20:21]] [INFO] Executing action 78/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[21:20:21]] [SUCCESS] Screenshot refreshed
[[21:20:21]] [INFO] Refreshing screenshot...
[[21:20:21]] [INFO] CkfAScJNq8=pass
[[21:20:16]] [INFO] CkfAScJNq8=running
[[21:20:16]] [INFO] Executing action 77/98: Tap on image: env[closebtnimage]
[[21:20:16]] [SUCCESS] Screenshot refreshed successfully
[[21:20:16]] [SUCCESS] Screenshot refreshed successfully
[[21:20:16]] [SUCCESS] Screenshot refreshed
[[21:20:16]] [INFO] Refreshing screenshot...
[[21:20:16]] [INFO] 1NWfFsDiTQ=pass
[[21:20:12]] [SUCCESS] Screenshot refreshed successfully
[[21:20:12]] [SUCCESS] Screenshot refreshed successfully
[[21:20:12]] [INFO] 1NWfFsDiTQ=running
[[21:20:12]] [INFO] Executing action 76/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[21:20:11]] [SUCCESS] Screenshot refreshed
[[21:20:11]] [INFO] Refreshing screenshot...
[[21:20:11]] [INFO] tufIibCj03=pass
[[21:20:08]] [SUCCESS] Screenshot refreshed successfully
[[21:20:08]] [SUCCESS] Screenshot refreshed successfully
[[21:20:07]] [INFO] tufIibCj03=running
[[21:20:07]] [INFO] Executing action 75/98: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[21:20:07]] [SUCCESS] Screenshot refreshed
[[21:20:07]] [INFO] Refreshing screenshot...
[[21:20:07]] [INFO] g8u66qfKkX=pass
[[21:20:03]] [INFO] g8u66qfKkX=running
[[21:20:03]] [INFO] Executing action 74/98: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[21:20:03]] [SUCCESS] Screenshot refreshed successfully
[[21:20:03]] [SUCCESS] Screenshot refreshed successfully
[[21:20:03]] [SUCCESS] Screenshot refreshed
[[21:20:03]] [INFO] Refreshing screenshot...
[[21:20:03]] [INFO] XryN8qR1DX=pass
[[21:19:59]] [SUCCESS] Screenshot refreshed successfully
[[21:19:59]] [SUCCESS] Screenshot refreshed successfully
[[21:19:59]] [INFO] XryN8qR1DX=running
[[21:19:59]] [INFO] Executing action 73/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[21:19:58]] [SUCCESS] Screenshot refreshed
[[21:19:58]] [INFO] Refreshing screenshot...
[[21:19:58]] [INFO] CkfAScJNq8=pass
[[21:19:54]] [SUCCESS] Screenshot refreshed successfully
[[21:19:54]] [SUCCESS] Screenshot refreshed successfully
[[21:19:54]] [INFO] CkfAScJNq8=running
[[21:19:54]] [INFO] Executing action 72/98: Tap on image: env[closebtnimage]
[[21:19:53]] [SUCCESS] Screenshot refreshed
[[21:19:53]] [INFO] Refreshing screenshot...
[[21:19:53]] [INFO] g8u66qfKkX=pass
[[21:19:47]] [INFO] g8u66qfKkX=running
[[21:19:47]] [INFO] Executing action 71/98: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[21:19:47]] [SUCCESS] Screenshot refreshed successfully
[[21:19:47]] [SUCCESS] Screenshot refreshed successfully
[[21:19:46]] [SUCCESS] Screenshot refreshed
[[21:19:46]] [INFO] Refreshing screenshot...
[[21:19:46]] [INFO] pCPTAtSZbf=pass
[[21:19:42]] [SUCCESS] Screenshot refreshed successfully
[[21:19:42]] [SUCCESS] Screenshot refreshed successfully
[[21:19:42]] [INFO] pCPTAtSZbf=running
[[21:19:42]] [INFO] Executing action 70/98: iOS Function: text - Text: "Wonderbaby@5"
[[21:19:41]] [SUCCESS] Screenshot refreshed
[[21:19:41]] [INFO] Refreshing screenshot...
[[21:19:41]] [INFO] DaVBARRwft=pass
[[21:19:37]] [SUCCESS] Screenshot refreshed successfully
[[21:19:37]] [SUCCESS] Screenshot refreshed successfully
[[21:19:37]] [INFO] DaVBARRwft=running
[[21:19:37]] [INFO] Executing action 69/98: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[21:19:37]] [SUCCESS] Screenshot refreshed
[[21:19:37]] [INFO] Refreshing screenshot...
[[21:19:37]] [INFO] e1RoZWCZJb=pass
[[21:19:32]] [SUCCESS] Screenshot refreshed successfully
[[21:19:32]] [SUCCESS] Screenshot refreshed successfully
[[21:19:32]] [INFO] e1RoZWCZJb=running
[[21:19:32]] [INFO] Executing action 68/98: iOS Function: text - Text: "<EMAIL>"
[[21:19:31]] [SUCCESS] Screenshot refreshed
[[21:19:31]] [INFO] Refreshing screenshot...
[[21:19:31]] [INFO] 50Z2jrodNd=pass
[[21:19:27]] [SUCCESS] Screenshot refreshed successfully
[[21:19:27]] [SUCCESS] Screenshot refreshed successfully
[[21:19:27]] [INFO] 50Z2jrodNd=running
[[21:19:27]] [INFO] Executing action 67/98: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[21:19:27]] [SUCCESS] Screenshot refreshed
[[21:19:27]] [INFO] Refreshing screenshot...
[[21:19:27]] [INFO] q9ZiyYoE5B=pass
[[21:19:24]] [SUCCESS] Screenshot refreshed successfully
[[21:19:24]] [SUCCESS] Screenshot refreshed successfully
[[21:19:24]] [INFO] q9ZiyYoE5B=running
[[21:19:24]] [INFO] Executing action 66/98: iOS Function: alert_accept
[[21:19:23]] [SUCCESS] Screenshot refreshed
[[21:19:23]] [INFO] Refreshing screenshot...
[[21:19:23]] [INFO] 6PL8P3rT57=pass
[[21:19:18]] [SUCCESS] Screenshot refreshed successfully
[[21:19:18]] [SUCCESS] Screenshot refreshed successfully
[[21:19:18]] [INFO] 6PL8P3rT57=running
[[21:19:18]] [INFO] Executing action 65/98: Tap on Text: "Sign"
[[21:19:17]] [SUCCESS] Screenshot refreshed
[[21:19:17]] [INFO] Refreshing screenshot...
[[21:19:17]] [INFO] 2YGctqXNED=pass
[[21:19:11]] [SUCCESS] Screenshot refreshed successfully
[[21:19:11]] [SUCCESS] Screenshot refreshed successfully
[[21:19:11]] [INFO] 2YGctqXNED=running
[[21:19:11]] [INFO] Executing action 64/98: Tap on element with accessibility_id: Continue to details
[[21:19:11]] [SUCCESS] Screenshot refreshed
[[21:19:11]] [INFO] Refreshing screenshot...
[[21:19:11]] [INFO] 2YGctqXNED=pass
[[21:19:03]] [SUCCESS] Screenshot refreshed successfully
[[21:19:03]] [SUCCESS] Screenshot refreshed successfully
[[21:19:02]] [INFO] 2YGctqXNED=running
[[21:19:02]] [INFO] Executing action 63/98: Swipe up till element accessibilityid: "Continue to details" is visible
[[21:19:02]] [SUCCESS] Screenshot refreshed
[[21:19:02]] [INFO] Refreshing screenshot...
[[21:19:02]] [INFO] tufIibCj03=pass
[[21:18:58]] [SUCCESS] Screenshot refreshed successfully
[[21:18:58]] [SUCCESS] Screenshot refreshed successfully
[[21:18:58]] [INFO] tufIibCj03=running
[[21:18:58]] [INFO] Executing action 62/98: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[21:18:57]] [SUCCESS] Screenshot refreshed
[[21:18:57]] [INFO] Refreshing screenshot...
[[21:18:57]] [INFO] g8u66qfKkX=pass
[[21:18:53]] [INFO] g8u66qfKkX=running
[[21:18:53]] [INFO] Executing action 61/98: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[21:18:53]] [SUCCESS] Screenshot refreshed successfully
[[21:18:53]] [SUCCESS] Screenshot refreshed successfully
[[21:18:53]] [SUCCESS] Screenshot refreshed
[[21:18:53]] [INFO] Refreshing screenshot...
[[21:18:53]] [INFO] XryN8qR1DX=pass
[[21:18:48]] [SUCCESS] Screenshot refreshed successfully
[[21:18:48]] [SUCCESS] Screenshot refreshed successfully
[[21:18:48]] [INFO] XryN8qR1DX=running
[[21:18:48]] [INFO] Executing action 60/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[21:18:47]] [SUCCESS] Screenshot refreshed
[[21:18:47]] [INFO] Refreshing screenshot...
[[21:18:47]] [INFO] K2w9XUGwnb=pass
[[21:18:29]] [SUCCESS] Screenshot refreshed successfully
[[21:18:29]] [SUCCESS] Screenshot refreshed successfully
[[21:18:29]] [INFO] K2w9XUGwnb=running
[[21:18:29]] [INFO] Executing action 59/98: Tap on element with accessibility_id: Add to bag
[[21:18:28]] [SUCCESS] Screenshot refreshed
[[21:18:28]] [INFO] Refreshing screenshot...
[[21:18:28]] [INFO] BTYxjEaZEk=pass
[[21:18:23]] [SUCCESS] Screenshot refreshed successfully
[[21:18:23]] [SUCCESS] Screenshot refreshed successfully
[[21:18:23]] [INFO] BTYxjEaZEk=running
[[21:18:23]] [INFO] Executing action 58/98: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[21:18:22]] [SUCCESS] Screenshot refreshed
[[21:18:22]] [INFO] Refreshing screenshot...
[[21:18:22]] [INFO] YC6bBrKQgq=pass
[[21:18:18]] [SUCCESS] Screenshot refreshed successfully
[[21:18:18]] [SUCCESS] Screenshot refreshed successfully
[[21:18:18]] [INFO] YC6bBrKQgq=running
[[21:18:18]] [INFO] Executing action 57/98: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[21:18:17]] [SUCCESS] Screenshot refreshed
[[21:18:17]] [INFO] Refreshing screenshot...
[[21:18:17]] [INFO] aRgHcQcLDP=pass
[[21:18:13]] [SUCCESS] Screenshot refreshed successfully
[[21:18:13]] [SUCCESS] Screenshot refreshed successfully
[[21:18:13]] [INFO] aRgHcQcLDP=running
[[21:18:13]] [INFO] Executing action 56/98: iOS Function: text - Text: "uno card"
[[21:18:12]] [SUCCESS] Screenshot refreshed
[[21:18:12]] [INFO] Refreshing screenshot...
[[21:18:12]] [INFO] 4PZC1vVWJW=pass
[[21:18:07]] [SUCCESS] Screenshot refreshed successfully
[[21:18:07]] [SUCCESS] Screenshot refreshed successfully
[[21:18:07]] [INFO] 4PZC1vVWJW=running
[[21:18:07]] [INFO] Executing action 55/98: Tap on Text: "Find"
[[21:18:06]] [SUCCESS] Screenshot refreshed
[[21:18:06]] [INFO] Refreshing screenshot...
[[21:18:06]] [INFO] XryN8qR1DX=pass
[[21:18:02]] [SUCCESS] Screenshot refreshed successfully
[[21:18:02]] [SUCCESS] Screenshot refreshed successfully
[[21:18:02]] [INFO] XryN8qR1DX=running
[[21:18:02]] [INFO] Executing action 54/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[21:18:01]] [SUCCESS] Screenshot refreshed
[[21:18:01]] [INFO] Refreshing screenshot...
[[21:18:01]] [INFO] 7WYExJTqjp=pass
[[21:17:57]] [SUCCESS] Screenshot refreshed successfully
[[21:17:57]] [SUCCESS] Screenshot refreshed successfully
[[21:17:57]] [INFO] 7WYExJTqjp=running
[[21:17:57]] [INFO] Executing action 53/98: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[21:17:56]] [SUCCESS] Screenshot refreshed
[[21:17:56]] [INFO] Refreshing screenshot...
[[21:17:56]] [INFO] 4WfPFN961S=pass
[[21:17:50]] [SUCCESS] Screenshot refreshed successfully
[[21:17:50]] [SUCCESS] Screenshot refreshed successfully
[[21:17:49]] [INFO] 4WfPFN961S=running
[[21:17:49]] [INFO] Executing action 52/98: Swipe from (50%, 70%) to (50%, 30%)
[[21:17:49]] [SUCCESS] Screenshot refreshed
[[21:17:49]] [INFO] Refreshing screenshot...
[[21:17:49]] [INFO] NurQsFoMkE=pass
[[21:17:46]] [SUCCESS] Screenshot refreshed successfully
[[21:17:46]] [SUCCESS] Screenshot refreshed successfully
[[21:17:44]] [INFO] NurQsFoMkE=running
[[21:17:44]] [INFO] Executing action 51/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[21:17:44]] [SUCCESS] Screenshot refreshed
[[21:17:44]] [INFO] Refreshing screenshot...
[[21:17:44]] [SUCCESS] Screenshot refreshed
[[21:17:44]] [INFO] Refreshing screenshot...
[[21:17:39]] [SUCCESS] Screenshot refreshed successfully
[[21:17:39]] [SUCCESS] Screenshot refreshed successfully
[[21:17:39]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[21:17:39]] [SUCCESS] Screenshot refreshed
[[21:17:39]] [INFO] Refreshing screenshot...
[[21:17:35]] [SUCCESS] Screenshot refreshed successfully
[[21:17:35]] [SUCCESS] Screenshot refreshed successfully
[[21:17:35]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[21:17:34]] [SUCCESS] Screenshot refreshed
[[21:17:34]] [INFO] Refreshing screenshot...
[[21:17:29]] [SUCCESS] Screenshot refreshed successfully
[[21:17:29]] [SUCCESS] Screenshot refreshed successfully
[[21:17:29]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[21:17:29]] [SUCCESS] Screenshot refreshed
[[21:17:29]] [INFO] Refreshing screenshot...
[[21:17:24]] [SUCCESS] Screenshot refreshed successfully
[[21:17:24]] [SUCCESS] Screenshot refreshed successfully
[[21:17:24]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[21:17:23]] [SUCCESS] Screenshot refreshed
[[21:17:23]] [INFO] Refreshing screenshot...
[[21:17:20]] [SUCCESS] Screenshot refreshed successfully
[[21:17:20]] [SUCCESS] Screenshot refreshed successfully
[[21:17:20]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[21:17:20]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[21:17:20]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[21:17:20]] [INFO] mWOCt0aAWW=running
[[21:17:20]] [INFO] Executing action 50/98: Execute Test Case: Kmart-Signin (5 steps)
[[21:17:19]] [SUCCESS] Screenshot refreshed
[[21:17:19]] [INFO] Refreshing screenshot...
[[21:17:19]] [INFO] byEe7qbCpq=pass
[[21:17:16]] [SUCCESS] Screenshot refreshed successfully
[[21:17:16]] [SUCCESS] Screenshot refreshed successfully
[[21:17:15]] [INFO] byEe7qbCpq=running
[[21:17:15]] [INFO] Executing action 49/98: iOS Function: alert_accept
[[21:17:15]] [SUCCESS] Screenshot refreshed
[[21:17:15]] [INFO] Refreshing screenshot...
[[21:17:15]] [INFO] L6wTorOX8B=pass
[[21:17:11]] [SUCCESS] Screenshot refreshed successfully
[[21:17:11]] [SUCCESS] Screenshot refreshed successfully
[[21:17:11]] [INFO] L6wTorOX8B=running
[[21:17:11]] [INFO] Executing action 48/98: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[21:17:11]] [SUCCESS] Screenshot refreshed
[[21:17:11]] [INFO] Refreshing screenshot...
[[21:17:11]] [INFO] XryN8qR1DX=pass
[[21:17:07]] [SUCCESS] Screenshot refreshed successfully
[[21:17:07]] [SUCCESS] Screenshot refreshed successfully
[[21:17:06]] [INFO] XryN8qR1DX=running
[[21:17:06]] [INFO] Executing action 47/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[21:17:06]] [SUCCESS] Screenshot refreshed
[[21:17:06]] [INFO] Refreshing screenshot...
[[21:17:06]] [INFO] lCSewtjn1z=pass
[[21:17:01]] [SUCCESS] Screenshot refreshed successfully
[[21:17:01]] [SUCCESS] Screenshot refreshed successfully
[[21:17:01]] [INFO] lCSewtjn1z=running
[[21:17:01]] [INFO] Executing action 46/98: Restart app: env[appid]
[[21:17:00]] [SUCCESS] Screenshot refreshed
[[21:17:00]] [INFO] Refreshing screenshot...
[[21:17:00]] [INFO] IJh702cxG0=pass
[[21:16:56]] [SUCCESS] Screenshot refreshed successfully
[[21:16:56]] [SUCCESS] Screenshot refreshed successfully
[[21:16:56]] [INFO] IJh702cxG0=running
[[21:16:56]] [INFO] Executing action 45/98: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[21:16:55]] [SUCCESS] Screenshot refreshed
[[21:16:55]] [INFO] Refreshing screenshot...
[[21:16:55]] [INFO] 4WfPFN961S=pass
[[21:16:47]] [SUCCESS] Screenshot refreshed successfully
[[21:16:47]] [SUCCESS] Screenshot refreshed successfully
[[21:16:47]] [INFO] 4WfPFN961S=running
[[21:16:47]] [INFO] Executing action 44/98: Swipe from (50%, 70%) to (50%, 30%)
[[21:16:47]] [SUCCESS] Screenshot refreshed
[[21:16:47]] [INFO] Refreshing screenshot...
[[21:16:47]] [INFO] AOcOOSuOsB=pass
[[21:16:43]] [SUCCESS] Screenshot refreshed successfully
[[21:16:43]] [SUCCESS] Screenshot refreshed successfully
[[21:16:42]] [INFO] AOcOOSuOsB=running
[[21:16:42]] [INFO] Executing action 43/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[21:16:42]] [SUCCESS] Screenshot refreshed
[[21:16:42]] [INFO] Refreshing screenshot...
[[21:16:42]] [INFO] AOcOOSuOsB=pass
[[21:16:37]] [SUCCESS] Screenshot refreshed successfully
[[21:16:37]] [SUCCESS] Screenshot refreshed successfully
[[21:16:36]] [INFO] AOcOOSuOsB=running
[[21:16:36]] [INFO] Executing action 42/98: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[21:16:36]] [SUCCESS] Screenshot refreshed
[[21:16:36]] [INFO] Refreshing screenshot...
[[21:16:36]] [INFO] N2yjynioko=pass
[[21:16:31]] [SUCCESS] Screenshot refreshed successfully
[[21:16:31]] [SUCCESS] Screenshot refreshed successfully
[[21:16:31]] [INFO] N2yjynioko=running
[[21:16:31]] [INFO] Executing action 41/98: iOS Function: text - Text: "Wonderbaby@5"
[[21:16:31]] [SUCCESS] Screenshot refreshed
[[21:16:31]] [INFO] Refreshing screenshot...
[[21:16:31]] [INFO] SHaIduBnay=pass
[[21:16:26]] [SUCCESS] Screenshot refreshed successfully
[[21:16:26]] [SUCCESS] Screenshot refreshed successfully
[[21:16:26]] [INFO] SHaIduBnay=running
[[21:16:26]] [INFO] Executing action 40/98: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[21:16:26]] [SUCCESS] Screenshot refreshed
[[21:16:26]] [INFO] Refreshing screenshot...
[[21:16:26]] [INFO] wuIMlAwYVA=pass
[[21:16:20]] [SUCCESS] Screenshot refreshed successfully
[[21:16:20]] [SUCCESS] Screenshot refreshed successfully
[[21:16:20]] [INFO] wuIMlAwYVA=running
[[21:16:20]] [INFO] Executing action 39/98: iOS Function: text - Text: "env[uname1]"
[[21:16:19]] [SUCCESS] Screenshot refreshed
[[21:16:19]] [INFO] Refreshing screenshot...
[[21:16:19]] [INFO] 50Z2jrodNd=pass
[[21:16:15]] [SUCCESS] Screenshot refreshed successfully
[[21:16:15]] [SUCCESS] Screenshot refreshed successfully
[[21:16:15]] [INFO] 50Z2jrodNd=running
[[21:16:15]] [INFO] Executing action 38/98: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[21:16:15]] [SUCCESS] Screenshot refreshed
[[21:16:15]] [INFO] Refreshing screenshot...
[[21:16:15]] [INFO] VK2oI6mXSB=pass
[[21:16:11]] [SUCCESS] Screenshot refreshed successfully
[[21:16:11]] [SUCCESS] Screenshot refreshed successfully
[[21:16:11]] [INFO] VK2oI6mXSB=running
[[21:16:11]] [INFO] Executing action 37/98: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[21:16:10]] [SUCCESS] Screenshot refreshed
[[21:16:10]] [INFO] Refreshing screenshot...
[[21:16:10]] [INFO] q9ZiyYoE5B=pass
[[21:16:08]] [SUCCESS] Screenshot refreshed successfully
[[21:16:08]] [SUCCESS] Screenshot refreshed successfully
[[21:16:07]] [INFO] q9ZiyYoE5B=running
[[21:16:07]] [INFO] Executing action 36/98: iOS Function: alert_accept
[[21:16:07]] [SUCCESS] Screenshot refreshed
[[21:16:07]] [INFO] Refreshing screenshot...
[[21:16:07]] [INFO] 4PZC1vVWJW=pass
[[21:16:02]] [SUCCESS] Screenshot refreshed successfully
[[21:16:02]] [SUCCESS] Screenshot refreshed successfully
[[21:16:01]] [INFO] 4PZC1vVWJW=running
[[21:16:01]] [INFO] Executing action 35/98: Tap on Text: "Sign"
[[21:16:01]] [SUCCESS] Screenshot refreshed
[[21:16:01]] [INFO] Refreshing screenshot...
[[21:16:01]] [INFO] mcscWdhpn2=pass
[[21:15:44]] [SUCCESS] Screenshot refreshed successfully
[[21:15:44]] [SUCCESS] Screenshot refreshed successfully
[[21:15:43]] [INFO] mcscWdhpn2=running
[[21:15:43]] [INFO] Executing action 34/98: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[21:15:43]] [SUCCESS] Screenshot refreshed
[[21:15:43]] [INFO] Refreshing screenshot...
[[21:15:43]] [INFO] 6zUBxjSFym=pass
[[21:15:39]] [SUCCESS] Screenshot refreshed successfully
[[21:15:39]] [SUCCESS] Screenshot refreshed successfully
[[21:15:39]] [INFO] 6zUBxjSFym=running
[[21:15:39]] [INFO] Executing action 33/98: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[21:15:38]] [SUCCESS] Screenshot refreshed
[[21:15:38]] [INFO] Refreshing screenshot...
[[21:15:38]] [INFO] BTYxjEaZEk=pass
[[21:15:34]] [SUCCESS] Screenshot refreshed successfully
[[21:15:34]] [SUCCESS] Screenshot refreshed successfully
[[21:15:34]] [INFO] BTYxjEaZEk=running
[[21:15:34]] [INFO] Executing action 32/98: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[21:15:33]] [SUCCESS] Screenshot refreshed
[[21:15:33]] [INFO] Refreshing screenshot...
[[21:15:33]] [INFO] YC6bBrKQgq=pass
[[21:15:30]] [SUCCESS] Screenshot refreshed successfully
[[21:15:30]] [SUCCESS] Screenshot refreshed successfully
[[21:15:29]] [INFO] YC6bBrKQgq=running
[[21:15:29]] [INFO] Executing action 31/98: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[21:15:29]] [SUCCESS] Screenshot refreshed
[[21:15:29]] [INFO] Refreshing screenshot...
[[21:15:29]] [INFO] aRgHcQcLDP=pass
[[21:15:24]] [SUCCESS] Screenshot refreshed successfully
[[21:15:24]] [SUCCESS] Screenshot refreshed successfully
[[21:15:24]] [INFO] aRgHcQcLDP=running
[[21:15:24]] [INFO] Executing action 30/98: iOS Function: text - Text: "uno card"
[[21:15:23]] [SUCCESS] Screenshot refreshed
[[21:15:23]] [INFO] Refreshing screenshot...
[[21:15:23]] [INFO] 4PZC1vVWJW=pass
[[21:15:18]] [SUCCESS] Screenshot refreshed successfully
[[21:15:18]] [SUCCESS] Screenshot refreshed successfully
[[21:15:17]] [INFO] 4PZC1vVWJW=running
[[21:15:17]] [INFO] Executing action 29/98: Tap on Text: "Find"
[[21:15:17]] [SUCCESS] Screenshot refreshed
[[21:15:17]] [INFO] Refreshing screenshot...
[[21:15:17]] [INFO] lCSewtjn1z=pass
[[21:15:12]] [SUCCESS] Screenshot refreshed successfully
[[21:15:12]] [SUCCESS] Screenshot refreshed successfully
[[21:15:12]] [INFO] lCSewtjn1z=running
[[21:15:12]] [INFO] Executing action 28/98: Restart app: env[appid]
[[21:15:11]] [SUCCESS] Screenshot refreshed
[[21:15:11]] [INFO] Refreshing screenshot...
[[21:15:11]] [INFO] A1Wz7p1iVG=pass
[[21:15:07]] [SUCCESS] Screenshot refreshed successfully
[[21:15:07]] [SUCCESS] Screenshot refreshed successfully
[[21:15:07]] [INFO] A1Wz7p1iVG=running
[[21:15:07]] [INFO] Executing action 27/98: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[21:15:06]] [SUCCESS] Screenshot refreshed
[[21:15:06]] [INFO] Refreshing screenshot...
[[21:15:06]] [INFO] ehyLmdZWP2=pass
[[21:14:59]] [SUCCESS] Screenshot refreshed successfully
[[21:14:59]] [SUCCESS] Screenshot refreshed successfully
[[21:14:59]] [INFO] ehyLmdZWP2=running
[[21:14:59]] [INFO] Executing action 26/98: Swipe from (50%, 70%) to (50%, 30%)
[[21:14:59]] [SUCCESS] Screenshot refreshed
[[21:14:59]] [INFO] Refreshing screenshot...
[[21:14:59]] [INFO] ydRnBBO1vR=pass
[[21:14:55]] [SUCCESS] Screenshot refreshed successfully
[[21:14:55]] [SUCCESS] Screenshot refreshed successfully
[[21:14:54]] [INFO] ydRnBBO1vR=running
[[21:14:54]] [INFO] Executing action 25/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[21:14:54]] [SUCCESS] Screenshot refreshed
[[21:14:54]] [INFO] Refreshing screenshot...
[[21:14:54]] [INFO] quZwUwj3a8=pass
[[21:14:50]] [SUCCESS] Screenshot refreshed successfully
[[21:14:50]] [SUCCESS] Screenshot refreshed successfully
[[21:14:50]] [INFO] quZwUwj3a8=running
[[21:14:50]] [INFO] Executing action 24/98: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[21:14:49]] [SUCCESS] Screenshot refreshed
[[21:14:49]] [INFO] Refreshing screenshot...
[[21:14:49]] [INFO] FHRlQXe58T=pass
[[21:14:45]] [SUCCESS] Screenshot refreshed successfully
[[21:14:45]] [SUCCESS] Screenshot refreshed successfully
[[21:14:44]] [INFO] FHRlQXe58T=running
[[21:14:44]] [INFO] Executing action 23/98: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[21:14:44]] [SUCCESS] Screenshot refreshed
[[21:14:44]] [INFO] Refreshing screenshot...
[[21:14:44]] [INFO] FHRlQXe58T=pass
[[21:14:39]] [SUCCESS] Screenshot refreshed successfully
[[21:14:39]] [SUCCESS] Screenshot refreshed successfully
[[21:14:39]] [INFO] FHRlQXe58T=running
[[21:14:39]] [INFO] Executing action 22/98: Tap on element with xpath: //XCUIElementTypeButton[@name="txtStart Shopping"]
[[21:14:39]] [SUCCESS] Screenshot refreshed
[[21:14:39]] [INFO] Refreshing screenshot...
[[21:14:39]] [INFO] N9sXy9WltY=pass
[[21:14:35]] [SUCCESS] Screenshot refreshed successfully
[[21:14:35]] [SUCCESS] Screenshot refreshed successfully
[[21:14:35]] [INFO] N9sXy9WltY=running
[[21:14:35]] [INFO] Executing action 21/98: Check if element with xpath="//XCUIElementTypeButton[@name="txtStart Shopping"]" exists
[[21:14:34]] [SUCCESS] Screenshot refreshed
[[21:14:34]] [INFO] Refreshing screenshot...
[[21:14:34]] [INFO] 8uojw2klHA=pass
[[21:14:30]] [SUCCESS] Screenshot refreshed successfully
[[21:14:30]] [SUCCESS] Screenshot refreshed successfully
[[21:14:30]] [INFO] 8uojw2klHA=running
[[21:14:30]] [INFO] Executing action 20/98: iOS Function: text - Text: "env[pwd]"
[[21:14:29]] [SUCCESS] Screenshot refreshed
[[21:14:29]] [INFO] Refreshing screenshot...
[[21:14:29]] [INFO] SHaIduBnay=pass
[[21:14:27]] [SUCCESS] Screenshot refreshed successfully
[[21:14:27]] [SUCCESS] Screenshot refreshed successfully
[[21:14:25]] [INFO] SHaIduBnay=running
[[21:14:25]] [INFO] Executing action 19/98: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[21:14:24]] [SUCCESS] Screenshot refreshed
[[21:14:24]] [INFO] Refreshing screenshot...
[[21:14:24]] [INFO] TGoXyeQtB7=pass
[[21:14:20]] [INFO] TGoXyeQtB7=running
[[21:14:20]] [INFO] Executing action 18/98: iOS Function: text - Text: "env[uname]"
[[21:14:20]] [SUCCESS] Screenshot refreshed successfully
[[21:14:20]] [SUCCESS] Screenshot refreshed successfully
[[21:14:19]] [SUCCESS] Screenshot refreshed
[[21:14:19]] [INFO] Refreshing screenshot...
[[21:14:19]] [INFO] rLCI6NVxSc=pass
[[21:14:15]] [SUCCESS] Screenshot refreshed successfully
[[21:14:15]] [SUCCESS] Screenshot refreshed successfully
[[21:14:15]] [INFO] rLCI6NVxSc=running
[[21:14:15]] [INFO] Executing action 17/98: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[21:14:14]] [SUCCESS] Screenshot refreshed
[[21:14:14]] [INFO] Refreshing screenshot...
[[21:14:14]] [INFO] 6mHVWI3j5e=pass
[[21:14:11]] [SUCCESS] Screenshot refreshed successfully
[[21:14:11]] [SUCCESS] Screenshot refreshed successfully
[[21:14:10]] [INFO] 6mHVWI3j5e=running
[[21:14:10]] [INFO] Executing action 16/98: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[21:14:10]] [SUCCESS] Screenshot refreshed
[[21:14:10]] [INFO] Refreshing screenshot...
[[21:14:10]] [INFO] rJVGLpLWM3=pass
[[21:14:07]] [SUCCESS] Screenshot refreshed successfully
[[21:14:07]] [SUCCESS] Screenshot refreshed successfully
[[21:14:07]] [INFO] rJVGLpLWM3=running
[[21:14:07]] [INFO] Executing action 15/98: iOS Function: alert_accept
[[21:14:07]] [SUCCESS] Screenshot refreshed
[[21:14:07]] [INFO] Refreshing screenshot...
[[21:14:07]] [INFO] WlISsMf9QA=pass
[[21:14:03]] [SUCCESS] Screenshot refreshed successfully
[[21:14:03]] [SUCCESS] Screenshot refreshed successfully
[[21:14:03]] [INFO] WlISsMf9QA=running
[[21:14:03]] [INFO] Executing action 14/98: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[21:14:02]] [SUCCESS] Screenshot refreshed
[[21:14:02]] [INFO] Refreshing screenshot...
[[21:14:02]] [INFO] IvqPpScAJa=pass
[[21:13:59]] [SUCCESS] Screenshot refreshed successfully
[[21:13:59]] [SUCCESS] Screenshot refreshed successfully
[[21:13:58]] [INFO] IvqPpScAJa=running
[[21:13:58]] [INFO] Executing action 13/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[21:13:58]] [SUCCESS] Screenshot refreshed
[[21:13:58]] [INFO] Refreshing screenshot...
[[21:13:58]] [INFO] bGo3feCwBQ=pass
[[21:13:53]] [SUCCESS] Screenshot refreshed successfully
[[21:13:53]] [SUCCESS] Screenshot refreshed successfully
[[21:13:53]] [INFO] bGo3feCwBQ=running
[[21:13:53]] [INFO] Executing action 12/98: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[21:13:52]] [SUCCESS] Screenshot refreshed
[[21:13:52]] [INFO] Refreshing screenshot...
[[21:13:52]] [INFO] 4WfPFN961S=pass
[[21:13:45]] [SUCCESS] Screenshot refreshed successfully
[[21:13:45]] [SUCCESS] Screenshot refreshed successfully
[[21:13:45]] [INFO] 4WfPFN961S=running
[[21:13:45]] [INFO] Executing action 11/98: Swipe from (50%, 70%) to (50%, 30%)
[[21:13:45]] [SUCCESS] Screenshot refreshed
[[21:13:45]] [INFO] Refreshing screenshot...
[[21:13:45]] [INFO] F0gZF1jEnT=pass
[[21:13:41]] [SUCCESS] Screenshot refreshed successfully
[[21:13:41]] [SUCCESS] Screenshot refreshed successfully
[[21:13:41]] [INFO] F0gZF1jEnT=running
[[21:13:41]] [INFO] Executing action 10/98: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[21:13:40]] [SUCCESS] Screenshot refreshed
[[21:13:40]] [INFO] Refreshing screenshot...
[[21:13:40]] [INFO] EDHl0X27Wi=pass
[[21:13:37]] [SUCCESS] Screenshot refreshed successfully
[[21:13:37]] [SUCCESS] Screenshot refreshed successfully
[[21:13:36]] [INFO] EDHl0X27Wi=running
[[21:13:36]] [INFO] Executing action 9/98: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[21:13:36]] [SUCCESS] Screenshot refreshed
[[21:13:36]] [INFO] Refreshing screenshot...
[[21:13:36]] [INFO] j8NXU87gV3=pass
[[21:13:31]] [SUCCESS] Screenshot refreshed successfully
[[21:13:31]] [SUCCESS] Screenshot refreshed successfully
[[21:13:31]] [INFO] j8NXU87gV3=running
[[21:13:31]] [INFO] Executing action 8/98: iOS Function: text - Text: "env[pwd]"
[[21:13:30]] [SUCCESS] Screenshot refreshed
[[21:13:30]] [INFO] Refreshing screenshot...
[[21:13:30]] [INFO] dpVaKL19uc=pass
[[21:13:26]] [SUCCESS] Screenshot refreshed successfully
[[21:13:26]] [SUCCESS] Screenshot refreshed successfully
[[21:13:26]] [INFO] dpVaKL19uc=running
[[21:13:26]] [INFO] Executing action 7/98: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[21:13:26]] [SUCCESS] Screenshot refreshed
[[21:13:26]] [INFO] Refreshing screenshot...
[[21:13:26]] [INFO] eOm1WExcrK=pass
[[21:13:20]] [INFO] eOm1WExcrK=running
[[21:13:20]] [INFO] Executing action 6/98: iOS Function: text - Text: "env[uname]"
[[21:13:20]] [SUCCESS] Screenshot refreshed successfully
[[21:13:20]] [SUCCESS] Screenshot refreshed successfully
[[21:13:20]] [SUCCESS] Screenshot refreshed
[[21:13:20]] [INFO] Refreshing screenshot...
[[21:13:20]] [INFO] 50Z2jrodNd=pass
[[21:13:16]] [SUCCESS] Screenshot refreshed successfully
[[21:13:16]] [SUCCESS] Screenshot refreshed successfully
[[21:13:16]] [INFO] 50Z2jrodNd=running
[[21:13:16]] [INFO] Executing action 5/98: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[21:13:15]] [SUCCESS] Screenshot refreshed
[[21:13:15]] [INFO] Refreshing screenshot...
[[21:13:15]] [INFO] eJnHS9n9VL=pass
[[21:13:11]] [SUCCESS] Screenshot refreshed successfully
[[21:13:11]] [SUCCESS] Screenshot refreshed successfully
[[21:13:11]] [INFO] eJnHS9n9VL=running
[[21:13:11]] [INFO] Executing action 4/98: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[21:13:11]] [SUCCESS] Screenshot refreshed
[[21:13:11]] [INFO] Refreshing screenshot...
[[21:13:11]] [INFO] XuLgjNG74w=pass
[[21:13:08]] [SUCCESS] Screenshot refreshed successfully
[[21:13:08]] [SUCCESS] Screenshot refreshed successfully
[[21:13:08]] [INFO] XuLgjNG74w=running
[[21:13:08]] [INFO] Executing action 3/98: iOS Function: alert_accept
[[21:13:07]] [SUCCESS] Screenshot refreshed
[[21:13:07]] [INFO] Refreshing screenshot...
[[21:13:07]] [INFO] qA1ap4n1m4=pass
[[21:13:01]] [SUCCESS] Screenshot refreshed successfully
[[21:13:01]] [SUCCESS] Screenshot refreshed successfully
[[21:13:00]] [INFO] qA1ap4n1m4=running
[[21:13:00]] [INFO] Executing action 2/98: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[21:13:00]] [SUCCESS] Screenshot refreshed
[[21:13:00]] [INFO] Refreshing screenshot...
[[21:13:00]] [INFO] JXFxYCr98V=pass
[[21:12:54]] [INFO] JXFxYCr98V=running
[[21:12:54]] [INFO] Executing action 1/98: Restart app: env[appid]
[[21:12:54]] [INFO] ExecutionManager: Starting execution of 98 actions...
[[21:12:54]] [SUCCESS] Cleared 1 screenshots from database
[[21:12:54]] [INFO] Clearing screenshots from database before execution...
[[21:12:54]] [SUCCESS] All screenshots deleted successfully
[[21:12:54]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:12:54]] [INFO] Skipping report initialization - single test case execution
[[21:12:53]] [SUCCESS] All screenshots deleted successfully
[[21:12:53]] [SUCCESS] Loaded test case "All Sign ins" with 98 actions
[[21:12:53]] [SUCCESS] Added action: cleanupSteps
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: swipe
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: multiStep
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tapOnText
[[21:12:53]] [SUCCESS] Added action: wait
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: swipeTillVisible
[[21:12:53]] [SUCCESS] Added action: multiStep
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: restartApp
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: swipe
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tapOnText
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: swipeTillVisible
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tapOnText
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: swipe
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: multiStep
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: restartApp
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: swipe
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tapOnText
[[21:12:53]] [SUCCESS] Added action: swipeTillVisible
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tapOnText
[[21:12:53]] [SUCCESS] Added action: restartApp
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: swipe
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: exists
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: swipe
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: waitTill
[[21:12:53]] [SUCCESS] Added action: iosFunctions
[[21:12:53]] [SUCCESS] Added action: tap
[[21:12:53]] [SUCCESS] Added action: restartApp
[[21:12:53]] [INFO] All actions cleared
[[21:12:53]] [INFO] Cleaning up screenshots...
[[21:12:50]] [SUCCESS] Screenshot refreshed successfully
[[21:12:49]] [SUCCESS] Screenshot refreshed
[[21:12:49]] [INFO] Refreshing screenshot...
[[21:12:48]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[21:12:48]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[21:12:44]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[21:12:43]] [SUCCESS] Found 1 device(s)
[[21:12:42]] [INFO] Refreshing device list...
