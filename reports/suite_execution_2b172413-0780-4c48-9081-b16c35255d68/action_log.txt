Action Log - 2025-07-03 16:38:28
================================================================================

[[16:38:28]] [INFO] Generating execution report...
[[16:38:28]] [SUCCESS] All tests passed successfully!
[[16:38:28]] [SUCCESS] Screenshot refreshed
[[16:38:28]] [INFO] Refreshing screenshot...
[[16:38:28]] [SUCCESS] Screenshot refreshed
[[16:38:28]] [INFO] Refreshing screenshot...
[[16:38:25]] [SUCCESS] Screenshot refreshed successfully
[[16:38:25]] [SUCCESS] Screenshot refreshed successfully
[[16:38:25]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[16:38:24]] [SUCCESS] Screenshot refreshed
[[16:38:24]] [INFO] Refreshing screenshot...
[[16:38:12]] [SUCCESS] Screenshot refreshed successfully
[[16:38:12]] [SUCCESS] Screenshot refreshed successfully
[[16:38:12]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[16:38:11]] [SUCCESS] Screenshot refreshed
[[16:38:11]] [INFO] Refreshing screenshot...
[[16:38:08]] [SUCCESS] Screenshot refreshed successfully
[[16:38:08]] [SUCCESS] Screenshot refreshed successfully
[[16:38:08]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[16:38:07]] [SUCCESS] Screenshot refreshed
[[16:38:07]] [INFO] Refreshing screenshot...
[[16:38:03]] [SUCCESS] Screenshot refreshed successfully
[[16:38:03]] [SUCCESS] Screenshot refreshed successfully
[[16:38:02]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[16:38:02]] [SUCCESS] Screenshot refreshed
[[16:38:02]] [INFO] Refreshing screenshot...
[[16:37:55]] [SUCCESS] Screenshot refreshed successfully
[[16:37:55]] [SUCCESS] Screenshot refreshed successfully
[[16:37:54]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[16:37:54]] [SUCCESS] Screenshot refreshed
[[16:37:54]] [INFO] Refreshing screenshot...
[[16:37:49]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[16:37:49]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[16:37:49]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[16:37:48]] [INFO] kPdSiomhwu=running
[[16:37:48]] [INFO] Executing action 61/61: cleanupSteps action
[[16:37:48]] [SUCCESS] Screenshot refreshed successfully
[[16:37:48]] [SUCCESS] Screenshot refreshed successfully
[[16:37:48]] [SUCCESS] Screenshot refreshed
[[16:37:48]] [INFO] Refreshing screenshot...
[[16:37:48]] [INFO] Qb1AArnpCH=pass
[[16:37:41]] [INFO] Qb1AArnpCH=running
[[16:37:41]] [INFO] Executing action 60/61: Wait for 5 ms
[[16:37:41]] [SUCCESS] Screenshot refreshed successfully
[[16:37:41]] [SUCCESS] Screenshot refreshed successfully
[[16:37:41]] [SUCCESS] Screenshot refreshed
[[16:37:41]] [INFO] Refreshing screenshot...
[[16:37:41]] [INFO] 0SHxVJkq0l=pass
[[16:37:18]] [INFO] 0SHxVJkq0l=running
[[16:37:18]] [INFO] Executing action 59/61: If exists: id="//XCUIElementTypeButton[contains(@name,"Remove")]" (timeout: 20s) → Then tap at (0, 0)
[[16:37:18]] [SUCCESS] Screenshot refreshed successfully
[[16:37:18]] [SUCCESS] Screenshot refreshed successfully
[[16:37:18]] [SUCCESS] Screenshot refreshed
[[16:37:18]] [INFO] Refreshing screenshot...
[[16:37:18]] [INFO] UpUSVInizv=pass
[[16:37:15]] [SUCCESS] Screenshot refreshed successfully
[[16:37:15]] [SUCCESS] Screenshot refreshed successfully
[[16:37:14]] [INFO] UpUSVInizv=running
[[16:37:14]] [INFO] Executing action 58/61: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[16:37:14]] [SUCCESS] Screenshot refreshed
[[16:37:14]] [INFO] Refreshing screenshot...
[[16:37:14]] [INFO] c4T3INQkzn=pass
[[16:37:09]] [SUCCESS] Screenshot refreshed successfully
[[16:37:09]] [SUCCESS] Screenshot refreshed successfully
[[16:37:08]] [INFO] c4T3INQkzn=running
[[16:37:08]] [INFO] Executing action 57/61: Restart app: env[appid]
[[16:37:08]] [SUCCESS] Screenshot refreshed
[[16:37:08]] [INFO] Refreshing screenshot...
[[16:37:08]] [INFO] Cr1z26u7Va=pass
[[16:37:03]] [SUCCESS] Screenshot refreshed successfully
[[16:37:03]] [SUCCESS] Screenshot refreshed successfully
[[16:37:01]] [INFO] Cr1z26u7Va=running
[[16:37:01]] [INFO] Executing action 56/61: If exists: accessibility_id="Add to bag" (timeout: 15s) → Then tap at (0, 0)
[[16:37:00]] [SUCCESS] Screenshot refreshed
[[16:37:00]] [INFO] Refreshing screenshot...
[[16:37:00]] [INFO] 2hGhWulI52=pass
[[16:36:58]] [SUCCESS] Screenshot refreshed successfully
[[16:36:58]] [SUCCESS] Screenshot refreshed successfully
[[16:36:57]] [INFO] 2hGhWulI52=running
[[16:36:57]] [INFO] Executing action 55/61: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[16:36:56]] [SUCCESS] Screenshot refreshed
[[16:36:56]] [INFO] Refreshing screenshot...
[[16:36:56]] [INFO] n57KEWjTea=pass
[[16:36:51]] [SUCCESS] Screenshot refreshed successfully
[[16:36:51]] [SUCCESS] Screenshot refreshed successfully
[[16:36:51]] [INFO] n57KEWjTea=running
[[16:36:51]] [INFO] Executing action 54/61: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[16:36:50]] [SUCCESS] Screenshot refreshed
[[16:36:50]] [INFO] Refreshing screenshot...
[[16:36:50]] [INFO] L59V5hqMX9=pass
[[16:36:46]] [SUCCESS] Screenshot refreshed successfully
[[16:36:46]] [SUCCESS] Screenshot refreshed successfully
[[16:36:46]] [INFO] L59V5hqMX9=running
[[16:36:46]] [INFO] Executing action 53/61: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[16:36:45]] [SUCCESS] Screenshot refreshed
[[16:36:45]] [INFO] Refreshing screenshot...
[[16:36:45]] [INFO] OKiI82VdnE=pass
[[16:36:39]] [SUCCESS] Screenshot refreshed successfully
[[16:36:39]] [SUCCESS] Screenshot refreshed successfully
[[16:36:38]] [INFO] OKiI82VdnE=running
[[16:36:38]] [INFO] Executing action 52/61: Swipe up till element xpath: "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]" is visible
[[16:36:38]] [SUCCESS] Screenshot refreshed
[[16:36:38]] [INFO] Refreshing screenshot...
[[16:36:38]] [INFO] 3KNqlNy6Bj=pass
[[16:36:33]] [SUCCESS] Screenshot refreshed successfully
[[16:36:33]] [SUCCESS] Screenshot refreshed successfully
[[16:36:33]] [INFO] 3KNqlNy6Bj=running
[[16:36:33]] [INFO] Executing action 51/61: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[16:36:32]] [SUCCESS] Screenshot refreshed
[[16:36:32]] [INFO] Refreshing screenshot...
[[16:36:32]] [INFO] 3NOS1fbxZs=pass
[[16:36:28]] [SUCCESS] Screenshot refreshed successfully
[[16:36:28]] [SUCCESS] Screenshot refreshed successfully
[[16:36:28]] [INFO] 3NOS1fbxZs=running
[[16:36:28]] [INFO] Executing action 50/61: Tap on image: banner-close-updated.png
[[16:36:28]] [SUCCESS] Screenshot refreshed
[[16:36:28]] [INFO] Refreshing screenshot...
[[16:36:28]] [INFO] K0c1gL9UK1=pass
[[16:36:24]] [SUCCESS] Screenshot refreshed successfully
[[16:36:24]] [SUCCESS] Screenshot refreshed successfully
[[16:36:24]] [INFO] K0c1gL9UK1=running
[[16:36:24]] [INFO] Executing action 49/61: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[16:36:23]] [SUCCESS] Screenshot refreshed
[[16:36:23]] [INFO] Refreshing screenshot...
[[16:36:23]] [INFO] IW6uAwdtiW=pass
[[16:36:19]] [SUCCESS] Screenshot refreshed successfully
[[16:36:19]] [SUCCESS] Screenshot refreshed successfully
[[16:36:19]] [INFO] IW6uAwdtiW=running
[[16:36:19]] [INFO] Executing action 48/61: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[16:36:18]] [SUCCESS] Screenshot refreshed
[[16:36:18]] [INFO] Refreshing screenshot...
[[16:36:18]] [INFO] DbM0d0m6rU=pass
[[16:36:14]] [INFO] DbM0d0m6rU=running
[[16:36:14]] [INFO] Executing action 47/61: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[16:36:14]] [SUCCESS] Screenshot refreshed successfully
[[16:36:14]] [SUCCESS] Screenshot refreshed successfully
[[16:36:14]] [SUCCESS] Screenshot refreshed
[[16:36:14]] [INFO] Refreshing screenshot...
[[16:36:14]] [INFO] UpUSVInizv=pass
[[16:36:10]] [SUCCESS] Screenshot refreshed successfully
[[16:36:10]] [SUCCESS] Screenshot refreshed successfully
[[16:36:10]] [INFO] UpUSVInizv=running
[[16:36:10]] [INFO] Executing action 46/61: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[16:36:10]] [SUCCESS] Screenshot refreshed
[[16:36:10]] [INFO] Refreshing screenshot...
[[16:36:10]] [INFO] Iab9zCfpqO=pass
[[16:35:52]] [SUCCESS] Screenshot refreshed successfully
[[16:35:52]] [SUCCESS] Screenshot refreshed successfully
[[16:35:52]] [INFO] Iab9zCfpqO=running
[[16:35:52]] [INFO] Executing action 45/61: Tap on element with accessibility_id: Add to bag
[[16:35:51]] [SUCCESS] Screenshot refreshed
[[16:35:51]] [INFO] Refreshing screenshot...
[[16:35:51]] [INFO] Qy0Y0uJchm=pass
[[16:35:48]] [SUCCESS] Screenshot refreshed successfully
[[16:35:48]] [SUCCESS] Screenshot refreshed successfully
[[16:35:47]] [INFO] Qy0Y0uJchm=running
[[16:35:47]] [INFO] Executing action 44/61: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[16:35:47]] [SUCCESS] Screenshot refreshed
[[16:35:47]] [INFO] Refreshing screenshot...
[[16:35:47]] [INFO] YHaMIjULRf=pass
[[16:35:41]] [SUCCESS] Screenshot refreshed successfully
[[16:35:41]] [SUCCESS] Screenshot refreshed successfully
[[16:35:41]] [INFO] YHaMIjULRf=running
[[16:35:41]] [INFO] Executing action 43/61: Tap on Text: "List"
[[16:35:40]] [SUCCESS] Screenshot refreshed
[[16:35:40]] [INFO] Refreshing screenshot...
[[16:35:40]] [INFO] igReeDqips=pass
[[16:35:36]] [SUCCESS] Screenshot refreshed successfully
[[16:35:36]] [SUCCESS] Screenshot refreshed successfully
[[16:35:35]] [INFO] igReeDqips=running
[[16:35:35]] [INFO] Executing action 42/61: Tap on image: env[catalogue-menu-img]
[[16:35:34]] [SUCCESS] Screenshot refreshed
[[16:35:34]] [INFO] Refreshing screenshot...
[[16:35:34]] [INFO] Xqj9EIVE7g=pass
[[16:35:13]] [SUCCESS] Screenshot refreshed successfully
[[16:35:13]] [SUCCESS] Screenshot refreshed successfully
[[16:35:12]] [INFO] Xqj9EIVE7g=running
[[16:35:12]] [INFO] Executing action 41/61: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[16:35:11]] [SUCCESS] Screenshot refreshed
[[16:35:11]] [INFO] Refreshing screenshot...
[[16:35:11]] [INFO] gkkQzTCmma=pass
[[16:34:55]] [SUCCESS] Screenshot refreshed successfully
[[16:34:55]] [SUCCESS] Screenshot refreshed successfully
[[16:34:55]] [INFO] gkkQzTCmma=running
[[16:34:55]] [INFO] Executing action 40/61: Tap on Text: "Catalogue"
[[16:34:55]] [SUCCESS] Screenshot refreshed
[[16:34:55]] [INFO] Refreshing screenshot...
[[16:34:55]] [INFO] UpUSVInizv=pass
[[16:34:51]] [INFO] UpUSVInizv=running
[[16:34:51]] [INFO] Executing action 39/61: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[16:34:51]] [INFO] Cmvm82hiAa=fail
[[16:34:51]] [ERROR] Action 38 failed: Element not found or not tappable: accessibility_id='Add to bag'
[[16:34:37]] [INFO] Cmvm82hiAa=running
[[16:34:37]] [INFO] Executing action 38/61: Tap on element with accessibility_id: Add to bag
[[16:34:37]] [INFO] JcAR0JctQ6=fail
[[16:34:37]] [ERROR] Action 37 failed: Element not found or not tappable: xpath='(//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]'
[[16:34:23]] [SUCCESS] Screenshot refreshed successfully
[[16:34:23]] [SUCCESS] Screenshot refreshed successfully
[[16:34:23]] [INFO] JcAR0JctQ6=running
[[16:34:23]] [INFO] Executing action 37/61: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[16:34:22]] [SUCCESS] Screenshot refreshed
[[16:34:22]] [INFO] Refreshing screenshot...
[[16:34:22]] [INFO] Pd7cReoJM6=pass
[[16:34:18]] [SUCCESS] Screenshot refreshed successfully
[[16:34:18]] [SUCCESS] Screenshot refreshed successfully
[[16:34:18]] [INFO] Pd7cReoJM6=running
[[16:34:18]] [INFO] Executing action 36/61: Tap on Text: "List"
[[16:34:17]] [SUCCESS] Screenshot refreshed
[[16:34:17]] [INFO] Refreshing screenshot...
[[16:34:17]] [INFO] igReeDqips=pass
[[16:34:14]] [SUCCESS] Screenshot refreshed successfully
[[16:34:14]] [SUCCESS] Screenshot refreshed successfully
[[16:34:14]] [INFO] igReeDqips=running
[[16:34:14]] [INFO] Executing action 35/61: Tap on image: env[catalogue-menu-img]
[[16:34:13]] [SUCCESS] Screenshot refreshed
[[16:34:13]] [INFO] Refreshing screenshot...
[[16:34:13]] [INFO] Xqj9EIVE7g=pass
[[16:33:50]] [INFO] Xqj9EIVE7g=running
[[16:33:50]] [INFO] Executing action 34/61: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[16:33:50]] [INFO] gkkQzTCmma=fail
[[16:33:50]] [ERROR] Action 33 failed: Text 'Catalogue' not found within timeout (30s)
[[16:33:17]] [SUCCESS] Screenshot refreshed successfully
[[16:33:17]] [SUCCESS] Screenshot refreshed successfully
[[16:33:17]] [INFO] gkkQzTCmma=running
[[16:33:17]] [INFO] Executing action 33/61: Tap on Text: "Catalogue"
[[16:33:17]] [SUCCESS] Screenshot refreshed
[[16:33:17]] [INFO] Refreshing screenshot...
[[16:33:17]] [INFO] UpUSVInizv=pass
[[16:33:13]] [SUCCESS] Screenshot refreshed successfully
[[16:33:13]] [SUCCESS] Screenshot refreshed successfully
[[16:33:13]] [INFO] UpUSVInizv=running
[[16:33:13]] [INFO] Executing action 32/61: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[16:33:12]] [SUCCESS] Screenshot refreshed
[[16:33:12]] [INFO] Refreshing screenshot...
[[16:33:12]] [INFO] 0QtNHB5WEK=pass
[[16:32:59]] [SUCCESS] Screenshot refreshed successfully
[[16:32:59]] [SUCCESS] Screenshot refreshed successfully
[[16:32:59]] [INFO] 0QtNHB5WEK=running
[[16:32:59]] [INFO] Executing action 31/61: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[16:32:58]] [SUCCESS] Screenshot refreshed
[[16:32:58]] [INFO] Refreshing screenshot...
[[16:32:58]] [INFO] fTdGMJ3NH3=pass
[[16:32:56]] [SUCCESS] Screenshot refreshed successfully
[[16:32:56]] [SUCCESS] Screenshot refreshed successfully
[[16:32:56]] [INFO] fTdGMJ3NH3=running
[[16:32:56]] [INFO] Executing action 30/61: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[16:32:55]] [SUCCESS] Screenshot refreshed
[[16:32:55]] [INFO] Refreshing screenshot...
[[16:32:55]] [INFO] rYJcLPh8Aq=pass
[[16:32:52]] [INFO] rYJcLPh8Aq=running
[[16:32:52]] [INFO] Executing action 29/61: iOS Function: text - Text: "kmart au"
[[16:32:52]] [SUCCESS] Screenshot refreshed successfully
[[16:32:52]] [SUCCESS] Screenshot refreshed successfully
[[16:32:51]] [SUCCESS] Screenshot refreshed
[[16:32:51]] [INFO] Refreshing screenshot...
[[16:32:51]] [INFO] 0Q0fm6OTij=pass
[[16:32:49]] [SUCCESS] Screenshot refreshed successfully
[[16:32:49]] [SUCCESS] Screenshot refreshed successfully
[[16:32:49]] [INFO] 0Q0fm6OTij=running
[[16:32:49]] [INFO] Executing action 28/61: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[16:32:48]] [SUCCESS] Screenshot refreshed
[[16:32:48]] [INFO] Refreshing screenshot...
[[16:32:48]] [INFO] xVuuejtCFA=pass
[[16:32:45]] [SUCCESS] Screenshot refreshed successfully
[[16:32:45]] [SUCCESS] Screenshot refreshed successfully
[[16:32:44]] [INFO] xVuuejtCFA=running
[[16:32:44]] [INFO] Executing action 27/61: Restart app: com.apple.mobilesafari
[[16:32:44]] [SUCCESS] Screenshot refreshed
[[16:32:44]] [INFO] Refreshing screenshot...
[[16:32:44]] [INFO] LcYLwUffqj=pass
[[16:32:39]] [SUCCESS] Screenshot refreshed successfully
[[16:32:39]] [SUCCESS] Screenshot refreshed successfully
[[16:32:39]] [INFO] LcYLwUffqj=running
[[16:32:39]] [INFO] Executing action 26/61: Tap on Text: "out"
[[16:32:38]] [SUCCESS] Screenshot refreshed
[[16:32:38]] [INFO] Refreshing screenshot...
[[16:32:38]] [INFO] ZZPNqTJ65s=pass
[[16:32:35]] [SUCCESS] Screenshot refreshed successfully
[[16:32:35]] [SUCCESS] Screenshot refreshed successfully
[[16:32:34]] [INFO] ZZPNqTJ65s=running
[[16:32:34]] [INFO] Executing action 25/61: Swipe from (50%, 70%) to (50%, 30%)
[[16:32:33]] [SUCCESS] Screenshot refreshed
[[16:32:33]] [INFO] Refreshing screenshot...
[[16:32:33]] [INFO] UpUSVInizv=pass
[[16:32:29]] [SUCCESS] Screenshot refreshed successfully
[[16:32:29]] [SUCCESS] Screenshot refreshed successfully
[[16:32:29]] [INFO] UpUSVInizv=running
[[16:32:29]] [INFO] Executing action 24/61: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[16:32:28]] [SUCCESS] Screenshot refreshed
[[16:32:28]] [INFO] Refreshing screenshot...
[[16:32:28]] [INFO] hCCEvRtj1A=pass
[[16:32:23]] [INFO] hCCEvRtj1A=running
[[16:32:23]] [INFO] Executing action 23/61: Restart app: env[appid]
[[16:32:23]] [SUCCESS] Screenshot refreshed successfully
[[16:32:23]] [SUCCESS] Screenshot refreshed successfully
[[16:32:23]] [SUCCESS] Screenshot refreshed
[[16:32:23]] [INFO] Refreshing screenshot...
[[16:32:23]] [INFO] V42eHtTRYW=pass
[[16:32:16]] [INFO] V42eHtTRYW=running
[[16:32:16]] [INFO] Executing action 22/61: Wait for 5 ms
[[16:32:16]] [SUCCESS] Screenshot refreshed successfully
[[16:32:16]] [SUCCESS] Screenshot refreshed successfully
[[16:32:16]] [SUCCESS] Screenshot refreshed
[[16:32:16]] [INFO] Refreshing screenshot...
[[16:32:16]] [INFO] GRwHMVK4sA=pass
[[16:32:13]] [INFO] GRwHMVK4sA=running
[[16:32:13]] [INFO] Executing action 21/61: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[16:32:13]] [SUCCESS] Screenshot refreshed successfully
[[16:32:13]] [SUCCESS] Screenshot refreshed successfully
[[16:32:13]] [SUCCESS] Screenshot refreshed
[[16:32:13]] [INFO] Refreshing screenshot...
[[16:32:13]] [INFO] V42eHtTRYW=pass
[[16:32:06]] [INFO] V42eHtTRYW=running
[[16:32:06]] [INFO] Executing action 20/61: Wait for 5 ms
[[16:32:06]] [SUCCESS] Screenshot refreshed successfully
[[16:32:06]] [SUCCESS] Screenshot refreshed successfully
[[16:32:06]] [SUCCESS] Screenshot refreshed
[[16:32:06]] [INFO] Refreshing screenshot...
[[16:32:06]] [INFO] LfyQctrEJn=pass
[[16:32:04]] [INFO] LfyQctrEJn=running
[[16:32:04]] [INFO] Executing action 19/61: Launch app: com.apple.Preferences
[[16:32:04]] [SUCCESS] Screenshot refreshed successfully
[[16:32:04]] [SUCCESS] Screenshot refreshed successfully
[[16:32:04]] [SUCCESS] Screenshot refreshed
[[16:32:04]] [INFO] Refreshing screenshot...
[[16:32:04]] [INFO] seQcUKjkSU=pass
[[16:32:02]] [INFO] seQcUKjkSU=running
[[16:32:02]] [INFO] Executing action 18/61: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[16:32:02]] [SUCCESS] Screenshot refreshed successfully
[[16:32:02]] [SUCCESS] Screenshot refreshed successfully
[[16:32:02]] [SUCCESS] Screenshot refreshed
[[16:32:02]] [INFO] Refreshing screenshot...
[[16:32:02]] [INFO] UpUSVInizv=pass
[[16:31:59]] [SUCCESS] Screenshot refreshed successfully
[[16:31:59]] [SUCCESS] Screenshot refreshed successfully
[[16:31:59]] [INFO] UpUSVInizv=running
[[16:31:59]] [INFO] Executing action 17/61: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[16:31:59]] [SUCCESS] Screenshot refreshed
[[16:31:59]] [INFO] Refreshing screenshot...
[[16:31:59]] [INFO] WoymrHdtrO=pass
[[16:31:57]] [SUCCESS] Screenshot refreshed successfully
[[16:31:57]] [SUCCESS] Screenshot refreshed successfully
[[16:31:57]] [INFO] WoymrHdtrO=running
[[16:31:57]] [INFO] Executing action 16/61: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[16:31:57]] [SUCCESS] Screenshot refreshed
[[16:31:57]] [INFO] Refreshing screenshot...
[[16:31:57]] [INFO] 6xgrAWyfZ4=pass
[[16:31:55]] [SUCCESS] Screenshot refreshed successfully
[[16:31:55]] [SUCCESS] Screenshot refreshed successfully
[[16:31:55]] [INFO] 6xgrAWyfZ4=running
[[16:31:55]] [INFO] Executing action 15/61: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[16:31:54]] [SUCCESS] Screenshot refreshed
[[16:31:54]] [INFO] Refreshing screenshot...
[[16:31:54]] [INFO] eSr9EFlJek=pass
[[16:31:52]] [SUCCESS] Screenshot refreshed successfully
[[16:31:52]] [SUCCESS] Screenshot refreshed successfully
[[16:31:52]] [INFO] eSr9EFlJek=running
[[16:31:52]] [INFO] Executing action 14/61: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[16:31:52]] [SUCCESS] Screenshot refreshed
[[16:31:52]] [INFO] Refreshing screenshot...
[[16:31:52]] [INFO] 3KNqlNy6Bj=pass
[[16:31:49]] [SUCCESS] Screenshot refreshed successfully
[[16:31:49]] [SUCCESS] Screenshot refreshed successfully
[[16:31:49]] [INFO] 3KNqlNy6Bj=running
[[16:31:49]] [INFO] Executing action 13/61: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[16:31:49]] [SUCCESS] Screenshot refreshed
[[16:31:49]] [INFO] Refreshing screenshot...
[[16:31:49]] [INFO] cokvFXhj4c=pass
[[16:31:47]] [SUCCESS] Screenshot refreshed successfully
[[16:31:47]] [SUCCESS] Screenshot refreshed successfully
[[16:31:47]] [INFO] cokvFXhj4c=running
[[16:31:47]] [INFO] Executing action 12/61: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[16:31:47]] [SUCCESS] Screenshot refreshed
[[16:31:47]] [INFO] Refreshing screenshot...
[[16:31:47]] [INFO] oSQ8sPdVOJ=pass
[[16:31:42]] [INFO] oSQ8sPdVOJ=running
[[16:31:42]] [INFO] Executing action 11/61: Restart app: env[appid]
[[16:31:42]] [SUCCESS] Screenshot refreshed successfully
[[16:31:42]] [SUCCESS] Screenshot refreshed successfully
[[16:31:41]] [SUCCESS] Screenshot refreshed
[[16:31:41]] [INFO] Refreshing screenshot...
[[16:31:41]] [INFO] V42eHtTRYW=pass
[[16:31:35]] [INFO] V42eHtTRYW=running
[[16:31:35]] [INFO] Executing action 10/61: Wait for 5 ms
[[16:31:35]] [SUCCESS] Screenshot refreshed successfully
[[16:31:35]] [SUCCESS] Screenshot refreshed successfully
[[16:31:34]] [SUCCESS] Screenshot refreshed
[[16:31:34]] [INFO] Refreshing screenshot...
[[16:31:34]] [INFO] jUCAk6GJc4=pass
[[16:31:32]] [INFO] jUCAk6GJc4=running
[[16:31:32]] [INFO] Executing action 9/61: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[16:31:32]] [SUCCESS] Screenshot refreshed successfully
[[16:31:32]] [SUCCESS] Screenshot refreshed successfully
[[16:31:31]] [SUCCESS] Screenshot refreshed
[[16:31:31]] [INFO] Refreshing screenshot...
[[16:31:31]] [INFO] V42eHtTRYW=pass
[[16:31:25]] [INFO] V42eHtTRYW=running
[[16:31:25]] [INFO] Executing action 8/61: Wait for 5 ms
[[16:31:25]] [SUCCESS] Screenshot refreshed successfully
[[16:31:25]] [SUCCESS] Screenshot refreshed successfully
[[16:31:24]] [SUCCESS] Screenshot refreshed
[[16:31:24]] [INFO] Refreshing screenshot...
[[16:31:24]] [INFO] w1RV76df9x=pass
[[16:31:20]] [INFO] w1RV76df9x=running
[[16:31:20]] [INFO] Executing action 7/61: Tap on Text: "Wi-Fi"
[[16:31:20]] [SUCCESS] Screenshot refreshed successfully
[[16:31:20]] [SUCCESS] Screenshot refreshed successfully
[[16:31:19]] [SUCCESS] Screenshot refreshed
[[16:31:19]] [INFO] Refreshing screenshot...
[[16:31:19]] [INFO] LfyQctrEJn=pass
[[16:31:17]] [SUCCESS] Screenshot refreshed successfully
[[16:31:17]] [SUCCESS] Screenshot refreshed successfully
[[16:31:17]] [INFO] LfyQctrEJn=running
[[16:31:17]] [INFO] Executing action 6/61: Launch app: com.apple.Preferences
[[16:31:16]] [SUCCESS] Screenshot refreshed
[[16:31:16]] [INFO] Refreshing screenshot...
[[16:31:16]] [INFO] mIKA85kXaW=pass
[[16:31:15]] [SUCCESS] Screenshot refreshed successfully
[[16:31:15]] [SUCCESS] Screenshot refreshed successfully
[[16:31:13]] [INFO] mIKA85kXaW=running
[[16:31:13]] [INFO] Executing action 5/61: Terminate app: com.apple.Preferences
[[16:31:13]] [SUCCESS] Screenshot refreshed
[[16:31:13]] [INFO] Refreshing screenshot...
[[16:31:12]] [SUCCESS] Screenshot refreshed
[[16:31:12]] [INFO] Refreshing screenshot...
[[16:31:08]] [SUCCESS] Screenshot refreshed successfully
[[16:31:08]] [SUCCESS] Screenshot refreshed successfully
[[16:31:07]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[16:31:07]] [SUCCESS] Screenshot refreshed
[[16:31:07]] [INFO] Refreshing screenshot...
[[16:31:02]] [SUCCESS] Screenshot refreshed successfully
[[16:31:02]] [SUCCESS] Screenshot refreshed successfully
[[16:31:02]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[16:31:02]] [SUCCESS] Screenshot refreshed
[[16:31:02]] [INFO] Refreshing screenshot...
[[16:30:57]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[16:30:57]] [SUCCESS] Screenshot refreshed successfully
[[16:30:57]] [SUCCESS] Screenshot refreshed successfully
[[16:30:56]] [SUCCESS] Screenshot refreshed
[[16:30:56]] [INFO] Refreshing screenshot...
[[16:30:52]] [SUCCESS] Screenshot refreshed successfully
[[16:30:52]] [SUCCESS] Screenshot refreshed successfully
[[16:30:52]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[16:30:52]] [SUCCESS] Screenshot refreshed
[[16:30:52]] [INFO] Refreshing screenshot...
[[16:30:48]] [SUCCESS] Screenshot refreshed successfully
[[16:30:48]] [SUCCESS] Screenshot refreshed successfully
[[16:30:48]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[16:30:48]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[16:30:48]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[16:30:48]] [INFO] x6vffndoRV=running
[[16:30:48]] [INFO] Executing action 4/61: Execute Test Case: Kmart-Signin (6 steps)
[[16:30:47]] [SUCCESS] Screenshot refreshed
[[16:30:47]] [INFO] Refreshing screenshot...
[[16:30:47]] [INFO] rJ86z4njuR=pass
[[16:30:45]] [SUCCESS] Screenshot refreshed successfully
[[16:30:45]] [SUCCESS] Screenshot refreshed successfully
[[16:30:45]] [INFO] rJ86z4njuR=running
[[16:30:45]] [INFO] Executing action 3/61: iOS Function: alert_accept
[[16:30:44]] [SUCCESS] Screenshot refreshed
[[16:30:44]] [INFO] Refreshing screenshot...
[[16:30:44]] [INFO] veukWo4573=pass
[[16:30:40]] [SUCCESS] Screenshot refreshed successfully
[[16:30:40]] [SUCCESS] Screenshot refreshed successfully
[[16:30:40]] [INFO] veukWo4573=running
[[16:30:40]] [INFO] Executing action 2/61: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[16:30:39]] [SUCCESS] Screenshot refreshed
[[16:30:39]] [INFO] Refreshing screenshot...
[[16:30:39]] [INFO] XEbZHdi0GT=pass
[[16:30:33]] [INFO] XEbZHdi0GT=running
[[16:30:33]] [INFO] Executing action 1/61: Restart app: env[appid]
[[16:30:33]] [INFO] ExecutionManager: Starting execution of 61 actions...
[[16:30:33]] [SUCCESS] Cleared 1 screenshots from database
[[16:30:33]] [INFO] Clearing screenshots from database before execution...
[[16:30:33]] [SUCCESS] All screenshots deleted successfully
[[16:30:33]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[16:30:33]] [INFO] Skipping report initialization - single test case execution
[[16:30:29]] [SUCCESS] All screenshots deleted successfully
[[16:30:29]] [SUCCESS] Loaded test case "App Settings AU" with 61 actions
[[16:30:29]] [SUCCESS] Added action: cleanupSteps
[[16:30:29]] [SUCCESS] Added action: wait
[[16:30:29]] [SUCCESS] Added action: ifElseSteps
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: restartApp
[[16:30:29]] [SUCCESS] Added action: ifElseSteps
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: swipeTillVisible
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tapOnText
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: ifElseSteps
[[16:30:29]] [SUCCESS] Added action: tapOnText
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: tapOnText
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: ifElseSteps
[[16:30:29]] [SUCCESS] Added action: tapOnText
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: exists
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: iosFunctions
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: restartApp
[[16:30:29]] [SUCCESS] Added action: tapOnText
[[16:30:29]] [SUCCESS] Added action: swipe
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: restartApp
[[16:30:29]] [SUCCESS] Added action: wait
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: wait
[[16:30:29]] [SUCCESS] Added action: launchApp
[[16:30:29]] [SUCCESS] Added action: exists
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: exists
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: exists
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: exists
[[16:30:29]] [SUCCESS] Added action: restartApp
[[16:30:29]] [SUCCESS] Added action: wait
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: wait
[[16:30:29]] [SUCCESS] Added action: tapOnText
[[16:30:29]] [SUCCESS] Added action: launchApp
[[16:30:29]] [SUCCESS] Added action: terminateApp
[[16:30:29]] [SUCCESS] Added action: multiStep
[[16:30:29]] [SUCCESS] Added action: iosFunctions
[[16:30:29]] [SUCCESS] Added action: tap
[[16:30:29]] [SUCCESS] Added action: restartApp
[[16:30:29]] [INFO] All actions cleared
[[16:30:29]] [INFO] Cleaning up screenshots...
[[16:30:02]] [SUCCESS] Screenshot refreshed successfully
[[16:30:01]] [SUCCESS] Screenshot refreshed
[[16:30:01]] [INFO] Refreshing screenshot...
[[16:30:00]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[16:30:00]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[16:29:53]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[16:28:53]] [SUCCESS] Found 1 device(s)
[[16:28:52]] [INFO] Refreshing device list...
