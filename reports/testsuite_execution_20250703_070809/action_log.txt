Action Log - 2025-07-03 07:22:01
================================================================================

[[07:22:01]] [INFO] Generating execution report...
[[07:22:01]] [SUCCESS] All tests passed successfully!
[[07:22:01]] [INFO] xyHVihJMBi=fail
[[07:22:01]] [ERROR] Action 50 failed: Element not found or not tappable: xpath='//android.widget.Button[@content-desc="txtSign out"]'
[[07:21:31]] [SUCCESS] Screenshot refreshed successfully
[[07:21:31]] [SUCCESS] Screenshot refreshed successfully
[[07:21:31]] [INFO] xyHVihJMBi=running
[[07:21:31]] [INFO] Executing action 50/50: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[07:21:30]] [SUCCESS] Screenshot refreshed
[[07:21:30]] [INFO] Refreshing screenshot...
[[07:21:30]] [INFO] mWeLQtXiL6=pass
[[07:21:25]] [INFO] mWeLQtXiL6=running
[[07:21:25]] [INFO] Executing action 49/50: Swipe from (50%, 70%) to (50%, 30%)
[[07:21:25]] [INFO] rkwVoJGZG4=fail
[[07:21:25]] [ERROR] Action 48 failed: Element not found or not tappable: xpath='//android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]'
[[07:20:58]] [INFO] rkwVoJGZG4=running
[[07:20:58]] [INFO] Executing action 48/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[07:20:58]] [INFO] 0f2FSZYjWq=fail
[[07:20:58]] [ERROR] Action 47 failed: Text not found: "3000"
[[07:18:48]] [INFO] 0f2FSZYjWq=running
[[07:18:48]] [INFO] Executing action 47/50: Check if element with text="3000" exists
[[07:18:48]] [INFO] Tebej51pT2=fail
[[07:18:48]] [ERROR] Action 46 failed: Error tapping element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[07:18:16]] [SUCCESS] Screenshot refreshed successfully
[[07:18:16]] [SUCCESS] Screenshot refreshed successfully
[[07:18:16]] [INFO] Tebej51pT2=running
[[07:18:16]] [INFO] Executing action 46/50: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[07:18:15]] [SUCCESS] Screenshot refreshed
[[07:18:15]] [INFO] Refreshing screenshot...
[[07:18:15]] [INFO] eVytJrry9x=pass
[[07:18:13]] [SUCCESS] Screenshot refreshed successfully
[[07:18:13]] [SUCCESS] Screenshot refreshed successfully
[[07:18:13]] [INFO] eVytJrry9x=running
[[07:18:13]] [INFO] Executing action 45/50: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[07:18:13]] [SUCCESS] Screenshot refreshed
[[07:18:13]] [INFO] Refreshing screenshot...
[[07:18:13]] [INFO] s8h8VDUIOC=pass
[[07:18:10]] [SUCCESS] Screenshot refreshed successfully
[[07:18:10]] [SUCCESS] Screenshot refreshed successfully
[[07:18:09]] [INFO] s8h8VDUIOC=running
[[07:18:09]] [INFO] Executing action 44/50: Swipe from (50%, 70%) to (50%, 30%)
[[07:18:09]] [SUCCESS] Screenshot refreshed
[[07:18:09]] [INFO] Refreshing screenshot...
[[07:18:09]] [INFO] GYK47u1y3A=pass
[[07:18:06]] [SUCCESS] Screenshot refreshed successfully
[[07:18:06]] [SUCCESS] Screenshot refreshed successfully
[[07:18:06]] [INFO] GYK47u1y3A=running
[[07:18:06]] [INFO] Executing action 43/50: Android Function: send_key_event - Key Event: TAB
[[07:18:06]] [SUCCESS] Screenshot refreshed
[[07:18:06]] [INFO] Refreshing screenshot...
[[07:18:06]] [INFO] ZWpYNcpbFA=pass
[[07:18:02]] [SUCCESS] Screenshot refreshed successfully
[[07:18:02]] [SUCCESS] Screenshot refreshed successfully
[[07:18:02]] [INFO] ZWpYNcpbFA=running
[[07:18:02]] [INFO] Executing action 42/50: Tap on Text: "VIC"
[[07:18:01]] [SUCCESS] Screenshot refreshed
[[07:18:01]] [INFO] Refreshing screenshot...
[[07:18:01]] [INFO] QpBLC6BStn=pass
[[07:17:59]] [SUCCESS] Screenshot refreshed successfully
[[07:17:59]] [SUCCESS] Screenshot refreshed successfully
[[07:17:59]] [INFO] QpBLC6BStn=running
[[07:17:59]] [INFO] Executing action 41/50: textClear action
[[07:17:58]] [SUCCESS] Screenshot refreshed
[[07:17:58]] [INFO] Refreshing screenshot...
[[07:17:58]] [INFO] G4A3KBlXHq=pass
[[07:17:18]] [SUCCESS] Screenshot refreshed successfully
[[07:17:18]] [SUCCESS] Screenshot refreshed successfully
[[07:17:17]] [INFO] G4A3KBlXHq=running
[[07:17:17]] [INFO] Executing action 40/50: Tap on Text: "Nearby"
[[07:17:17]] [SUCCESS] Screenshot refreshed
[[07:17:17]] [INFO] Refreshing screenshot...
[[07:17:17]] [INFO] 3gJsiap2Ds=pass
[[07:17:13]] [SUCCESS] Screenshot refreshed successfully
[[07:17:13]] [SUCCESS] Screenshot refreshed successfully
[[07:17:13]] [INFO] 3gJsiap2Ds=running
[[07:17:13]] [INFO] Executing action 39/50: Tap on Text: "Collect"
[[07:17:13]] [SUCCESS] Screenshot refreshed
[[07:17:13]] [INFO] Refreshing screenshot...
[[07:17:13]] [INFO] qofJDqXBME=pass
[[07:17:05]] [SUCCESS] Screenshot refreshed successfully
[[07:17:05]] [SUCCESS] Screenshot refreshed successfully
[[07:17:05]] [INFO] qofJDqXBME=running
[[07:17:05]] [INFO] Executing action 38/50: Wait till text appears: "Delivery"
[[07:17:04]] [SUCCESS] Screenshot refreshed
[[07:17:04]] [INFO] Refreshing screenshot...
[[07:17:04]] [INFO] rkwVoJGZG4=pass
[[07:17:03]] [SUCCESS] Screenshot refreshed successfully
[[07:17:03]] [SUCCESS] Screenshot refreshed successfully
[[07:17:02]] [INFO] rkwVoJGZG4=running
[[07:17:02]] [INFO] Executing action 37/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[07:17:02]] [SUCCESS] Screenshot refreshed
[[07:17:02]] [INFO] Refreshing screenshot...
[[07:17:02]] [INFO] 94ikwhIEE2=pass
[[07:16:58]] [SUCCESS] Screenshot refreshed successfully
[[07:16:58]] [SUCCESS] Screenshot refreshed successfully
[[07:16:14]] [INFO] 94ikwhIEE2=running
[[07:16:14]] [INFO] Executing action 36/50: Tap on Text: "bag"
[[07:16:13]] [SUCCESS] Screenshot refreshed
[[07:16:13]] [INFO] Refreshing screenshot...
[[07:16:13]] [INFO] DfwaiVZ8Z9=pass
[[07:16:10]] [SUCCESS] Screenshot refreshed successfully
[[07:16:10]] [SUCCESS] Screenshot refreshed successfully
[[07:16:10]] [INFO] DfwaiVZ8Z9=running
[[07:16:10]] [INFO] Executing action 35/50: Swipe from (50%, 70%) to (50%, 50%)
[[07:16:09]] [SUCCESS] Screenshot refreshed
[[07:16:09]] [INFO] Refreshing screenshot...
[[07:16:09]] [INFO] eRCmRhc3re=pass
[[07:16:07]] [SUCCESS] Screenshot refreshed successfully
[[07:16:07]] [SUCCESS] Screenshot refreshed successfully
[[07:16:06]] [INFO] eRCmRhc3re=running
[[07:16:06]] [INFO] Executing action 34/50: Check if element with text="Broadway" exists
[[07:16:06]] [SUCCESS] Screenshot refreshed
[[07:16:06]] [INFO] Refreshing screenshot...
[[07:16:06]] [INFO] E2jpN7BioW=pass
[[07:16:04]] [SUCCESS] Screenshot refreshed successfully
[[07:16:04]] [SUCCESS] Screenshot refreshed successfully
[[07:16:03]] [INFO] E2jpN7BioW=running
[[07:16:03]] [INFO] Executing action 33/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[07:16:03]] [SUCCESS] Screenshot refreshed
[[07:16:03]] [INFO] Refreshing screenshot...
[[07:16:03]] [INFO] IOc0IwmLPQ=pass
[[07:16:01]] [SUCCESS] Screenshot refreshed successfully
[[07:16:01]] [SUCCESS] Screenshot refreshed successfully
[[07:16:01]] [INFO] IOc0IwmLPQ=running
[[07:16:01]] [INFO] Executing action 32/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[07:16:00]] [SUCCESS] Screenshot refreshed
[[07:16:00]] [INFO] Refreshing screenshot...
[[07:16:00]] [INFO] H0ODFz7sWJ=pass
[[07:15:18]] [SUCCESS] Screenshot refreshed successfully
[[07:15:18]] [SUCCESS] Screenshot refreshed successfully
[[07:15:17]] [INFO] H0ODFz7sWJ=running
[[07:15:17]] [INFO] Executing action 31/50: Tap on Text: "2000"
[[07:15:17]] [SUCCESS] Screenshot refreshed
[[07:15:17]] [INFO] Refreshing screenshot...
[[07:15:17]] [INFO] pldheRUBVi=pass
[[07:15:15]] [SUCCESS] Screenshot refreshed successfully
[[07:15:15]] [SUCCESS] Screenshot refreshed successfully
[[07:15:15]] [INFO] pldheRUBVi=running
[[07:15:15]] [INFO] Executing action 30/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[07:15:14]] [SUCCESS] Screenshot refreshed
[[07:15:14]] [INFO] Refreshing screenshot...
[[07:15:14]] [INFO] uZHvvAzVfx=pass
[[07:15:11]] [SUCCESS] Screenshot refreshed successfully
[[07:15:11]] [SUCCESS] Screenshot refreshed successfully
[[07:15:11]] [INFO] uZHvvAzVfx=running
[[07:15:11]] [INFO] Executing action 29/50: textClear action
[[07:15:11]] [SUCCESS] Screenshot refreshed
[[07:15:11]] [INFO] Refreshing screenshot...
[[07:15:11]] [INFO] WmNWcsWVHv=pass
[[07:15:07]] [SUCCESS] Screenshot refreshed successfully
[[07:15:07]] [SUCCESS] Screenshot refreshed successfully
[[07:15:07]] [INFO] WmNWcsWVHv=running
[[07:15:07]] [INFO] Executing action 28/50: Tap on Text: "4000"
[[07:15:06]] [SUCCESS] Screenshot refreshed
[[07:15:06]] [INFO] Refreshing screenshot...
[[07:15:06]] [INFO] lnjoz8hHUU=pass
[[07:15:00]] [SUCCESS] Screenshot refreshed successfully
[[07:15:00]] [SUCCESS] Screenshot refreshed successfully
[[07:15:00]] [INFO] lnjoz8hHUU=running
[[07:15:00]] [INFO] Executing action 27/50: Tap on Text: "Edit"
[[07:14:59]] [SUCCESS] Screenshot refreshed
[[07:14:59]] [INFO] Refreshing screenshot...
[[07:14:59]] [INFO] BQ7Cxm53HQ=pass
[[07:14:57]] [SUCCESS] Screenshot refreshed successfully
[[07:14:57]] [SUCCESS] Screenshot refreshed successfully
[[07:14:57]] [INFO] BQ7Cxm53HQ=running
[[07:14:57]] [INFO] Executing action 26/50: Wait till text appears: "UNO"
[[07:14:56]] [SUCCESS] Screenshot refreshed
[[07:14:56]] [INFO] Refreshing screenshot...
[[07:14:56]] [INFO] VkUKQbf1Qt=pass
[[07:14:18]] [SUCCESS] Screenshot refreshed successfully
[[07:14:18]] [SUCCESS] Screenshot refreshed successfully
[[07:14:17]] [INFO] VkUKQbf1Qt=running
[[07:14:17]] [INFO] Executing action 25/50: Tap on Text: "UNO"
[[07:14:17]] [SUCCESS] Screenshot refreshed
[[07:14:17]] [INFO] Refreshing screenshot...
[[07:14:17]] [INFO] 73NABkfWyY=pass
[[07:14:11]] [SUCCESS] Screenshot refreshed successfully
[[07:14:11]] [SUCCESS] Screenshot refreshed successfully
[[07:14:10]] [INFO] 73NABkfWyY=running
[[07:14:10]] [INFO] Executing action 24/50: Check if element with text="Toowong" exists
[[07:14:10]] [SUCCESS] Screenshot refreshed
[[07:14:10]] [INFO] Refreshing screenshot...
[[07:14:10]] [INFO] E2jpN7BioW=pass
[[07:14:07]] [SUCCESS] Screenshot refreshed successfully
[[07:14:07]] [SUCCESS] Screenshot refreshed successfully
[[07:14:07]] [INFO] E2jpN7BioW=running
[[07:14:07]] [INFO] Executing action 23/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[07:14:07]] [SUCCESS] Screenshot refreshed
[[07:14:07]] [INFO] Refreshing screenshot...
[[07:14:07]] [INFO] IOc0IwmLPQ=pass
[[07:14:05]] [SUCCESS] Screenshot refreshed successfully
[[07:14:05]] [SUCCESS] Screenshot refreshed successfully
[[07:14:05]] [INFO] IOc0IwmLPQ=running
[[07:14:05]] [INFO] Executing action 22/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[07:14:04]] [SUCCESS] Screenshot refreshed
[[07:14:04]] [INFO] Refreshing screenshot...
[[07:14:04]] [INFO] VkUKQbf1Qt=pass
[[07:13:56]] [SUCCESS] Screenshot refreshed successfully
[[07:13:56]] [SUCCESS] Screenshot refreshed successfully
[[07:13:56]] [INFO] VkUKQbf1Qt=running
[[07:13:56]] [INFO] Executing action 21/50: Tap on Text: "CITY"
[[07:13:55]] [SUCCESS] Screenshot refreshed
[[07:13:55]] [INFO] Refreshing screenshot...
[[07:13:55]] [INFO] pldheRUBVi=pass
[[07:13:52]] [SUCCESS] Screenshot refreshed successfully
[[07:13:52]] [SUCCESS] Screenshot refreshed successfully
[[07:13:52]] [INFO] pldheRUBVi=running
[[07:13:52]] [INFO] Executing action 20/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[07:13:51]] [SUCCESS] Screenshot refreshed
[[07:13:51]] [INFO] Refreshing screenshot...
[[07:13:51]] [INFO] kbdEPCPYod=pass
[[07:13:40]] [INFO] kbdEPCPYod=running
[[07:13:40]] [INFO] Executing action 19/50: textClear action
[[07:13:40]] [INFO] pldheRUBVi=fail
[[07:13:40]] [ERROR] Action 18 failed: Error tapping element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[07:13:08]] [SUCCESS] Screenshot refreshed successfully
[[07:13:08]] [SUCCESS] Screenshot refreshed successfully
[[07:13:08]] [INFO] pldheRUBVi=running
[[07:13:08]] [INFO] Executing action 18/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[07:13:07]] [SUCCESS] Screenshot refreshed
[[07:13:07]] [INFO] Refreshing screenshot...
[[07:13:07]] [INFO] YhLhTn3Wtm=pass
[[07:13:01]] [SUCCESS] Screenshot refreshed successfully
[[07:13:01]] [SUCCESS] Screenshot refreshed successfully
[[07:13:00]] [INFO] YhLhTn3Wtm=running
[[07:13:00]] [INFO] Executing action 17/50: Wait for 5 ms
[[07:13:00]] [SUCCESS] Screenshot refreshed
[[07:13:00]] [INFO] Refreshing screenshot...
[[07:13:00]] [INFO] VkUKQbf1Qt=pass
[[07:12:17]] [SUCCESS] Screenshot refreshed successfully
[[07:12:17]] [SUCCESS] Screenshot refreshed successfully
[[07:12:17]] [INFO] VkUKQbf1Qt=running
[[07:12:17]] [INFO] Executing action 16/50: Tap on Text: "Edit"
[[07:12:17]] [SUCCESS] Screenshot refreshed
[[07:12:17]] [INFO] Refreshing screenshot...
[[07:12:17]] [INFO] BQ7Cxm53HQ=pass
[[07:12:13]] [SUCCESS] Screenshot refreshed successfully
[[07:12:13]] [SUCCESS] Screenshot refreshed successfully
[[07:12:12]] [INFO] BQ7Cxm53HQ=running
[[07:12:12]] [INFO] Executing action 15/50: Wait till text appears: "UNO"
[[07:12:12]] [SUCCESS] Screenshot refreshed
[[07:12:12]] [INFO] Refreshing screenshot...
[[07:12:12]] [INFO] IupxLP2Jsr=pass
[[07:12:10]] [SUCCESS] Screenshot refreshed successfully
[[07:12:10]] [SUCCESS] Screenshot refreshed successfully
[[07:12:10]] [INFO] IupxLP2Jsr=running
[[07:12:10]] [INFO] Executing action 14/50: Input text: "P_6225544"
[[07:12:09]] [SUCCESS] Screenshot refreshed
[[07:12:09]] [INFO] Refreshing screenshot...
[[07:12:09]] [INFO] 70iOOakiG7=pass
[[07:12:02]] [SUCCESS] Screenshot refreshed successfully
[[07:12:02]] [SUCCESS] Screenshot refreshed successfully
[[07:12:02]] [INFO] 70iOOakiG7=running
[[07:12:02]] [INFO] Executing action 13/50: Tap on Text: "Find"
[[07:12:01]] [SUCCESS] Screenshot refreshed
[[07:12:01]] [INFO] Refreshing screenshot...
[[07:12:01]] [INFO] E2jpN7BioW=pass
[[07:11:59]] [SUCCESS] Screenshot refreshed successfully
[[07:11:59]] [SUCCESS] Screenshot refreshed successfully
[[07:11:58]] [INFO] E2jpN7BioW=running
[[07:11:58]] [INFO] Executing action 12/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[07:11:58]] [SUCCESS] Screenshot refreshed
[[07:11:58]] [INFO] Refreshing screenshot...
[[07:11:58]] [INFO] kDnmoQJG4o=pass
[[07:11:47]] [INFO] kDnmoQJG4o=running
[[07:11:47]] [INFO] Executing action 11/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[07:11:47]] [INFO] mw9GQ4mzRE=fail
[[07:11:47]] [ERROR] Action 10 failed: Error tapping on text: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[07:11:12]] [SUCCESS] Screenshot refreshed successfully
[[07:11:12]] [SUCCESS] Screenshot refreshed successfully
[[07:11:12]] [INFO] mw9GQ4mzRE=running
[[07:11:12]] [INFO] Executing action 10/50: Tap on Text: "BC"
[[07:11:11]] [SUCCESS] Screenshot refreshed
[[07:11:11]] [INFO] Refreshing screenshot...
[[07:11:11]] [INFO] pldheRUBVi=pass
[[07:11:09]] [SUCCESS] Screenshot refreshed successfully
[[07:11:09]] [SUCCESS] Screenshot refreshed successfully
[[07:11:08]] [INFO] pldheRUBVi=running
[[07:11:08]] [INFO] Executing action 9/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[07:11:08]] [SUCCESS] Screenshot refreshed
[[07:11:08]] [INFO] Refreshing screenshot...
[[07:11:08]] [INFO] kbdEPCPYod=pass
[[07:11:05]] [SUCCESS] Screenshot refreshed successfully
[[07:11:05]] [SUCCESS] Screenshot refreshed successfully
[[07:11:04]] [INFO] kbdEPCPYod=running
[[07:11:04]] [INFO] Executing action 8/50: textClear action
[[07:11:04]] [SUCCESS] Screenshot refreshed
[[07:11:04]] [INFO] Refreshing screenshot...
[[07:11:04]] [INFO] pldheRUBVi=pass
[[07:11:01]] [SUCCESS] Screenshot refreshed successfully
[[07:11:01]] [SUCCESS] Screenshot refreshed successfully
[[07:11:01]] [INFO] pldheRUBVi=running
[[07:11:01]] [INFO] Executing action 7/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[07:11:00]] [SUCCESS] Screenshot refreshed
[[07:11:00]] [INFO] Refreshing screenshot...
[[07:11:00]] [INFO] QMXBlswP6H=pass
[[07:10:56]] [SUCCESS] Screenshot refreshed successfully
[[07:10:56]] [SUCCESS] Screenshot refreshed successfully
[[07:10:27]] [INFO] QMXBlswP6H=running
[[07:10:27]] [INFO] Executing action 6/50: Tap on Text: "Edit"
[[07:10:26]] [SUCCESS] Screenshot refreshed
[[07:10:26]] [INFO] Refreshing screenshot...
[[07:10:26]] [INFO] RLz6vQo3ag=pass
[[07:10:24]] [SUCCESS] Screenshot refreshed successfully
[[07:10:24]] [SUCCESS] Screenshot refreshed successfully
[[07:10:19]] [INFO] RLz6vQo3ag=running
[[07:10:19]] [INFO] Executing action 5/50: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[07:10:19]] [SUCCESS] Screenshot refreshed
[[07:10:19]] [INFO] Refreshing screenshot...
[[07:10:19]] [SUCCESS] Screenshot refreshed
[[07:10:19]] [INFO] Refreshing screenshot...
[[07:10:17]] [SUCCESS] Screenshot refreshed successfully
[[07:10:17]] [SUCCESS] Screenshot refreshed successfully
[[07:10:17]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[07:10:16]] [SUCCESS] Screenshot refreshed
[[07:10:16]] [INFO] Refreshing screenshot...
[[07:10:14]] [SUCCESS] Screenshot refreshed successfully
[[07:10:14]] [SUCCESS] Screenshot refreshed successfully
[[07:10:14]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[07:10:13]] [SUCCESS] Screenshot refreshed
[[07:10:13]] [INFO] Refreshing screenshot...
[[07:10:11]] [SUCCESS] Screenshot refreshed successfully
[[07:10:11]] [SUCCESS] Screenshot refreshed successfully
[[07:10:11]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[07:10:10]] [SUCCESS] Screenshot refreshed
[[07:10:10]] [INFO] Refreshing screenshot...
[[07:10:06]] [SUCCESS] Screenshot refreshed successfully
[[07:10:06]] [SUCCESS] Screenshot refreshed successfully
[[07:10:06]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[07:10:05]] [SUCCESS] Screenshot refreshed
[[07:10:05]] [INFO] Refreshing screenshot...
[[07:10:03]] [SUCCESS] Screenshot refreshed successfully
[[07:10:03]] [SUCCESS] Screenshot refreshed successfully
[[07:10:03]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[07:10:03]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[07:10:03]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[07:10:03]] [INFO] xz8njynjpZ=running
[[07:10:03]] [INFO] Executing action 4/50: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[07:10:02]] [SUCCESS] Screenshot refreshed
[[07:10:02]] [INFO] Refreshing screenshot...
[[07:10:02]] [INFO] J9loj6Zl5K=pass
[[07:10:00]] [SUCCESS] Screenshot refreshed successfully
[[07:10:00]] [SUCCESS] Screenshot refreshed successfully
[[07:10:00]] [INFO] J9loj6Zl5K=running
[[07:10:00]] [INFO] Executing action 3/50: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[07:10:00]] [SUCCESS] Screenshot refreshed
[[07:10:00]] [INFO] Refreshing screenshot...
[[07:10:00]] [INFO] Y8vz7AJD1i=pass
[[07:09:53]] [SUCCESS] Screenshot refreshed successfully
[[07:09:53]] [SUCCESS] Screenshot refreshed successfully
[[07:09:53]] [INFO] Y8vz7AJD1i=running
[[07:09:53]] [INFO] Executing action 2/50: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:09:52]] [SUCCESS] Screenshot refreshed
[[07:09:52]] [INFO] Refreshing screenshot...
[[07:09:52]] [INFO] H9fy9qcFbZ=pass
[[07:09:49]] [INFO] H9fy9qcFbZ=running
[[07:09:49]] [INFO] Executing action 1/50: Launch app: au.com.kmart
[[07:09:49]] [INFO] ExecutionManager: Starting execution of 50 actions...
[[07:09:49]] [SUCCESS] Cleared 1 screenshots from database
[[07:09:49]] [INFO] Clearing screenshots from database before execution...
[[07:09:49]] [SUCCESS] All screenshots deleted successfully
[[07:09:49]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[07:09:49]] [INFO] Skipping report initialization - single test case execution
[[07:09:47]] [SUCCESS] All screenshots deleted successfully
[[07:09:47]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 50 actions
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: swipe
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: exists
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: swipe
[[07:09:47]] [SUCCESS] Added action: androidFunctions
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: textClear
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: waitTill
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: swipe
[[07:09:47]] [SUCCESS] Added action: exists
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: waitTill
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: textClear
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: waitTill
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: exists
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: waitTill
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: textClear
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: wait
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: waitTill
[[07:09:47]] [SUCCESS] Added action: text
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: waitTill
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: textClear
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: tapOnText
[[07:09:47]] [SUCCESS] Added action: waitTill
[[07:09:47]] [SUCCESS] Added action: multiStep
[[07:09:47]] [SUCCESS] Added action: waitTill
[[07:09:47]] [SUCCESS] Added action: tap
[[07:09:47]] [SUCCESS] Added action: launchApp
[[07:09:47]] [INFO] All actions cleared
[[07:09:47]] [INFO] Cleaning up screenshots...
[[07:09:42]] [SUCCESS] Screenshot refreshed successfully
[[07:09:40]] [SUCCESS] Screenshot refreshed
[[07:09:40]] [INFO] Refreshing screenshot...
[[07:09:39]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[07:09:39]] [INFO] Device info updated: RMX2151
[[07:09:30]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[07:09:21]] [SUCCESS] Found 1 device(s)
[[07:09:21]] [INFO] Refreshing device list...
