<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 7/1/2025, 10:53:43 AM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-passed">passed</span>
            <span class="stats-summary">
                <span class="passed-count">9</span> passed,
                <span class="failed-count">0</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 01/07/2025, 10:53:42
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="53 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 Postcode Flow_NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            53 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2462ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4846ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1158ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1923ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1736ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2053ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Search suburb or postcode <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3668ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                textClear action <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3240ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "8053" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3333ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till accessibility_id=btnSaveOrContinue <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2794ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Save" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3260ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate" <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4655ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with text="Papanui" exists <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">10619ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3999ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "Uno card" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2562ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1682ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Edit" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3670ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Search suburb or postcode <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3673ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                textClear action <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3063ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "0616" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3114ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till accessibility_id=btnSaveOrContinue <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2790ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Save" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3099ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with text="Henderson" exists <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">30144ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2195ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2041ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Edit" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3783ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Search suburb or postcode <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3632ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                textClear action <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3082ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "8053" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3142ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till accessibility_id=btnSaveOrContinue <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2770ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Save" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3083ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with text="Papanui" exists <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">11101ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Add to bag <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4688ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1545ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2441ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Delivery"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1624ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Collect" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3105ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1677ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Nearby" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3373ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: delete <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4152ms</span>
                        </li>
                        <li class="test-step" data-step-id="42" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-41')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "0616" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">5183ms</span>
                        </li>
                        <li class="test-step" data-step-id="43" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-42')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "AUCKLAND" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3338ms</span>
                        </li>
                        <li class="test-step" data-step-id="44" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-43')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Done <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4111ms</span>
                        </li>
                        <li class="test-step" data-step-id="45" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-44')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2837ms</span>
                        </li>
                        <li class="test-step" data-step-id="46" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-45')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2113ms</span>
                        </li>
                        <li class="test-step" data-step-id="47" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-46')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1501ms</span>
                        </li>
                        <li class="test-step" data-step-id="48" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-47')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1909ms</span>
                        </li>
                        <li class="test-step" data-step-id="49" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-48')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with text="Henderson" exists <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">30448ms</span>
                        </li>
                        <li class="test-step" data-step-id="50" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-49')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2451ms</span>
                        </li>
                        <li class="test-step" data-step-id="51" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-50')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2606ms</span>
                        </li>
                        <li class="test-step" data-step-id="52" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-51')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2052ms</span>
                        </li>
                        <li class="test-step" data-step-id="53" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-0-52')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="37 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 Browse & PDP NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            37 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3384ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1690ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeOther[@name="txtShopMenuTitle"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1155ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Toys" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2589ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Latest" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2639ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2506ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::* <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1534ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308) <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1957ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1377ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[product-share-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2738ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1419ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if image "product-share-logo.png" exists on screen <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2037ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2883ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Add to bag"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1784ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 50%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">4396ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "more" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2754ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1765ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1604ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "Kid toy" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2191ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if image "search-result-test-se.png" exists on screen <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2033ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2228ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2159ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1609ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1022ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1021ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2293ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3256ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3907ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "notebook" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2200ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1486ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1955ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1939ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1337ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1726ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1730ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1100ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-1-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="27 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #3 Delivery & CNC- NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            27 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2446ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-2-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">7535ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1233ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2919ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1785ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "P_43250042" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3212ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2415ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3092ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1984ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3124ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if image "cnc-tab-se.png" exists on screen <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2546ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Collect" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1833ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3919ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">15378ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1733ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3139ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">6814ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3003ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3171ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2130ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "P_43250042" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3219ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2496ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3113ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3020ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Delivery Buy Step NZ (33 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-2-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="39 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #4 NZ- MyAccount
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            39 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3340ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">5017ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-3-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4807ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1182ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3351ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1602ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="txtMy orders"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1598ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy orders"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2621ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">5019ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="main"]//XCUIElementTypeLink)[4] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2194ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">10388ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2665ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-3-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Print order details <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3842ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2412ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2615ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Return" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3176ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2060ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2063ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1606ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "details" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2792ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2068ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "address" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2816ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2034ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "payment" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2805ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2132ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2110ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2615ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "locator" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2742ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Nearby" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">6020ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Allow While Using App"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2302ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap and Type at (29, 262): "0616" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">5833ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "AUCKLAND" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3340ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-3-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2351ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2216ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Customer" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2834ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2167ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-3-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "out" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2723ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-3-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="28 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #5 All Sign ins NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            28 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3483ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-4-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3921ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1148ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1495ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Kmart-NZ-Signin (5 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1705ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2677ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">7691ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1712ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1595ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1155ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1620ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Kmart-NZ-Signin (5 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1686ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1309ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1721ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2307ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1680ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3234ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1669ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1763ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1149ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Kmart-NZ-Signin (5 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1786ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2339ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-4-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1789ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-4-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-4-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="41 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #6 Kmart-Prod Sign in NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            41 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3441ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">7743ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1283ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2871ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3598ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "env[uname]" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3755ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "env[pwd]" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">8873ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3104ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">14248ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2831ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">5858ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1238ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2923ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Apple" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3126ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 10 ms <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">10013ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Passcode" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1884ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="5"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2058ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="9"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2015ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="1"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1971ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="2"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2014ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="3"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2010ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="4"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2021ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">6056ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3218ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">14328ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2840ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-5-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">5900ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1285ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3003ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Google" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3145ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3840ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">4030ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3226ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-5-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2800ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-5-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-5-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="46 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #7 WishList NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            46 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3375ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-6-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4853ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1160ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1859ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">4108ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "Uno card" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2564ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1765ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2353ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2208ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2177ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2470ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2432ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2204ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1695ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2107ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[device-back-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2061ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2222ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1695ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3591ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3187ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1345ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1953ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Move" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2732ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1578ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2559ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1936ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1976ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2423ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe up till element xpath: "//XCUIElementTypeButton[@name="Move to wishlist"]" is visible <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">4445ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1647ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2782ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2175ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2467ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3208ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2741ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3204ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2930ms</span>
                        </li>
                        <li class="test-step" data-step-id="42" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-41')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1952ms</span>
                        </li>
                        <li class="test-step" data-step-id="43" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-42')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2605ms</span>
                        </li>
                        <li class="test-step" data-step-id="44" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-6-43')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2046ms</span>
                        </li>
                        <li class="test-step" data-step-id="45" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-6-44')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 10%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2129ms</span>
                        </li>
                        <li class="test-step" data-step-id="46" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-6-45')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="39 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #8 App Settings NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            39 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2206ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2410ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1131ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Kmart-NZ-Signin (6 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-7-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">32ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-7-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Launch app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">1207ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Wi-Fi" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3310ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1034ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3220ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">235ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">724ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">207ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">652ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">214ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">681ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">176ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-7-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Launch app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">125ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">762ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">5016ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3213ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">681ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "out" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="mobilesafa.png" data-action-id="mobilesafa" onclick="showStepDetails('step-7-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: com.apple.mobilesafari <span class="action-id-badge" title="Action ID: mobilesafa">mobilesafa</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "kmart nz" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.co.nz"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">724ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "notebook" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-7-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Add to bag <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-7-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">681ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-7-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-7-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="41 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #9 NZ- Performance
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            41 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3382ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3885ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "P_43515028" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2224ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1658ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">13893ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2034ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Done" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">4261ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3220ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1816ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3770ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "kids toys" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2360ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="Pagination.png" data-action-id="Pagination" onclick="showStepDetails('step-8-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Click_Paginations (10 steps) <span class="action-id-badge" title="Action ID: Pagination">Pagination</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3430ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1709ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Toys" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2541ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Age" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2583ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Months" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2474ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (5%, 50%) to (90%, 50%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3927ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (5%, 50%) to (90%, 50%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3287ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1603ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-8-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">3058ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1163ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2046ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "env[uname]" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2732ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2072ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "env[pwd]" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2531ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1021ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3967ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text - Text: "enn[cooker-id]" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2151ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1384ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1872ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2872ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2257ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2251ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (90%, 20%) to (30%, 20%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1655ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (90%, 20%) to (30%, 20%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1564ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-8-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1487ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1702ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-8-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "out" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">2529ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-8-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait till accessibility_id=txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">2307ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-8-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 01/07/2025, 10:53:42","testCases":[{"name":"Postcode Flow_NZ\n                            \n                            \n                                    PASSED\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            53 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"unknown","duration":"2462ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"unknown","duration":"4846ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1158ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"1923ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeOther[contains(@name,\"Deliver\")]","status":"unknown","duration":"1736ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeOther[contains(@name,\"Deliver\")]","status":"unknown","duration":"2053ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Search suburb or postcode","status":"unknown","duration":"3668ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"textClear action","status":"unknown","duration":"3240ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"8053\"","status":"unknown","duration":"3333ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till accessibility_id=btnSaveOrContinue","status":"unknown","duration":"2794ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on Text: \"Save\"","status":"unknown","duration":"3260ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"If exists: accessibility_id=\"btnUpdate\" (timeout: 10s) → Then click element: accessibility_id=\"btnUpdate\"","status":"unknown","duration":"4655ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Check if element with text=\"Papanui\" exists","status":"unknown","duration":"10619ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"3999ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"iOS Function: text - Text: \"Uno card\"","status":"unknown","duration":"2562ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"unknown","duration":"1682ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Edit\"","status":"unknown","duration":"3670ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: Search suburb or postcode","status":"unknown","duration":"3673ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"textClear action","status":"unknown","duration":"3063ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"0616\"","status":"unknown","duration":"3114ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till accessibility_id=btnSaveOrContinue","status":"unknown","duration":"2790ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on Text: \"Save\"","status":"unknown","duration":"3099ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Check if element with text=\"Henderson\" exists","status":"unknown","duration":"30144ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"2195ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"unknown","duration":"2041ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Edit\"","status":"unknown","duration":"3783ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: Search suburb or postcode","status":"unknown","duration":"3632ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"textClear action","status":"unknown","duration":"3082ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"8053\"","status":"unknown","duration":"3142ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till accessibility_id=btnSaveOrContinue","status":"unknown","duration":"2770ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on Text: \"Save\"","status":"unknown","duration":"3083ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Check if element with text=\"Papanui\" exists","status":"unknown","duration":"11101ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: Add to bag","status":"unknown","duration":"4688ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists","status":"unknown","duration":"1545ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"unknown","duration":"2441ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]","status":"unknown","duration":"1624ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Collect\"","status":"unknown","duration":"3105ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]","status":"unknown","duration":"1677ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Nearby\"","status":"unknown","duration":"3373ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: delete","status":"unknown","duration":"4152ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): \"0616\"","status":"unknown","duration":"5183ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"AUCKLAND\"","status":"unknown","duration":"3338ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: Done","status":"unknown","duration":"4111ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2837ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"unknown","duration":"2113ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"Continue shopping\"]","status":"unknown","duration":"1501ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]","status":"unknown","duration":"1909ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with text=\"Henderson\" exists","status":"unknown","duration":"30448ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"2451ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2606ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"2052ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Browse & PDP NZ\n                            \n                            \n                                    PASSED\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            37 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"unknown","duration":"3384ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]","status":"unknown","duration":"1690ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeOther[@name=\"txtShopMenuTitle\"]","status":"unknown","duration":"1155ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Toys\"","status":"unknown","duration":"2589ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Latest\"","status":"unknown","duration":"2639ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2506ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath= (//XCUIElementTypeButton[contains(@name,\"bag Add\")])[1]/parent::*","status":"unknown","duration":"1534ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)","status":"unknown","duration":"1957ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"unknown","duration":"1377ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[product-share-img]","status":"unknown","duration":"2738ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"Check out \")]\" exists","status":"unknown","duration":"1419ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if image \"product-share-logo.png\" exists on screen","status":"unknown","duration":"2037ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on image: banner-close-updated.png","status":"unknown","duration":"2883ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Add to bag\"]","status":"unknown","duration":"1784ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 50%)","status":"unknown","duration":"4396ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"more\"","status":"unknown","duration":"2754ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]","status":"unknown","duration":"1765ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]","status":"unknown","duration":"1604ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"Kid toy\"","status":"unknown","duration":"2191ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Check if image \"search-result-test-se.png\" exists on screen","status":"unknown","duration":"2033ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2228ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2159ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]","status":"unknown","duration":"1609ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeImage[@name=\"More\"]\" exists","status":"unknown","duration":"1022ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Scan barcode\"]\" exists","status":"unknown","duration":"1021ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2293ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"3256ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"3907ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"iOS Function: text - Text: \"notebook\"","status":"unknown","duration":"2200ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"unknown","duration":"1486ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]","status":"unknown","duration":"1955ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"unknown","duration":"1939ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"unknown","duration":"1337ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"unknown","duration":"1726ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"unknown","duration":"1730ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: env[appid]","status":"unknown","duration":"1100ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Delivery & CNC- NZ\n                            \n                            \n                                    PASSED\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            27 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"unknown","duration":"2446ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"unknown","duration":"7535ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1233ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"2919ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"1785ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"iOS Function: text - Text: \"P_43250042\"","status":"unknown","duration":"3212ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"unknown","duration":"2415ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]","status":"unknown","duration":"3092ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists","status":"unknown","duration":"1984ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"unknown","duration":"3124ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if image \"cnc-tab-se.png\" exists on screen","status":"unknown","duration":"2546ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Collect\"","status":"unknown","duration":"1833ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"3919ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"unknown","duration":"15378ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: banner-close-updated.png","status":"unknown","duration":"1733ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"3139ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"6814ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"3003ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"unknown","duration":"3171ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"2130ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"iOS Function: text - Text: \"P_43250042\"","status":"unknown","duration":"3219ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"unknown","duration":"2496ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]","status":"unknown","duration":"3113ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"unknown","duration":"3020ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Delivery Buy Step NZ (33 steps)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"NZ- MyAccount\n                            \n                            \n                                    PASSED\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            39 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"unknown","duration":"3340ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait for 5 ms","status":"unknown","duration":"5017ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"unknown","duration":"4807ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1182ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"3351ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]","status":"unknown","duration":"1602ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"txtMy orders\"]","status":"unknown","duration":"1598ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMy orders\"]","status":"unknown","duration":"2621ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"unknown","duration":"5019ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"main\"]//XCUIElementTypeLink)[4]","status":"unknown","duration":"2194ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Email tax invoice\"]\" is visible","status":"unknown","duration":"10388ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Email tax invoice\"]","status":"unknown","duration":"2665ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Print order details","status":"unknown","duration":"3842ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Cancel\"]","status":"unknown","duration":"2412ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2615ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Return\"","status":"unknown","duration":"3176ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"unknown","duration":"2060ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"2063ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]","status":"unknown","duration":"1606ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"details\"","status":"unknown","duration":"2792ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2068ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"address\"","status":"unknown","duration":"2816ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2034ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"payment\"","status":"unknown","duration":"2805ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2132ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2110ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2615ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"locator\"","status":"unknown","duration":"2742ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Nearby\"","status":"unknown","duration":"6020ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"If exists: xpath=\"//XCUIElementTypeButton[@name=\"Allow While Using App\"]\" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name=\"Allow While Using App\"]","status":"unknown","duration":"2302ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap and Type at (29, 262): \"0616\"","status":"unknown","duration":"5833ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"AUCKLAND\"","status":"unknown","duration":"3340ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Done\"]","status":"unknown","duration":"2351ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2216ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Customer\"","status":"unknown","duration":"2834ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2167ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"out\"","status":"unknown","duration":"2723ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"All Sign ins NZ\n                            \n                            \n                                    PASSED\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            28 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"unknown","duration":"3483ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"unknown","duration":"3921ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1148ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"1495ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (5 steps)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"1705ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2677ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"7691ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"unknown","duration":"1712ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtLog in\"]","status":"unknown","duration":"1595ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1155ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"1620ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (5 steps)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"unknown","duration":"1686ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]","status":"unknown","duration":"1309ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"1721ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2307ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"1680ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"3234ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"1669ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]","status":"unknown","duration":"1763ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1149ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (5 steps)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"1786ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2339ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"1789ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Kmart-Prod Sign in NZ\n                            \n                            \n                                    PASSED\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            41 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"unknown","duration":"3441ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"unknown","duration":"7743ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1283ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"2871ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"3598ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[uname]\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]","status":"unknown","duration":"3755ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[pwd]\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"unknown","duration":"8873ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"3104ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"14248ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists","status":"unknown","duration":"2831ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"unknown","duration":"5858ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1238ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"2923ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Apple\"","status":"unknown","duration":"3126ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait for 10 ms","status":"unknown","duration":"10013ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Passcode\"","status":"unknown","duration":"1884ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"5\"]","status":"unknown","duration":"2058ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"9\"]","status":"unknown","duration":"2015ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"1\"]","status":"unknown","duration":"1971ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"2\"]","status":"unknown","duration":"2014ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"3\"]","status":"unknown","duration":"2010ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"4\"]","status":"unknown","duration":"2021ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"unknown","duration":"6056ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"3218ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"14328ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists","status":"unknown","duration":"2840ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"unknown","duration":"5900ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1285ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"3003ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Google\"","status":"unknown","duration":"3145ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]","status":"unknown","duration":"3840ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"unknown","duration":"4030ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"3226ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"txtSign out\"]\" is visible","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"2800ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"WishList NZ\n                            \n                            \n                                    PASSED\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            46 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"unknown","duration":"3375ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"unknown","duration":"4853ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1160ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"1859ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"4108ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"iOS Function: text - Text: \"Uno card\"","status":"unknown","duration":"2564ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"unknown","duration":"1765ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"2353ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"2208ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"unknown","duration":"2177ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"unknown","duration":"2470ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2432ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]","status":"unknown","duration":"2204ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"unknown","duration":"1695ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"unknown","duration":"2107ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[device-back-img]","status":"unknown","duration":"2061ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]","status":"unknown","duration":"2222ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"unknown","duration":"1695ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"unknown","duration":"3591ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"unknown","duration":"3187ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"unknown","duration":"1345ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"unknown","duration":"1953ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Move\"","status":"unknown","duration":"2732ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]","status":"unknown","duration":"1578ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Remove\"","status":"unknown","duration":"2559ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]","status":"unknown","duration":"1936ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"unknown","duration":"1976ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"unknown","duration":"2423ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Move to wishlist\"]\" is visible","status":"unknown","duration":"4445ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]","status":"unknown","duration":"1647ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]","status":"unknown","duration":"2782ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: banner-close-updated.png","status":"unknown","duration":"2175ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"unknown","duration":"2467ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"unknown","duration":"3208ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Remove\"","status":"unknown","duration":"2741ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"unknown","duration":"3204ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Remove\"","status":"unknown","duration":"2930ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"1952ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"2605ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"unknown","duration":"2046ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 10%)","status":"unknown","duration":"2129ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"App Settings NZ\n                            \n                            \n                                    PASSED\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            39 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"unknown","duration":"2206ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]","status":"unknown","duration":"2410ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1131ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (6 steps)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Terminate app: com.apple.Preferences","status":"unknown","duration":"32ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Launch app: com.apple.Preferences","status":"unknown","duration":"1207ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Tap on Text: \"Wi-Fi\"","status":"unknown","duration":"3310ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]","status":"unknown","duration":"1034ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"3220ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"unknown","duration":"235ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]","status":"unknown","duration":"724ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"unknown","duration":"207ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]","status":"unknown","duration":"652ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"unknown","duration":"214ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]","status":"unknown","duration":"681ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"unknown","duration":"176ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Launch app: com.apple.Preferences","status":"unknown","duration":"125ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]","status":"unknown","duration":"762ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"unknown","duration":"5016ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"3213ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]","status":"unknown","duration":"681ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"out\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Restart app: com.apple.mobilesafari","status":"unknown","duration":"0ms","action_id":"mobilesafa","screenshot_filename":"mobilesafa.png","report_screenshot":"mobilesafa.png","resolved_screenshot":"screenshots/mobilesafa.png","action_id_screenshot":"screenshots/mobilesafa.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"kmart nz\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.co.nz\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]","status":"unknown","duration":"724ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"iOS Function: text - Text: \"notebook\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Add to bag","status":"unknown","duration":"0ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]","status":"unknown","duration":"681ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"NZ- Performance\n                            \n                            \n                                    PASSED\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            41 actions","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"unknown","duration":"3382ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"3885ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"iOS Function: text - Text: \"P_43515028\"","status":"unknown","duration":"2224ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"1658ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"13893ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Instruction Manual\"]","status":"unknown","duration":"2034ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Done\"","status":"unknown","duration":"4261ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"3220ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"unknown","duration":"1816ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"3770ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"iOS Function: text - Text: \"kids toys\"","status":"unknown","duration":"2360ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Execute Test Case: Click_Paginations (10 steps)","status":"unknown","duration":"0ms","action_id":"Pagination","screenshot_filename":"Pagination.png","report_screenshot":"Pagination.png","resolved_screenshot":"screenshots/Pagination.png","action_id_screenshot":"screenshots/Pagination.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"3430ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]","status":"unknown","duration":"1709ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Toys\"","status":"unknown","duration":"2541ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Age\"","status":"unknown","duration":"2583ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Months\"","status":"unknown","duration":"2474ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Swipe from (5%, 50%) to (90%, 50%)","status":"unknown","duration":"3927ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Swipe from (5%, 50%) to (90%, 50%)","status":"unknown","duration":"3287ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"unknown","duration":"1603ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"unknown","duration":"3058ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"unknown","duration":"1163ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"2046ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[uname]\"","status":"unknown","duration":"2732ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]","status":"unknown","duration":"2072ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text - Text: \"env[pwd]\"","status":"unknown","duration":"2531ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"unknown","duration":"1021ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"unknown","duration":"3967ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"iOS Function: text - Text: \"enn[cooker-id]\"","status":"unknown","duration":"2151ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"unknown","duration":"1384ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"1872ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"unknown","duration":"2872ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"unknown","duration":"2257ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"unknown","duration":"2251ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (90%, 20%) to (30%, 20%)","status":"unknown","duration":"1655ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Swipe from (90%, 20%) to (30%, 20%)","status":"unknown","duration":"1564ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"unknown","duration":"1487ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"1702ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"out\"","status":"unknown","duration":"2529ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Wait till accessibility_id=txtHomeAccountCtaSignIn","status":"unknown","duration":"2307ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]}],"passed":9,"failed":0,"skipped":0,"status":"passed","availableScreenshots":[],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>