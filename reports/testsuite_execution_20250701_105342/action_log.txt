Action Log - 2025-07-01 10:53:43
================================================================================

[[10:53:42]] [INFO] Generating execution report...
[[10:53:42]] [SUCCESS] All tests passed successfully!
[[10:53:42]] [INFO] Skipping test case - already passed: NZ- Performance
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            41 actions
[[10:53:42]] [INFO] OR0SKKnFxy=running
[[10:53:42]] [INFO] Executing action 311/351: Restart app: env[appid]
[[10:53:42]] [INFO] Skipping test case - already passed: App Settings NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            39 actions
[[10:53:42]] [INFO] XEbZHdi0GT=running
[[10:53:42]] [INFO] Executing action 272/351: Restart app: env[appid]
[[10:53:42]] [INFO] Skipping test case - already passed: WishList NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            46 actions
[[10:53:42]] [INFO] HotUJOd6oB=running
[[10:53:42]] [INFO] Executing action 226/351: Restart app: env[appid]
[[10:53:42]] [INFO] Skipping test case - already passed: Kmart-Prod Sign in NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            41 actions
[[10:53:42]] [INFO] Vxt7QOYeDD=running
[[10:53:42]] [INFO] Executing action 185/351: Restart app: env[appid]
[[10:53:42]] [INFO] Skipping test case - already passed: All Sign ins NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            28 actions
[[10:53:42]] [INFO] JXFxYCr98V=running
[[10:53:42]] [INFO] Executing action 157/351: Restart app: env[appid]
[[10:53:42]] [INFO] Skipping test case - already passed: NZ- MyAccount
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            39 actions
[[10:53:42]] [INFO] pjFNt3w5Fr=running
[[10:53:42]] [INFO] Executing action 118/351: Restart app: env[appid]
[[10:53:42]] [INFO] Skipping test case - already passed: Delivery & CNC- NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            27 actions
[[10:53:42]] [INFO] HotUJOd6oB=running
[[10:53:42]] [INFO] Executing action 91/351: Restart app: env[appid]
[[10:53:42]] [INFO] Skipping test case - already passed: Browse & PDP NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            37 actions
[[10:53:42]] [INFO] H9fy9qcFbZ=running
[[10:53:42]] [INFO] Executing action 54/351: Restart app: env[appid]
[[10:53:42]] [INFO] Skipping test case - already passed: Postcode Flow_NZ
                            
                            
                                    nztest
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            53 actions
[[10:53:42]] [INFO] H9fy9qcFbZ=running
[[10:53:42]] [INFO] Executing action 1/351: Restart app: env[appid]
[[10:53:42]] [INFO] ExecutionManager: Starting execution of 351 actions...
[[10:53:42]] [SUCCESS] Cleared 1 screenshots from database
[[10:53:42]] [INFO] Clearing screenshots from database before execution...
[[10:53:42]] [SUCCESS] All screenshots deleted successfully
[[10:53:42]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:53:42]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250701_105342/screenshots
[[10:53:42]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250701_105342
[[10:53:42]] [SUCCESS] Report directory initialized successfully
[[10:53:42]] [INFO] Initializing report directory and screenshots folder for test suite...
[[10:52:19]] [SUCCESS] All screenshots deleted successfully
[[10:52:19]] [INFO] All actions cleared
[[10:52:19]] [INFO] Cleaning up screenshots...
[[10:52:10]] [SUCCESS] Screenshot refreshed successfully
[[10:52:09]] [SUCCESS] Screenshot refreshed
[[10:52:09]] [INFO] Refreshing screenshot...
[[10:52:08]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[10:52:08]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[10:52:04]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[10:52:02]] [SUCCESS] Found 1 device(s)
[[10:52:01]] [INFO] Refreshing device list...
