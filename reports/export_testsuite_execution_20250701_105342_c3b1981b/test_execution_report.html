<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Execution - 2025-07-01 17:50:05</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            color: #333;
            line-height: 1.6;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            margin-bottom: 20px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        
        .test-case {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-case-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background-color: #f8f8f8;
            border-bottom: 1px solid #ddd;
            cursor: pointer;
        }
        
        .test-case-header.success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .test-case-header.error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .test-case-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .test-case-content {
            display: none;
            padding: 0;
        }
        
        .test-case-content.show {
            display: block;
        }
        
        .action-item {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .action-item:last-child {
            border-bottom: none;
        }

        .action-item.info-action {
            background-color: #d4edda;
        }
        
        .action-item .action-number {
            min-width: 30px;
            height: 30px;
            background-color: #e9ecef;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .action-type {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .action-type.tap {
            background-color: #cce5ff;
            color: #004085;
        }
        
        .action-type.swipe {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .action-type.text {
            background-color: #d4edda;
            color: #155724;
        }
        
        .action-type.wait {
            background-color: #ffeeba;
            color: #856404;
        }
        
        .action-type.addLog {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .action-type.launchApp {
            background-color: #d0f0fd;
            color: #055160;
        }
        
        .action-type.terminateApp {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .action-type.clickElement {
            background-color: #e0cffc;
            color: #3b0764;
        }

        .action-type.info {
            background-color: #d4edda;
            color: #155724;
        }

        .action-type.cleanupSteps {
            background-color: #f8d7da;
            color: #721c24;
        }

        .screenshot-link {
            display: inline-block;
            background-color: #4a6fdc;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            margin-left: 10px;
            cursor: pointer;
            font-size: 0.85rem;
        }
        
        .screenshot-link:hover {
            background-color: #3a5bb9;
        }
        
        .screenshot-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            align-items: center;
            justify-content: center;
        }
        
        .screenshot-modal.show {
            display: flex;
        }
        
        .screenshot-modal-content {
            max-width: 90%;
            max-height: 90%;
            position: relative;
        }
        
        .screenshot-modal-content img {
            max-width: 100%;
            max-height: 90vh;
            border: 5px solid white;
            border-radius: 5px;
        }
        
        .close-modal {
            position: absolute;
            top: -20px;
            right: -20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            background-color: rgba(0, 0, 0, 0.5);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .badge {
            margin-left: 10px;
        }
        
        .summary-section {
            margin-bottom: 30px;
        }
        
        .summary-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .stat-card {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-card.total {
            background-color: #e9ecef;
        }
        
        .stat-card.passed {
            background-color: #d4edda;
        }
        
        .stat-card.failed {
            background-color: #f8d7da;
        }
        
        .stat-card h4 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: 700;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Execution - 2025-07-01 17:50:05</h1>
        
        <div class="summary-section">
            <h2>Summary</h2>
            <div class="summary-stats">
                <div class="stat-card total">
                    <h4>Total Test Cases</h4>
                    <div class="number">9</div>
                </div>
                <div class="stat-card passed">
                    <h4>Passed</h4>
                    <div class="number">9</div>
                </div>
                <div class="stat-card failed">
                    <h4>Failed</h4>
                    <div class="number">0</div>
                </div>
            </div>
        </div>
        
        <div class="test-cases-section">
            <h2>Test Cases</h2>
            
            
            <div class="test-case">
                <div class="test-case-header success">
                    <h3>Postcode Flow_NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            53 actions</h3>
                    <div>
                        <span class="badge bg-success">
                            Passed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: txtHomeAccountCtaSignIn</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeTextField[@name="Email"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type multiStep">multiStep</span>
                            <span class="action-description">Execute Test Case: Kmart-NZ-Signin (6 steps)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">7</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">8</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: Search suburb or postcode</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">9</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">textClear action</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">10</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "8053"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">11</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till accessibility_id=btnSaveOrContinue</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">12</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Save"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">13</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">14</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with text="Papanui" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">15</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Find"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">16</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "Uno card"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">17</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeButton[@name="Filter"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">18</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Edit"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">19</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: Search suburb or postcode</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">20</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">textClear action</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">21</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "0616"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">22</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till accessibility_id=btnSaveOrContinue</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">23</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Save"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">24</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with text="Henderson" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">25</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">26</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">27</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Edit"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">28</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: Search suburb or postcode</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">29</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">textClear action</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">30</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "8053"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">31</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till accessibility_id=btnSaveOrContinue</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">32</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Save"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">33</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with text="Papanui" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">34</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: Add to bag</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">35</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">36</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">37</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">38</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Collect"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">39</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">40</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Nearby"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">41</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: delete</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">42</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "0616"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">43</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "AUCKLAND"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">44</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: Done</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">45</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">46</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">47</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">48</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">49</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with text="Henderson" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">50</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">51</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">52</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">53</div>
                            <span class="action-type cleanupSteps">cleanupSteps</span>
                            <span class="action-description">cleanupSteps action</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <div class="test-case">
                <div class="test-case-header success">
                    <h3>Browse & PDP NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            37 actions</h3>
                    <div>
                        <span class="badge bg-success">
                            Passed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeOther[@name="txtShopMenuTitle"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Toys"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Latest"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">7</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::*</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">8</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">9</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">10</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[product-share-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">11</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">12</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if image "product-share-logo.png" exists on screen</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">13</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: banner-close-updated.png</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">14</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="Add to bag"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">15</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 50%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">16</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "more"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">17</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">18</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">19</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "Kid toy"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">20</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if image "search-result-test-se.png" exists on screen</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">21</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">22</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">23</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">24</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">25</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">26</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">27</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">28</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Find"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">29</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "notebook"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">30</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeButton[@name="Filter"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">31</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">32</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">33</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeButton[contains(@name,"Remove")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">34</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">35</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">36</div>
                            <span class="action-type terminateApp">terminateApp</span>
                            <span class="action-description">Terminate app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">37</div>
                            <span class="action-type cleanupSteps">cleanupSteps</span>
                            <span class="action-description">cleanupSteps action</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <div class="test-case">
                <div class="test-case-header success">
                    <h3>Delivery & CNC- NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            27 actions</h3>
                    <div>
                        <span class="badge bg-success">
                            Passed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: txtHomeAccountCtaSignIn</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeTextField[@name="Email"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type multiStep">multiStep</span>
                            <span class="action-description">Execute Test Case: Kmart-NZ-Signin (6 steps)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Find"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">7</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "P_43250042"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">8</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeButton[@name="Filter"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">9</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">10</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">11</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">12</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if image "cnc-tab-se.png" exists on screen</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">13</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Collect"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">14</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">15</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">16</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: banner-close-updated.png</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">17</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">18</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">19</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">20</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">21</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Find"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">22</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "P_43250042"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">23</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeButton[@name="Filter"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">24</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">25</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">26</div>
                            <span class="action-type multiStep">multiStep</span>
                            <span class="action-description">Execute Test Case: Delivery Buy Step NZ (33 steps)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">27</div>
                            <span class="action-type cleanupSteps">cleanupSteps</span>
                            <span class="action-description">cleanupSteps action</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <div class="test-case">
                <div class="test-case-header success">
                    <h3>NZ- MyAccount
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            39 actions</h3>
                    <div>
                        <span class="badge bg-success">
                            Passed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait for 5 ms</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: txtHomeAccountCtaSignIn</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type multiStep">multiStep</span>
                            <span class="action-description">Execute Test Case: Kmart-NZ-Signin (6 steps)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">7</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">8</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeButton[@name="txtMy orders"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">9</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy orders"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">10</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait for 5 ms</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">11</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="main"]//XCUIElementTypeLink)[4]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">12</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">13</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">14</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: Print order details</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">15</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">16</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">17</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Return"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">18</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">19</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">20</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">21</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "details"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">22</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">23</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "address"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">24</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">25</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "payment"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">26</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">27</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">28</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">29</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "locator"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">30</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Nearby"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">31</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Allow While Using App"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">32</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">Tap and Type at (29, 262): "0616"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">33</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "AUCKLAND"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">34</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">35</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">36</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Customer"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">37</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">38</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "out"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">39</div>
                            <span class="action-type cleanupSteps">cleanupSteps</span>
                            <span class="action-description">cleanupSteps action</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <div class="test-case">
                <div class="test-case-header success">
                    <h3>All Sign ins NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            28 actions</h3>
                    <div>
                        <span class="badge bg-success">
                            Passed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: txtHomeAccountCtaSignIn</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeTextField[@name="Email"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type multiStep">multiStep</span>
                            <span class="action-description">Execute Test Case: Kmart-NZ-Signin (5 steps)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">7</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">8</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">9</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">10</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">11</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">12</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeTextField[@name="Email"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">13</div>
                            <span class="action-type multiStep">multiStep</span>
                            <span class="action-description">Execute Test Case: Kmart-NZ-Signin (5 steps)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">14</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">15</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">16</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">17</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">18</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">19</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">20</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">21</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">22</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">23</div>
                            <span class="action-type multiStep">multiStep</span>
                            <span class="action-description">Execute Test Case: Kmart-NZ-Signin (5 steps)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">24</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">25</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">26</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">27</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait for 5 ms</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">28</div>
                            <span class="action-type cleanupSteps">cleanupSteps</span>
                            <span class="action-description">cleanupSteps action</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <div class="test-case">
                <div class="test-case-header success">
                    <h3>Kmart-Prod Sign in NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            41 actions</h3>
                    <div>
                        <span class="badge bg-success">
                            Passed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: txtHomeAccountCtaSignIn</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeTextField[@name="Email"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "env[uname]"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">7</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">8</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "env[pwd]"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">9</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">10</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">11</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">12</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">13</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">14</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: txtHomeAccountCtaSignIn</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">15</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">16</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeTextField[@name="Email"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">17</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Apple"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">18</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait for 10 ms</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">19</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Passcode"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">20</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="5"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">21</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="9"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">22</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="1"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">23</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="2"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">24</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="3"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">25</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="4"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">26</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">27</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">28</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">29</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">30</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">31</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: txtHomeAccountCtaSignIn</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">32</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">33</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeTextField[@name="Email"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">34</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Google"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">35</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">36</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">37</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">38</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe up till element xpath: "//XCUIElementTypeButton[@name="txtSign out"]" is visible</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">39</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">40</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait for 5 ms</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">41</div>
                            <span class="action-type cleanupSteps">cleanupSteps</span>
                            <span class="action-description">cleanupSteps action</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <div class="test-case">
                <div class="test-case-header success">
                    <h3>WishList NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            46 actions</h3>
                    <div>
                        <span class="badge bg-success">
                            Passed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: txtHomeAccountCtaSignIn</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeTextField[@name="Email"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type multiStep">multiStep</span>
                            <span class="action-description">Execute Test Case: Kmart-NZ-Signin (6 steps)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Find"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">7</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "Uno card"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">8</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeButton[@name="Filter"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">9</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">10</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">11</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">12</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">13</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">14</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">15</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">16</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">17</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: env[device-back-img]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">18</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">19</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">20</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">21</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">22</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">23</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">24</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">25</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait for 5 ms</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">26</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Move"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">27</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">28</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait for 5 ms</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">29</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Remove"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">30</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">31</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">32</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">33</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe up till element xpath: "//XCUIElementTypeButton[@name="Move to wishlist"]" is visible</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">34</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">35</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">36</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on image: banner-close-updated.png</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">37</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">38</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">39</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Remove"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">40</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">41</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Remove"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">42</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">43</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">44</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">45</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 10%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">46</div>
                            <span class="action-type cleanupSteps">cleanupSteps</span>
                            <span class="action-description">cleanupSteps action</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <div class="test-case">
                <div class="test-case-header success">
                    <h3>App Settings NZ
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            39 actions</h3>
                    <div>
                        <span class="badge bg-success">
                            Passed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type multiStep">multiStep</span>
                            <span class="action-description">Execute Test Case: Kmart-NZ-Signin (6 steps)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type terminateApp">terminateApp</span>
                            <span class="action-description">Terminate app: com.apple.Preferences</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type launchApp">launchApp</span>
                            <span class="action-description">Launch app: com.apple.Preferences</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">7</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Wi-Fi"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">8</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">9</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">10</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">11</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">12</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">13</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">14</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">15</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">16</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">17</div>
                            <span class="action-type launchApp">launchApp</span>
                            <span class="action-description">Launch app: com.apple.Preferences</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">18</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">19</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait for 5 ms</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">20</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">21</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">22</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">23</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "out"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">24</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: com.apple.mobilesafari</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">25</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">26</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "kmart nz"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">27</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.co.nz"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">28</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">29</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Find"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">30</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "notebook"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">31</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">32</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">33</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: Add to bag</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">34</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">35</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">36</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">37</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">38</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">39</div>
                            <span class="action-type cleanupSteps">cleanupSteps</span>
                            <span class="action-description">cleanupSteps action</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <div class="test-case">
                <div class="test-case-header success">
                    <h3>NZ- Performance
                            
                            
                                    PASSED
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            41 actions</h3>
                    <div>
                        <span class="badge bg-success">
                            Passed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Find"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "P_43515028"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">7</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Done"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">8</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">9</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">10</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Find"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">11</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "kids toys"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">12</div>
                            <span class="action-type multiStep">multiStep</span>
                            <span class="action-description">Execute Test Case: Click_Paginations (10 steps)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">13</div>
                            <span class="action-type restartApp">restartApp</span>
                            <span class="action-description">Restart app: env[appid]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">14</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">15</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Toys"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">16</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Age"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">17</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Months"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">18</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (5%, 50%) to (90%, 50%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">19</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (5%, 50%) to (90%, 50%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">20</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">21</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with accessibility_id: txtHomeAccountCtaSignIn</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">22</div>
                            <span class="action-type iosFunctions">iosFunctions</span>
                            <span class="action-description">iOS Function: alert_accept</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">23</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">24</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "env[uname]"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">25</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">26</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "env[pwd]"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">27</div>
                            <span class="action-type exists">exists</span>
                            <span class="action-description">Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">28</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Find"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">29</div>
                            <span class="action-type text">text</span>
                            <span class="action-description">iOS Function: text - Text: "enn[cooker-id]"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">30</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeButton[@name="Filter"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">31</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">32</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">33</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">34</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">35</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (90%, 20%) to (30%, 20%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">36</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (90%, 20%) to (30%, 20%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">37</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">38</div>
                            <span class="action-type swipe">swipe</span>
                            <span class="action-description">Swipe from (50%, 70%) to (50%, 30%)</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">39</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "out"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">40</div>
                            <span class="action-type wait">wait</span>
                            <span class="action-description">Wait till accessibility_id=txtHomeAccountCtaSignIn</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">41</div>
                            <span class="action-type cleanupSteps">cleanupSteps</span>
                            <span class="action-description">cleanupSteps action</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
        </div>
    </div>
    
    <div class="screenshot-modal">
        <div class="screenshot-modal-content">
            <span class="close-modal">&times;</span>
            <img src="" alt="Screenshot" id="screenshotModalImage">
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle test case expansion
            const testCaseHeaders = document.querySelectorAll('.test-case-header');
            testCaseHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const content = this.nextElementSibling;
                    content.classList.toggle('show');
                    const icon = this.querySelector('.toggle-icon');
                    icon.classList.toggle('bi-chevron-down');
                    icon.classList.toggle('bi-chevron-up');
                });
            });
            
            // Screenshot modal functionality
            const modal = document.querySelector('.screenshot-modal');
            const modalImg = document.getElementById('screenshotModalImage');
            const closeModal = document.querySelector('.close-modal');
            
            document.querySelectorAll('.screenshot-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.stopPropagation();  // Prevent the click from propagating
                    const screenshotPath = this.getAttribute('data-screenshot');
                    modalImg.src = screenshotPath;
                    modal.classList.add('show');
                    
                    // Log for debugging
                    console.log('Opening screenshot:', screenshotPath);
                });
            });
            
            closeModal.addEventListener('click', function() {
                modal.classList.remove('show');
            });
            
            // Close modal when clicking outside of it
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html> 