Action Log - 2025-07-03 16:25:44
================================================================================

[[16:25:44]] [INFO] Generating execution report...
[[16:25:44]] [SUCCESS] All tests passed successfully!
[[16:25:44]] [SUCCESS] Screenshot refreshed
[[16:25:44]] [INFO] Refreshing screenshot...
[[16:25:44]] [INFO] xyHVihJMBi=pass
[[16:25:38]] [SUCCESS] Screenshot refreshed successfully
[[16:25:38]] [SUCCESS] Screenshot refreshed successfully
[[16:25:37]] [INFO] xyHVihJMBi=running
[[16:25:37]] [INFO] Executing action 51/51: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:25:37]] [SUCCESS] Screenshot refreshed
[[16:25:37]] [INFO] Refreshing screenshot...
[[16:25:37]] [INFO] mWeLQtXiL6=pass
[[16:25:32]] [SUCCESS] Screenshot refreshed successfully
[[16:25:32]] [SUCCESS] Screenshot refreshed successfully
[[16:25:32]] [INFO] mWeLQtXiL6=running
[[16:25:32]] [INFO] Executing action 50/51: Swipe from (50%, 70%) to (50%, 30%)
[[16:25:31]] [SUCCESS] Screenshot refreshed
[[16:25:31]] [INFO] Refreshing screenshot...
[[16:25:31]] [INFO] rkwVoJGZG4=pass
[[16:25:05]] [SUCCESS] Screenshot refreshed successfully
[[16:25:05]] [SUCCESS] Screenshot refreshed successfully
[[16:25:04]] [INFO] rkwVoJGZG4=running
[[16:25:04]] [INFO] Executing action 49/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:25:04]] [SUCCESS] Screenshot refreshed
[[16:25:04]] [INFO] Refreshing screenshot...
[[16:25:04]] [INFO] 0f2FSZYjWq=pass
[[16:24:58]] [SUCCESS] Screenshot refreshed successfully
[[16:24:58]] [SUCCESS] Screenshot refreshed successfully
[[16:24:57]] [INFO] 0f2FSZYjWq=running
[[16:24:57]] [INFO] Executing action 48/51: Check if element with text="3000" exists
[[16:24:57]] [SUCCESS] Screenshot refreshed
[[16:24:57]] [INFO] Refreshing screenshot...
[[16:24:57]] [INFO] Tebej51pT2=pass
[[16:24:55]] [SUCCESS] Screenshot refreshed successfully
[[16:24:55]] [SUCCESS] Screenshot refreshed successfully
[[16:24:55]] [INFO] Tebej51pT2=running
[[16:24:55]] [INFO] Executing action 47/51: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[16:24:54]] [SUCCESS] Screenshot refreshed
[[16:24:54]] [INFO] Refreshing screenshot...
[[16:24:54]] [INFO] JrPVGdts3J=pass
[[16:24:39]] [SUCCESS] Screenshot refreshed successfully
[[16:24:39]] [SUCCESS] Screenshot refreshed successfully
[[16:24:38]] [INFO] JrPVGdts3J=running
[[16:24:38]] [INFO] Executing action 46/51: Tap on image: bag-remove-btn-android.png
[[16:24:38]] [SUCCESS] Screenshot refreshed
[[16:24:38]] [INFO] Refreshing screenshot...
[[16:24:38]] [INFO] s8h8VDUIOC=pass
[[16:24:35]] [SUCCESS] Screenshot refreshed successfully
[[16:24:35]] [SUCCESS] Screenshot refreshed successfully
[[16:24:35]] [INFO] s8h8VDUIOC=running
[[16:24:35]] [INFO] Executing action 45/51: Swipe from (50%, 70%) to (50%, 30%)
[[16:24:34]] [SUCCESS] Screenshot refreshed
[[16:24:34]] [INFO] Refreshing screenshot...
[[16:24:34]] [INFO] GYK47u1y3A=pass
[[16:24:32]] [SUCCESS] Screenshot refreshed successfully
[[16:24:32]] [SUCCESS] Screenshot refreshed successfully
[[16:24:32]] [INFO] GYK47u1y3A=running
[[16:24:32]] [INFO] Executing action 44/51: Android Function: send_key_event - Key Event: TAB
[[16:24:31]] [SUCCESS] Screenshot refreshed
[[16:24:31]] [INFO] Refreshing screenshot...
[[16:24:31]] [INFO] ZWpYNcpbFA=pass
[[16:24:28]] [SUCCESS] Screenshot refreshed successfully
[[16:24:28]] [SUCCESS] Screenshot refreshed successfully
[[16:24:12]] [INFO] ZWpYNcpbFA=running
[[16:24:12]] [INFO] Executing action 43/51: Tap on Text: "VIC"
[[16:24:11]] [SUCCESS] Screenshot refreshed
[[16:24:11]] [INFO] Refreshing screenshot...
[[16:24:11]] [INFO] QpBLC6BStn=pass
[[16:24:09]] [SUCCESS] Screenshot refreshed successfully
[[16:24:09]] [SUCCESS] Screenshot refreshed successfully
[[16:24:08]] [INFO] QpBLC6BStn=running
[[16:24:08]] [INFO] Executing action 42/51: textClear action
[[16:24:08]] [SUCCESS] Screenshot refreshed
[[16:24:08]] [INFO] Refreshing screenshot...
[[16:24:08]] [INFO] G4A3KBlXHq=pass
[[16:24:04]] [SUCCESS] Screenshot refreshed successfully
[[16:24:04]] [SUCCESS] Screenshot refreshed successfully
[[16:24:04]] [INFO] G4A3KBlXHq=running
[[16:24:04]] [INFO] Executing action 41/51: Tap on Text: "Nearby"
[[16:24:03]] [SUCCESS] Screenshot refreshed
[[16:24:03]] [INFO] Refreshing screenshot...
[[16:24:03]] [INFO] 3gJsiap2Ds=pass
[[16:24:00]] [SUCCESS] Screenshot refreshed successfully
[[16:24:00]] [SUCCESS] Screenshot refreshed successfully
[[16:24:00]] [INFO] 3gJsiap2Ds=running
[[16:24:00]] [INFO] Executing action 40/51: Tap on Text: "Collect"
[[16:23:59]] [SUCCESS] Screenshot refreshed
[[16:23:59]] [INFO] Refreshing screenshot...
[[16:23:59]] [INFO] qofJDqXBME=pass
[[16:23:53]] [SUCCESS] Screenshot refreshed successfully
[[16:23:53]] [SUCCESS] Screenshot refreshed successfully
[[16:23:53]] [INFO] qofJDqXBME=running
[[16:23:53]] [INFO] Executing action 39/51: Wait till text appears: "Delivery"
[[16:23:52]] [SUCCESS] Screenshot refreshed
[[16:23:52]] [INFO] Refreshing screenshot...
[[16:23:52]] [INFO] rkwVoJGZG4=pass
[[16:23:50]] [SUCCESS] Screenshot refreshed successfully
[[16:23:50]] [SUCCESS] Screenshot refreshed successfully
[[16:23:50]] [INFO] rkwVoJGZG4=running
[[16:23:50]] [INFO] Executing action 38/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[16:23:50]] [SUCCESS] Screenshot refreshed
[[16:23:50]] [INFO] Refreshing screenshot...
[[16:23:50]] [INFO] 94ikwhIEE2=pass
[[16:23:46]] [SUCCESS] Screenshot refreshed successfully
[[16:23:46]] [SUCCESS] Screenshot refreshed successfully
[[16:23:42]] [INFO] 94ikwhIEE2=running
[[16:23:42]] [INFO] Executing action 37/51: Tap on Text: "bag"
[[16:23:42]] [SUCCESS] Screenshot refreshed
[[16:23:42]] [INFO] Refreshing screenshot...
[[16:23:42]] [INFO] DfwaiVZ8Z9=pass
[[16:23:39]] [SUCCESS] Screenshot refreshed successfully
[[16:23:39]] [SUCCESS] Screenshot refreshed successfully
[[16:23:38]] [INFO] DfwaiVZ8Z9=running
[[16:23:38]] [INFO] Executing action 36/51: Swipe from (50%, 70%) to (50%, 50%)
[[16:23:38]] [SUCCESS] Screenshot refreshed
[[16:23:38]] [INFO] Refreshing screenshot...
[[16:23:38]] [INFO] eRCmRhc3re=pass
[[16:23:35]] [SUCCESS] Screenshot refreshed successfully
[[16:23:35]] [SUCCESS] Screenshot refreshed successfully
[[16:23:35]] [INFO] eRCmRhc3re=running
[[16:23:35]] [INFO] Executing action 35/51: Check if element with text="Broadway" exists
[[16:23:34]] [SUCCESS] Screenshot refreshed
[[16:23:34]] [INFO] Refreshing screenshot...
[[16:23:34]] [INFO] E2jpN7BioW=pass
[[16:23:31]] [SUCCESS] Screenshot refreshed successfully
[[16:23:31]] [SUCCESS] Screenshot refreshed successfully
[[16:23:31]] [INFO] E2jpN7BioW=running
[[16:23:31]] [INFO] Executing action 34/51: Tap on element with accessibility_id: btnSaveOrContinue
[[16:23:30]] [SUCCESS] Screenshot refreshed
[[16:23:30]] [INFO] Refreshing screenshot...
[[16:23:30]] [INFO] kDnmoQJG4o=pass
[[16:23:11]] [SUCCESS] Screenshot refreshed successfully
[[16:23:11]] [SUCCESS] Screenshot refreshed successfully
[[16:23:11]] [INFO] kDnmoQJG4o=running
[[16:23:11]] [INFO] Executing action 33/51: Wait till accessibility_id=btnSaveOrContinue
[[16:23:11]] [SUCCESS] Screenshot refreshed
[[16:23:11]] [INFO] Refreshing screenshot...
[[16:23:11]] [INFO] H0ODFz7sWJ=pass
[[16:23:06]] [SUCCESS] Screenshot refreshed successfully
[[16:23:06]] [SUCCESS] Screenshot refreshed successfully
[[16:23:04]] [INFO] H0ODFz7sWJ=running
[[16:23:04]] [INFO] Executing action 32/51: Tap on Text: "2000"
[[16:23:04]] [SUCCESS] Screenshot refreshed
[[16:23:04]] [INFO] Refreshing screenshot...
[[16:23:04]] [INFO] pldheRUBVi=pass
[[16:22:49]] [SUCCESS] Screenshot refreshed successfully
[[16:22:49]] [SUCCESS] Screenshot refreshed successfully
[[16:22:48]] [INFO] pldheRUBVi=running
[[16:22:48]] [INFO] Executing action 31/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:22:48]] [SUCCESS] Screenshot refreshed
[[16:22:48]] [INFO] Refreshing screenshot...
[[16:22:48]] [INFO] uZHvvAzVfx=pass
[[16:22:46]] [SUCCESS] Screenshot refreshed successfully
[[16:22:46]] [SUCCESS] Screenshot refreshed successfully
[[16:22:45]] [INFO] uZHvvAzVfx=running
[[16:22:45]] [INFO] Executing action 30/51: textClear action
[[16:22:45]] [SUCCESS] Screenshot refreshed
[[16:22:45]] [INFO] Refreshing screenshot...
[[16:22:45]] [INFO] pldheRUBVi=pass
[[16:22:42]] [SUCCESS] Screenshot refreshed successfully
[[16:22:42]] [SUCCESS] Screenshot refreshed successfully
[[16:22:42]] [INFO] pldheRUBVi=running
[[16:22:42]] [INFO] Executing action 29/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:22:41]] [SUCCESS] Screenshot refreshed
[[16:22:41]] [INFO] Refreshing screenshot...
[[16:22:41]] [INFO] pldheRUBVi=pass
[[16:22:39]] [SUCCESS] Screenshot refreshed successfully
[[16:22:39]] [SUCCESS] Screenshot refreshed successfully
[[16:22:39]] [INFO] pldheRUBVi=running
[[16:22:39]] [INFO] Executing action 28/51: Wait till xpath=//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:22:39]] [SUCCESS] Screenshot refreshed
[[16:22:39]] [INFO] Refreshing screenshot...
[[16:22:39]] [INFO] WmNWcsWVHv=pass
[[16:22:35]] [SUCCESS] Screenshot refreshed successfully
[[16:22:35]] [SUCCESS] Screenshot refreshed successfully
[[16:22:32]] [INFO] WmNWcsWVHv=running
[[16:22:32]] [INFO] Executing action 27/51: Tap on Text: "4000"
[[16:22:31]] [SUCCESS] Screenshot refreshed
[[16:22:31]] [INFO] Refreshing screenshot...
[[16:22:31]] [INFO] lnjoz8hHUU=pass
[[16:22:25]] [SUCCESS] Screenshot refreshed successfully
[[16:22:25]] [SUCCESS] Screenshot refreshed successfully
[[16:22:25]] [INFO] lnjoz8hHUU=running
[[16:22:25]] [INFO] Executing action 26/51: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[16:22:24]] [SUCCESS] Screenshot refreshed
[[16:22:24]] [INFO] Refreshing screenshot...
[[16:22:24]] [INFO] VkUKQbf1Qt=pass
[[16:22:19]] [SUCCESS] Screenshot refreshed successfully
[[16:22:19]] [SUCCESS] Screenshot refreshed successfully
[[16:22:19]] [INFO] VkUKQbf1Qt=running
[[16:22:19]] [INFO] Executing action 25/51: Tap on Text: "UNO"
[[16:22:18]] [SUCCESS] Screenshot refreshed
[[16:22:18]] [INFO] Refreshing screenshot...
[[16:22:18]] [INFO] 73NABkfWyY=pass
[[16:22:12]] [SUCCESS] Screenshot refreshed successfully
[[16:22:12]] [SUCCESS] Screenshot refreshed successfully
[[16:22:11]] [INFO] 73NABkfWyY=running
[[16:22:11]] [INFO] Executing action 24/51: Check if element with text="Toowong" exists
[[16:22:11]] [SUCCESS] Screenshot refreshed
[[16:22:11]] [INFO] Refreshing screenshot...
[[16:22:11]] [INFO] E2jpN7BioW=pass
[[16:22:10]] [SUCCESS] Screenshot refreshed successfully
[[16:22:10]] [SUCCESS] Screenshot refreshed successfully
[[16:22:07]] [INFO] E2jpN7BioW=running
[[16:22:07]] [INFO] Executing action 23/51: Tap on element with accessibility_id: btnSaveOrContinue
[[16:22:07]] [SUCCESS] Screenshot refreshed
[[16:22:07]] [INFO] Refreshing screenshot...
[[16:22:07]] [INFO] kDnmoQJG4o=pass
[[16:22:05]] [SUCCESS] Screenshot refreshed successfully
[[16:22:05]] [SUCCESS] Screenshot refreshed successfully
[[16:22:05]] [INFO] kDnmoQJG4o=running
[[16:22:05]] [INFO] Executing action 22/51: Wait till accessibility_id=btnSaveOrContinue
[[16:22:04]] [SUCCESS] Screenshot refreshed
[[16:22:04]] [INFO] Refreshing screenshot...
[[16:22:04]] [INFO] VkUKQbf1Qt=pass
[[16:22:01]] [SUCCESS] Screenshot refreshed successfully
[[16:22:01]] [SUCCESS] Screenshot refreshed successfully
[[16:21:38]] [INFO] VkUKQbf1Qt=running
[[16:21:38]] [INFO] Executing action 21/51: Tap on Text: "CITY"
[[16:21:38]] [SUCCESS] Screenshot refreshed
[[16:21:38]] [INFO] Refreshing screenshot...
[[16:21:38]] [INFO] pldheRUBVi=pass
[[16:21:36]] [SUCCESS] Screenshot refreshed successfully
[[16:21:36]] [SUCCESS] Screenshot refreshed successfully
[[16:21:35]] [INFO] pldheRUBVi=running
[[16:21:35]] [INFO] Executing action 20/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:21:35]] [SUCCESS] Screenshot refreshed
[[16:21:35]] [INFO] Refreshing screenshot...
[[16:21:35]] [INFO] kbdEPCPYod=pass
[[16:21:32]] [SUCCESS] Screenshot refreshed successfully
[[16:21:32]] [SUCCESS] Screenshot refreshed successfully
[[16:21:31]] [INFO] kbdEPCPYod=running
[[16:21:31]] [INFO] Executing action 19/51: textClear action
[[16:21:31]] [SUCCESS] Screenshot refreshed
[[16:21:31]] [INFO] Refreshing screenshot...
[[16:21:31]] [INFO] pldheRUBVi=pass
[[16:21:04]] [SUCCESS] Screenshot refreshed successfully
[[16:21:04]] [SUCCESS] Screenshot refreshed successfully
[[16:21:04]] [INFO] pldheRUBVi=running
[[16:21:04]] [INFO] Executing action 18/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:21:03]] [SUCCESS] Screenshot refreshed
[[16:21:03]] [INFO] Refreshing screenshot...
[[16:21:03]] [INFO] YhLhTn3Wtm=pass
[[16:20:57]] [SUCCESS] Screenshot refreshed successfully
[[16:20:57]] [SUCCESS] Screenshot refreshed successfully
[[16:20:56]] [INFO] YhLhTn3Wtm=running
[[16:20:56]] [INFO] Executing action 17/51: Wait for 5 ms
[[16:20:56]] [SUCCESS] Screenshot refreshed
[[16:20:56]] [INFO] Refreshing screenshot...
[[16:20:56]] [INFO] VkUKQbf1Qt=pass
[[16:20:43]] [SUCCESS] Screenshot refreshed successfully
[[16:20:43]] [SUCCESS] Screenshot refreshed successfully
[[16:20:43]] [INFO] VkUKQbf1Qt=running
[[16:20:43]] [INFO] Executing action 16/51: Tap on Text: "Edit"
[[16:20:42]] [SUCCESS] Screenshot refreshed
[[16:20:42]] [INFO] Refreshing screenshot...
[[16:20:42]] [INFO] MpdUKUazHa=pass
[[16:20:38]] [SUCCESS] Screenshot refreshed successfully
[[16:20:38]] [SUCCESS] Screenshot refreshed successfully
[[16:20:38]] [INFO] MpdUKUazHa=running
[[16:20:38]] [INFO] Executing action 15/51: Wait till image appears: sort-by-relevance-android.png
[[16:20:37]] [SUCCESS] Screenshot refreshed
[[16:20:37]] [INFO] Refreshing screenshot...
[[16:20:37]] [INFO] IupxLP2Jsr=pass
[[16:20:36]] [SUCCESS] Screenshot refreshed successfully
[[16:20:36]] [SUCCESS] Screenshot refreshed successfully
[[16:20:35]] [INFO] IupxLP2Jsr=running
[[16:20:35]] [INFO] Executing action 14/51: Input text: "P_6225544"
[[16:20:35]] [SUCCESS] Screenshot refreshed
[[16:20:35]] [INFO] Refreshing screenshot...
[[16:20:35]] [INFO] 70iOOakiG7=pass
[[16:20:28]] [SUCCESS] Screenshot refreshed successfully
[[16:20:28]] [SUCCESS] Screenshot refreshed successfully
[[16:20:28]] [INFO] 70iOOakiG7=running
[[16:20:28]] [INFO] Executing action 13/51: Tap on Text: "Find"
[[16:20:27]] [SUCCESS] Screenshot refreshed
[[16:20:27]] [INFO] Refreshing screenshot...
[[16:20:27]] [INFO] E2jpN7BioW=pass
[[16:20:24]] [SUCCESS] Screenshot refreshed successfully
[[16:20:24]] [SUCCESS] Screenshot refreshed successfully
[[16:20:23]] [INFO] E2jpN7BioW=running
[[16:20:23]] [INFO] Executing action 12/51: Tap on element with accessibility_id: btnSaveOrContinue
[[16:20:23]] [SUCCESS] Screenshot refreshed
[[16:20:23]] [INFO] Refreshing screenshot...
[[16:20:23]] [INFO] kDnmoQJG4o=pass
[[16:20:21]] [SUCCESS] Screenshot refreshed successfully
[[16:20:21]] [SUCCESS] Screenshot refreshed successfully
[[16:20:21]] [INFO] kDnmoQJG4o=running
[[16:20:21]] [INFO] Executing action 11/51: Wait till accessibility_id=btnSaveOrContinue
[[16:20:20]] [SUCCESS] Screenshot refreshed
[[16:20:20]] [INFO] Refreshing screenshot...
[[16:20:20]] [INFO] mw9GQ4mzRE=pass
[[16:20:16]] [SUCCESS] Screenshot refreshed successfully
[[16:20:16]] [SUCCESS] Screenshot refreshed successfully
[[16:20:11]] [INFO] mw9GQ4mzRE=running
[[16:20:11]] [INFO] Executing action 10/51: Tap on Text: "BC"
[[16:20:11]] [SUCCESS] Screenshot refreshed
[[16:20:11]] [INFO] Refreshing screenshot...
[[16:20:11]] [INFO] pldheRUBVi=pass
[[16:20:08]] [SUCCESS] Screenshot refreshed successfully
[[16:20:08]] [SUCCESS] Screenshot refreshed successfully
[[16:20:08]] [INFO] pldheRUBVi=running
[[16:20:08]] [INFO] Executing action 9/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:20:08]] [SUCCESS] Screenshot refreshed
[[16:20:08]] [INFO] Refreshing screenshot...
[[16:20:08]] [INFO] kbdEPCPYod=pass
[[16:20:05]] [SUCCESS] Screenshot refreshed successfully
[[16:20:05]] [SUCCESS] Screenshot refreshed successfully
[[16:20:04]] [INFO] kbdEPCPYod=running
[[16:20:04]] [INFO] Executing action 8/51: textClear action
[[16:20:04]] [SUCCESS] Screenshot refreshed
[[16:20:04]] [INFO] Refreshing screenshot...
[[16:20:04]] [INFO] pldheRUBVi=pass
[[16:19:39]] [SUCCESS] Screenshot refreshed successfully
[[16:19:39]] [SUCCESS] Screenshot refreshed successfully
[[16:19:39]] [INFO] pldheRUBVi=running
[[16:19:39]] [INFO] Executing action 7/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[16:19:38]] [SUCCESS] Screenshot refreshed
[[16:19:38]] [INFO] Refreshing screenshot...
[[16:19:38]] [INFO] QMXBlswP6H=pass
[[16:19:31]] [SUCCESS] Screenshot refreshed successfully
[[16:19:31]] [SUCCESS] Screenshot refreshed successfully
[[16:19:31]] [INFO] QMXBlswP6H=running
[[16:19:31]] [INFO] Executing action 6/51: Tap on Text: "Edit"
[[16:19:31]] [SUCCESS] Screenshot refreshed
[[16:19:31]] [INFO] Refreshing screenshot...
[[16:19:31]] [INFO] RLz6vQo3ag=pass
[[16:19:29]] [SUCCESS] Screenshot refreshed successfully
[[16:19:29]] [SUCCESS] Screenshot refreshed successfully
[[16:19:09]] [INFO] RLz6vQo3ag=running
[[16:19:09]] [INFO] Executing action 5/51: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[16:19:09]] [SUCCESS] Screenshot refreshed
[[16:19:09]] [INFO] Refreshing screenshot...
[[16:19:08]] [SUCCESS] Screenshot refreshed
[[16:19:08]] [INFO] Refreshing screenshot...
[[16:19:06]] [SUCCESS] Screenshot refreshed successfully
[[16:19:06]] [SUCCESS] Screenshot refreshed successfully
[[16:19:06]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[16:19:06]] [SUCCESS] Screenshot refreshed
[[16:19:06]] [INFO] Refreshing screenshot...
[[16:19:03]] [SUCCESS] Screenshot refreshed successfully
[[16:19:03]] [SUCCESS] Screenshot refreshed successfully
[[16:19:03]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:19:03]] [SUCCESS] Screenshot refreshed
[[16:19:03]] [INFO] Refreshing screenshot...
[[16:19:00]] [SUCCESS] Screenshot refreshed successfully
[[16:19:00]] [SUCCESS] Screenshot refreshed successfully
[[16:19:00]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[16:19:00]] [SUCCESS] Screenshot refreshed
[[16:19:00]] [INFO] Refreshing screenshot...
[[16:18:58]] [SUCCESS] Screenshot refreshed successfully
[[16:18:58]] [SUCCESS] Screenshot refreshed successfully
[[16:18:58]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:18:57]] [SUCCESS] Screenshot refreshed
[[16:18:57]] [INFO] Refreshing screenshot...
[[16:18:55]] [SUCCESS] Screenshot refreshed successfully
[[16:18:55]] [SUCCESS] Screenshot refreshed successfully
[[16:18:55]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:18:55]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[16:18:55]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[16:18:55]] [INFO] xz8njynjpZ=running
[[16:18:55]] [INFO] Executing action 4/51: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[16:18:55]] [SUCCESS] Screenshot refreshed
[[16:18:55]] [INFO] Refreshing screenshot...
[[16:18:55]] [INFO] J9loj6Zl5K=pass
[[16:18:53]] [SUCCESS] Screenshot refreshed successfully
[[16:18:53]] [SUCCESS] Screenshot refreshed successfully
[[16:18:53]] [INFO] J9loj6Zl5K=running
[[16:18:53]] [INFO] Executing action 3/51: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:18:52]] [SUCCESS] Screenshot refreshed
[[16:18:52]] [INFO] Refreshing screenshot...
[[16:18:52]] [INFO] Y8vz7AJD1i=pass
[[16:18:46]] [SUCCESS] Screenshot refreshed successfully
[[16:18:46]] [SUCCESS] Screenshot refreshed successfully
[[16:18:45]] [INFO] Y8vz7AJD1i=running
[[16:18:45]] [INFO] Executing action 2/51: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[16:18:45]] [SUCCESS] Screenshot refreshed
[[16:18:45]] [INFO] Refreshing screenshot...
[[16:18:45]] [INFO] H9fy9qcFbZ=pass
[[16:18:42]] [INFO] H9fy9qcFbZ=running
[[16:18:42]] [INFO] Executing action 1/51: Launch app: au.com.kmart
[[16:18:42]] [INFO] ExecutionManager: Starting execution of 51 actions...
[[16:18:42]] [SUCCESS] Cleared 1 screenshots from database
[[16:18:42]] [INFO] Clearing screenshots from database before execution...
[[16:18:42]] [SUCCESS] All screenshots deleted successfully
[[16:18:42]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[16:18:42]] [INFO] Skipping report initialization - single test case execution
[[16:18:40]] [SUCCESS] All screenshots deleted successfully
[[16:18:40]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 51 actions
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: swipe
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: exists
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: swipe
[[16:18:40]] [SUCCESS] Added action: androidFunctions
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: textClear
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: waitTill
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: swipe
[[16:18:40]] [SUCCESS] Added action: exists
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: waitTill
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: textClear
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: waitTill
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: waitTill
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: exists
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: waitTill
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: textClear
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: wait
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: waitTill
[[16:18:40]] [SUCCESS] Added action: text
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: waitTill
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: textClear
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: tapOnText
[[16:18:40]] [SUCCESS] Added action: waitTill
[[16:18:40]] [SUCCESS] Added action: multiStep
[[16:18:40]] [SUCCESS] Added action: waitTill
[[16:18:40]] [SUCCESS] Added action: tap
[[16:18:40]] [SUCCESS] Added action: launchApp
[[16:18:40]] [INFO] All actions cleared
[[16:18:40]] [INFO] Cleaning up screenshots...
[[16:18:35]] [SUCCESS] Screenshot refreshed successfully
[[16:18:33]] [SUCCESS] Screenshot refreshed
[[16:18:33]] [INFO] Refreshing screenshot...
[[16:18:32]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[16:18:32]] [INFO] Device info updated: RMX2151
[[16:18:26]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[16:18:24]] [SUCCESS] Found 1 device(s)
[[16:18:24]] [INFO] Refreshing device list...
