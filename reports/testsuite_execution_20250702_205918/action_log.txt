Action Log - 2025-07-02 21:25:40
================================================================================

[[21:25:40]] [INFO] Generating execution report...
[[21:25:40]] [SUCCESS] All tests passed successfully!
[[21:25:39]] [SUCCESS] Screenshot refreshed
[[21:25:39]] [INFO] Refreshing screenshot...
[[21:25:39]] [INFO] KvFsgyybzD=pass
[[21:25:38]] [SUCCESS] Screenshot refreshed successfully
[[21:25:38]] [SUCCESS] Screenshot refreshed successfully
[[21:25:37]] [INFO] KvFsgyybzD=running
[[21:25:37]] [INFO] Executing action 51/51: Tap at (229, 566)
[[21:25:37]] [SUCCESS] Screenshot refreshed
[[21:25:37]] [INFO] Refreshing screenshot...
[[21:25:37]] [INFO] xyHVihJMBi=pass
[[21:25:33]] [SUCCESS] Screenshot refreshed successfully
[[21:25:33]] [SUCCESS] Screenshot refreshed successfully
[[21:25:33]] [INFO] xyHVihJMBi=running
[[21:25:33]] [INFO] Executing action 50/51: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[21:25:32]] [SUCCESS] Screenshot refreshed
[[21:25:32]] [INFO] Refreshing screenshot...
[[21:25:32]] [INFO] mWeLQtXiL6=pass
[[21:25:28]] [SUCCESS] Screenshot refreshed successfully
[[21:25:28]] [SUCCESS] Screenshot refreshed successfully
[[21:25:27]] [INFO] mWeLQtXiL6=running
[[21:25:27]] [INFO] Executing action 49/51: Swipe from (50%, 70%) to (50%, 30%)
[[21:25:27]] [SUCCESS] Screenshot refreshed
[[21:25:27]] [INFO] Refreshing screenshot...
[[21:25:27]] [INFO] rkwVoJGZG4=pass
[[21:25:25]] [SUCCESS] Screenshot refreshed successfully
[[21:25:25]] [SUCCESS] Screenshot refreshed successfully
[[21:25:24]] [INFO] rkwVoJGZG4=running
[[21:25:24]] [INFO] Executing action 48/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[21:25:24]] [SUCCESS] Screenshot refreshed
[[21:25:24]] [INFO] Refreshing screenshot...
[[21:25:24]] [INFO] 0f2FSZYjWq=pass
[[21:25:20]] [SUCCESS] Screenshot refreshed successfully
[[21:25:20]] [SUCCESS] Screenshot refreshed successfully
[[21:25:17]] [INFO] 0f2FSZYjWq=running
[[21:25:17]] [INFO] Executing action 47/51: Check if element with text="3000" exists
[[21:25:16]] [SUCCESS] Screenshot refreshed
[[21:25:16]] [INFO] Refreshing screenshot...
[[21:25:16]] [INFO] Tebej51pT2=pass
[[21:25:04]] [INFO] Tebej51pT2=running
[[21:25:04]] [INFO] Executing action 46/51: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[21:25:04]] [INFO] eVytJrry9x=fail
[[21:25:04]] [ERROR] Action 45 failed: Element not found or not tappable: xpath='//android.widget.Button[contains(@text,"Remove")]'
[[21:24:34]] [SUCCESS] Screenshot refreshed successfully
[[21:24:34]] [SUCCESS] Screenshot refreshed successfully
[[21:24:34]] [INFO] eVytJrry9x=running
[[21:24:34]] [INFO] Executing action 45/51: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[21:24:34]] [SUCCESS] Screenshot refreshed
[[21:24:34]] [INFO] Refreshing screenshot...
[[21:24:34]] [INFO] s8h8VDUIOC=pass
[[21:24:31]] [SUCCESS] Screenshot refreshed successfully
[[21:24:31]] [SUCCESS] Screenshot refreshed successfully
[[21:24:30]] [INFO] s8h8VDUIOC=running
[[21:24:30]] [INFO] Executing action 44/51: Swipe from (50%, 70%) to (50%, 30%)
[[21:24:30]] [SUCCESS] Screenshot refreshed
[[21:24:30]] [INFO] Refreshing screenshot...
[[21:24:30]] [INFO] ZWpYNcpbFA=pass
[[21:24:25]] [SUCCESS] Screenshot refreshed successfully
[[21:24:25]] [SUCCESS] Screenshot refreshed successfully
[[21:24:25]] [INFO] ZWpYNcpbFA=running
[[21:24:25]] [INFO] Executing action 43/51: Tap on Text: "VIC"
[[21:24:24]] [SUCCESS] Screenshot refreshed
[[21:24:24]] [INFO] Refreshing screenshot...
[[21:24:24]] [INFO] QpBLC6BStn=pass
[[21:24:22]] [SUCCESS] Screenshot refreshed successfully
[[21:24:22]] [SUCCESS] Screenshot refreshed successfully
[[21:24:22]] [INFO] QpBLC6BStn=running
[[21:24:22]] [INFO] Executing action 42/51: textClear action
[[21:24:21]] [SUCCESS] Screenshot refreshed
[[21:24:21]] [INFO] Refreshing screenshot...
[[21:24:21]] [INFO] G4A3KBlXHq=pass
[[21:24:18]] [SUCCESS] Screenshot refreshed successfully
[[21:24:18]] [SUCCESS] Screenshot refreshed successfully
[[21:24:17]] [INFO] G4A3KBlXHq=running
[[21:24:17]] [INFO] Executing action 41/51: Tap on Text: "Nearby"
[[21:24:17]] [SUCCESS] Screenshot refreshed
[[21:24:17]] [INFO] Refreshing screenshot...
[[21:24:17]] [INFO] 3gJsiap2Ds=pass
[[21:24:13]] [SUCCESS] Screenshot refreshed successfully
[[21:24:13]] [SUCCESS] Screenshot refreshed successfully
[[21:24:13]] [INFO] 3gJsiap2Ds=running
[[21:24:13]] [INFO] Executing action 40/51: Tap on Text: "Collect"
[[21:24:13]] [SUCCESS] Screenshot refreshed
[[21:24:13]] [INFO] Refreshing screenshot...
[[21:24:13]] [INFO] qofJDqXBME=pass
[[21:24:06]] [SUCCESS] Screenshot refreshed successfully
[[21:24:06]] [SUCCESS] Screenshot refreshed successfully
[[21:24:06]] [INFO] qofJDqXBME=running
[[21:24:06]] [INFO] Executing action 39/51: Wait till text appears: "Delivery"
[[21:24:06]] [SUCCESS] Screenshot refreshed
[[21:24:06]] [INFO] Refreshing screenshot...
[[21:24:06]] [INFO] rkwVoJGZG4=pass
[[21:24:04]] [SUCCESS] Screenshot refreshed successfully
[[21:24:04]] [SUCCESS] Screenshot refreshed successfully
[[21:24:03]] [INFO] rkwVoJGZG4=running
[[21:24:03]] [INFO] Executing action 38/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[21:24:03]] [SUCCESS] Screenshot refreshed
[[21:24:03]] [INFO] Refreshing screenshot...
[[21:24:03]] [INFO] 94ikwhIEE2=pass
[[21:23:59]] [SUCCESS] Screenshot refreshed successfully
[[21:23:59]] [SUCCESS] Screenshot refreshed successfully
[[21:23:56]] [INFO] 94ikwhIEE2=running
[[21:23:56]] [INFO] Executing action 37/51: Tap on Text: "bag"
[[21:23:55]] [SUCCESS] Screenshot refreshed
[[21:23:55]] [INFO] Refreshing screenshot...
[[21:23:55]] [INFO] DfwaiVZ8Z9=pass
[[21:23:52]] [SUCCESS] Screenshot refreshed successfully
[[21:23:52]] [SUCCESS] Screenshot refreshed successfully
[[21:23:52]] [INFO] DfwaiVZ8Z9=running
[[21:23:52]] [INFO] Executing action 36/51: Swipe from (50%, 70%) to (50%, 50%)
[[21:23:52]] [SUCCESS] Screenshot refreshed
[[21:23:52]] [INFO] Refreshing screenshot...
[[21:23:52]] [INFO] eRCmRhc3re=pass
[[21:23:49]] [SUCCESS] Screenshot refreshed successfully
[[21:23:49]] [SUCCESS] Screenshot refreshed successfully
[[21:23:48]] [INFO] eRCmRhc3re=running
[[21:23:48]] [INFO] Executing action 35/51: Check if element with text="Broadway" exists
[[21:23:48]] [SUCCESS] Screenshot refreshed
[[21:23:48]] [INFO] Refreshing screenshot...
[[21:23:48]] [INFO] E2jpN7BioW=pass
[[21:23:46]] [SUCCESS] Screenshot refreshed successfully
[[21:23:46]] [SUCCESS] Screenshot refreshed successfully
[[21:23:45]] [INFO] E2jpN7BioW=running
[[21:23:45]] [INFO] Executing action 34/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:23:45]] [SUCCESS] Screenshot refreshed
[[21:23:45]] [INFO] Refreshing screenshot...
[[21:23:45]] [INFO] IOc0IwmLPQ=pass
[[21:23:43]] [SUCCESS] Screenshot refreshed successfully
[[21:23:43]] [SUCCESS] Screenshot refreshed successfully
[[21:23:43]] [INFO] IOc0IwmLPQ=running
[[21:23:43]] [INFO] Executing action 33/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:23:42]] [SUCCESS] Screenshot refreshed
[[21:23:42]] [INFO] Refreshing screenshot...
[[21:23:42]] [INFO] H0ODFz7sWJ=pass
[[21:23:36]] [SUCCESS] Screenshot refreshed successfully
[[21:23:36]] [SUCCESS] Screenshot refreshed successfully
[[21:23:36]] [INFO] H0ODFz7sWJ=running
[[21:23:36]] [INFO] Executing action 32/51: Tap on Text: "2000"
[[21:23:35]] [SUCCESS] Screenshot refreshed
[[21:23:35]] [INFO] Refreshing screenshot...
[[21:23:35]] [INFO] pldheRUBVi=pass
[[21:23:33]] [SUCCESS] Screenshot refreshed successfully
[[21:23:33]] [SUCCESS] Screenshot refreshed successfully
[[21:23:32]] [INFO] pldheRUBVi=running
[[21:23:32]] [INFO] Executing action 31/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:23:32]] [SUCCESS] Screenshot refreshed
[[21:23:32]] [INFO] Refreshing screenshot...
[[21:23:32]] [INFO] uZHvvAzVfx=pass
[[21:23:30]] [SUCCESS] Screenshot refreshed successfully
[[21:23:30]] [SUCCESS] Screenshot refreshed successfully
[[21:23:29]] [INFO] uZHvvAzVfx=running
[[21:23:29]] [INFO] Executing action 30/51: textClear action
[[21:23:29]] [SUCCESS] Screenshot refreshed
[[21:23:29]] [INFO] Refreshing screenshot...
[[21:23:29]] [INFO] WmNWcsWVHv=pass
[[21:23:24]] [SUCCESS] Screenshot refreshed successfully
[[21:23:24]] [SUCCESS] Screenshot refreshed successfully
[[21:23:23]] [INFO] WmNWcsWVHv=running
[[21:23:23]] [INFO] Executing action 29/51: Tap on Text: "4000"
[[21:23:23]] [SUCCESS] Screenshot refreshed
[[21:23:23]] [INFO] Refreshing screenshot...
[[21:23:23]] [INFO] lnjoz8hHUU=pass
[[21:23:07]] [SUCCESS] Screenshot refreshed successfully
[[21:23:07]] [SUCCESS] Screenshot refreshed successfully
[[21:23:07]] [INFO] lnjoz8hHUU=running
[[21:23:07]] [INFO] Executing action 28/51: Tap on Text: "Edit"
[[21:23:06]] [SUCCESS] Screenshot refreshed
[[21:23:06]] [INFO] Refreshing screenshot...
[[21:23:06]] [INFO] BQ7Cxm53HQ=pass
[[21:23:04]] [SUCCESS] Screenshot refreshed successfully
[[21:23:04]] [SUCCESS] Screenshot refreshed successfully
[[21:23:04]] [INFO] BQ7Cxm53HQ=running
[[21:23:04]] [INFO] Executing action 27/51: Wait till text appears: "UNO"
[[21:23:04]] [SUCCESS] Screenshot refreshed
[[21:23:04]] [INFO] Refreshing screenshot...
[[21:23:04]] [INFO] VkUKQbf1Qt=pass
[[21:22:55]] [SUCCESS] Screenshot refreshed successfully
[[21:22:55]] [SUCCESS] Screenshot refreshed successfully
[[21:22:54]] [INFO] VkUKQbf1Qt=running
[[21:22:54]] [INFO] Executing action 26/51: Tap on Text: "UNO"
[[21:22:54]] [SUCCESS] Screenshot refreshed
[[21:22:54]] [INFO] Refreshing screenshot...
[[21:22:54]] [INFO] 73NABkfWyY=pass
[[21:22:51]] [SUCCESS] Screenshot refreshed successfully
[[21:22:51]] [SUCCESS] Screenshot refreshed successfully
[[21:22:50]] [INFO] 73NABkfWyY=running
[[21:22:50]] [INFO] Executing action 25/51: Check if element with text="Toowong" exists
[[21:22:50]] [SUCCESS] Screenshot refreshed
[[21:22:50]] [INFO] Refreshing screenshot...
[[21:22:50]] [INFO] E2jpN7BioW=pass
[[21:22:47]] [SUCCESS] Screenshot refreshed successfully
[[21:22:47]] [SUCCESS] Screenshot refreshed successfully
[[21:22:47]] [INFO] E2jpN7BioW=running
[[21:22:47]] [INFO] Executing action 24/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:22:47]] [SUCCESS] Screenshot refreshed
[[21:22:47]] [INFO] Refreshing screenshot...
[[21:22:47]] [INFO] IOc0IwmLPQ=pass
[[21:22:44]] [SUCCESS] Screenshot refreshed successfully
[[21:22:44]] [SUCCESS] Screenshot refreshed successfully
[[21:22:43]] [INFO] IOc0IwmLPQ=running
[[21:22:43]] [INFO] Executing action 23/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:22:43]] [SUCCESS] Screenshot refreshed
[[21:22:43]] [INFO] Refreshing screenshot...
[[21:22:43]] [INFO] VkUKQbf1Qt=pass
[[21:22:39]] [SUCCESS] Screenshot refreshed successfully
[[21:22:39]] [SUCCESS] Screenshot refreshed successfully
[[21:22:39]] [INFO] VkUKQbf1Qt=running
[[21:22:39]] [INFO] Executing action 22/51: Tap on Text: "CITY"
[[21:22:39]] [SUCCESS] Screenshot refreshed
[[21:22:39]] [INFO] Refreshing screenshot...
[[21:22:39]] [INFO] pldheRUBVi=pass
[[21:22:36]] [SUCCESS] Screenshot refreshed successfully
[[21:22:36]] [SUCCESS] Screenshot refreshed successfully
[[21:22:36]] [INFO] pldheRUBVi=running
[[21:22:36]] [INFO] Executing action 21/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:22:36]] [SUCCESS] Screenshot refreshed
[[21:22:36]] [INFO] Refreshing screenshot...
[[21:22:36]] [INFO] kbdEPCPYod=pass
[[21:22:32]] [SUCCESS] Screenshot refreshed successfully
[[21:22:32]] [SUCCESS] Screenshot refreshed successfully
[[21:22:32]] [INFO] kbdEPCPYod=running
[[21:22:32]] [INFO] Executing action 20/51: textClear action
[[21:22:32]] [SUCCESS] Screenshot refreshed
[[21:22:32]] [INFO] Refreshing screenshot...
[[21:22:32]] [INFO] pldheRUBVi=pass
[[21:22:29]] [SUCCESS] Screenshot refreshed successfully
[[21:22:29]] [SUCCESS] Screenshot refreshed successfully
[[21:22:29]] [INFO] pldheRUBVi=running
[[21:22:29]] [INFO] Executing action 19/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:22:28]] [SUCCESS] Screenshot refreshed
[[21:22:28]] [INFO] Refreshing screenshot...
[[21:22:28]] [INFO] 0FggZQe6oU=pass
[[21:22:27]] [SUCCESS] Screenshot refreshed successfully
[[21:22:27]] [SUCCESS] Screenshot refreshed successfully
[[21:22:26]] [INFO] 0FggZQe6oU=running
[[21:22:26]] [INFO] Executing action 18/51: Wait till xpath=//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:22:26]] [SUCCESS] Screenshot refreshed
[[21:22:26]] [INFO] Refreshing screenshot...
[[21:22:26]] [INFO] VkUKQbf1Qt=pass
[[21:22:18]] [SUCCESS] Screenshot refreshed successfully
[[21:22:18]] [SUCCESS] Screenshot refreshed successfully
[[21:22:17]] [INFO] VkUKQbf1Qt=running
[[21:22:17]] [INFO] Executing action 17/51: Tap on Text: "Edit"
[[21:22:17]] [SUCCESS] Screenshot refreshed
[[21:22:17]] [INFO] Refreshing screenshot...
[[21:22:17]] [INFO] BQ7Cxm53HQ=pass
[[21:22:13]] [SUCCESS] Screenshot refreshed successfully
[[21:22:13]] [SUCCESS] Screenshot refreshed successfully
[[21:22:13]] [INFO] BQ7Cxm53HQ=running
[[21:22:13]] [INFO] Executing action 16/51: Wait till text appears: "UNO"
[[21:22:13]] [SUCCESS] Screenshot refreshed
[[21:22:13]] [INFO] Refreshing screenshot...
[[21:22:13]] [INFO] IupxLP2Jsr=pass
[[21:22:11]] [SUCCESS] Screenshot refreshed successfully
[[21:22:11]] [SUCCESS] Screenshot refreshed successfully
[[21:22:11]] [INFO] IupxLP2Jsr=running
[[21:22:11]] [INFO] Executing action 15/51: Input text: "P_6225544"
[[21:22:10]] [SUCCESS] Screenshot refreshed
[[21:22:10]] [INFO] Refreshing screenshot...
[[21:22:10]] [INFO] 70iOOakiG7=pass
[[21:22:06]] [SUCCESS] Screenshot refreshed successfully
[[21:22:06]] [SUCCESS] Screenshot refreshed successfully
[[21:22:03]] [INFO] 70iOOakiG7=running
[[21:22:03]] [INFO] Executing action 14/51: Tap on Text: "Find"
[[21:22:02]] [SUCCESS] Screenshot refreshed
[[21:22:02]] [INFO] Refreshing screenshot...
[[21:22:02]] [INFO] Xqj9EIVEfg=pass
[[21:21:24]] [SUCCESS] Screenshot refreshed successfully
[[21:21:24]] [SUCCESS] Screenshot refreshed successfully
[[21:21:24]] [INFO] Xqj9EIVEfg=running
[[21:21:24]] [INFO] Executing action 13/51: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[21:21:23]] [SUCCESS] Screenshot refreshed
[[21:21:23]] [INFO] Refreshing screenshot...
[[21:21:23]] [INFO] E2jpN7BioW=pass
[[21:21:13]] [SUCCESS] Screenshot refreshed successfully
[[21:21:13]] [SUCCESS] Screenshot refreshed successfully
[[21:21:12]] [INFO] E2jpN7BioW=running
[[21:21:12]] [INFO] Executing action 12/51: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:21:12]] [SUCCESS] Screenshot refreshed
[[21:21:12]] [INFO] Refreshing screenshot...
[[21:21:12]] [INFO] kDnmoQJG4o=pass
[[21:21:10]] [SUCCESS] Screenshot refreshed successfully
[[21:21:10]] [SUCCESS] Screenshot refreshed successfully
[[21:21:10]] [INFO] kDnmoQJG4o=running
[[21:21:10]] [INFO] Executing action 11/51: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[21:21:09]] [SUCCESS] Screenshot refreshed
[[21:21:09]] [INFO] Refreshing screenshot...
[[21:21:09]] [INFO] mw9GQ4mzRE=pass
[[21:21:05]] [SUCCESS] Screenshot refreshed successfully
[[21:21:05]] [SUCCESS] Screenshot refreshed successfully
[[21:21:05]] [INFO] mw9GQ4mzRE=running
[[21:21:05]] [INFO] Executing action 10/51: Tap on Text: "ADELAIDE"
[[21:21:04]] [SUCCESS] Screenshot refreshed
[[21:21:04]] [INFO] Refreshing screenshot...
[[21:21:04]] [INFO] pldheRUBVi=pass
[[21:20:42]] [SUCCESS] Screenshot refreshed successfully
[[21:20:42]] [SUCCESS] Screenshot refreshed successfully
[[21:20:42]] [INFO] pldheRUBVi=running
[[21:20:42]] [INFO] Executing action 9/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:20:41]] [SUCCESS] Screenshot refreshed
[[21:20:41]] [INFO] Refreshing screenshot...
[[21:20:41]] [INFO] kbdEPCPYod=pass
[[21:20:38]] [SUCCESS] Screenshot refreshed successfully
[[21:20:38]] [SUCCESS] Screenshot refreshed successfully
[[21:20:38]] [INFO] kbdEPCPYod=running
[[21:20:38]] [INFO] Executing action 8/51: textClear action
[[21:20:37]] [SUCCESS] Screenshot refreshed
[[21:20:37]] [INFO] Refreshing screenshot...
[[21:20:37]] [INFO] pldheRUBVi=pass
[[21:20:35]] [SUCCESS] Screenshot refreshed successfully
[[21:20:35]] [SUCCESS] Screenshot refreshed successfully
[[21:20:35]] [INFO] pldheRUBVi=running
[[21:20:35]] [INFO] Executing action 7/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[21:20:35]] [SUCCESS] Screenshot refreshed
[[21:20:35]] [INFO] Refreshing screenshot...
[[21:20:35]] [INFO] QMXBlswP6H=pass
[[21:20:28]] [SUCCESS] Screenshot refreshed successfully
[[21:20:28]] [SUCCESS] Screenshot refreshed successfully
[[21:20:22]] [INFO] QMXBlswP6H=running
[[21:20:22]] [INFO] Executing action 6/51: Tap on Text: "Edit"
[[21:20:21]] [SUCCESS] Screenshot refreshed
[[21:20:21]] [INFO] Refreshing screenshot...
[[21:20:21]] [INFO] RLz6vQo3ag=pass
[[21:20:16]] [INFO] RLz6vQo3ag=running
[[21:20:16]] [INFO] Executing action 5/51: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[21:20:16]] [SUCCESS] Screenshot refreshed
[[21:20:16]] [INFO] Refreshing screenshot...
[[21:20:15]] [SUCCESS] Screenshot refreshed
[[21:20:15]] [INFO] Refreshing screenshot...
[[21:20:14]] [SUCCESS] Screenshot refreshed successfully
[[21:20:14]] [SUCCESS] Screenshot refreshed successfully
[[21:20:13]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[21:20:13]] [SUCCESS] Screenshot refreshed
[[21:20:13]] [INFO] Refreshing screenshot...
[[21:20:11]] [SUCCESS] Screenshot refreshed successfully
[[21:20:11]] [SUCCESS] Screenshot refreshed successfully
[[21:20:11]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[21:20:10]] [SUCCESS] Screenshot refreshed
[[21:20:10]] [INFO] Refreshing screenshot...
[[21:20:08]] [SUCCESS] Screenshot refreshed successfully
[[21:20:08]] [SUCCESS] Screenshot refreshed successfully
[[21:20:07]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[21:20:07]] [SUCCESS] Screenshot refreshed
[[21:20:07]] [INFO] Refreshing screenshot...
[[21:20:05]] [SUCCESS] Screenshot refreshed successfully
[[21:20:05]] [SUCCESS] Screenshot refreshed successfully
[[21:20:05]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[21:20:04]] [SUCCESS] Screenshot refreshed
[[21:20:04]] [INFO] Refreshing screenshot...
[[21:20:02]] [SUCCESS] Screenshot refreshed successfully
[[21:20:02]] [SUCCESS] Screenshot refreshed successfully
[[21:20:02]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[21:20:02]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[21:20:02]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[21:20:02]] [INFO] xz8njynjpZ=running
[[21:20:02]] [INFO] Executing action 4/51: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[21:20:02]] [SUCCESS] Screenshot refreshed
[[21:20:02]] [INFO] Refreshing screenshot...
[[21:20:02]] [INFO] J9loj6Zl5K=pass
[[21:20:00]] [SUCCESS] Screenshot refreshed successfully
[[21:20:00]] [SUCCESS] Screenshot refreshed successfully
[[21:19:59]] [INFO] J9loj6Zl5K=running
[[21:19:59]] [INFO] Executing action 3/51: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[21:19:59]] [SUCCESS] Screenshot refreshed
[[21:19:59]] [INFO] Refreshing screenshot...
[[21:19:59]] [INFO] Y8vz7AJD1i=pass
[[21:19:55]] [SUCCESS] Screenshot refreshed successfully
[[21:19:55]] [SUCCESS] Screenshot refreshed successfully
[[21:19:55]] [INFO] Y8vz7AJD1i=running
[[21:19:55]] [INFO] Executing action 2/51: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[21:19:54]] [SUCCESS] Screenshot refreshed
[[21:19:54]] [INFO] Refreshing screenshot...
[[21:19:54]] [INFO] H9fy9qcFbZ=pass
[[21:19:51]] [INFO] H9fy9qcFbZ=running
[[21:19:51]] [INFO] Executing action 1/51: Launch app: au.com.kmart
[[21:19:51]] [INFO] ExecutionManager: Starting execution of 51 actions...
[[21:19:51]] [SUCCESS] Cleared 1 screenshots from database
[[21:19:51]] [INFO] Clearing screenshots from database before execution...
[[21:19:51]] [SUCCESS] All screenshots deleted successfully
[[21:19:51]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:19:51]] [INFO] Skipping report initialization - single test case execution
[[21:19:49]] [SUCCESS] All screenshots deleted successfully
[[21:19:49]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 51 actions
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: swipe
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: exists
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: swipe
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: textClear
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: waitTill
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: swipe
[[21:19:49]] [SUCCESS] Added action: exists
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: waitTill
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: textClear
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: waitTill
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: exists
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: waitTill
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: textClear
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: waitTill
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: waitTill
[[21:19:49]] [SUCCESS] Added action: text
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: ifElseSteps
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: waitTill
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: textClear
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: tapOnText
[[21:19:49]] [SUCCESS] Added action: waitTill
[[21:19:49]] [SUCCESS] Added action: multiStep
[[21:19:49]] [SUCCESS] Added action: waitTill
[[21:19:49]] [SUCCESS] Added action: tap
[[21:19:49]] [SUCCESS] Added action: launchApp
[[21:19:49]] [INFO] All actions cleared
[[21:19:49]] [INFO] Cleaning up screenshots...
[[21:19:41]] [SUCCESS] Screenshot refreshed successfully
[[21:19:40]] [SUCCESS] Screenshot refreshed
[[21:19:40]] [INFO] Refreshing screenshot...
[[21:19:39]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[21:19:39]] [INFO] Device info updated: RMX2151
[[21:19:29]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[21:19:27]] [SUCCESS] Found 1 device(s)
[[21:19:26]] [INFO] Refreshing device list...
