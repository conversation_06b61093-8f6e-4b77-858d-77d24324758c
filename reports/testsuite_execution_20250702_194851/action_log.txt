Action Log - 2025-07-02 19:56:05
================================================================================

[[19:56:05]] [INFO] Generating execution report...
[[19:56:05]] [SUCCESS] All tests passed successfully!
[[19:56:04]] [SUCCESS] Screenshot refreshed
[[19:56:04]] [INFO] Refreshing screenshot...
[[19:56:04]] [INFO] xyHVihJMBi=pass
[[19:55:32]] [SUCCESS] Screenshot refreshed successfully
[[19:55:32]] [SUCCESS] Screenshot refreshed successfully
[[19:55:31]] [INFO] xyHVihJMBi=running
[[19:55:31]] [INFO] Executing action 50/50: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[19:55:31]] [SUCCESS] Screenshot refreshed
[[19:55:31]] [INFO] Refreshing screenshot...
[[19:55:31]] [INFO] mWeLQtXiL6=pass
[[19:55:26]] [SUCCESS] Screenshot refreshed successfully
[[19:55:26]] [SUCCESS] Screenshot refreshed successfully
[[19:55:26]] [INFO] mWeLQtXiL6=running
[[19:55:26]] [INFO] Executing action 49/50: Swipe from (50%, 70%) to (50%, 30%)
[[19:55:25]] [SUCCESS] Screenshot refreshed
[[19:55:25]] [INFO] Refreshing screenshot...
[[19:55:25]] [INFO] rkwVoJGZG4=pass
[[19:55:23]] [SUCCESS] Screenshot refreshed successfully
[[19:55:23]] [SUCCESS] Screenshot refreshed successfully
[[19:55:23]] [INFO] rkwVoJGZG4=running
[[19:55:23]] [INFO] Executing action 48/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[19:55:22]] [SUCCESS] Screenshot refreshed
[[19:55:22]] [INFO] Refreshing screenshot...
[[19:55:22]] [INFO] 0f2FSZYjWq=pass
[[19:55:10]] [SUCCESS] Screenshot refreshed successfully
[[19:55:10]] [SUCCESS] Screenshot refreshed successfully
[[19:55:10]] [INFO] 0f2FSZYjWq=running
[[19:55:10]] [INFO] Executing action 47/50: Check if element with text="3000" exists
[[19:55:09]] [SUCCESS] Screenshot refreshed
[[19:55:09]] [INFO] Refreshing screenshot...
[[19:55:09]] [INFO] Tebej51pT2=pass
[[19:55:07]] [SUCCESS] Screenshot refreshed successfully
[[19:55:07]] [SUCCESS] Screenshot refreshed successfully
[[19:55:07]] [INFO] Tebej51pT2=running
[[19:55:07]] [INFO] Executing action 46/50: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[19:55:06]] [SUCCESS] Screenshot refreshed
[[19:55:06]] [INFO] Refreshing screenshot...
[[19:55:06]] [INFO] eVytJrry9x=pass
[[19:55:04]] [SUCCESS] Screenshot refreshed successfully
[[19:55:04]] [SUCCESS] Screenshot refreshed successfully
[[19:55:04]] [INFO] eVytJrry9x=running
[[19:55:04]] [INFO] Executing action 45/50: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[19:55:04]] [SUCCESS] Screenshot refreshed
[[19:55:04]] [INFO] Refreshing screenshot...
[[19:55:04]] [INFO] s8h8VDUIOC=pass
[[19:55:01]] [INFO] s8h8VDUIOC=running
[[19:55:01]] [INFO] Executing action 44/50: Swipe from (50%, 70%) to (50%, 30%)
[[19:55:00]] [INFO] ZWpYNcpbFA=fail
[[19:55:00]] [ERROR] Action 43 failed: Error tapping on text: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[19:54:26]] [SUCCESS] Screenshot refreshed successfully
[[19:54:26]] [SUCCESS] Screenshot refreshed successfully
[[19:54:26]] [INFO] ZWpYNcpbFA=running
[[19:54:26]] [INFO] Executing action 43/50: Tap on Text: "VIC"
[[19:54:25]] [SUCCESS] Screenshot refreshed
[[19:54:25]] [INFO] Refreshing screenshot...
[[19:54:25]] [INFO] QpBLC6BStn=pass
[[19:54:23]] [SUCCESS] Screenshot refreshed successfully
[[19:54:23]] [SUCCESS] Screenshot refreshed successfully
[[19:54:23]] [INFO] QpBLC6BStn=running
[[19:54:23]] [INFO] Executing action 42/50: textClear action
[[19:54:22]] [SUCCESS] Screenshot refreshed
[[19:54:22]] [INFO] Refreshing screenshot...
[[19:54:22]] [INFO] G4A3KBlXHq=pass
[[19:54:12]] [SUCCESS] Screenshot refreshed successfully
[[19:54:12]] [SUCCESS] Screenshot refreshed successfully
[[19:54:12]] [INFO] G4A3KBlXHq=running
[[19:54:12]] [INFO] Executing action 41/50: Tap on Text: "Nearby"
[[19:54:11]] [SUCCESS] Screenshot refreshed
[[19:54:11]] [INFO] Refreshing screenshot...
[[19:54:11]] [INFO] 3gJsiap2Ds=pass
[[19:54:07]] [INFO] 3gJsiap2Ds=running
[[19:54:07]] [INFO] Executing action 40/50: Tap on Text: "Collect"
[[19:54:07]] [INFO] sevnilsRXy=fail
[[19:54:07]] [ERROR] Action 39 failed: Element with xpath '(//android.widget.TextView[@text="Delivery"])[2]' not found within timeout of 30.0 seconds
[[19:53:36]] [SUCCESS] Screenshot refreshed successfully
[[19:53:36]] [SUCCESS] Screenshot refreshed successfully
[[19:53:35]] [INFO] sevnilsRXy=running
[[19:53:35]] [INFO] Executing action 39/50: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[19:53:35]] [SUCCESS] Screenshot refreshed
[[19:53:35]] [INFO] Refreshing screenshot...
[[19:53:35]] [INFO] rkwVoJGZG4=pass
[[19:53:33]] [SUCCESS] Screenshot refreshed successfully
[[19:53:33]] [SUCCESS] Screenshot refreshed successfully
[[19:53:33]] [INFO] rkwVoJGZG4=running
[[19:53:33]] [INFO] Executing action 38/50: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[19:53:32]] [SUCCESS] Screenshot refreshed
[[19:53:32]] [INFO] Refreshing screenshot...
[[19:53:32]] [INFO] 94ikwhIEE2=pass
[[19:53:26]] [SUCCESS] Screenshot refreshed successfully
[[19:53:26]] [SUCCESS] Screenshot refreshed successfully
[[19:53:25]] [INFO] 94ikwhIEE2=running
[[19:53:25]] [INFO] Executing action 37/50: Tap on Text: "bag"
[[19:53:25]] [SUCCESS] Screenshot refreshed
[[19:53:25]] [INFO] Refreshing screenshot...
[[19:53:25]] [INFO] DfwaiVZ8Z9=pass
[[19:53:22]] [SUCCESS] Screenshot refreshed successfully
[[19:53:22]] [SUCCESS] Screenshot refreshed successfully
[[19:53:21]] [INFO] DfwaiVZ8Z9=running
[[19:53:21]] [INFO] Executing action 36/50: Swipe from (50%, 70%) to (50%, 50%)
[[19:53:21]] [SUCCESS] Screenshot refreshed
[[19:53:21]] [INFO] Refreshing screenshot...
[[19:53:21]] [INFO] eRCmRhc3re=pass
[[19:53:18]] [SUCCESS] Screenshot refreshed successfully
[[19:53:18]] [SUCCESS] Screenshot refreshed successfully
[[19:53:18]] [INFO] eRCmRhc3re=running
[[19:53:18]] [INFO] Executing action 35/50: Check if element with text="Broadway" exists
[[19:53:17]] [SUCCESS] Screenshot refreshed
[[19:53:17]] [INFO] Refreshing screenshot...
[[19:53:17]] [INFO] E2jpN7BioW=pass
[[19:53:15]] [SUCCESS] Screenshot refreshed successfully
[[19:53:15]] [SUCCESS] Screenshot refreshed successfully
[[19:53:15]] [INFO] E2jpN7BioW=running
[[19:53:15]] [INFO] Executing action 34/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[19:53:14]] [SUCCESS] Screenshot refreshed
[[19:53:14]] [INFO] Refreshing screenshot...
[[19:53:14]] [INFO] IOc0IwmLPQ=pass
[[19:53:13]] [SUCCESS] Screenshot refreshed successfully
[[19:53:13]] [SUCCESS] Screenshot refreshed successfully
[[19:53:12]] [INFO] IOc0IwmLPQ=running
[[19:53:12]] [INFO] Executing action 33/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[19:53:12]] [SUCCESS] Screenshot refreshed
[[19:53:12]] [INFO] Refreshing screenshot...
[[19:53:12]] [INFO] H0ODFz7sWJ=pass
[[19:53:07]] [SUCCESS] Screenshot refreshed successfully
[[19:53:07]] [SUCCESS] Screenshot refreshed successfully
[[19:53:07]] [INFO] H0ODFz7sWJ=running
[[19:53:07]] [INFO] Executing action 32/50: Tap on Text: "2000"
[[19:53:06]] [SUCCESS] Screenshot refreshed
[[19:53:06]] [INFO] Refreshing screenshot...
[[19:53:06]] [INFO] pldheRUBVi=pass
[[19:53:04]] [SUCCESS] Screenshot refreshed successfully
[[19:53:04]] [SUCCESS] Screenshot refreshed successfully
[[19:53:04]] [INFO] pldheRUBVi=running
[[19:53:04]] [INFO] Executing action 31/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[19:53:04]] [SUCCESS] Screenshot refreshed
[[19:53:04]] [INFO] Refreshing screenshot...
[[19:53:04]] [INFO] uZHvvAzVfx=pass
[[19:52:34]] [SUCCESS] Screenshot refreshed successfully
[[19:52:34]] [SUCCESS] Screenshot refreshed successfully
[[19:52:33]] [INFO] uZHvvAzVfx=running
[[19:52:33]] [INFO] Executing action 30/50: textClear action
[[19:52:33]] [SUCCESS] Screenshot refreshed
[[19:52:33]] [INFO] Refreshing screenshot...
[[19:52:33]] [INFO] WmNWcsWVHv=pass
[[19:52:29]] [SUCCESS] Screenshot refreshed successfully
[[19:52:29]] [SUCCESS] Screenshot refreshed successfully
[[19:52:29]] [INFO] WmNWcsWVHv=running
[[19:52:29]] [INFO] Executing action 29/50: Tap on Text: "4000"
[[19:52:29]] [SUCCESS] Screenshot refreshed
[[19:52:29]] [INFO] Refreshing screenshot...
[[19:52:29]] [INFO] lnjoz8hHUU=pass
[[19:52:25]] [SUCCESS] Screenshot refreshed successfully
[[19:52:25]] [SUCCESS] Screenshot refreshed successfully
[[19:52:25]] [INFO] lnjoz8hHUU=running
[[19:52:25]] [INFO] Executing action 28/50: Tap on Text: "Edit"
[[19:52:24]] [SUCCESS] Screenshot refreshed
[[19:52:24]] [INFO] Refreshing screenshot...
[[19:52:24]] [INFO] BQ7Cxm53HQ=pass
[[19:52:22]] [SUCCESS] Screenshot refreshed successfully
[[19:52:22]] [SUCCESS] Screenshot refreshed successfully
[[19:52:22]] [INFO] BQ7Cxm53HQ=running
[[19:52:22]] [INFO] Executing action 27/50: Wait till text appears: "UNO"
[[19:52:22]] [SUCCESS] Screenshot refreshed
[[19:52:22]] [INFO] Refreshing screenshot...
[[19:52:22]] [INFO] VkUKQbf1Qt=pass
[[19:52:16]] [SUCCESS] Screenshot refreshed successfully
[[19:52:16]] [SUCCESS] Screenshot refreshed successfully
[[19:52:16]] [INFO] VkUKQbf1Qt=running
[[19:52:16]] [INFO] Executing action 26/50: Tap on Text: "UNO"
[[19:52:16]] [SUCCESS] Screenshot refreshed
[[19:52:16]] [INFO] Refreshing screenshot...
[[19:52:16]] [INFO] 73NABkfWyY=pass
[[19:52:09]] [SUCCESS] Screenshot refreshed successfully
[[19:52:09]] [SUCCESS] Screenshot refreshed successfully
[[19:52:09]] [INFO] 73NABkfWyY=running
[[19:52:09]] [INFO] Executing action 25/50: Check if element with text="Toowong" exists
[[19:52:08]] [SUCCESS] Screenshot refreshed
[[19:52:08]] [INFO] Refreshing screenshot...
[[19:52:08]] [INFO] E2jpN7BioW=pass
[[19:52:06]] [SUCCESS] Screenshot refreshed successfully
[[19:52:06]] [SUCCESS] Screenshot refreshed successfully
[[19:52:06]] [INFO] E2jpN7BioW=running
[[19:52:06]] [INFO] Executing action 24/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[19:52:05]] [SUCCESS] Screenshot refreshed
[[19:52:05]] [INFO] Refreshing screenshot...
[[19:52:05]] [INFO] IOc0IwmLPQ=pass
[[19:52:03]] [SUCCESS] Screenshot refreshed successfully
[[19:52:03]] [SUCCESS] Screenshot refreshed successfully
[[19:52:03]] [INFO] IOc0IwmLPQ=running
[[19:52:03]] [INFO] Executing action 23/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[19:52:02]] [SUCCESS] Screenshot refreshed
[[19:52:02]] [INFO] Refreshing screenshot...
[[19:52:02]] [INFO] VkUKQbf1Qt=pass
[[19:51:37]] [SUCCESS] Screenshot refreshed successfully
[[19:51:37]] [SUCCESS] Screenshot refreshed successfully
[[19:51:35]] [INFO] VkUKQbf1Qt=running
[[19:51:35]] [INFO] Executing action 22/50: Tap on Text: "CITY"
[[19:51:34]] [SUCCESS] Screenshot refreshed
[[19:51:34]] [INFO] Refreshing screenshot...
[[19:51:34]] [INFO] pldheRUBVi=pass
[[19:51:32]] [SUCCESS] Screenshot refreshed successfully
[[19:51:32]] [SUCCESS] Screenshot refreshed successfully
[[19:51:32]] [INFO] pldheRUBVi=running
[[19:51:32]] [INFO] Executing action 21/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[19:51:31]] [SUCCESS] Screenshot refreshed
[[19:51:31]] [INFO] Refreshing screenshot...
[[19:51:31]] [INFO] kbdEPCPYod=pass
[[19:51:29]] [SUCCESS] Screenshot refreshed successfully
[[19:51:29]] [SUCCESS] Screenshot refreshed successfully
[[19:51:28]] [INFO] kbdEPCPYod=running
[[19:51:28]] [INFO] Executing action 20/50: textClear action
[[19:51:28]] [SUCCESS] Screenshot refreshed
[[19:51:28]] [INFO] Refreshing screenshot...
[[19:51:28]] [INFO] GYRHQr7TWx=pass
[[19:51:22]] [SUCCESS] Screenshot refreshed successfully
[[19:51:22]] [SUCCESS] Screenshot refreshed successfully
[[19:51:22]] [INFO] GYRHQr7TWx=running
[[19:51:22]] [INFO] Executing action 19/50: Tap on Text: "3000"
[[19:51:21]] [SUCCESS] Screenshot refreshed
[[19:51:21]] [INFO] Refreshing screenshot...
[[19:51:21]] [INFO] 8WCusTZ8q9=pass
[[19:51:09]] [SUCCESS] Screenshot refreshed successfully
[[19:51:09]] [SUCCESS] Screenshot refreshed successfully
[[19:51:09]] [INFO] 8WCusTZ8q9=running
[[19:51:09]] [INFO] Executing action 18/50: Tap on Text: "Search"
[[19:51:08]] [SUCCESS] Screenshot refreshed
[[19:51:08]] [INFO] Refreshing screenshot...
[[19:51:08]] [INFO] VkUKQbf1Qt=pass
[[19:51:04]] [SUCCESS] Screenshot refreshed successfully
[[19:51:04]] [SUCCESS] Screenshot refreshed successfully
[[19:51:04]] [INFO] VkUKQbf1Qt=running
[[19:51:04]] [INFO] Executing action 17/50: Tap on Text: "Edit"
[[19:51:03]] [SUCCESS] Screenshot refreshed
[[19:51:03]] [INFO] Refreshing screenshot...
[[19:51:03]] [INFO] BQ7Cxm53HQ=pass
[[19:50:44]] [SUCCESS] Screenshot refreshed successfully
[[19:50:44]] [SUCCESS] Screenshot refreshed successfully
[[19:50:44]] [INFO] BQ7Cxm53HQ=running
[[19:50:44]] [INFO] Executing action 16/50: Wait till text appears: "UNO"
[[19:50:44]] [SUCCESS] Screenshot refreshed
[[19:50:44]] [INFO] Refreshing screenshot...
[[19:50:44]] [INFO] IupxLP2Jsr=pass
[[19:50:42]] [SUCCESS] Screenshot refreshed successfully
[[19:50:42]] [SUCCESS] Screenshot refreshed successfully
[[19:50:41]] [INFO] IupxLP2Jsr=running
[[19:50:41]] [INFO] Executing action 15/50: Input text: "P_6225544"
[[19:50:41]] [SUCCESS] Screenshot refreshed
[[19:50:41]] [INFO] Refreshing screenshot...
[[19:50:41]] [INFO] 70iOOakiG7=pass
[[19:50:15]] [SUCCESS] Screenshot refreshed successfully
[[19:50:15]] [SUCCESS] Screenshot refreshed successfully
[[19:50:14]] [INFO] 70iOOakiG7=running
[[19:50:14]] [INFO] Executing action 14/50: Tap on Text: "Find"
[[19:50:14]] [SUCCESS] Screenshot refreshed
[[19:50:14]] [INFO] Refreshing screenshot...
[[19:50:14]] [INFO] Xqj9EIVEfg=pass
[[19:50:02]] [SUCCESS] Screenshot refreshed successfully
[[19:50:02]] [SUCCESS] Screenshot refreshed successfully
[[19:50:02]] [INFO] Xqj9EIVEfg=running
[[19:50:02]] [INFO] Executing action 13/50: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[19:50:01]] [SUCCESS] Screenshot refreshed
[[19:50:01]] [INFO] Refreshing screenshot...
[[19:50:01]] [INFO] E2jpN7BioW=pass
[[19:49:59]] [SUCCESS] Screenshot refreshed successfully
[[19:49:59]] [SUCCESS] Screenshot refreshed successfully
[[19:49:59]] [INFO] E2jpN7BioW=running
[[19:49:59]] [INFO] Executing action 12/50: Tap on element with xpath: //android.widget.Button[@content-desc="btnSaveOrContinue"]
[[19:49:58]] [SUCCESS] Screenshot refreshed
[[19:49:58]] [INFO] Refreshing screenshot...
[[19:49:58]] [INFO] kDnmoQJG4o=pass
[[19:49:56]] [SUCCESS] Screenshot refreshed successfully
[[19:49:56]] [SUCCESS] Screenshot refreshed successfully
[[19:49:56]] [INFO] kDnmoQJG4o=running
[[19:49:56]] [INFO] Executing action 11/50: Wait till xpath=//android.widget.Button[@content-desc="btnSaveOrContinue"]
[[19:49:56]] [SUCCESS] Screenshot refreshed
[[19:49:56]] [INFO] Refreshing screenshot...
[[19:49:56]] [INFO] mw9GQ4mzRE=pass
[[19:49:51]] [SUCCESS] Screenshot refreshed successfully
[[19:49:51]] [SUCCESS] Screenshot refreshed successfully
[[19:49:51]] [INFO] mw9GQ4mzRE=running
[[19:49:51]] [INFO] Executing action 10/50: Tap on Text: "VIC"
[[19:49:51]] [SUCCESS] Screenshot refreshed
[[19:49:51]] [INFO] Refreshing screenshot...
[[19:49:51]] [INFO] pldheRUBVi=pass
[[19:49:48]] [SUCCESS] Screenshot refreshed successfully
[[19:49:48]] [SUCCESS] Screenshot refreshed successfully
[[19:49:48]] [INFO] pldheRUBVi=running
[[19:49:48]] [INFO] Executing action 9/50: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[19:49:47]] [SUCCESS] Screenshot refreshed
[[19:49:47]] [INFO] Refreshing screenshot...
[[19:49:47]] [INFO] kbdEPCPYod=pass
[[19:49:44]] [SUCCESS] Screenshot refreshed successfully
[[19:49:44]] [SUCCESS] Screenshot refreshed successfully
[[19:49:44]] [INFO] kbdEPCPYod=running
[[19:49:44]] [INFO] Executing action 8/50: textClear action
[[19:49:43]] [SUCCESS] Screenshot refreshed
[[19:49:43]] [INFO] Refreshing screenshot...
[[19:49:43]] [INFO] 8WCusTZ8q9=pass
[[19:49:38]] [SUCCESS] Screenshot refreshed successfully
[[19:49:38]] [SUCCESS] Screenshot refreshed successfully
[[19:49:38]] [INFO] 8WCusTZ8q9=running
[[19:49:38]] [INFO] Executing action 7/50: Tap on Text: "3000"
[[19:49:37]] [SUCCESS] Screenshot refreshed
[[19:49:37]] [INFO] Refreshing screenshot...
[[19:49:37]] [INFO] QMXBlswP6H=pass
[[19:49:32]] [SUCCESS] Screenshot refreshed successfully
[[19:49:32]] [SUCCESS] Screenshot refreshed successfully
[[19:49:29]] [INFO] QMXBlswP6H=running
[[19:49:29]] [INFO] Executing action 6/50: Tap on Text: "Edit"
[[19:49:28]] [SUCCESS] Screenshot refreshed
[[19:49:28]] [INFO] Refreshing screenshot...
[[19:49:28]] [INFO] RLz6vQo3ag=pass
[[19:49:25]] [SUCCESS] Screenshot refreshed successfully
[[19:49:25]] [SUCCESS] Screenshot refreshed successfully
[[19:49:21]] [INFO] RLz6vQo3ag=running
[[19:49:21]] [INFO] Executing action 5/50: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[19:49:21]] [SUCCESS] Screenshot refreshed
[[19:49:21]] [INFO] Refreshing screenshot...
[[19:49:20]] [SUCCESS] Screenshot refreshed
[[19:49:20]] [INFO] Refreshing screenshot...
[[19:49:18]] [SUCCESS] Screenshot refreshed successfully
[[19:49:18]] [SUCCESS] Screenshot refreshed successfully
[[19:49:18]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[19:49:18]] [SUCCESS] Screenshot refreshed
[[19:49:18]] [INFO] Refreshing screenshot...
[[19:49:16]] [SUCCESS] Screenshot refreshed successfully
[[19:49:16]] [SUCCESS] Screenshot refreshed successfully
[[19:49:15]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[19:49:15]] [SUCCESS] Screenshot refreshed
[[19:49:15]] [INFO] Refreshing screenshot...
[[19:49:12]] [SUCCESS] Screenshot refreshed successfully
[[19:49:12]] [SUCCESS] Screenshot refreshed successfully
[[19:49:12]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[19:49:12]] [SUCCESS] Screenshot refreshed
[[19:49:12]] [INFO] Refreshing screenshot...
[[19:49:10]] [SUCCESS] Screenshot refreshed successfully
[[19:49:10]] [SUCCESS] Screenshot refreshed successfully
[[19:49:09]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[19:49:09]] [SUCCESS] Screenshot refreshed
[[19:49:09]] [INFO] Refreshing screenshot...
[[19:49:07]] [SUCCESS] Screenshot refreshed successfully
[[19:49:07]] [SUCCESS] Screenshot refreshed successfully
[[19:49:07]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[19:49:07]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[19:49:07]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[19:49:07]] [INFO] xz8njynjpZ=running
[[19:49:07]] [INFO] Executing action 4/50: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[19:49:06]] [SUCCESS] Screenshot refreshed
[[19:49:06]] [INFO] Refreshing screenshot...
[[19:49:06]] [INFO] J9loj6Zl5K=pass
[[19:49:03]] [SUCCESS] Screenshot refreshed successfully
[[19:49:03]] [SUCCESS] Screenshot refreshed successfully
[[19:49:03]] [INFO] J9loj6Zl5K=running
[[19:49:03]] [INFO] Executing action 3/50: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[19:49:03]] [SUCCESS] Screenshot refreshed
[[19:49:03]] [INFO] Refreshing screenshot...
[[19:49:03]] [INFO] Y8vz7AJD1i=pass
[[19:48:52]] [SUCCESS] Screenshot refreshed successfully
[[19:48:52]] [SUCCESS] Screenshot refreshed successfully
[[19:48:52]] [INFO] Y8vz7AJD1i=running
[[19:48:52]] [INFO] Executing action 2/50: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[19:48:51]] [SUCCESS] Screenshot refreshed
[[19:48:51]] [INFO] Refreshing screenshot...
[[19:48:51]] [INFO] H9fy9qcFbZ=pass
[[19:48:47]] [INFO] H9fy9qcFbZ=running
[[19:48:47]] [INFO] Executing action 1/50: Launch app: au.com.kmart
[[19:48:47]] [INFO] ExecutionManager: Starting execution of 50 actions...
[[19:48:47]] [SUCCESS] Cleared 1 screenshots from database
[[19:48:47]] [INFO] Clearing screenshots from database before execution...
[[19:48:47]] [SUCCESS] All screenshots deleted successfully
[[19:48:47]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[19:48:47]] [INFO] Skipping report initialization - single test case execution
[[19:48:45]] [SUCCESS] All screenshots deleted successfully
[[19:48:45]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 50 actions
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: swipe
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: exists
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: swipe
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: textClear
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: waitTill
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: swipe
[[19:48:45]] [SUCCESS] Added action: exists
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: waitTill
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: textClear
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: waitTill
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: exists
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: waitTill
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: textClear
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: waitTill
[[19:48:45]] [SUCCESS] Added action: text
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: ifElseSteps
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: waitTill
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: textClear
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: tapOnText
[[19:48:45]] [SUCCESS] Added action: waitTill
[[19:48:45]] [SUCCESS] Added action: multiStep
[[19:48:45]] [SUCCESS] Added action: waitTill
[[19:48:45]] [SUCCESS] Added action: tap
[[19:48:45]] [SUCCESS] Added action: launchApp
[[19:48:45]] [INFO] All actions cleared
[[19:48:45]] [INFO] Cleaning up screenshots...
[[19:48:32]] [SUCCESS] Screenshot refreshed successfully
[[19:48:31]] [SUCCESS] Screenshot refreshed
[[19:48:31]] [INFO] Refreshing screenshot...
[[19:48:30]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[19:48:30]] [INFO] Device info updated: RMX2151
[[19:48:01]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[19:47:49]] [SUCCESS] Found 1 device(s)
[[19:47:48]] [INFO] Refreshing device list...
