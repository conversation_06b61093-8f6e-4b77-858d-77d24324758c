Action Log - 2025-07-02 07:24:55
================================================================================

[[07:24:55]] [INFO] Generating execution report...
[[07:24:55]] [SUCCESS] All tests passed successfully!
[[07:24:55]] [SUCCESS] Screenshot refreshed
[[07:24:55]] [INFO] Refreshing screenshot...
[[07:24:55]] [INFO] K7yV3GGsgr=pass
[[07:24:53]] [SUCCESS] Screenshot refreshed successfully
[[07:24:53]] [SUCCESS] Screenshot refreshed successfully
[[07:24:53]] [INFO] K7yV3GGsgr=running
[[07:24:53]] [INFO] Executing action 4/4: Input text: "Wonderbaby@5"
[[07:24:52]] [SUCCESS] Screenshot refreshed
[[07:24:52]] [INFO] Refreshing screenshot...
[[07:24:52]] [INFO] AtwFJEKbDH=pass
[[07:24:50]] [SUCCESS] Screenshot refreshed successfully
[[07:24:50]] [SUCCESS] Screenshot refreshed successfully
[[07:24:49]] [INFO] AtwFJEKbDH=running
[[07:24:49]] [INFO] Executing action 3/4: Input text: "<EMAIL>"
[[07:24:49]] [SUCCESS] Screenshot refreshed
[[07:24:49]] [INFO] Refreshing screenshot...
[[07:24:49]] [INFO] RCYxT9YD8u=pass
[[07:24:47]] [SUCCESS] Screenshot refreshed successfully
[[07:24:47]] [SUCCESS] Screenshot refreshed successfully
[[07:24:47]] [INFO] RCYxT9YD8u=running
[[07:24:47]] [INFO] Executing action 2/4: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[07:24:46]] [SUCCESS] Screenshot refreshed
[[07:24:46]] [INFO] Refreshing screenshot...
[[07:24:46]] [INFO] HEBUNq0P6l=pass
[[07:24:43]] [INFO] HEBUNq0P6l=running
[[07:24:43]] [INFO] Executing action 1/4: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[07:24:43]] [INFO] ExecutionManager: Starting execution of 4 actions...
[[07:24:43]] [SUCCESS] Cleared 1 screenshots from database
[[07:24:43]] [INFO] Clearing screenshots from database before execution...
[[07:24:43]] [SUCCESS] All screenshots deleted successfully
[[07:24:43]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[07:24:43]] [INFO] Skipping report initialization - single test case execution
[[07:24:36]] [SUCCESS] All screenshots deleted successfully
[[07:24:36]] [SUCCESS] Loaded test case "Kmart-Signin-AU-ANDROID" with 4 actions
[[07:24:36]] [SUCCESS] Added action: text
[[07:24:36]] [SUCCESS] Added action: text
[[07:24:36]] [SUCCESS] Added action: tap
[[07:24:36]] [SUCCESS] Added action: waitTill
[[07:24:36]] [INFO] All actions cleared
[[07:24:36]] [INFO] Cleaning up screenshots...
[[07:24:30]] [SUCCESS] Screenshot refreshed successfully
[[07:24:29]] [SUCCESS] Screenshot refreshed
[[07:24:29]] [INFO] Refreshing screenshot...
[[07:24:28]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[07:24:28]] [INFO] Device info updated: RMX2151
[[07:24:22]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[07:24:20]] [SUCCESS] Found 1 device(s)
[[07:24:20]] [INFO] Refreshing device list...
