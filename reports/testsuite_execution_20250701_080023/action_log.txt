Action Log - 2025-07-01 09:16:24
================================================================================

[[09:16:24]] [INFO] Generating execution report...
[[09:16:24]] [WARNING] 1 test failed.
[[09:16:24]] [SUCCESS] Screenshot refreshed
[[09:16:24]] [INFO] Refreshing screenshot...
[[09:16:23]] [SUCCESS] Screenshot refreshed
[[09:16:23]] [INFO] Refreshing screenshot...
[[09:16:20]] [SUCCESS] Screenshot refreshed successfully
[[09:16:20]] [SUCCESS] Screenshot refreshed successfully
[[09:16:20]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[09:16:20]] [SUCCESS] Screenshot refreshed
[[09:16:20]] [INFO] Refreshing screenshot...
[[09:16:07]] [SUCCESS] Screenshot refreshed successfully
[[09:16:07]] [SUCCESS] Screenshot refreshed successfully
[[09:16:07]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[09:16:07]] [SUCCESS] Screenshot refreshed
[[09:16:07]] [INFO] Refreshing screenshot...
[[09:16:03]] [SUCCESS] Screenshot refreshed successfully
[[09:16:03]] [SUCCESS] Screenshot refreshed successfully
[[09:16:03]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[09:16:02]] [SUCCESS] Screenshot refreshed
[[09:16:02]] [INFO] Refreshing screenshot...
[[09:15:59]] [SUCCESS] Screenshot refreshed successfully
[[09:15:59]] [SUCCESS] Screenshot refreshed successfully
[[09:15:58]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:15:58]] [SUCCESS] Screenshot refreshed
[[09:15:58]] [INFO] Refreshing screenshot...
[[09:15:51]] [SUCCESS] Screenshot refreshed successfully
[[09:15:51]] [SUCCESS] Screenshot refreshed successfully
[[09:15:51]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[09:15:50]] [SUCCESS] Screenshot refreshed
[[09:15:50]] [INFO] Refreshing screenshot...
[[09:15:44]] [SUCCESS] Screenshot refreshed successfully
[[09:15:44]] [SUCCESS] Screenshot refreshed successfully
[[09:15:44]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[09:15:44]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[09:15:44]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[09:15:44]] [INFO] Ll4UlkE3L9=running
[[09:15:44]] [INFO] Executing action 576/576: cleanupSteps action
[[09:15:43]] [SUCCESS] Screenshot refreshed
[[09:15:43]] [INFO] Refreshing screenshot...
[[09:15:43]] [INFO] 25UEKPIknm=pass
[[09:15:40]] [SUCCESS] Screenshot refreshed successfully
[[09:15:40]] [SUCCESS] Screenshot refreshed successfully
[[09:15:40]] [INFO] 25UEKPIknm=running
[[09:15:40]] [INFO] Executing action 575/576: Terminate app: env[appid]
[[09:15:39]] [SUCCESS] Screenshot refreshed
[[09:15:39]] [INFO] Refreshing screenshot...
[[09:15:39]] [INFO] UqgDn5CuPY=pass
[[09:15:36]] [SUCCESS] Screenshot refreshed successfully
[[09:15:36]] [SUCCESS] Screenshot refreshed successfully
[[09:15:36]] [INFO] UqgDn5CuPY=running
[[09:15:36]] [INFO] Executing action 574/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[09:15:36]] [SUCCESS] Screenshot refreshed
[[09:15:36]] [INFO] Refreshing screenshot...
[[09:15:36]] [INFO] VfTTTtrliQ=pass
[[09:15:33]] [SUCCESS] Screenshot refreshed successfully
[[09:15:33]] [SUCCESS] Screenshot refreshed successfully
[[09:15:33]] [INFO] VfTTTtrliQ=running
[[09:15:33]] [INFO] Executing action 573/576: iOS Function: alert_accept
[[09:15:32]] [SUCCESS] Screenshot refreshed
[[09:15:32]] [INFO] Refreshing screenshot...
[[09:15:32]] [INFO] ipT2XD9io6=pass
[[09:15:28]] [SUCCESS] Screenshot refreshed successfully
[[09:15:28]] [SUCCESS] Screenshot refreshed successfully
[[09:15:28]] [INFO] ipT2XD9io6=running
[[09:15:28]] [INFO] Executing action 572/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountJoinTodayButton"]
[[09:15:27]] [SUCCESS] Screenshot refreshed
[[09:15:27]] [INFO] Refreshing screenshot...
[[09:15:27]] [INFO] OKCHAK6HCJ=pass
[[09:15:23]] [SUCCESS] Screenshot refreshed successfully
[[09:15:23]] [SUCCESS] Screenshot refreshed successfully
[[09:15:22]] [INFO] OKCHAK6HCJ=running
[[09:15:22]] [INFO] Executing action 571/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:15:22]] [SUCCESS] Screenshot refreshed
[[09:15:22]] [INFO] Refreshing screenshot...
[[09:15:22]] [INFO] AEnFqnkOa1=pass
[[09:15:18]] [SUCCESS] Screenshot refreshed successfully
[[09:15:18]] [SUCCESS] Screenshot refreshed successfully
[[09:15:17]] [INFO] AEnFqnkOa1=running
[[09:15:17]] [INFO] Executing action 570/576: Tap on image: banner-close-updated.png
[[09:15:17]] [SUCCESS] Screenshot refreshed
[[09:15:17]] [INFO] Refreshing screenshot...
[[09:15:17]] [INFO] z1CfcW4xYT=pass
[[09:15:13]] [SUCCESS] Screenshot refreshed successfully
[[09:15:13]] [SUCCESS] Screenshot refreshed successfully
[[09:15:13]] [INFO] z1CfcW4xYT=running
[[09:15:13]] [INFO] Executing action 569/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:15:12]] [SUCCESS] Screenshot refreshed
[[09:15:12]] [INFO] Refreshing screenshot...
[[09:15:12]] [INFO] dJNRgTXoqs=pass
[[09:15:08]] [SUCCESS] Screenshot refreshed successfully
[[09:15:08]] [SUCCESS] Screenshot refreshed successfully
[[09:15:08]] [INFO] dJNRgTXoqs=running
[[09:15:08]] [INFO] Executing action 568/576: Swipe from (50%, 30%) to (50%, 70%)
[[09:15:07]] [SUCCESS] Screenshot refreshed
[[09:15:07]] [INFO] Refreshing screenshot...
[[09:15:07]] [INFO] ceF4VRTJlO=pass
[[09:15:03]] [SUCCESS] Screenshot refreshed successfully
[[09:15:03]] [SUCCESS] Screenshot refreshed successfully
[[09:15:03]] [INFO] ceF4VRTJlO=running
[[09:15:03]] [INFO] Executing action 567/576: Tap on image: banner-close-updated.png
[[09:15:03]] [SUCCESS] Screenshot refreshed
[[09:15:03]] [INFO] Refreshing screenshot...
[[09:15:03]] [INFO] 8hCPyY2zPt=pass
[[09:14:58]] [SUCCESS] Screenshot refreshed successfully
[[09:14:58]] [SUCCESS] Screenshot refreshed successfully
[[09:14:58]] [INFO] 8hCPyY2zPt=running
[[09:14:58]] [INFO] Executing action 566/576: Tap on element with xpath: //XCUIElementTypeButton[@name="About KHub Stores"]
[[09:14:58]] [SUCCESS] Screenshot refreshed
[[09:14:58]] [INFO] Refreshing screenshot...
[[09:14:58]] [INFO] r0FfJ85LFM=pass
[[09:14:54]] [SUCCESS] Screenshot refreshed successfully
[[09:14:54]] [SUCCESS] Screenshot refreshed successfully
[[09:14:54]] [INFO] r0FfJ85LFM=running
[[09:14:54]] [INFO] Executing action 565/576: Tap on image: banner-close-updated.png
[[09:14:53]] [SUCCESS] Screenshot refreshed
[[09:14:53]] [INFO] Refreshing screenshot...
[[09:14:53]] [INFO] 2QEdm5WM18=pass
[[09:14:49]] [SUCCESS] Screenshot refreshed successfully
[[09:14:49]] [SUCCESS] Screenshot refreshed successfully
[[09:14:49]] [INFO] 2QEdm5WM18=running
[[09:14:49]] [INFO] Executing action 564/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Bags for Click & Collect orders"]
[[09:14:49]] [SUCCESS] Screenshot refreshed
[[09:14:49]] [INFO] Refreshing screenshot...
[[09:14:49]] [INFO] NW6M15JbAy=pass
[[09:14:36]] [SUCCESS] Screenshot refreshed successfully
[[09:14:36]] [SUCCESS] Screenshot refreshed successfully
[[09:14:36]] [INFO] NW6M15JbAy=running
[[09:14:36]] [INFO] Executing action 563/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Bags for Click & Collect orders"]" is visible
[[09:14:35]] [SUCCESS] Screenshot refreshed
[[09:14:35]] [INFO] Refreshing screenshot...
[[09:14:35]] [INFO] njiHWyVooT=pass
[[09:14:31]] [SUCCESS] Screenshot refreshed successfully
[[09:14:31]] [SUCCESS] Screenshot refreshed successfully
[[09:14:30]] [INFO] njiHWyVooT=running
[[09:14:30]] [INFO] Executing action 562/576: Tap on image: banner-close-updated.png
[[09:14:30]] [SUCCESS] Screenshot refreshed
[[09:14:30]] [INFO] Refreshing screenshot...
[[09:14:30]] [INFO] 93bAew9Y4Y=pass
[[09:14:25]] [SUCCESS] Screenshot refreshed successfully
[[09:14:25]] [SUCCESS] Screenshot refreshed successfully
[[09:14:25]] [INFO] 93bAew9Y4Y=running
[[09:14:25]] [INFO] Executing action 561/576: Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,"store details")])[1]
[[09:14:24]] [SUCCESS] Screenshot refreshed
[[09:14:24]] [INFO] Refreshing screenshot...
[[09:14:24]] [INFO] rPQ5EkTza1=pass
[[09:14:20]] [SUCCESS] Screenshot refreshed successfully
[[09:14:20]] [SUCCESS] Screenshot refreshed successfully
[[09:14:20]] [INFO] rPQ5EkTza1=running
[[09:14:20]] [INFO] Executing action 560/576: Tap on Text: "Click"
[[09:14:20]] [SUCCESS] Screenshot refreshed
[[09:14:20]] [INFO] Refreshing screenshot...
[[09:14:19]] [SUCCESS] Screenshot refreshed
[[09:14:19]] [INFO] Refreshing screenshot...
[[09:14:16]] [INFO] Executing Multi Step action step 6/6: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[09:14:15]] [SUCCESS] Screenshot refreshed successfully
[[09:14:15]] [SUCCESS] Screenshot refreshed successfully
[[09:14:15]] [SUCCESS] Screenshot refreshed
[[09:14:15]] [INFO] Refreshing screenshot...
[[09:14:11]] [SUCCESS] Screenshot refreshed successfully
[[09:14:11]] [SUCCESS] Screenshot refreshed successfully
[[09:14:11]] [INFO] Executing Multi Step action step 5/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:14:10]] [SUCCESS] Screenshot refreshed
[[09:14:10]] [INFO] Refreshing screenshot...
[[09:14:06]] [SUCCESS] Screenshot refreshed successfully
[[09:14:06]] [SUCCESS] Screenshot refreshed successfully
[[09:14:06]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[09:14:05]] [SUCCESS] Screenshot refreshed
[[09:14:05]] [INFO] Refreshing screenshot...
[[09:14:01]] [SUCCESS] Screenshot refreshed successfully
[[09:14:01]] [SUCCESS] Screenshot refreshed successfully
[[09:14:01]] [INFO] Executing Multi Step action step 3/6: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:14:00]] [SUCCESS] Screenshot refreshed
[[09:14:00]] [INFO] Refreshing screenshot...
[[09:13:56]] [SUCCESS] Screenshot refreshed successfully
[[09:13:56]] [SUCCESS] Screenshot refreshed successfully
[[09:13:56]] [INFO] Executing Multi Step action step 2/6: iOS Function: text - Text: "Notebook"
[[09:13:55]] [SUCCESS] Screenshot refreshed
[[09:13:55]] [INFO] Refreshing screenshot...
[[09:13:48]] [SUCCESS] Screenshot refreshed successfully
[[09:13:48]] [SUCCESS] Screenshot refreshed successfully
[[09:13:47]] [INFO] Executing Multi Step action step 1/6: Tap on Text: "Find"
[[09:13:47]] [INFO] Loaded 6 steps from test case: Search and Add (Notebooks)
[[09:13:47]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[09:13:47]] [INFO] 0YgZZfWdYY=running
[[09:13:47]] [INFO] Executing action 559/576: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[09:13:47]] [SUCCESS] Screenshot refreshed
[[09:13:47]] [INFO] Refreshing screenshot...
[[09:13:47]] [INFO] arH1CZCPXh=pass
[[09:13:42]] [SUCCESS] Screenshot refreshed successfully
[[09:13:42]] [SUCCESS] Screenshot refreshed successfully
[[09:13:41]] [INFO] arH1CZCPXh=running
[[09:13:41]] [INFO] Executing action 558/576: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[09:13:41]] [SUCCESS] Screenshot refreshed
[[09:13:41]] [INFO] Refreshing screenshot...
[[09:13:41]] [INFO] JLAJhxPdsl=pass
[[09:13:35]] [SUCCESS] Screenshot refreshed successfully
[[09:13:35]] [SUCCESS] Screenshot refreshed successfully
[[09:13:35]] [INFO] JLAJhxPdsl=running
[[09:13:35]] [INFO] Executing action 557/576: Tap on Text: "Cancel"
[[09:13:35]] [SUCCESS] Screenshot refreshed
[[09:13:35]] [INFO] Refreshing screenshot...
[[09:13:35]] [INFO] UqgDn5CuPY=pass
[[09:13:32]] [SUCCESS] Screenshot refreshed successfully
[[09:13:32]] [SUCCESS] Screenshot refreshed successfully
[[09:13:31]] [INFO] UqgDn5CuPY=running
[[09:13:31]] [INFO] Executing action 556/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[09:13:30]] [SUCCESS] Screenshot refreshed
[[09:13:30]] [INFO] Refreshing screenshot...
[[09:13:30]] [INFO] VfTTTtrliQ=pass
[[09:13:28]] [SUCCESS] Screenshot refreshed successfully
[[09:13:28]] [SUCCESS] Screenshot refreshed successfully
[[09:13:27]] [INFO] VfTTTtrliQ=running
[[09:13:27]] [INFO] Executing action 555/576: iOS Function: alert_accept
[[09:13:27]] [SUCCESS] Screenshot refreshed
[[09:13:27]] [INFO] Refreshing screenshot...
[[09:13:27]] [INFO] ipT2XD9io6=pass
[[09:13:22]] [SUCCESS] Screenshot refreshed successfully
[[09:13:22]] [SUCCESS] Screenshot refreshed successfully
[[09:13:22]] [INFO] ipT2XD9io6=running
[[09:13:22]] [INFO] Executing action 554/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtJoinTodayButton"]
[[09:13:21]] [SUCCESS] Screenshot refreshed
[[09:13:21]] [INFO] Refreshing screenshot...
[[09:13:21]] [INFO] OKCHAK6HCJ=pass
[[09:13:17]] [SUCCESS] Screenshot refreshed successfully
[[09:13:17]] [SUCCESS] Screenshot refreshed successfully
[[09:13:17]] [INFO] OKCHAK6HCJ=running
[[09:13:17]] [INFO] Executing action 553/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:13:16]] [SUCCESS] Screenshot refreshed
[[09:13:16]] [INFO] Refreshing screenshot...
[[09:13:16]] [INFO] RbD937Xbte=pass
[[09:13:11]] [SUCCESS] Screenshot refreshed successfully
[[09:13:11]] [SUCCESS] Screenshot refreshed successfully
[[09:13:11]] [INFO] RbD937Xbte=running
[[09:13:11]] [INFO] Executing action 552/576: Tap on Text: "out"
[[09:13:11]] [SUCCESS] Screenshot refreshed
[[09:13:11]] [INFO] Refreshing screenshot...
[[09:13:11]] [INFO] ylslyLAYKb=pass
[[09:13:07]] [SUCCESS] Screenshot refreshed successfully
[[09:13:07]] [SUCCESS] Screenshot refreshed successfully
[[09:13:07]] [INFO] ylslyLAYKb=running
[[09:13:07]] [INFO] Executing action 551/576: Swipe from (50%, 70%) to (50%, 30%)
[[09:13:06]] [SUCCESS] Screenshot refreshed
[[09:13:06]] [INFO] Refreshing screenshot...
[[09:13:06]] [INFO] wguGCt7OoB=pass
[[09:13:02]] [SUCCESS] Screenshot refreshed successfully
[[09:13:02]] [SUCCESS] Screenshot refreshed successfully
[[09:13:02]] [INFO] wguGCt7OoB=running
[[09:13:02]] [INFO] Executing action 550/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:13:02]] [SUCCESS] Screenshot refreshed
[[09:13:02]] [INFO] Refreshing screenshot...
[[09:13:02]] [INFO] RDQCFIxjA0=pass
[[09:12:57]] [SUCCESS] Screenshot refreshed successfully
[[09:12:57]] [SUCCESS] Screenshot refreshed successfully
[[09:12:57]] [INFO] RDQCFIxjA0=running
[[09:12:57]] [INFO] Executing action 549/576: Swipe from (90%, 20%) to (30%, 20%)
[[09:12:57]] [SUCCESS] Screenshot refreshed
[[09:12:57]] [INFO] Refreshing screenshot...
[[09:12:57]] [INFO] x4Mid4HQ0Z=pass
[[09:12:52]] [SUCCESS] Screenshot refreshed successfully
[[09:12:52]] [SUCCESS] Screenshot refreshed successfully
[[09:12:52]] [INFO] x4Mid4HQ0Z=running
[[09:12:52]] [INFO] Executing action 548/576: Swipe from (90%, 20%) to (30%, 20%)
[[09:12:52]] [SUCCESS] Screenshot refreshed
[[09:12:52]] [INFO] Refreshing screenshot...
[[09:12:52]] [INFO] OKCHAK6HCJ=pass
[[09:12:47]] [SUCCESS] Screenshot refreshed successfully
[[09:12:47]] [SUCCESS] Screenshot refreshed successfully
[[09:12:47]] [INFO] OKCHAK6HCJ=running
[[09:12:47]] [INFO] Executing action 547/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[09:12:47]] [SUCCESS] Screenshot refreshed
[[09:12:47]] [INFO] Refreshing screenshot...
[[09:12:47]] [INFO] Ef6OumM2eS=pass
[[09:12:42]] [SUCCESS] Screenshot refreshed successfully
[[09:12:42]] [SUCCESS] Screenshot refreshed successfully
[[09:12:42]] [INFO] Ef6OumM2eS=running
[[09:12:42]] [INFO] Executing action 546/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[09:12:42]] [SUCCESS] Screenshot refreshed
[[09:12:42]] [INFO] Refreshing screenshot...
[[09:12:42]] [INFO] QkaF93zxUg=pass
[[09:12:38]] [SUCCESS] Screenshot refreshed successfully
[[09:12:38]] [SUCCESS] Screenshot refreshed successfully
[[09:12:38]] [INFO] QkaF93zxUg=running
[[09:12:38]] [INFO] Executing action 545/576: Check if element with xpath="(//XCUIElementTypeStaticText[@name="Value"])[1]" exists
[[09:12:37]] [SUCCESS] Screenshot refreshed
[[09:12:37]] [INFO] Refreshing screenshot...
[[09:12:37]] [INFO] HZT2s0AzX7=pass
[[09:12:33]] [SUCCESS] Screenshot refreshed successfully
[[09:12:33]] [SUCCESS] Screenshot refreshed successfully
[[09:12:33]] [INFO] HZT2s0AzX7=running
[[09:12:33]] [INFO] Executing action 544/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="("]/following-sibling::*[1]
[[09:12:32]] [SUCCESS] Screenshot refreshed
[[09:12:32]] [INFO] Refreshing screenshot...
[[09:12:32]] [INFO] 0bnBNoqPt8=pass
[[09:12:28]] [SUCCESS] Screenshot refreshed successfully
[[09:12:28]] [SUCCESS] Screenshot refreshed successfully
[[09:12:28]] [INFO] 0bnBNoqPt8=running
[[09:12:28]] [INFO] Executing action 543/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[09:12:27]] [SUCCESS] Screenshot refreshed
[[09:12:27]] [INFO] Refreshing screenshot...
[[09:12:27]] [INFO] xmelRkcdVx=pass
[[09:12:23]] [SUCCESS] Screenshot refreshed successfully
[[09:12:23]] [SUCCESS] Screenshot refreshed successfully
[[09:12:22]] [INFO] xmelRkcdVx=running
[[09:12:22]] [INFO] Executing action 542/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[09:12:22]] [SUCCESS] Screenshot refreshed
[[09:12:22]] [INFO] Refreshing screenshot...
[[09:12:22]] [INFO] ksCBjJiwHZ=pass
[[09:12:18]] [SUCCESS] Screenshot refreshed successfully
[[09:12:18]] [SUCCESS] Screenshot refreshed successfully
[[09:12:18]] [INFO] ksCBjJiwHZ=running
[[09:12:18]] [INFO] Executing action 541/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:12:17]] [SUCCESS] Screenshot refreshed
[[09:12:17]] [INFO] Refreshing screenshot...
[[09:12:17]] [INFO] RuPGkdCdah=pass
[[09:12:13]] [SUCCESS] Screenshot refreshed successfully
[[09:12:13]] [SUCCESS] Screenshot refreshed successfully
[[09:12:13]] [INFO] RuPGkdCdah=running
[[09:12:13]] [INFO] Executing action 540/576: iOS Function: text - Text: "enn[cooker-id]"
[[09:12:13]] [SUCCESS] Screenshot refreshed
[[09:12:13]] [INFO] Refreshing screenshot...
[[09:12:13]] [INFO] ewuLtuqVuo=pass
[[09:12:07]] [SUCCESS] Screenshot refreshed successfully
[[09:12:07]] [SUCCESS] Screenshot refreshed successfully
[[09:12:07]] [INFO] ewuLtuqVuo=running
[[09:12:07]] [INFO] Executing action 539/576: Tap on Text: "Find"
[[09:12:06]] [SUCCESS] Screenshot refreshed
[[09:12:06]] [INFO] Refreshing screenshot...
[[09:12:06]] [INFO] GTXmST3hEA=pass
[[09:12:02]] [SUCCESS] Screenshot refreshed successfully
[[09:12:02]] [SUCCESS] Screenshot refreshed successfully
[[09:12:02]] [INFO] GTXmST3hEA=running
[[09:12:02]] [INFO] Executing action 538/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[09:12:01]] [SUCCESS] Screenshot refreshed
[[09:12:01]] [INFO] Refreshing screenshot...
[[09:12:01]] [INFO] qkZ5KShdEU=pass
[[09:11:56]] [SUCCESS] Screenshot refreshed successfully
[[09:11:56]] [SUCCESS] Screenshot refreshed successfully
[[09:11:56]] [INFO] qkZ5KShdEU=running
[[09:11:56]] [INFO] Executing action 537/576: iOS Function: text - Text: "env[pwd]"
[[09:11:55]] [SUCCESS] Screenshot refreshed
[[09:11:55]] [INFO] Refreshing screenshot...
[[09:11:55]] [INFO] 7g2LmvjtEZ=pass
[[09:11:51]] [SUCCESS] Screenshot refreshed successfully
[[09:11:51]] [SUCCESS] Screenshot refreshed successfully
[[09:11:51]] [INFO] 7g2LmvjtEZ=running
[[09:11:51]] [INFO] Executing action 536/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:11:51]] [SUCCESS] Screenshot refreshed
[[09:11:51]] [INFO] Refreshing screenshot...
[[09:11:51]] [INFO] OUT2ASweb6=pass
[[09:11:46]] [SUCCESS] Screenshot refreshed successfully
[[09:11:46]] [SUCCESS] Screenshot refreshed successfully
[[09:11:46]] [INFO] OUT2ASweb6=running
[[09:11:46]] [INFO] Executing action 535/576: iOS Function: text - Text: "env[uname]"
[[09:11:45]] [SUCCESS] Screenshot refreshed
[[09:11:45]] [INFO] Refreshing screenshot...
[[09:11:45]] [INFO] TV4kJIIV9v=pass
[[09:11:41]] [SUCCESS] Screenshot refreshed successfully
[[09:11:41]] [SUCCESS] Screenshot refreshed successfully
[[09:11:41]] [INFO] TV4kJIIV9v=running
[[09:11:41]] [INFO] Executing action 534/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:11:40]] [SUCCESS] Screenshot refreshed
[[09:11:40]] [INFO] Refreshing screenshot...
[[09:11:40]] [INFO] kQJbqm7uCi=pass
[[09:11:38]] [SUCCESS] Screenshot refreshed successfully
[[09:11:38]] [SUCCESS] Screenshot refreshed successfully
[[09:11:37]] [INFO] kQJbqm7uCi=running
[[09:11:37]] [INFO] Executing action 533/576: iOS Function: alert_accept
[[09:11:37]] [SUCCESS] Screenshot refreshed
[[09:11:37]] [INFO] Refreshing screenshot...
[[09:11:37]] [INFO] SPE01N6pyp=pass
[[09:11:30]] [SUCCESS] Screenshot refreshed successfully
[[09:11:30]] [SUCCESS] Screenshot refreshed successfully
[[09:11:30]] [INFO] SPE01N6pyp=running
[[09:11:30]] [INFO] Executing action 532/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:11:29]] [SUCCESS] Screenshot refreshed
[[09:11:29]] [INFO] Refreshing screenshot...
[[09:11:29]] [INFO] WEB5St2Mb7=pass
[[09:11:25]] [SUCCESS] Screenshot refreshed successfully
[[09:11:25]] [SUCCESS] Screenshot refreshed successfully
[[09:11:25]] [INFO] WEB5St2Mb7=running
[[09:11:25]] [INFO] Executing action 531/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:11:24]] [SUCCESS] Screenshot refreshed
[[09:11:24]] [INFO] Refreshing screenshot...
[[09:11:24]] [INFO] To7bij5MnF=pass
[[09:11:19]] [INFO] To7bij5MnF=running
[[09:11:19]] [INFO] Executing action 530/576: Swipe from (5%, 50%) to (90%, 50%)
[[09:11:19]] [SUCCESS] Screenshot refreshed successfully
[[09:11:19]] [SUCCESS] Screenshot refreshed successfully
[[09:11:19]] [SUCCESS] Screenshot refreshed
[[09:11:19]] [INFO] Refreshing screenshot...
[[09:11:19]] [INFO] NkybTKfs2U=pass
[[09:11:13]] [SUCCESS] Screenshot refreshed successfully
[[09:11:13]] [SUCCESS] Screenshot refreshed successfully
[[09:11:13]] [INFO] NkybTKfs2U=running
[[09:11:13]] [INFO] Executing action 529/576: Swipe from (5%, 50%) to (90%, 50%)
[[09:11:13]] [SUCCESS] Screenshot refreshed
[[09:11:13]] [INFO] Refreshing screenshot...
[[09:11:13]] [INFO] dYEtjrv6lz=pass
[[09:11:08]] [INFO] dYEtjrv6lz=running
[[09:11:08]] [INFO] Executing action 528/576: Tap on Text: "Months"
[[09:11:08]] [SUCCESS] Screenshot refreshed successfully
[[09:11:08]] [SUCCESS] Screenshot refreshed successfully
[[09:11:07]] [SUCCESS] Screenshot refreshed
[[09:11:07]] [INFO] Refreshing screenshot...
[[09:11:07]] [INFO] eGQ7VrKUSo=pass
[[09:11:03]] [SUCCESS] Screenshot refreshed successfully
[[09:11:03]] [SUCCESS] Screenshot refreshed successfully
[[09:11:03]] [INFO] eGQ7VrKUSo=running
[[09:11:03]] [INFO] Executing action 527/576: Tap on Text: "Age"
[[09:11:02]] [SUCCESS] Screenshot refreshed
[[09:11:02]] [INFO] Refreshing screenshot...
[[09:11:02]] [INFO] zNRPvs2cC4=pass
[[09:10:58]] [SUCCESS] Screenshot refreshed successfully
[[09:10:58]] [SUCCESS] Screenshot refreshed successfully
[[09:10:58]] [INFO] zNRPvs2cC4=running
[[09:10:58]] [INFO] Executing action 526/576: Tap on Text: "Toys"
[[09:10:57]] [SUCCESS] Screenshot refreshed
[[09:10:57]] [INFO] Refreshing screenshot...
[[09:10:57]] [INFO] KyyS139agr=pass
[[09:10:53]] [SUCCESS] Screenshot refreshed successfully
[[09:10:53]] [SUCCESS] Screenshot refreshed successfully
[[09:10:52]] [INFO] KyyS139agr=running
[[09:10:52]] [INFO] Executing action 525/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[09:10:52]] [SUCCESS] Screenshot refreshed
[[09:10:52]] [INFO] Refreshing screenshot...
[[09:10:52]] [INFO] 5e4LeoW1YU=pass
[[09:10:48]] [SUCCESS] Screenshot refreshed successfully
[[09:10:48]] [SUCCESS] Screenshot refreshed successfully
[[09:10:46]] [INFO] 5e4LeoW1YU=running
[[09:10:46]] [INFO] Executing action 524/576: Restart app: env[appid]
[[09:10:46]] [SUCCESS] Screenshot refreshed
[[09:10:46]] [INFO] Refreshing screenshot...
[[09:10:46]] [SUCCESS] Screenshot refreshed
[[09:10:46]] [INFO] Refreshing screenshot...
[[09:10:31]] [SUCCESS] Screenshot refreshed successfully
[[09:10:31]] [SUCCESS] Screenshot refreshed successfully
[[09:10:30]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[09:10:30]] [SUCCESS] Screenshot refreshed
[[09:10:30]] [INFO] Refreshing screenshot...
[[09:09:44]] [SUCCESS] Screenshot refreshed successfully
[[09:09:44]] [SUCCESS] Screenshot refreshed successfully
[[09:09:44]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[09:09:43]] [SUCCESS] Screenshot refreshed
[[09:09:43]] [INFO] Refreshing screenshot...
[[09:09:28]] [SUCCESS] Screenshot refreshed successfully
[[09:09:28]] [SUCCESS] Screenshot refreshed successfully
[[09:09:27]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[09:09:27]] [SUCCESS] Screenshot refreshed
[[09:09:27]] [INFO] Refreshing screenshot...
[[09:08:41]] [SUCCESS] Screenshot refreshed successfully
[[09:08:41]] [SUCCESS] Screenshot refreshed successfully
[[09:08:41]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[09:08:40]] [SUCCESS] Screenshot refreshed
[[09:08:40]] [INFO] Refreshing screenshot...
[[09:08:28]] [SUCCESS] Screenshot refreshed successfully
[[09:08:28]] [SUCCESS] Screenshot refreshed successfully
[[09:08:24]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[09:08:23]] [SUCCESS] Screenshot refreshed
[[09:08:23]] [INFO] Refreshing screenshot...
[[09:07:38]] [SUCCESS] Screenshot refreshed successfully
[[09:07:38]] [SUCCESS] Screenshot refreshed successfully
[[09:07:37]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[09:07:37]] [SUCCESS] Screenshot refreshed
[[09:07:37]] [INFO] Refreshing screenshot...
[[09:07:20]] [SUCCESS] Screenshot refreshed successfully
[[09:07:20]] [SUCCESS] Screenshot refreshed successfully
[[09:07:19]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[09:07:19]] [SUCCESS] Screenshot refreshed
[[09:07:19]] [INFO] Refreshing screenshot...
[[09:06:33]] [SUCCESS] Screenshot refreshed successfully
[[09:06:33]] [SUCCESS] Screenshot refreshed successfully
[[09:06:33]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[09:06:32]] [SUCCESS] Screenshot refreshed
[[09:06:32]] [INFO] Refreshing screenshot...
[[09:06:16]] [SUCCESS] Screenshot refreshed successfully
[[09:06:16]] [SUCCESS] Screenshot refreshed successfully
[[09:06:16]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[09:06:15]] [SUCCESS] Screenshot refreshed
[[09:06:15]] [INFO] Refreshing screenshot...
[[09:05:26]] [SUCCESS] Screenshot refreshed successfully
[[09:05:26]] [SUCCESS] Screenshot refreshed successfully
[[09:05:26]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[09:05:26]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[09:05:26]] [INFO] Loading steps for multiStep action: Click_Paginations
[[09:05:26]] [INFO] aqs7O0Yq2p=running
[[09:05:26]] [INFO] Executing action 523/576: Execute Test Case: Click_Paginations (10 steps)
[[09:05:25]] [SUCCESS] Screenshot refreshed
[[09:05:25]] [INFO] Refreshing screenshot...
[[09:05:25]] [INFO] IL6kON0uQ9=pass
[[09:05:21]] [SUCCESS] Screenshot refreshed successfully
[[09:05:21]] [SUCCESS] Screenshot refreshed successfully
[[09:05:21]] [INFO] IL6kON0uQ9=running
[[09:05:21]] [INFO] Executing action 522/576: iOS Function: text - Text: "kids toys"
[[09:05:20]] [SUCCESS] Screenshot refreshed
[[09:05:20]] [INFO] Refreshing screenshot...
[[09:05:20]] [INFO] 6G6P3UE7Uy=pass
[[09:05:15]] [SUCCESS] Screenshot refreshed successfully
[[09:05:15]] [SUCCESS] Screenshot refreshed successfully
[[09:05:14]] [INFO] 6G6P3UE7Uy=running
[[09:05:14]] [INFO] Executing action 521/576: Tap on Text: "Find"
[[09:05:14]] [SUCCESS] Screenshot refreshed
[[09:05:14]] [INFO] Refreshing screenshot...
[[09:05:14]] [INFO] 7xs3GiydGF=pass
[[09:05:09]] [SUCCESS] Screenshot refreshed successfully
[[09:05:09]] [SUCCESS] Screenshot refreshed successfully
[[09:05:09]] [INFO] 7xs3GiydGF=running
[[09:05:09]] [INFO] Executing action 520/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:05:09]] [SUCCESS] Screenshot refreshed
[[09:05:09]] [INFO] Refreshing screenshot...
[[09:05:09]] [INFO] VqSa9z9R2Q=pass
[[09:05:07]] [INFO] VqSa9z9R2Q=running
[[09:05:07]] [INFO] Executing action 519/576: Launch app: env[appid]
[[09:05:07]] [SUCCESS] Screenshot refreshed successfully
[[09:05:07]] [SUCCESS] Screenshot refreshed successfully
[[09:05:06]] [SUCCESS] Screenshot refreshed
[[09:05:06]] [INFO] Refreshing screenshot...
[[09:05:06]] [INFO] RHEU77LRMw=pass
[[09:05:02]] [SUCCESS] Screenshot refreshed successfully
[[09:05:02]] [SUCCESS] Screenshot refreshed successfully
[[09:05:02]] [INFO] RHEU77LRMw=running
[[09:05:02]] [INFO] Executing action 518/576: Tap on Text: "+61"
[[09:05:02]] [SUCCESS] Screenshot refreshed
[[09:05:02]] [INFO] Refreshing screenshot...
[[09:05:02]] [INFO] MTRbUlaRvI=pass
[[09:04:57]] [SUCCESS] Screenshot refreshed successfully
[[09:04:57]] [SUCCESS] Screenshot refreshed successfully
[[09:04:57]] [INFO] MTRbUlaRvI=running
[[09:04:57]] [INFO] Executing action 517/576: Tap on Text: "1800"
[[09:04:56]] [SUCCESS] Screenshot refreshed
[[09:04:56]] [INFO] Refreshing screenshot...
[[09:04:56]] [INFO] I0tM87Yjhc=pass
[[09:04:52]] [SUCCESS] Screenshot refreshed successfully
[[09:04:52]] [SUCCESS] Screenshot refreshed successfully
[[09:04:51]] [INFO] I0tM87Yjhc=running
[[09:04:51]] [INFO] Executing action 516/576: Tap on Text: "click"
[[09:04:51]] [SUCCESS] Screenshot refreshed
[[09:04:51]] [INFO] Refreshing screenshot...
[[09:04:51]] [INFO] t6L5vWfBYM=pass
[[09:04:19]] [SUCCESS] Screenshot refreshed successfully
[[09:04:19]] [SUCCESS] Screenshot refreshed successfully
[[09:04:19]] [INFO] t6L5vWfBYM=running
[[09:04:19]] [INFO] Executing action 515/576: Swipe from (50%, 70%) to (50%, 30%)
[[09:04:18]] [SUCCESS] Screenshot refreshed
[[09:04:18]] [INFO] Refreshing screenshot...
[[09:04:18]] [INFO] DhFJzlme9K=pass
[[09:04:14]] [SUCCESS] Screenshot refreshed successfully
[[09:04:14]] [SUCCESS] Screenshot refreshed successfully
[[09:04:14]] [INFO] DhFJzlme9K=running
[[09:04:14]] [INFO] Executing action 514/576: Tap on Text: "FAQ"
[[09:04:13]] [SUCCESS] Screenshot refreshed
[[09:04:13]] [INFO] Refreshing screenshot...
[[09:04:13]] [INFO] g17Boaefhg=pass
[[09:04:09]] [SUCCESS] Screenshot refreshed successfully
[[09:04:09]] [SUCCESS] Screenshot refreshed successfully
[[09:04:09]] [INFO] g17Boaefhg=running
[[09:04:09]] [INFO] Executing action 513/576: Tap on Text: "Help"
[[09:04:08]] [SUCCESS] Screenshot refreshed
[[09:04:08]] [INFO] Refreshing screenshot...
[[09:04:08]] [INFO] SqDiBhmyOG=pass
[[09:04:04]] [SUCCESS] Screenshot refreshed successfully
[[09:04:04]] [SUCCESS] Screenshot refreshed successfully
[[09:04:04]] [INFO] SqDiBhmyOG=running
[[09:04:04]] [INFO] Executing action 512/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:04:03]] [SUCCESS] Screenshot refreshed
[[09:04:03]] [INFO] Refreshing screenshot...
[[09:04:03]] [INFO] OR0SKKnFxy=pass
[[09:03:50]] [SUCCESS] Screenshot refreshed successfully
[[09:03:50]] [SUCCESS] Screenshot refreshed successfully
[[09:03:49]] [INFO] OR0SKKnFxy=running
[[09:03:49]] [INFO] Executing action 511/576: Restart app: env[appid]
[[09:03:49]] [SUCCESS] Screenshot refreshed
[[09:03:49]] [INFO] Refreshing screenshot...
[[09:03:49]] [SUCCESS] Screenshot refreshed
[[09:03:49]] [INFO] Refreshing screenshot...
[[09:03:46]] [SUCCESS] Screenshot refreshed successfully
[[09:03:46]] [SUCCESS] Screenshot refreshed successfully
[[09:03:45]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[09:03:45]] [SUCCESS] Screenshot refreshed
[[09:03:45]] [INFO] Refreshing screenshot...
[[09:03:33]] [SUCCESS] Screenshot refreshed successfully
[[09:03:33]] [SUCCESS] Screenshot refreshed successfully
[[09:03:33]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[09:03:32]] [SUCCESS] Screenshot refreshed
[[09:03:32]] [INFO] Refreshing screenshot...
[[09:03:28]] [SUCCESS] Screenshot refreshed successfully
[[09:03:28]] [SUCCESS] Screenshot refreshed successfully
[[09:03:28]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[09:03:28]] [SUCCESS] Screenshot refreshed
[[09:03:28]] [INFO] Refreshing screenshot...
[[09:03:23]] [SUCCESS] Screenshot refreshed successfully
[[09:03:23]] [SUCCESS] Screenshot refreshed successfully
[[09:03:23]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:03:22]] [SUCCESS] Screenshot refreshed
[[09:03:22]] [INFO] Refreshing screenshot...
[[09:03:16]] [SUCCESS] Screenshot refreshed successfully
[[09:03:16]] [SUCCESS] Screenshot refreshed successfully
[[09:03:15]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[09:03:15]] [SUCCESS] Screenshot refreshed
[[09:03:15]] [INFO] Refreshing screenshot...
[[09:03:08]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[09:03:08]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[09:03:07]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[09:03:07]] [INFO] kPdSiomhwu=running
[[09:03:07]] [INFO] Executing action 510/576: cleanupSteps action
[[09:03:07]] [INFO] Skipping remaining steps in failed test case (moving from action 494 to 509), but preserving cleanup steps
[[09:03:07]] [INFO] Iab9zCfpqO=fail
[[09:03:07]] [ERROR] Action 494 failed: Element not found or not tappable: accessibility_id='Add to bag'
[[09:02:53]] [SUCCESS] Screenshot refreshed successfully
[[09:02:53]] [SUCCESS] Screenshot refreshed successfully
[[09:02:53]] [INFO] Iab9zCfpqO=running
[[09:02:53]] [INFO] Executing action 494/576: Tap on element with accessibility_id: Add to bag
[[09:02:53]] [SUCCESS] Screenshot refreshed
[[09:02:53]] [INFO] Refreshing screenshot...
[[09:02:53]] [INFO] Qy0Y0uJchm=pass
[[09:02:49]] [SUCCESS] Screenshot refreshed successfully
[[09:02:49]] [SUCCESS] Screenshot refreshed successfully
[[09:02:49]] [INFO] Qy0Y0uJchm=running
[[09:02:49]] [INFO] Executing action 493/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[09:02:48]] [SUCCESS] Screenshot refreshed
[[09:02:48]] [INFO] Refreshing screenshot...
[[09:02:48]] [INFO] YHaMIjULRf=pass
[[09:02:43]] [SUCCESS] Screenshot refreshed successfully
[[09:02:43]] [SUCCESS] Screenshot refreshed successfully
[[09:02:42]] [INFO] YHaMIjULRf=running
[[09:02:42]] [INFO] Executing action 492/576: Tap on Text: "List"
[[09:02:41]] [SUCCESS] Screenshot refreshed
[[09:02:41]] [INFO] Refreshing screenshot...
[[09:02:41]] [INFO] igReeDqips=pass
[[09:02:37]] [SUCCESS] Screenshot refreshed successfully
[[09:02:37]] [SUCCESS] Screenshot refreshed successfully
[[09:02:36]] [INFO] igReeDqips=running
[[09:02:36]] [INFO] Executing action 491/576: Tap on image: env[catalogue-menu-img]
[[09:02:36]] [SUCCESS] Screenshot refreshed
[[09:02:36]] [INFO] Refreshing screenshot...
[[09:02:36]] [INFO] Xqj9EIVE7g=pass
[[09:02:14]] [SUCCESS] Screenshot refreshed successfully
[[09:02:14]] [SUCCESS] Screenshot refreshed successfully
[[09:02:13]] [INFO] Xqj9EIVE7g=running
[[09:02:13]] [INFO] Executing action 490/576: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[09:02:12]] [SUCCESS] Screenshot refreshed
[[09:02:12]] [INFO] Refreshing screenshot...
[[09:02:12]] [INFO] gkkQzTCmma=pass
[[09:02:06]] [SUCCESS] Screenshot refreshed successfully
[[09:02:06]] [SUCCESS] Screenshot refreshed successfully
[[09:02:06]] [INFO] gkkQzTCmma=running
[[09:02:06]] [INFO] Executing action 489/576: Tap on Text: "Catalogue"
[[09:02:06]] [SUCCESS] Screenshot refreshed
[[09:02:06]] [INFO] Refreshing screenshot...
[[09:02:06]] [INFO] UpUSVInizv=pass
[[09:02:02]] [SUCCESS] Screenshot refreshed successfully
[[09:02:02]] [SUCCESS] Screenshot refreshed successfully
[[09:02:01]] [INFO] UpUSVInizv=running
[[09:02:01]] [INFO] Executing action 488/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[09:02:01]] [SUCCESS] Screenshot refreshed
[[09:02:01]] [INFO] Refreshing screenshot...
[[09:02:01]] [INFO] Cmvm82hiAa=pass
[[09:01:53]] [SUCCESS] Screenshot refreshed successfully
[[09:01:53]] [SUCCESS] Screenshot refreshed successfully
[[09:01:53]] [INFO] Cmvm82hiAa=running
[[09:01:53]] [INFO] Executing action 487/576: Tap on element with accessibility_id: Add to bag
[[09:01:53]] [SUCCESS] Screenshot refreshed
[[09:01:53]] [INFO] Refreshing screenshot...
[[09:01:53]] [INFO] JcAR0JctQ6=pass
[[09:01:49]] [SUCCESS] Screenshot refreshed successfully
[[09:01:49]] [SUCCESS] Screenshot refreshed successfully
[[09:01:49]] [INFO] JcAR0JctQ6=running
[[09:01:49]] [INFO] Executing action 486/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[09:01:48]] [SUCCESS] Screenshot refreshed
[[09:01:48]] [INFO] Refreshing screenshot...
[[09:01:48]] [INFO] Pd7cReoJM6=pass
[[09:01:43]] [SUCCESS] Screenshot refreshed successfully
[[09:01:43]] [SUCCESS] Screenshot refreshed successfully
[[09:01:42]] [INFO] Pd7cReoJM6=running
[[09:01:42]] [INFO] Executing action 485/576: Tap on Text: "List"
[[09:01:41]] [SUCCESS] Screenshot refreshed
[[09:01:41]] [INFO] Refreshing screenshot...
[[09:01:41]] [INFO] igReeDqips=pass
[[09:01:37]] [SUCCESS] Screenshot refreshed successfully
[[09:01:37]] [SUCCESS] Screenshot refreshed successfully
[[09:01:36]] [INFO] igReeDqips=running
[[09:01:36]] [INFO] Executing action 484/576: Tap on image: env[catalogue-menu-img]
[[09:01:36]] [SUCCESS] Screenshot refreshed
[[09:01:36]] [INFO] Refreshing screenshot...
[[09:01:36]] [INFO] Xqj9EIVE7g=pass
[[09:01:13]] [SUCCESS] Screenshot refreshed successfully
[[09:01:13]] [SUCCESS] Screenshot refreshed successfully
[[09:01:11]] [INFO] Xqj9EIVE7g=running
[[09:01:11]] [INFO] Executing action 483/576: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[09:01:11]] [SUCCESS] Screenshot refreshed
[[09:01:11]] [INFO] Refreshing screenshot...
[[09:01:11]] [INFO] gkkQzTCmma=pass
[[09:01:05]] [SUCCESS] Screenshot refreshed successfully
[[09:01:05]] [SUCCESS] Screenshot refreshed successfully
[[09:01:05]] [INFO] gkkQzTCmma=running
[[09:01:05]] [INFO] Executing action 482/576: Tap on Text: "Catalogue"
[[09:01:05]] [SUCCESS] Screenshot refreshed
[[09:01:05]] [INFO] Refreshing screenshot...
[[09:01:05]] [INFO] UpUSVInizv=pass
[[09:01:00]] [SUCCESS] Screenshot refreshed successfully
[[09:01:00]] [SUCCESS] Screenshot refreshed successfully
[[09:01:00]] [INFO] UpUSVInizv=running
[[09:01:00]] [INFO] Executing action 481/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[09:00:59]] [SUCCESS] Screenshot refreshed
[[09:00:59]] [INFO] Refreshing screenshot...
[[09:00:59]] [INFO] 0QtNHB5WEK=pass
[[09:00:56]] [SUCCESS] Screenshot refreshed successfully
[[09:00:56]] [SUCCESS] Screenshot refreshed successfully
[[09:00:56]] [INFO] 0QtNHB5WEK=running
[[09:00:56]] [INFO] Executing action 480/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[09:00:55]] [SUCCESS] Screenshot refreshed
[[09:00:55]] [INFO] Refreshing screenshot...
[[09:00:55]] [INFO] fTdGMJ3NH3=pass
[[09:00:52]] [SUCCESS] Screenshot refreshed successfully
[[09:00:52]] [SUCCESS] Screenshot refreshed successfully
[[09:00:52]] [INFO] fTdGMJ3NH3=running
[[09:00:52]] [INFO] Executing action 479/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[09:00:52]] [SUCCESS] Screenshot refreshed
[[09:00:52]] [INFO] Refreshing screenshot...
[[09:00:52]] [INFO] rYJcLPh8Aq=pass
[[09:00:48]] [INFO] rYJcLPh8Aq=running
[[09:00:48]] [INFO] Executing action 478/576: iOS Function: text - Text: "kmart au"
[[09:00:48]] [SUCCESS] Screenshot refreshed successfully
[[09:00:48]] [SUCCESS] Screenshot refreshed successfully
[[09:00:47]] [SUCCESS] Screenshot refreshed
[[09:00:47]] [INFO] Refreshing screenshot...
[[09:00:47]] [INFO] 0Q0fm6OTij=pass
[[09:00:44]] [SUCCESS] Screenshot refreshed successfully
[[09:00:44]] [SUCCESS] Screenshot refreshed successfully
[[09:00:44]] [INFO] 0Q0fm6OTij=running
[[09:00:44]] [INFO] Executing action 477/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[09:00:43]] [SUCCESS] Screenshot refreshed
[[09:00:43]] [INFO] Refreshing screenshot...
[[09:00:43]] [INFO] xVuuejtCFA=pass
[[09:00:40]] [SUCCESS] Screenshot refreshed successfully
[[09:00:40]] [SUCCESS] Screenshot refreshed successfully
[[09:00:39]] [INFO] xVuuejtCFA=running
[[09:00:39]] [INFO] Executing action 476/576: Restart app: com.apple.mobilesafari
[[09:00:39]] [SUCCESS] Screenshot refreshed
[[09:00:39]] [INFO] Refreshing screenshot...
[[09:00:39]] [INFO] LcYLwUffqj=pass
[[09:00:34]] [SUCCESS] Screenshot refreshed successfully
[[09:00:34]] [SUCCESS] Screenshot refreshed successfully
[[09:00:34]] [INFO] LcYLwUffqj=running
[[09:00:34]] [INFO] Executing action 475/576: Tap on Text: "out"
[[09:00:33]] [SUCCESS] Screenshot refreshed
[[09:00:33]] [INFO] Refreshing screenshot...
[[09:00:33]] [INFO] ZZPNqTJ65s=pass
[[09:00:29]] [SUCCESS] Screenshot refreshed successfully
[[09:00:29]] [SUCCESS] Screenshot refreshed successfully
[[09:00:29]] [INFO] ZZPNqTJ65s=running
[[09:00:29]] [INFO] Executing action 474/576: Swipe from (50%, 70%) to (50%, 30%)
[[09:00:28]] [SUCCESS] Screenshot refreshed
[[09:00:28]] [INFO] Refreshing screenshot...
[[09:00:28]] [INFO] UpUSVInizv=pass
[[09:00:25]] [SUCCESS] Screenshot refreshed successfully
[[09:00:25]] [SUCCESS] Screenshot refreshed successfully
[[09:00:24]] [INFO] UpUSVInizv=running
[[09:00:24]] [INFO] Executing action 473/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[09:00:24]] [SUCCESS] Screenshot refreshed
[[09:00:24]] [INFO] Refreshing screenshot...
[[09:00:24]] [INFO] hCCEvRtj1A=pass
[[09:00:19]] [INFO] hCCEvRtj1A=running
[[09:00:19]] [INFO] Executing action 472/576: Restart app: env[appid]
[[09:00:18]] [SUCCESS] Screenshot refreshed successfully
[[09:00:18]] [SUCCESS] Screenshot refreshed successfully
[[09:00:18]] [SUCCESS] Screenshot refreshed
[[09:00:18]] [INFO] Refreshing screenshot...
[[09:00:18]] [INFO] V42eHtTRYW=pass
[[09:00:11]] [INFO] V42eHtTRYW=running
[[09:00:11]] [INFO] Executing action 471/576: Wait for 5 ms
[[09:00:11]] [SUCCESS] Screenshot refreshed successfully
[[09:00:11]] [SUCCESS] Screenshot refreshed successfully
[[09:00:11]] [SUCCESS] Screenshot refreshed
[[09:00:11]] [INFO] Refreshing screenshot...
[[09:00:11]] [INFO] GRwHMVK4sA=pass
[[09:00:09]] [INFO] GRwHMVK4sA=running
[[09:00:09]] [INFO] Executing action 470/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[09:00:09]] [SUCCESS] Screenshot refreshed successfully
[[09:00:09]] [SUCCESS] Screenshot refreshed successfully
[[09:00:08]] [SUCCESS] Screenshot refreshed
[[09:00:08]] [INFO] Refreshing screenshot...
[[09:00:08]] [INFO] V42eHtTRYW=pass
[[09:00:02]] [INFO] V42eHtTRYW=running
[[09:00:02]] [INFO] Executing action 469/576: Wait for 5 ms
[[09:00:01]] [SUCCESS] Screenshot refreshed successfully
[[09:00:01]] [SUCCESS] Screenshot refreshed successfully
[[09:00:01]] [SUCCESS] Screenshot refreshed
[[09:00:01]] [INFO] Refreshing screenshot...
[[09:00:01]] [INFO] LfyQctrEJn=pass
[[08:59:59]] [SUCCESS] Screenshot refreshed successfully
[[08:59:59]] [SUCCESS] Screenshot refreshed successfully
[[08:59:59]] [INFO] LfyQctrEJn=running
[[08:59:59]] [INFO] Executing action 468/576: Launch app: com.apple.Preferences
[[08:59:59]] [SUCCESS] Screenshot refreshed
[[08:59:59]] [INFO] Refreshing screenshot...
[[08:59:59]] [INFO] seQcUKjkSU=pass
[[08:59:57]] [SUCCESS] Screenshot refreshed successfully
[[08:59:57]] [SUCCESS] Screenshot refreshed successfully
[[08:59:57]] [INFO] seQcUKjkSU=running
[[08:59:57]] [INFO] Executing action 467/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[08:59:56]] [SUCCESS] Screenshot refreshed
[[08:59:56]] [INFO] Refreshing screenshot...
[[08:59:56]] [INFO] UpUSVInizv=pass
[[08:59:54]] [SUCCESS] Screenshot refreshed successfully
[[08:59:54]] [SUCCESS] Screenshot refreshed successfully
[[08:59:54]] [INFO] UpUSVInizv=running
[[08:59:54]] [INFO] Executing action 466/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[08:59:54]] [SUCCESS] Screenshot refreshed
[[08:59:54]] [INFO] Refreshing screenshot...
[[08:59:54]] [INFO] WoymrHdtrO=pass
[[08:59:52]] [SUCCESS] Screenshot refreshed successfully
[[08:59:52]] [SUCCESS] Screenshot refreshed successfully
[[08:59:52]] [INFO] WoymrHdtrO=running
[[08:59:52]] [INFO] Executing action 465/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[08:59:51]] [SUCCESS] Screenshot refreshed
[[08:59:51]] [INFO] Refreshing screenshot...
[[08:59:51]] [INFO] 6xgrAWyfZ4=pass
[[08:59:49]] [SUCCESS] Screenshot refreshed successfully
[[08:59:49]] [SUCCESS] Screenshot refreshed successfully
[[08:59:49]] [INFO] 6xgrAWyfZ4=running
[[08:59:49]] [INFO] Executing action 464/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[08:59:48]] [SUCCESS] Screenshot refreshed
[[08:59:48]] [INFO] Refreshing screenshot...
[[08:59:48]] [INFO] eSr9EFlJek=pass
[[08:59:46]] [SUCCESS] Screenshot refreshed successfully
[[08:59:46]] [SUCCESS] Screenshot refreshed successfully
[[08:59:46]] [INFO] eSr9EFlJek=running
[[08:59:46]] [INFO] Executing action 463/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[08:59:46]] [SUCCESS] Screenshot refreshed
[[08:59:46]] [INFO] Refreshing screenshot...
[[08:59:46]] [INFO] 3KNqlNy6Bj=pass
[[08:59:43]] [SUCCESS] Screenshot refreshed successfully
[[08:59:43]] [SUCCESS] Screenshot refreshed successfully
[[08:59:43]] [INFO] 3KNqlNy6Bj=running
[[08:59:43]] [INFO] Executing action 462/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[08:59:43]] [SUCCESS] Screenshot refreshed
[[08:59:43]] [INFO] Refreshing screenshot...
[[08:59:43]] [INFO] cokvFXhj4c=pass
[[08:59:40]] [SUCCESS] Screenshot refreshed successfully
[[08:59:40]] [SUCCESS] Screenshot refreshed successfully
[[08:59:40]] [INFO] cokvFXhj4c=running
[[08:59:40]] [INFO] Executing action 461/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[08:59:40]] [SUCCESS] Screenshot refreshed
[[08:59:40]] [INFO] Refreshing screenshot...
[[08:59:40]] [INFO] oSQ8sPdVOJ=pass
[[08:59:35]] [INFO] oSQ8sPdVOJ=running
[[08:59:35]] [INFO] Executing action 460/576: Restart app: env[appid]
[[08:59:34]] [SUCCESS] Screenshot refreshed successfully
[[08:59:34]] [SUCCESS] Screenshot refreshed successfully
[[08:59:34]] [SUCCESS] Screenshot refreshed
[[08:59:34]] [INFO] Refreshing screenshot...
[[08:59:34]] [INFO] V42eHtTRYW=pass
[[08:59:28]] [INFO] V42eHtTRYW=running
[[08:59:28]] [INFO] Executing action 459/576: Wait for 5 ms
[[08:59:27]] [SUCCESS] Screenshot refreshed successfully
[[08:59:27]] [SUCCESS] Screenshot refreshed successfully
[[08:59:27]] [SUCCESS] Screenshot refreshed
[[08:59:27]] [INFO] Refreshing screenshot...
[[08:59:27]] [INFO] jUCAk6GJc4=pass
[[08:59:25]] [INFO] jUCAk6GJc4=running
[[08:59:25]] [INFO] Executing action 458/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[08:59:24]] [SUCCESS] Screenshot refreshed successfully
[[08:59:24]] [SUCCESS] Screenshot refreshed successfully
[[08:59:24]] [SUCCESS] Screenshot refreshed
[[08:59:24]] [INFO] Refreshing screenshot...
[[08:59:24]] [INFO] V42eHtTRYW=pass
[[08:59:17]] [INFO] V42eHtTRYW=running
[[08:59:17]] [INFO] Executing action 457/576: Wait for 5 ms
[[08:59:17]] [SUCCESS] Screenshot refreshed successfully
[[08:59:17]] [SUCCESS] Screenshot refreshed successfully
[[08:59:17]] [SUCCESS] Screenshot refreshed
[[08:59:17]] [INFO] Refreshing screenshot...
[[08:59:17]] [INFO] w1RV76df9x=pass
[[08:59:12]] [INFO] w1RV76df9x=running
[[08:59:12]] [INFO] Executing action 456/576: Tap on Text: "Wi-Fi"
[[08:59:12]] [SUCCESS] Screenshot refreshed successfully
[[08:59:12]] [SUCCESS] Screenshot refreshed successfully
[[08:59:12]] [SUCCESS] Screenshot refreshed
[[08:59:12]] [INFO] Refreshing screenshot...
[[08:59:12]] [INFO] LfyQctrEJn=pass
[[08:59:10]] [SUCCESS] Screenshot refreshed successfully
[[08:59:10]] [SUCCESS] Screenshot refreshed successfully
[[08:59:09]] [INFO] LfyQctrEJn=running
[[08:59:09]] [INFO] Executing action 455/576: Launch app: com.apple.Preferences
[[08:59:09]] [SUCCESS] Screenshot refreshed
[[08:59:09]] [INFO] Refreshing screenshot...
[[08:59:09]] [INFO] mIKA85kXaW=pass
[[08:59:07]] [SUCCESS] Screenshot refreshed successfully
[[08:59:07]] [SUCCESS] Screenshot refreshed successfully
[[08:59:06]] [INFO] mIKA85kXaW=running
[[08:59:06]] [INFO] Executing action 454/576: Terminate app: com.apple.Preferences
[[08:59:06]] [SUCCESS] Screenshot refreshed
[[08:59:06]] [INFO] Refreshing screenshot...
[[08:59:06]] [SUCCESS] Screenshot refreshed
[[08:59:06]] [INFO] Refreshing screenshot...
[[08:59:00]] [SUCCESS] Screenshot refreshed successfully
[[08:59:00]] [SUCCESS] Screenshot refreshed successfully
[[08:59:00]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:59:00]] [SUCCESS] Screenshot refreshed
[[08:59:00]] [INFO] Refreshing screenshot...
[[08:58:56]] [SUCCESS] Screenshot refreshed successfully
[[08:58:56]] [SUCCESS] Screenshot refreshed successfully
[[08:58:55]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:58:55]] [SUCCESS] Screenshot refreshed
[[08:58:55]] [INFO] Refreshing screenshot...
[[08:58:50]] [SUCCESS] Screenshot refreshed successfully
[[08:58:50]] [SUCCESS] Screenshot refreshed successfully
[[08:58:50]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:58:50]] [SUCCESS] Screenshot refreshed
[[08:58:50]] [INFO] Refreshing screenshot...
[[08:58:46]] [SUCCESS] Screenshot refreshed successfully
[[08:58:46]] [SUCCESS] Screenshot refreshed successfully
[[08:58:45]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:58:45]] [SUCCESS] Screenshot refreshed
[[08:58:45]] [INFO] Refreshing screenshot...
[[08:58:39]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:58:39]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:58:39]] [SUCCESS] Screenshot refreshed successfully
[[08:58:39]] [SUCCESS] Screenshot refreshed successfully
[[08:58:39]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:58:39]] [INFO] x6vffndoRV=running
[[08:58:39]] [INFO] Executing action 453/576: Execute Test Case: Kmart-Signin (6 steps)
[[08:58:39]] [SUCCESS] Screenshot refreshed
[[08:58:39]] [INFO] Refreshing screenshot...
[[08:58:39]] [INFO] rJ86z4njuR=pass
[[08:58:36]] [SUCCESS] Screenshot refreshed successfully
[[08:58:36]] [SUCCESS] Screenshot refreshed successfully
[[08:58:36]] [INFO] rJ86z4njuR=running
[[08:58:36]] [INFO] Executing action 452/576: iOS Function: alert_accept
[[08:58:35]] [SUCCESS] Screenshot refreshed
[[08:58:35]] [INFO] Refreshing screenshot...
[[08:58:35]] [INFO] veukWo4573=pass
[[08:58:31]] [SUCCESS] Screenshot refreshed successfully
[[08:58:31]] [SUCCESS] Screenshot refreshed successfully
[[08:58:30]] [INFO] veukWo4573=running
[[08:58:30]] [INFO] Executing action 451/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[08:58:30]] [SUCCESS] Screenshot refreshed
[[08:58:30]] [INFO] Refreshing screenshot...
[[08:58:30]] [INFO] XEbZHdi0GT=pass
[[08:58:17]] [SUCCESS] Screenshot refreshed successfully
[[08:58:17]] [SUCCESS] Screenshot refreshed successfully
[[08:58:15]] [INFO] XEbZHdi0GT=running
[[08:58:15]] [INFO] Executing action 450/576: Restart app: env[appid]
[[08:58:15]] [SUCCESS] Screenshot refreshed
[[08:58:15]] [INFO] Refreshing screenshot...
[[08:58:15]] [SUCCESS] Screenshot refreshed
[[08:58:15]] [INFO] Refreshing screenshot...
[[08:58:12]] [SUCCESS] Screenshot refreshed successfully
[[08:58:12]] [SUCCESS] Screenshot refreshed successfully
[[08:58:12]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:58:11]] [SUCCESS] Screenshot refreshed
[[08:58:11]] [INFO] Refreshing screenshot...
[[08:57:59]] [SUCCESS] Screenshot refreshed successfully
[[08:57:59]] [SUCCESS] Screenshot refreshed successfully
[[08:57:59]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:57:59]] [SUCCESS] Screenshot refreshed
[[08:57:59]] [INFO] Refreshing screenshot...
[[08:57:55]] [SUCCESS] Screenshot refreshed successfully
[[08:57:55]] [SUCCESS] Screenshot refreshed successfully
[[08:57:55]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:57:54]] [SUCCESS] Screenshot refreshed
[[08:57:54]] [INFO] Refreshing screenshot...
[[08:57:51]] [SUCCESS] Screenshot refreshed successfully
[[08:57:51]] [SUCCESS] Screenshot refreshed successfully
[[08:57:50]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:57:50]] [SUCCESS] Screenshot refreshed
[[08:57:50]] [INFO] Refreshing screenshot...
[[08:57:43]] [SUCCESS] Screenshot refreshed successfully
[[08:57:43]] [SUCCESS] Screenshot refreshed successfully
[[08:57:43]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:57:42]] [SUCCESS] Screenshot refreshed
[[08:57:42]] [INFO] Refreshing screenshot...
[[08:57:36]] [SUCCESS] Screenshot refreshed successfully
[[08:57:36]] [SUCCESS] Screenshot refreshed successfully
[[08:57:35]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:57:35]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:57:35]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:57:35]] [INFO] ubySifeF65=running
[[08:57:35]] [INFO] Executing action 449/576: cleanupSteps action
[[08:57:34]] [SUCCESS] Screenshot refreshed
[[08:57:34]] [INFO] Refreshing screenshot...
[[08:57:34]] [INFO] xyHVihJMBi=pass
[[08:57:29]] [SUCCESS] Screenshot refreshed successfully
[[08:57:29]] [SUCCESS] Screenshot refreshed successfully
[[08:57:29]] [INFO] xyHVihJMBi=running
[[08:57:29]] [INFO] Executing action 448/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:57:29]] [SUCCESS] Screenshot refreshed
[[08:57:29]] [INFO] Refreshing screenshot...
[[08:57:29]] [INFO] mWeLQtXiL6=pass
[[08:57:22]] [SUCCESS] Screenshot refreshed successfully
[[08:57:22]] [SUCCESS] Screenshot refreshed successfully
[[08:57:22]] [INFO] mWeLQtXiL6=running
[[08:57:22]] [INFO] Executing action 447/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:57:21]] [SUCCESS] Screenshot refreshed
[[08:57:21]] [INFO] Refreshing screenshot...
[[08:57:21]] [INFO] F4NGh9HrLw=pass
[[08:57:17]] [SUCCESS] Screenshot refreshed successfully
[[08:57:17]] [SUCCESS] Screenshot refreshed successfully
[[08:57:17]] [INFO] F4NGh9HrLw=running
[[08:57:17]] [INFO] Executing action 446/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:57:16]] [SUCCESS] Screenshot refreshed
[[08:57:16]] [INFO] Refreshing screenshot...
[[08:57:16]] [INFO] 0f2FSZYjWq=pass
[[08:56:59]] [SUCCESS] Screenshot refreshed successfully
[[08:56:59]] [SUCCESS] Screenshot refreshed successfully
[[08:56:59]] [INFO] 0f2FSZYjWq=running
[[08:56:59]] [INFO] Executing action 445/576: Check if element with text="Melbourne" exists
[[08:56:58]] [SUCCESS] Screenshot refreshed
[[08:56:58]] [INFO] Refreshing screenshot...
[[08:56:58]] [INFO] Tebej51pT2=pass
[[08:56:54]] [SUCCESS] Screenshot refreshed successfully
[[08:56:54]] [SUCCESS] Screenshot refreshed successfully
[[08:56:54]] [INFO] Tebej51pT2=running
[[08:56:54]] [INFO] Executing action 444/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[08:56:53]] [SUCCESS] Screenshot refreshed
[[08:56:53]] [INFO] Refreshing screenshot...
[[08:56:53]] [INFO] I4gwigwXSj=pass
[[08:56:50]] [SUCCESS] Screenshot refreshed successfully
[[08:56:50]] [SUCCESS] Screenshot refreshed successfully
[[08:56:50]] [INFO] I4gwigwXSj=running
[[08:56:50]] [INFO] Executing action 443/576: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[08:56:49]] [SUCCESS] Screenshot refreshed
[[08:56:49]] [INFO] Refreshing screenshot...
[[08:56:49]] [INFO] eVytJrry9x=pass
[[08:56:45]] [SUCCESS] Screenshot refreshed successfully
[[08:56:45]] [SUCCESS] Screenshot refreshed successfully
[[08:56:45]] [INFO] eVytJrry9x=running
[[08:56:45]] [INFO] Executing action 442/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:56:44]] [SUCCESS] Screenshot refreshed
[[08:56:44]] [INFO] Refreshing screenshot...
[[08:56:44]] [INFO] s8h8VDUIOC=pass
[[08:56:40]] [SUCCESS] Screenshot refreshed successfully
[[08:56:40]] [SUCCESS] Screenshot refreshed successfully
[[08:56:40]] [INFO] s8h8VDUIOC=running
[[08:56:40]] [INFO] Executing action 441/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:56:39]] [SUCCESS] Screenshot refreshed
[[08:56:39]] [INFO] Refreshing screenshot...
[[08:56:39]] [INFO] bkU728TrRF=pass
[[08:56:33]] [SUCCESS] Screenshot refreshed successfully
[[08:56:33]] [SUCCESS] Screenshot refreshed successfully
[[08:56:33]] [INFO] bkU728TrRF=running
[[08:56:33]] [INFO] Executing action 440/576: Tap on element with accessibility_id: Done
[[08:56:32]] [SUCCESS] Screenshot refreshed
[[08:56:32]] [INFO] Refreshing screenshot...
[[08:56:32]] [INFO] ZWpYNcpbFA=pass
[[08:56:27]] [SUCCESS] Screenshot refreshed successfully
[[08:56:27]] [SUCCESS] Screenshot refreshed successfully
[[08:56:27]] [INFO] ZWpYNcpbFA=running
[[08:56:27]] [INFO] Executing action 439/576: Tap on Text: "VIC"
[[08:56:27]] [SUCCESS] Screenshot refreshed
[[08:56:27]] [INFO] Refreshing screenshot...
[[08:56:27]] [INFO] Wld5Urg70o=pass
[[08:56:20]] [SUCCESS] Screenshot refreshed successfully
[[08:56:20]] [SUCCESS] Screenshot refreshed successfully
[[08:56:19]] [INFO] Wld5Urg70o=running
[[08:56:19]] [INFO] Executing action 438/576: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[08:56:19]] [SUCCESS] Screenshot refreshed
[[08:56:19]] [INFO] Refreshing screenshot...
[[08:56:19]] [INFO] QpBLC6BStn=pass
[[08:56:13]] [SUCCESS] Screenshot refreshed successfully
[[08:56:13]] [SUCCESS] Screenshot refreshed successfully
[[08:56:12]] [INFO] QpBLC6BStn=running
[[08:56:12]] [INFO] Executing action 437/576: Tap on element with accessibility_id: delete
[[08:56:12]] [SUCCESS] Screenshot refreshed
[[08:56:12]] [INFO] Refreshing screenshot...
[[08:56:12]] [INFO] G4A3KBlXHq=pass
[[08:56:07]] [SUCCESS] Screenshot refreshed successfully
[[08:56:07]] [SUCCESS] Screenshot refreshed successfully
[[08:56:07]] [INFO] G4A3KBlXHq=running
[[08:56:07]] [INFO] Executing action 436/576: Tap on Text: "Nearby"
[[08:56:06]] [SUCCESS] Screenshot refreshed
[[08:56:06]] [INFO] Refreshing screenshot...
[[08:56:06]] [INFO] uArzgeZYf7=pass
[[08:56:03]] [SUCCESS] Screenshot refreshed successfully
[[08:56:03]] [SUCCESS] Screenshot refreshed successfully
[[08:56:03]] [INFO] uArzgeZYf7=running
[[08:56:03]] [INFO] Executing action 435/576: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[08:56:02]] [SUCCESS] Screenshot refreshed
[[08:56:02]] [INFO] Refreshing screenshot...
[[08:56:02]] [INFO] 3gJsiap2Ds=pass
[[08:55:58]] [SUCCESS] Screenshot refreshed successfully
[[08:55:58]] [SUCCESS] Screenshot refreshed successfully
[[08:55:58]] [INFO] 3gJsiap2Ds=running
[[08:55:58]] [INFO] Executing action 434/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[08:55:57]] [SUCCESS] Screenshot refreshed
[[08:55:57]] [INFO] Refreshing screenshot...
[[08:55:57]] [INFO] dF3hpprg71=pass
[[08:55:55]] [SUCCESS] Screenshot refreshed successfully
[[08:55:55]] [SUCCESS] Screenshot refreshed successfully
[[08:55:54]] [INFO] dF3hpprg71=running
[[08:55:54]] [INFO] Executing action 433/576: Wait till xpath=//XCUIElementTypeOther[@name="Delivery options"]/XCUIElementTypeButton[3]
[[08:55:53]] [SUCCESS] Screenshot refreshed
[[08:55:53]] [INFO] Refreshing screenshot...
[[08:55:53]] [INFO] 94ikwhIEE2=pass
[[08:55:49]] [SUCCESS] Screenshot refreshed successfully
[[08:55:49]] [SUCCESS] Screenshot refreshed successfully
[[08:55:49]] [INFO] 94ikwhIEE2=running
[[08:55:49]] [INFO] Executing action 432/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:55:48]] [SUCCESS] Screenshot refreshed
[[08:55:48]] [INFO] Refreshing screenshot...
[[08:55:48]] [INFO] q8oldD8uZt=pass
[[08:55:45]] [SUCCESS] Screenshot refreshed successfully
[[08:55:45]] [SUCCESS] Screenshot refreshed successfully
[[08:55:44]] [INFO] q8oldD8uZt=running
[[08:55:44]] [INFO] Executing action 431/576: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[08:55:44]] [SUCCESS] Screenshot refreshed
[[08:55:44]] [INFO] Refreshing screenshot...
[[08:55:44]] [INFO] Jf2wJyOphY=pass
[[08:55:37]] [SUCCESS] Screenshot refreshed successfully
[[08:55:37]] [SUCCESS] Screenshot refreshed successfully
[[08:55:36]] [INFO] Jf2wJyOphY=running
[[08:55:36]] [INFO] Executing action 430/576: Tap on element with accessibility_id: Add to bag
[[08:55:36]] [SUCCESS] Screenshot refreshed
[[08:55:36]] [INFO] Refreshing screenshot...
[[08:55:36]] [INFO] eRCmRhc3re=pass
[[08:55:21]] [INFO] eRCmRhc3re=running
[[08:55:21]] [INFO] Executing action 429/576: Check if element with text="Broadway" exists
[[08:55:21]] [SUCCESS] Screenshot refreshed successfully
[[08:55:21]] [SUCCESS] Screenshot refreshed successfully
[[08:55:20]] [SUCCESS] Screenshot refreshed
[[08:55:20]] [INFO] Refreshing screenshot...
[[08:55:20]] [INFO] ORI6ZFMBK1=pass
[[08:55:15]] [SUCCESS] Screenshot refreshed successfully
[[08:55:15]] [SUCCESS] Screenshot refreshed successfully
[[08:55:15]] [INFO] ORI6ZFMBK1=running
[[08:55:15]] [INFO] Executing action 428/576: Tap on Text: "Save"
[[08:55:15]] [SUCCESS] Screenshot refreshed
[[08:55:15]] [INFO] Refreshing screenshot...
[[08:55:15]] [INFO] hr0IVckpYI=pass
[[08:55:10]] [SUCCESS] Screenshot refreshed successfully
[[08:55:10]] [SUCCESS] Screenshot refreshed successfully
[[08:55:10]] [INFO] hr0IVckpYI=running
[[08:55:10]] [INFO] Executing action 427/576: Wait till accessibility_id=btnSaveOrContinue
[[08:55:10]] [SUCCESS] Screenshot refreshed
[[08:55:10]] [INFO] Refreshing screenshot...
[[08:55:10]] [INFO] H0ODFz7sWJ=pass
[[08:55:05]] [SUCCESS] Screenshot refreshed successfully
[[08:55:05]] [SUCCESS] Screenshot refreshed successfully
[[08:55:05]] [INFO] H0ODFz7sWJ=running
[[08:55:05]] [INFO] Executing action 426/576: Tap on Text: "2000"
[[08:55:04]] [SUCCESS] Screenshot refreshed
[[08:55:04]] [INFO] Refreshing screenshot...
[[08:55:04]] [INFO] uZHvvAzVfx=pass
[[08:54:59]] [SUCCESS] Screenshot refreshed successfully
[[08:54:59]] [SUCCESS] Screenshot refreshed successfully
[[08:54:59]] [INFO] uZHvvAzVfx=running
[[08:54:59]] [INFO] Executing action 425/576: textClear action
[[08:54:58]] [SUCCESS] Screenshot refreshed
[[08:54:58]] [INFO] Refreshing screenshot...
[[08:54:58]] [INFO] WmNWcsWVHv=pass
[[08:54:52]] [SUCCESS] Screenshot refreshed successfully
[[08:54:52]] [SUCCESS] Screenshot refreshed successfully
[[08:54:52]] [INFO] WmNWcsWVHv=running
[[08:54:52]] [INFO] Executing action 424/576: Tap on element with accessibility_id: Search suburb or postcode
[[08:54:51]] [SUCCESS] Screenshot refreshed
[[08:54:51]] [INFO] Refreshing screenshot...
[[08:54:51]] [INFO] lnjoz8hHUU=pass
[[08:54:46]] [SUCCESS] Screenshot refreshed successfully
[[08:54:46]] [SUCCESS] Screenshot refreshed successfully
[[08:54:46]] [INFO] lnjoz8hHUU=running
[[08:54:46]] [INFO] Executing action 423/576: Tap on Text: "Edit"
[[08:54:45]] [SUCCESS] Screenshot refreshed
[[08:54:45]] [INFO] Refreshing screenshot...
[[08:54:45]] [INFO] letbbewlnA=pass
[[08:54:41]] [SUCCESS] Screenshot refreshed successfully
[[08:54:41]] [SUCCESS] Screenshot refreshed successfully
[[08:54:41]] [INFO] letbbewlnA=running
[[08:54:41]] [INFO] Executing action 422/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:54:40]] [SUCCESS] Screenshot refreshed
[[08:54:40]] [INFO] Refreshing screenshot...
[[08:54:40]] [INFO] trBISwJ8eZ=pass
[[08:54:36]] [SUCCESS] Screenshot refreshed successfully
[[08:54:36]] [SUCCESS] Screenshot refreshed successfully
[[08:54:36]] [INFO] trBISwJ8eZ=running
[[08:54:36]] [INFO] Executing action 421/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:54:35]] [SUCCESS] Screenshot refreshed
[[08:54:35]] [INFO] Refreshing screenshot...
[[08:54:35]] [INFO] foVGMl9wvu=pass
[[08:54:33]] [SUCCESS] Screenshot refreshed successfully
[[08:54:33]] [SUCCESS] Screenshot refreshed successfully
[[08:54:31]] [INFO] foVGMl9wvu=running
[[08:54:31]] [INFO] Executing action 420/576: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:54:30]] [SUCCESS] Screenshot refreshed
[[08:54:30]] [INFO] Refreshing screenshot...
[[08:54:30]] [INFO] 73NABkfWyY=pass
[[08:54:14]] [SUCCESS] Screenshot refreshed successfully
[[08:54:14]] [SUCCESS] Screenshot refreshed successfully
[[08:54:14]] [INFO] 73NABkfWyY=running
[[08:54:14]] [INFO] Executing action 419/576: Check if element with text="Sanctuary" exists
[[08:54:14]] [SUCCESS] Screenshot refreshed
[[08:54:14]] [INFO] Refreshing screenshot...
[[08:54:14]] [INFO] pKjXoj4mNg=pass
[[08:54:08]] [SUCCESS] Screenshot refreshed successfully
[[08:54:08]] [SUCCESS] Screenshot refreshed successfully
[[08:54:08]] [INFO] pKjXoj4mNg=running
[[08:54:08]] [INFO] Executing action 418/576: Tap on Text: "Save"
[[08:54:08]] [SUCCESS] Screenshot refreshed
[[08:54:08]] [INFO] Refreshing screenshot...
[[08:54:08]] [INFO] M3dXqigqRv=pass
[[08:54:03]] [SUCCESS] Screenshot refreshed successfully
[[08:54:03]] [SUCCESS] Screenshot refreshed successfully
[[08:54:03]] [INFO] M3dXqigqRv=running
[[08:54:03]] [INFO] Executing action 417/576: Wait till accessibility_id=btnSaveOrContinue
[[08:54:02]] [SUCCESS] Screenshot refreshed
[[08:54:02]] [INFO] Refreshing screenshot...
[[08:54:02]] [INFO] GYRHQr7TWx=pass
[[08:53:58]] [SUCCESS] Screenshot refreshed successfully
[[08:53:58]] [SUCCESS] Screenshot refreshed successfully
[[08:53:58]] [INFO] GYRHQr7TWx=running
[[08:53:58]] [INFO] Executing action 416/576: Tap on Text: "current"
[[08:53:57]] [SUCCESS] Screenshot refreshed
[[08:53:57]] [INFO] Refreshing screenshot...
[[08:53:57]] [INFO] kiM0WyWE9I=pass
[[08:53:52]] [SUCCESS] Screenshot refreshed successfully
[[08:53:52]] [SUCCESS] Screenshot refreshed successfully
[[08:53:52]] [INFO] kiM0WyWE9I=running
[[08:53:52]] [INFO] Executing action 415/576: Wait till accessibility_id=btnCurrentLocationButton
[[08:53:51]] [SUCCESS] Screenshot refreshed
[[08:53:51]] [INFO] Refreshing screenshot...
[[08:53:51]] [INFO] VkUKQbf1Qt=pass
[[08:53:46]] [SUCCESS] Screenshot refreshed successfully
[[08:53:46]] [SUCCESS] Screenshot refreshed successfully
[[08:53:46]] [INFO] VkUKQbf1Qt=running
[[08:53:46]] [INFO] Executing action 414/576: Tap on Text: "Edit"
[[08:53:45]] [SUCCESS] Screenshot refreshed
[[08:53:45]] [INFO] Refreshing screenshot...
[[08:53:45]] [INFO] C6JHhLdWTv=pass
[[08:53:42]] [SUCCESS] Screenshot refreshed successfully
[[08:53:42]] [SUCCESS] Screenshot refreshed successfully
[[08:53:41]] [INFO] C6JHhLdWTv=running
[[08:53:41]] [INFO] Executing action 413/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:53:41]] [SUCCESS] Screenshot refreshed
[[08:53:41]] [INFO] Refreshing screenshot...
[[08:53:41]] [INFO] IupxLP2Jsr=pass
[[08:53:36]] [SUCCESS] Screenshot refreshed successfully
[[08:53:36]] [SUCCESS] Screenshot refreshed successfully
[[08:53:36]] [INFO] IupxLP2Jsr=running
[[08:53:36]] [INFO] Executing action 412/576: iOS Function: text - Text: "Uno card"
[[08:53:36]] [SUCCESS] Screenshot refreshed
[[08:53:36]] [INFO] Refreshing screenshot...
[[08:53:36]] [INFO] 70iOOakiG7=pass
[[08:53:30]] [SUCCESS] Screenshot refreshed successfully
[[08:53:30]] [SUCCESS] Screenshot refreshed successfully
[[08:53:30]] [INFO] 70iOOakiG7=running
[[08:53:30]] [INFO] Executing action 411/576: Tap on Text: "Find"
[[08:53:29]] [SUCCESS] Screenshot refreshed
[[08:53:29]] [INFO] Refreshing screenshot...
[[08:53:29]] [INFO] Xqj9EIVEfg=pass
[[08:53:21]] [SUCCESS] Screenshot refreshed successfully
[[08:53:21]] [SUCCESS] Screenshot refreshed successfully
[[08:53:21]] [INFO] Xqj9EIVEfg=running
[[08:53:21]] [INFO] Executing action 410/576: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[08:53:20]] [SUCCESS] Screenshot refreshed
[[08:53:20]] [INFO] Refreshing screenshot...
[[08:53:20]] [INFO] E2jpN7BioW=pass
[[08:53:16]] [SUCCESS] Screenshot refreshed successfully
[[08:53:16]] [SUCCESS] Screenshot refreshed successfully
[[08:53:15]] [INFO] E2jpN7BioW=running
[[08:53:15]] [INFO] Executing action 409/576: Tap on Text: "Save"
[[08:53:15]] [SUCCESS] Screenshot refreshed
[[08:53:15]] [INFO] Refreshing screenshot...
[[08:53:15]] [INFO] Sl6eiqZkRm=pass
[[08:53:10]] [SUCCESS] Screenshot refreshed successfully
[[08:53:10]] [SUCCESS] Screenshot refreshed successfully
[[08:53:10]] [INFO] Sl6eiqZkRm=running
[[08:53:10]] [INFO] Executing action 408/576: Wait till accessibility_id=btnSaveOrContinue
[[08:53:10]] [SUCCESS] Screenshot refreshed
[[08:53:10]] [INFO] Refreshing screenshot...
[[08:53:10]] [INFO] mw9GQ4mzRE=pass
[[08:53:05]] [SUCCESS] Screenshot refreshed successfully
[[08:53:05]] [SUCCESS] Screenshot refreshed successfully
[[08:53:05]] [INFO] mw9GQ4mzRE=running
[[08:53:05]] [INFO] Executing action 407/576: Tap on Text: "2000"
[[08:53:04]] [SUCCESS] Screenshot refreshed
[[08:53:04]] [INFO] Refreshing screenshot...
[[08:53:04]] [INFO] kbdEPCPYod=pass
[[08:52:59]] [SUCCESS] Screenshot refreshed successfully
[[08:52:59]] [SUCCESS] Screenshot refreshed successfully
[[08:52:59]] [INFO] kbdEPCPYod=running
[[08:52:59]] [INFO] Executing action 406/576: textClear action
[[08:52:58]] [SUCCESS] Screenshot refreshed
[[08:52:58]] [INFO] Refreshing screenshot...
[[08:52:58]] [INFO] 8WCusTZ8q9=pass
[[08:52:52]] [SUCCESS] Screenshot refreshed successfully
[[08:52:52]] [SUCCESS] Screenshot refreshed successfully
[[08:52:52]] [INFO] 8WCusTZ8q9=running
[[08:52:52]] [INFO] Executing action 405/576: Tap on element with accessibility_id: Search suburb or postcode
[[08:52:51]] [SUCCESS] Screenshot refreshed
[[08:52:51]] [INFO] Refreshing screenshot...
[[08:52:51]] [INFO] QMXBlswP6H=pass
[[08:52:48]] [SUCCESS] Screenshot refreshed successfully
[[08:52:48]] [SUCCESS] Screenshot refreshed successfully
[[08:52:47]] [INFO] QMXBlswP6H=running
[[08:52:47]] [INFO] Executing action 404/576: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[08:52:47]] [SUCCESS] Screenshot refreshed
[[08:52:47]] [INFO] Refreshing screenshot...
[[08:52:47]] [INFO] m0956RsrdM=pass
[[08:52:45]] [SUCCESS] Screenshot refreshed successfully
[[08:52:45]] [SUCCESS] Screenshot refreshed successfully
[[08:52:43]] [INFO] m0956RsrdM=running
[[08:52:43]] [INFO] Executing action 403/576: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[08:52:43]] [SUCCESS] Screenshot refreshed
[[08:52:43]] [INFO] Refreshing screenshot...
[[08:52:42]] [SUCCESS] Screenshot refreshed
[[08:52:42]] [INFO] Refreshing screenshot...
[[08:52:38]] [SUCCESS] Screenshot refreshed successfully
[[08:52:38]] [SUCCESS] Screenshot refreshed successfully
[[08:52:37]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:52:37]] [SUCCESS] Screenshot refreshed
[[08:52:37]] [INFO] Refreshing screenshot...
[[08:52:33]] [SUCCESS] Screenshot refreshed successfully
[[08:52:33]] [SUCCESS] Screenshot refreshed successfully
[[08:52:33]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:52:32]] [SUCCESS] Screenshot refreshed
[[08:52:32]] [INFO] Refreshing screenshot...
[[08:52:28]] [SUCCESS] Screenshot refreshed successfully
[[08:52:28]] [SUCCESS] Screenshot refreshed successfully
[[08:52:27]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:52:27]] [SUCCESS] Screenshot refreshed
[[08:52:27]] [INFO] Refreshing screenshot...
[[08:52:22]] [SUCCESS] Screenshot refreshed successfully
[[08:52:22]] [SUCCESS] Screenshot refreshed successfully
[[08:52:22]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:52:22]] [SUCCESS] Screenshot refreshed
[[08:52:22]] [INFO] Refreshing screenshot...
[[08:52:16]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:52:16]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:52:16]] [SUCCESS] Screenshot refreshed successfully
[[08:52:16]] [SUCCESS] Screenshot refreshed successfully
[[08:52:16]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:52:16]] [INFO] C3UHsKxa5P=running
[[08:52:16]] [INFO] Executing action 402/576: Execute Test Case: Kmart-Signin (6 steps)
[[08:52:15]] [SUCCESS] Screenshot refreshed
[[08:52:15]] [INFO] Refreshing screenshot...
[[08:52:15]] [INFO] Azb1flbIJJ=pass
[[08:52:11]] [SUCCESS] Screenshot refreshed successfully
[[08:52:11]] [SUCCESS] Screenshot refreshed successfully
[[08:52:11]] [INFO] Azb1flbIJJ=running
[[08:52:11]] [INFO] Executing action 401/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:52:11]] [SUCCESS] Screenshot refreshed
[[08:52:11]] [INFO] Refreshing screenshot...
[[08:52:11]] [INFO] 2xC5fLfLe8=pass
[[08:52:08]] [SUCCESS] Screenshot refreshed successfully
[[08:52:08]] [SUCCESS] Screenshot refreshed successfully
[[08:52:08]] [INFO] 2xC5fLfLe8=running
[[08:52:08]] [INFO] Executing action 400/576: iOS Function: alert_accept
[[08:52:07]] [SUCCESS] Screenshot refreshed
[[08:52:07]] [INFO] Refreshing screenshot...
[[08:52:07]] [INFO] Y8vz7AJD1i=pass
[[08:52:01]] [SUCCESS] Screenshot refreshed successfully
[[08:52:01]] [SUCCESS] Screenshot refreshed successfully
[[08:52:00]] [INFO] Y8vz7AJD1i=running
[[08:52:00]] [INFO] Executing action 399/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:51:59]] [SUCCESS] Screenshot refreshed
[[08:51:59]] [INFO] Refreshing screenshot...
[[08:51:59]] [INFO] H9fy9qcFbZ=pass
[[08:51:46]] [SUCCESS] Screenshot refreshed successfully
[[08:51:46]] [SUCCESS] Screenshot refreshed successfully
[[08:51:45]] [INFO] H9fy9qcFbZ=running
[[08:51:45]] [INFO] Executing action 398/576: Restart app: env[appid]
[[08:51:45]] [SUCCESS] Screenshot refreshed
[[08:51:45]] [INFO] Refreshing screenshot...
[[08:51:45]] [SUCCESS] Screenshot refreshed
[[08:51:45]] [INFO] Refreshing screenshot...
[[08:51:42]] [SUCCESS] Screenshot refreshed successfully
[[08:51:42]] [SUCCESS] Screenshot refreshed successfully
[[08:51:41]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:51:41]] [SUCCESS] Screenshot refreshed
[[08:51:41]] [INFO] Refreshing screenshot...
[[08:51:28]] [SUCCESS] Screenshot refreshed successfully
[[08:51:28]] [SUCCESS] Screenshot refreshed successfully
[[08:51:28]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:51:28]] [SUCCESS] Screenshot refreshed
[[08:51:28]] [INFO] Refreshing screenshot...
[[08:51:24]] [SUCCESS] Screenshot refreshed successfully
[[08:51:24]] [SUCCESS] Screenshot refreshed successfully
[[08:51:24]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:51:24]] [SUCCESS] Screenshot refreshed
[[08:51:24]] [INFO] Refreshing screenshot...
[[08:51:20]] [SUCCESS] Screenshot refreshed successfully
[[08:51:20]] [SUCCESS] Screenshot refreshed successfully
[[08:51:20]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:51:19]] [SUCCESS] Screenshot refreshed
[[08:51:19]] [INFO] Refreshing screenshot...
[[08:51:13]] [SUCCESS] Screenshot refreshed successfully
[[08:51:13]] [SUCCESS] Screenshot refreshed successfully
[[08:51:12]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:51:12]] [SUCCESS] Screenshot refreshed
[[08:51:12]] [INFO] Refreshing screenshot...
[[08:51:06]] [SUCCESS] Screenshot refreshed successfully
[[08:51:06]] [SUCCESS] Screenshot refreshed successfully
[[08:51:05]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:51:05]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:51:05]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:51:05]] [INFO] OMgc2gHHyq=running
[[08:51:05]] [INFO] Executing action 397/576: cleanupSteps action
[[08:51:05]] [SUCCESS] Screenshot refreshed
[[08:51:05]] [INFO] Refreshing screenshot...
[[08:51:05]] [INFO] x4yLCZHaCR=pass
[[08:51:02]] [SUCCESS] Screenshot refreshed successfully
[[08:51:02]] [SUCCESS] Screenshot refreshed successfully
[[08:51:01]] [INFO] x4yLCZHaCR=running
[[08:51:01]] [INFO] Executing action 396/576: Terminate app: env[appid]
[[08:51:01]] [SUCCESS] Screenshot refreshed
[[08:51:01]] [INFO] Refreshing screenshot...
[[08:51:01]] [INFO] 2p13JoJbbA=pass
[[08:50:56]] [SUCCESS] Screenshot refreshed successfully
[[08:50:56]] [SUCCESS] Screenshot refreshed successfully
[[08:50:56]] [INFO] 2p13JoJbbA=running
[[08:50:56]] [INFO] Executing action 395/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:50:56]] [SUCCESS] Screenshot refreshed
[[08:50:56]] [INFO] Refreshing screenshot...
[[08:50:56]] [INFO] qHdMgerbTE=pass
[[08:50:52]] [SUCCESS] Screenshot refreshed successfully
[[08:50:52]] [SUCCESS] Screenshot refreshed successfully
[[08:50:52]] [INFO] qHdMgerbTE=running
[[08:50:52]] [INFO] Executing action 394/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:50:51]] [SUCCESS] Screenshot refreshed
[[08:50:51]] [INFO] Refreshing screenshot...
[[08:50:51]] [INFO] F4NGh9HrLw=pass
[[08:50:49]] [SUCCESS] Screenshot refreshed successfully
[[08:50:49]] [SUCCESS] Screenshot refreshed successfully
[[08:50:46]] [INFO] F4NGh9HrLw=running
[[08:50:46]] [INFO] Executing action 393/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:50:46]] [SUCCESS] Screenshot refreshed
[[08:50:46]] [INFO] Refreshing screenshot...
[[08:50:46]] [SUCCESS] Screenshot refreshed
[[08:50:46]] [INFO] Refreshing screenshot...
[[08:50:42]] [SUCCESS] Screenshot refreshed successfully
[[08:50:42]] [SUCCESS] Screenshot refreshed successfully
[[08:50:42]] [INFO] Executing Multi Step action step 41/41: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[08:50:41]] [SUCCESS] Screenshot refreshed
[[08:50:41]] [INFO] Refreshing screenshot...
[[08:50:37]] [SUCCESS] Screenshot refreshed successfully
[[08:50:37]] [SUCCESS] Screenshot refreshed successfully
[[08:50:37]] [INFO] Executing Multi Step action step 40/41: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:50:37]] [SUCCESS] Screenshot refreshed
[[08:50:37]] [INFO] Refreshing screenshot...
[[08:50:30]] [SUCCESS] Screenshot refreshed successfully
[[08:50:30]] [SUCCESS] Screenshot refreshed successfully
[[08:50:29]] [INFO] Executing Multi Step action step 39/41: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[08:50:29]] [SUCCESS] Screenshot refreshed
[[08:50:29]] [INFO] Refreshing screenshot...
[[08:50:25]] [SUCCESS] Screenshot refreshed successfully
[[08:50:25]] [SUCCESS] Screenshot refreshed successfully
[[08:50:25]] [INFO] Executing Multi Step action step 38/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:50:25]] [SUCCESS] Screenshot refreshed
[[08:50:25]] [INFO] Refreshing screenshot...
[[08:50:21]] [SUCCESS] Screenshot refreshed successfully
[[08:50:21]] [SUCCESS] Screenshot refreshed successfully
[[08:50:20]] [INFO] Executing Multi Step action step 37/41: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:50:20]] [SUCCESS] Screenshot refreshed
[[08:50:20]] [INFO] Refreshing screenshot...
[[08:50:15]] [SUCCESS] Screenshot refreshed successfully
[[08:50:15]] [SUCCESS] Screenshot refreshed successfully
[[08:50:15]] [INFO] Executing Multi Step action step 36/41: Tap on image: banner-close-updated.png
[[08:50:15]] [SUCCESS] Screenshot refreshed
[[08:50:15]] [INFO] Refreshing screenshot...
[[08:50:12]] [INFO] Executing Multi Step action step 35/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[08:50:12]] [SUCCESS] Screenshot refreshed successfully
[[08:50:12]] [SUCCESS] Screenshot refreshed successfully
[[08:50:11]] [SUCCESS] Screenshot refreshed
[[08:50:11]] [INFO] Refreshing screenshot...
[[08:50:07]] [SUCCESS] Screenshot refreshed successfully
[[08:50:07]] [SUCCESS] Screenshot refreshed successfully
[[08:50:07]] [INFO] Executing Multi Step action step 34/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[08:50:07]] [SUCCESS] Screenshot refreshed
[[08:50:07]] [INFO] Refreshing screenshot...
[[08:50:03]] [SUCCESS] Screenshot refreshed successfully
[[08:50:03]] [SUCCESS] Screenshot refreshed successfully
[[08:50:02]] [INFO] Executing Multi Step action step 33/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[08:50:02]] [SUCCESS] Screenshot refreshed
[[08:50:02]] [INFO] Refreshing screenshot...
[[08:49:57]] [SUCCESS] Screenshot refreshed successfully
[[08:49:57]] [SUCCESS] Screenshot refreshed successfully
[[08:49:57]] [INFO] Executing Multi Step action step 32/41: Tap on image: banner-close-updated.png
[[08:49:57]] [SUCCESS] Screenshot refreshed
[[08:49:57]] [INFO] Refreshing screenshot...
[[08:49:51]] [INFO] Executing Multi Step action step 31/41: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[08:49:51]] [SUCCESS] Screenshot refreshed successfully
[[08:49:51]] [SUCCESS] Screenshot refreshed successfully
[[08:49:51]] [SUCCESS] Screenshot refreshed
[[08:49:51]] [INFO] Refreshing screenshot...
[[08:49:47]] [SUCCESS] Screenshot refreshed successfully
[[08:49:47]] [SUCCESS] Screenshot refreshed successfully
[[08:49:47]] [INFO] Executing Multi Step action step 30/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[08:49:46]] [SUCCESS] Screenshot refreshed
[[08:49:46]] [INFO] Refreshing screenshot...
[[08:49:42]] [SUCCESS] Screenshot refreshed successfully
[[08:49:42]] [SUCCESS] Screenshot refreshed successfully
[[08:49:42]] [INFO] Executing Multi Step action step 29/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[08:49:42]] [SUCCESS] Screenshot refreshed
[[08:49:42]] [INFO] Refreshing screenshot...
[[08:49:37]] [SUCCESS] Screenshot refreshed successfully
[[08:49:37]] [SUCCESS] Screenshot refreshed successfully
[[08:49:37]] [INFO] Executing Multi Step action step 28/41: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[08:49:37]] [SUCCESS] Screenshot refreshed
[[08:49:37]] [INFO] Refreshing screenshot...
[[08:49:34]] [SUCCESS] Screenshot refreshed successfully
[[08:49:34]] [SUCCESS] Screenshot refreshed successfully
[[08:49:33]] [INFO] Executing Multi Step action step 27/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[08:49:33]] [SUCCESS] Screenshot refreshed
[[08:49:33]] [INFO] Refreshing screenshot...
[[08:49:29]] [SUCCESS] Screenshot refreshed successfully
[[08:49:29]] [SUCCESS] Screenshot refreshed successfully
[[08:49:29]] [INFO] Executing Multi Step action step 26/41: Tap on element with xpath: //XCUIElementTypeLink[@name="Pay in 4"]
[[08:49:28]] [SUCCESS] Screenshot refreshed
[[08:49:28]] [INFO] Refreshing screenshot...
[[08:49:26]] [SUCCESS] Screenshot refreshed successfully
[[08:49:26]] [SUCCESS] Screenshot refreshed successfully
[[08:49:24]] [INFO] Executing Multi Step action step 25/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[08:49:23]] [SUCCESS] Screenshot refreshed
[[08:49:23]] [INFO] Refreshing screenshot...
[[08:49:19]] [SUCCESS] Screenshot refreshed successfully
[[08:49:19]] [SUCCESS] Screenshot refreshed successfully
[[08:49:19]] [INFO] Executing Multi Step action step 24/41: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[08:49:19]] [SUCCESS] Screenshot refreshed
[[08:49:19]] [INFO] Refreshing screenshot...
[[08:49:15]] [SUCCESS] Screenshot refreshed successfully
[[08:49:15]] [SUCCESS] Screenshot refreshed successfully
[[08:49:15]] [INFO] Executing Multi Step action step 23/41: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[08:49:15]] [SUCCESS] Screenshot refreshed
[[08:49:15]] [INFO] Refreshing screenshot...
[[08:49:11]] [SUCCESS] Screenshot refreshed successfully
[[08:49:11]] [SUCCESS] Screenshot refreshed successfully
[[08:49:11]] [INFO] Executing Multi Step action step 22/41: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[08:49:10]] [SUCCESS] Screenshot refreshed
[[08:49:10]] [INFO] Refreshing screenshot...
[[08:49:02]] [SUCCESS] Screenshot refreshed successfully
[[08:49:02]] [SUCCESS] Screenshot refreshed successfully
[[08:49:02]] [INFO] Executing Multi Step action step 21/41: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[08:49:02]] [SUCCESS] Screenshot refreshed
[[08:49:02]] [INFO] Refreshing screenshot...
[[08:48:57]] [SUCCESS] Screenshot refreshed successfully
[[08:48:57]] [SUCCESS] Screenshot refreshed successfully
[[08:48:57]] [INFO] Executing Multi Step action step 20/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[08:48:56]] [SUCCESS] Screenshot refreshed
[[08:48:56]] [INFO] Refreshing screenshot...
[[08:48:52]] [SUCCESS] Screenshot refreshed successfully
[[08:48:52]] [SUCCESS] Screenshot refreshed successfully
[[08:48:52]] [INFO] Executing Multi Step action step 19/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[08:48:51]] [SUCCESS] Screenshot refreshed
[[08:48:51]] [INFO] Refreshing screenshot...
[[08:48:43]] [SUCCESS] Screenshot refreshed successfully
[[08:48:43]] [SUCCESS] Screenshot refreshed successfully
[[08:48:43]] [INFO] Executing Multi Step action step 18/41: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[08:48:43]] [SUCCESS] Screenshot refreshed
[[08:48:43]] [INFO] Refreshing screenshot...
[[08:48:39]] [SUCCESS] Screenshot refreshed successfully
[[08:48:39]] [SUCCESS] Screenshot refreshed successfully
[[08:48:39]] [INFO] Executing Multi Step action step 17/41: Tap on image: env[delivery-address-img]
[[08:48:38]] [SUCCESS] Screenshot refreshed
[[08:48:38]] [INFO] Refreshing screenshot...
[[08:48:34]] [SUCCESS] Screenshot refreshed successfully
[[08:48:34]] [SUCCESS] Screenshot refreshed successfully
[[08:48:34]] [INFO] Executing Multi Step action step 16/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[08:48:33]] [SUCCESS] Screenshot refreshed
[[08:48:33]] [INFO] Refreshing screenshot...
[[08:48:26]] [SUCCESS] Screenshot refreshed successfully
[[08:48:26]] [SUCCESS] Screenshot refreshed successfully
[[08:48:26]] [INFO] Executing Multi Step action step 15/41: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "env[deliver-address]"
[[08:48:25]] [SUCCESS] Screenshot refreshed
[[08:48:25]] [INFO] Refreshing screenshot...
[[08:48:20]] [SUCCESS] Screenshot refreshed successfully
[[08:48:20]] [SUCCESS] Screenshot refreshed successfully
[[08:48:20]] [INFO] Executing Multi Step action step 14/41: Tap on Text: "address"
[[08:48:19]] [SUCCESS] Screenshot refreshed
[[08:48:19]] [INFO] Refreshing screenshot...
[[08:48:15]] [SUCCESS] Screenshot refreshed successfully
[[08:48:15]] [SUCCESS] Screenshot refreshed successfully
[[08:48:15]] [INFO] Executing Multi Step action step 13/41: iOS Function: text - Text: " "
[[08:48:15]] [SUCCESS] Screenshot refreshed
[[08:48:15]] [INFO] Refreshing screenshot...
[[08:48:08]] [SUCCESS] Screenshot refreshed successfully
[[08:48:08]] [SUCCESS] Screenshot refreshed successfully
[[08:48:08]] [INFO] Executing Multi Step action step 12/41: textClear action
[[08:48:07]] [SUCCESS] Screenshot refreshed
[[08:48:07]] [INFO] Refreshing screenshot...
[[08:48:03]] [SUCCESS] Screenshot refreshed successfully
[[08:48:03]] [SUCCESS] Screenshot refreshed successfully
[[08:48:03]] [INFO] Executing Multi Step action step 11/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[08:48:03]] [SUCCESS] Screenshot refreshed
[[08:48:03]] [INFO] Refreshing screenshot...
[[08:47:56]] [SUCCESS] Screenshot refreshed successfully
[[08:47:56]] [SUCCESS] Screenshot refreshed successfully
[[08:47:56]] [INFO] Executing Multi Step action step 10/41: textClear action
[[08:47:55]] [SUCCESS] Screenshot refreshed
[[08:47:55]] [INFO] Refreshing screenshot...
[[08:47:51]] [SUCCESS] Screenshot refreshed successfully
[[08:47:51]] [SUCCESS] Screenshot refreshed successfully
[[08:47:51]] [INFO] Executing Multi Step action step 9/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:47:51]] [SUCCESS] Screenshot refreshed
[[08:47:51]] [INFO] Refreshing screenshot...
[[08:47:44]] [SUCCESS] Screenshot refreshed successfully
[[08:47:44]] [SUCCESS] Screenshot refreshed successfully
[[08:47:44]] [INFO] Executing Multi Step action step 8/41: textClear action
[[08:47:44]] [SUCCESS] Screenshot refreshed
[[08:47:44]] [INFO] Refreshing screenshot...
[[08:47:40]] [SUCCESS] Screenshot refreshed successfully
[[08:47:40]] [SUCCESS] Screenshot refreshed successfully
[[08:47:40]] [INFO] Executing Multi Step action step 7/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[08:47:39]] [SUCCESS] Screenshot refreshed
[[08:47:39]] [INFO] Refreshing screenshot...
[[08:47:32]] [SUCCESS] Screenshot refreshed successfully
[[08:47:32]] [SUCCESS] Screenshot refreshed successfully
[[08:47:32]] [INFO] Executing Multi Step action step 6/41: textClear action
[[08:47:32]] [SUCCESS] Screenshot refreshed
[[08:47:32]] [INFO] Refreshing screenshot...
[[08:47:28]] [SUCCESS] Screenshot refreshed successfully
[[08:47:28]] [SUCCESS] Screenshot refreshed successfully
[[08:47:28]] [INFO] Executing Multi Step action step 5/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[08:47:27]] [SUCCESS] Screenshot refreshed
[[08:47:27]] [INFO] Refreshing screenshot...
[[08:47:23]] [SUCCESS] Screenshot refreshed successfully
[[08:47:23]] [SUCCESS] Screenshot refreshed successfully
[[08:47:22]] [INFO] Executing Multi Step action step 4/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[08:47:22]] [SUCCESS] Screenshot refreshed
[[08:47:22]] [INFO] Refreshing screenshot...
[[08:47:02]] [SUCCESS] Screenshot refreshed successfully
[[08:47:02]] [SUCCESS] Screenshot refreshed successfully
[[08:47:02]] [INFO] Executing Multi Step action step 3/41: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[08:47:02]] [SUCCESS] Screenshot refreshed
[[08:47:02]] [INFO] Refreshing screenshot...
[[08:46:58]] [SUCCESS] Screenshot refreshed successfully
[[08:46:58]] [SUCCESS] Screenshot refreshed successfully
[[08:46:57]] [INFO] Executing Multi Step action step 2/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:46:57]] [SUCCESS] Screenshot refreshed
[[08:46:57]] [INFO] Refreshing screenshot...
[[08:46:50]] [INFO] Executing Multi Step action step 1/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:46:50]] [INFO] Loaded 41 steps from test case: Delivery Buy Steps
[[08:46:50]] [INFO] Loading steps for multiStep action: Delivery Buy Steps
[[08:46:50]] [INFO] ZxObWodIp8=running
[[08:46:50]] [INFO] Executing action 392/576: Execute Test Case: Delivery Buy Steps (41 steps)
[[08:46:50]] [SUCCESS] Screenshot refreshed successfully
[[08:46:50]] [SUCCESS] Screenshot refreshed successfully
[[08:46:50]] [SUCCESS] Screenshot refreshed
[[08:46:50]] [INFO] Refreshing screenshot...
[[08:46:50]] [INFO] F4NGh9HrLw=pass
[[08:46:46]] [SUCCESS] Screenshot refreshed successfully
[[08:46:46]] [SUCCESS] Screenshot refreshed successfully
[[08:46:45]] [INFO] F4NGh9HrLw=running
[[08:46:45]] [INFO] Executing action 391/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:46:45]] [SUCCESS] Screenshot refreshed
[[08:46:45]] [INFO] Refreshing screenshot...
[[08:46:45]] [INFO] 4eEEGs1x8i=pass
[[08:46:33]] [SUCCESS] Screenshot refreshed successfully
[[08:46:33]] [SUCCESS] Screenshot refreshed successfully
[[08:46:33]] [INFO] 4eEEGs1x8i=running
[[08:46:33]] [INFO] Executing action 390/576: If exists: xpath="//XCUIElementTypeButton[@name="Save my location"]" (timeout: 10s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Save my location"]
[[08:46:32]] [SUCCESS] Screenshot refreshed
[[08:46:32]] [INFO] Refreshing screenshot...
[[08:46:32]] [INFO] O8XvoFFGEB=pass
[[08:46:27]] [SUCCESS] Screenshot refreshed successfully
[[08:46:27]] [SUCCESS] Screenshot refreshed successfully
[[08:46:27]] [INFO] O8XvoFFGEB=running
[[08:46:27]] [INFO] Executing action 389/576: Tap on image: env[atg-pdp]
[[08:46:26]] [SUCCESS] Screenshot refreshed
[[08:46:26]] [INFO] Refreshing screenshot...
[[08:46:26]] [INFO] CcFsA41sKp=pass
[[08:46:22]] [SUCCESS] Screenshot refreshed successfully
[[08:46:22]] [SUCCESS] Screenshot refreshed successfully
[[08:46:21]] [INFO] CcFsA41sKp=running
[[08:46:21]] [INFO] Executing action 388/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:46:21]] [SUCCESS] Screenshot refreshed
[[08:46:21]] [INFO] Refreshing screenshot...
[[08:46:21]] [INFO] 8XWyF2kgwW=pass
[[08:46:18]] [SUCCESS] Screenshot refreshed successfully
[[08:46:18]] [SUCCESS] Screenshot refreshed successfully
[[08:46:17]] [INFO] 8XWyF2kgwW=running
[[08:46:17]] [INFO] Executing action 387/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:46:17]] [SUCCESS] Screenshot refreshed
[[08:46:17]] [INFO] Refreshing screenshot...
[[08:46:17]] [INFO] qG4RkNac30=pass
[[08:46:12]] [SUCCESS] Screenshot refreshed successfully
[[08:46:12]] [SUCCESS] Screenshot refreshed successfully
[[08:46:12]] [INFO] qG4RkNac30=running
[[08:46:12]] [INFO] Executing action 386/576: iOS Function: text - Text: "P_42691341"
[[08:46:12]] [SUCCESS] Screenshot refreshed
[[08:46:12]] [INFO] Refreshing screenshot...
[[08:46:12]] [INFO] Jtn2FK4THX=pass
[[08:46:07]] [SUCCESS] Screenshot refreshed successfully
[[08:46:07]] [SUCCESS] Screenshot refreshed successfully
[[08:46:06]] [INFO] Jtn2FK4THX=running
[[08:46:06]] [INFO] Executing action 385/576: Tap on Text: "Find"
[[08:46:06]] [SUCCESS] Screenshot refreshed
[[08:46:06]] [INFO] Refreshing screenshot...
[[08:46:06]] [INFO] tWq2Qzn22D=pass
[[08:46:02]] [SUCCESS] Screenshot refreshed successfully
[[08:46:02]] [SUCCESS] Screenshot refreshed successfully
[[08:46:01]] [INFO] tWq2Qzn22D=running
[[08:46:01]] [INFO] Executing action 384/576: Tap on image: env[device-back-img]
[[08:46:00]] [SUCCESS] Screenshot refreshed
[[08:46:00]] [INFO] Refreshing screenshot...
[[08:46:00]] [INFO] 5hClb2pKKx=pass
[[08:45:38]] [SUCCESS] Screenshot refreshed successfully
[[08:45:38]] [SUCCESS] Screenshot refreshed successfully
[[08:45:38]] [INFO] 5hClb2pKKx=running
[[08:45:38]] [INFO] Executing action 383/576: If exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="btnUpdate"]
[[08:45:37]] [SUCCESS] Screenshot refreshed
[[08:45:37]] [INFO] Refreshing screenshot...
[[08:45:37]] [INFO] jmKjclMUWT=pass
[[08:45:32]] [SUCCESS] Screenshot refreshed successfully
[[08:45:32]] [SUCCESS] Screenshot refreshed successfully
[[08:45:32]] [INFO] jmKjclMUWT=running
[[08:45:32]] [INFO] Executing action 382/576: Tap on Text: "current"
[[08:45:32]] [SUCCESS] Screenshot refreshed
[[08:45:32]] [INFO] Refreshing screenshot...
[[08:45:32]] [INFO] UoH0wdtcLk=pass
[[08:45:26]] [SUCCESS] Screenshot refreshed successfully
[[08:45:26]] [SUCCESS] Screenshot refreshed successfully
[[08:45:26]] [INFO] UoH0wdtcLk=running
[[08:45:26]] [INFO] Executing action 381/576: Tap on Text: "Edit"
[[08:45:25]] [SUCCESS] Screenshot refreshed
[[08:45:25]] [INFO] Refreshing screenshot...
[[08:45:25]] [INFO] U48qCNydwd=pass
[[08:45:20]] [SUCCESS] Screenshot refreshed successfully
[[08:45:20]] [SUCCESS] Screenshot refreshed successfully
[[08:45:20]] [INFO] U48qCNydwd=running
[[08:45:20]] [INFO] Executing action 380/576: Restart app: env[appid]
[[08:45:20]] [SUCCESS] Screenshot refreshed
[[08:45:20]] [INFO] Refreshing screenshot...
[[08:45:20]] [INFO] XjclKOaCTh=pass
[[08:45:15]] [SUCCESS] Screenshot refreshed successfully
[[08:45:15]] [SUCCESS] Screenshot refreshed successfully
[[08:45:15]] [INFO] XjclKOaCTh=running
[[08:45:15]] [INFO] Executing action 379/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[08:45:14]] [SUCCESS] Screenshot refreshed
[[08:45:14]] [INFO] Refreshing screenshot...
[[08:45:14]] [INFO] q6cKxgMAIn=pass
[[08:45:11]] [SUCCESS] Screenshot refreshed successfully
[[08:45:11]] [SUCCESS] Screenshot refreshed successfully
[[08:45:11]] [INFO] q6cKxgMAIn=running
[[08:45:11]] [INFO] Executing action 378/576: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[08:45:10]] [SUCCESS] Screenshot refreshed
[[08:45:10]] [INFO] Refreshing screenshot...
[[08:45:10]] [INFO] zdh8hKYC1a=pass
[[08:45:06]] [SUCCESS] Screenshot refreshed successfully
[[08:45:06]] [SUCCESS] Screenshot refreshed successfully
[[08:45:06]] [INFO] zdh8hKYC1a=running
[[08:45:06]] [INFO] Executing action 377/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[08:45:06]] [SUCCESS] Screenshot refreshed
[[08:45:06]] [INFO] Refreshing screenshot...
[[08:45:06]] [INFO] P4b2BITpCf=pass
[[08:45:03]] [SUCCESS] Screenshot refreshed successfully
[[08:45:03]] [SUCCESS] Screenshot refreshed successfully
[[08:45:03]] [INFO] P4b2BITpCf=running
[[08:45:03]] [INFO] Executing action 376/576: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[08:45:02]] [SUCCESS] Screenshot refreshed
[[08:45:02]] [INFO] Refreshing screenshot...
[[08:45:02]] [INFO] inrxgdWzXr=pass
[[08:44:57]] [SUCCESS] Screenshot refreshed successfully
[[08:44:57]] [SUCCESS] Screenshot refreshed successfully
[[08:44:57]] [INFO] inrxgdWzXr=running
[[08:44:57]] [INFO] Executing action 375/576: Tap on Text: "Store"
[[08:44:57]] [SUCCESS] Screenshot refreshed
[[08:44:57]] [INFO] Refreshing screenshot...
[[08:44:57]] [INFO] inrxgdWzXr=pass
[[08:44:52]] [SUCCESS] Screenshot refreshed successfully
[[08:44:52]] [SUCCESS] Screenshot refreshed successfully
[[08:44:52]] [INFO] inrxgdWzXr=running
[[08:44:52]] [INFO] Executing action 374/576: Tap on Text: "receipts"
[[08:44:52]] [SUCCESS] Screenshot refreshed
[[08:44:52]] [INFO] Refreshing screenshot...
[[08:44:52]] [INFO] GEMv6goQtW=pass
[[08:44:48]] [SUCCESS] Screenshot refreshed successfully
[[08:44:48]] [SUCCESS] Screenshot refreshed successfully
[[08:44:48]] [INFO] GEMv6goQtW=running
[[08:44:48]] [INFO] Executing action 373/576: Tap on image: env[device-back-img]
[[08:44:47]] [SUCCESS] Screenshot refreshed
[[08:44:47]] [INFO] Refreshing screenshot...
[[08:44:47]] [INFO] DhWa2PCBXE=pass
[[08:44:44]] [SUCCESS] Screenshot refreshed successfully
[[08:44:44]] [SUCCESS] Screenshot refreshed successfully
[[08:44:44]] [INFO] DhWa2PCBXE=running
[[08:44:44]] [INFO] Executing action 372/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[08:44:44]] [SUCCESS] Screenshot refreshed
[[08:44:44]] [INFO] Refreshing screenshot...
[[08:44:44]] [INFO] pk2DLZFBmx=pass
[[08:44:39]] [SUCCESS] Screenshot refreshed successfully
[[08:44:39]] [SUCCESS] Screenshot refreshed successfully
[[08:44:39]] [INFO] pk2DLZFBmx=running
[[08:44:39]] [INFO] Executing action 371/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[08:44:39]] [SUCCESS] Screenshot refreshed
[[08:44:39]] [INFO] Refreshing screenshot...
[[08:44:39]] [INFO] ShJSdXvmVL=pass
[[08:44:36]] [SUCCESS] Screenshot refreshed successfully
[[08:44:36]] [SUCCESS] Screenshot refreshed successfully
[[08:44:36]] [INFO] ShJSdXvmVL=running
[[08:44:36]] [INFO] Executing action 370/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[08:44:35]] [SUCCESS] Screenshot refreshed
[[08:44:35]] [INFO] Refreshing screenshot...
[[08:44:35]] [INFO] kWPRvuo7kk=pass
[[08:44:30]] [SUCCESS] Screenshot refreshed successfully
[[08:44:30]] [SUCCESS] Screenshot refreshed successfully
[[08:44:30]] [INFO] kWPRvuo7kk=running
[[08:44:30]] [INFO] Executing action 369/576: iOS Function: text - Text: "env[pwd-op]"
[[08:44:29]] [SUCCESS] Screenshot refreshed
[[08:44:29]] [INFO] Refreshing screenshot...
[[08:44:29]] [INFO] d6vTfR4Y0D=pass
[[08:44:25]] [SUCCESS] Screenshot refreshed successfully
[[08:44:25]] [SUCCESS] Screenshot refreshed successfully
[[08:44:25]] [INFO] d6vTfR4Y0D=running
[[08:44:25]] [INFO] Executing action 368/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:44:24]] [SUCCESS] Screenshot refreshed
[[08:44:24]] [INFO] Refreshing screenshot...
[[08:44:24]] [INFO] pe9W6tZdXT=pass
[[08:44:19]] [SUCCESS] Screenshot refreshed successfully
[[08:44:19]] [SUCCESS] Screenshot refreshed successfully
[[08:44:19]] [INFO] pe9W6tZdXT=running
[[08:44:19]] [INFO] Executing action 367/576: iOS Function: text - Text: "env[uname-op]"
[[08:44:18]] [SUCCESS] Screenshot refreshed
[[08:44:18]] [INFO] Refreshing screenshot...
[[08:44:18]] [INFO] u928vFzSni=pass
[[08:44:14]] [SUCCESS] Screenshot refreshed successfully
[[08:44:14]] [SUCCESS] Screenshot refreshed successfully
[[08:44:14]] [INFO] u928vFzSni=running
[[08:44:14]] [INFO] Executing action 366/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:44:14]] [SUCCESS] Screenshot refreshed
[[08:44:14]] [INFO] Refreshing screenshot...
[[08:44:14]] [INFO] s0WyiD1w0B=pass
[[08:44:11]] [SUCCESS] Screenshot refreshed successfully
[[08:44:11]] [SUCCESS] Screenshot refreshed successfully
[[08:44:11]] [INFO] s0WyiD1w0B=running
[[08:44:11]] [INFO] Executing action 365/576: iOS Function: alert_accept
[[08:44:10]] [SUCCESS] Screenshot refreshed
[[08:44:10]] [INFO] Refreshing screenshot...
[[08:44:10]] [INFO] gekNSY5O2E=pass
[[08:44:06]] [SUCCESS] Screenshot refreshed successfully
[[08:44:06]] [SUCCESS] Screenshot refreshed successfully
[[08:44:06]] [INFO] gekNSY5O2E=running
[[08:44:06]] [INFO] Executing action 364/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[08:44:05]] [SUCCESS] Screenshot refreshed
[[08:44:05]] [INFO] Refreshing screenshot...
[[08:44:05]] [INFO] VJJ3EXXotU=pass
[[08:44:02]] [SUCCESS] Screenshot refreshed successfully
[[08:44:02]] [SUCCESS] Screenshot refreshed successfully
[[08:44:01]] [INFO] VJJ3EXXotU=running
[[08:44:01]] [INFO] Executing action 363/576: Tap on image: env[device-back-img]
[[08:44:00]] [SUCCESS] Screenshot refreshed
[[08:44:00]] [INFO] Refreshing screenshot...
[[08:44:00]] [INFO] 83tV9A4NOn=pass
[[08:43:57]] [SUCCESS] Screenshot refreshed successfully
[[08:43:57]] [SUCCESS] Screenshot refreshed successfully
[[08:43:57]] [INFO] 83tV9A4NOn=running
[[08:43:57]] [INFO] Executing action 362/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[08:43:57]] [SUCCESS] Screenshot refreshed
[[08:43:57]] [INFO] Refreshing screenshot...
[[08:43:57]] [INFO] aNN0yYFLEd=pass
[[08:43:52]] [SUCCESS] Screenshot refreshed successfully
[[08:43:52]] [SUCCESS] Screenshot refreshed successfully
[[08:43:52]] [INFO] aNN0yYFLEd=running
[[08:43:52]] [INFO] Executing action 361/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[08:43:51]] [SUCCESS] Screenshot refreshed
[[08:43:51]] [INFO] Refreshing screenshot...
[[08:43:51]] [INFO] XJv08Gkucs=pass
[[08:43:48]] [SUCCESS] Screenshot refreshed successfully
[[08:43:48]] [SUCCESS] Screenshot refreshed successfully
[[08:43:48]] [INFO] XJv08Gkucs=running
[[08:43:48]] [INFO] Executing action 360/576: Input text: "env[uname-op]"
[[08:43:47]] [SUCCESS] Screenshot refreshed
[[08:43:47]] [INFO] Refreshing screenshot...
[[08:43:47]] [INFO] kAQ1yIIw3h=pass
[[08:43:43]] [SUCCESS] Screenshot refreshed successfully
[[08:43:43]] [SUCCESS] Screenshot refreshed successfully
[[08:43:43]] [INFO] kAQ1yIIw3h=running
[[08:43:43]] [INFO] Executing action 359/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[08:43:43]] [SUCCESS] Screenshot refreshed
[[08:43:43]] [INFO] Refreshing screenshot...
[[08:43:43]] [INFO] 7YbjwQH1Jc=pass
[[08:43:40]] [SUCCESS] Screenshot refreshed successfully
[[08:43:40]] [SUCCESS] Screenshot refreshed successfully
[[08:43:40]] [INFO] 7YbjwQH1Jc=running
[[08:43:40]] [INFO] Executing action 358/576: Input text: "env[searchorder]"
[[08:43:39]] [SUCCESS] Screenshot refreshed
[[08:43:39]] [INFO] Refreshing screenshot...
[[08:43:39]] [INFO] OmKfD9iBjD=pass
[[08:43:35]] [SUCCESS] Screenshot refreshed successfully
[[08:43:35]] [SUCCESS] Screenshot refreshed successfully
[[08:43:35]] [INFO] OmKfD9iBjD=running
[[08:43:35]] [INFO] Executing action 357/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[08:43:35]] [SUCCESS] Screenshot refreshed
[[08:43:35]] [INFO] Refreshing screenshot...
[[08:43:35]] [INFO] eHLWiRoqqS=pass
[[08:43:30]] [SUCCESS] Screenshot refreshed successfully
[[08:43:30]] [SUCCESS] Screenshot refreshed successfully
[[08:43:30]] [INFO] eHLWiRoqqS=running
[[08:43:30]] [INFO] Executing action 356/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[08:43:30]] [SUCCESS] Screenshot refreshed
[[08:43:30]] [INFO] Refreshing screenshot...
[[08:43:30]] [INFO] F4NGh9HrLw=pass
[[08:43:26]] [SUCCESS] Screenshot refreshed successfully
[[08:43:26]] [SUCCESS] Screenshot refreshed successfully
[[08:43:25]] [INFO] F4NGh9HrLw=running
[[08:43:25]] [INFO] Executing action 355/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:43:25]] [SUCCESS] Screenshot refreshed
[[08:43:25]] [INFO] Refreshing screenshot...
[[08:43:25]] [INFO] 74XW7x54ad=pass
[[08:43:21]] [SUCCESS] Screenshot refreshed successfully
[[08:43:21]] [SUCCESS] Screenshot refreshed successfully
[[08:43:20]] [INFO] 74XW7x54ad=running
[[08:43:20]] [INFO] Executing action 354/576: Tap on image: env[device-back-img]
[[08:43:19]] [SUCCESS] Screenshot refreshed
[[08:43:19]] [INFO] Refreshing screenshot...
[[08:43:19]] [INFO] xUbWFa8Ok2=pass
[[08:43:17]] [SUCCESS] Screenshot refreshed successfully
[[08:43:17]] [SUCCESS] Screenshot refreshed successfully
[[08:43:15]] [INFO] xUbWFa8Ok2=running
[[08:43:15]] [INFO] Executing action 353/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[08:43:14]] [SUCCESS] Screenshot refreshed
[[08:43:14]] [INFO] Refreshing screenshot...
[[08:43:14]] [INFO] RbNtEW6N9T=pass
[[08:43:12]] [SUCCESS] Screenshot refreshed successfully
[[08:43:12]] [SUCCESS] Screenshot refreshed successfully
[[08:43:10]] [INFO] RbNtEW6N9T=running
[[08:43:10]] [INFO] Executing action 352/576: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[08:43:10]] [SUCCESS] Screenshot refreshed
[[08:43:10]] [INFO] Refreshing screenshot...
[[08:43:10]] [INFO] F4NGh9HrLw=pass
[[08:43:07]] [SUCCESS] Screenshot refreshed successfully
[[08:43:07]] [SUCCESS] Screenshot refreshed successfully
[[08:43:05]] [INFO] F4NGh9HrLw=running
[[08:43:05]] [INFO] Executing action 351/576: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[08:43:05]] [SUCCESS] Screenshot refreshed
[[08:43:05]] [INFO] Refreshing screenshot...
[[08:43:05]] [INFO] RlDZFks4Lc=pass
[[08:43:02]] [SUCCESS] Screenshot refreshed successfully
[[08:43:02]] [SUCCESS] Screenshot refreshed successfully
[[08:43:00]] [INFO] RlDZFks4Lc=running
[[08:43:00]] [INFO] Executing action 350/576: iOS Function: alert_accept
[[08:43:00]] [SUCCESS] Screenshot refreshed
[[08:43:00]] [INFO] Refreshing screenshot...
[[08:43:00]] [INFO] Dzn2Q7JTe0=pass
[[08:42:55]] [SUCCESS] Screenshot refreshed successfully
[[08:42:55]] [SUCCESS] Screenshot refreshed successfully
[[08:42:54]] [INFO] Dzn2Q7JTe0=running
[[08:42:54]] [INFO] Executing action 349/576: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[08:42:54]] [SUCCESS] Screenshot refreshed
[[08:42:54]] [INFO] Refreshing screenshot...
[[08:42:54]] [INFO] H9fy9qcFbZ=pass
[[08:42:41]] [SUCCESS] Screenshot refreshed successfully
[[08:42:41]] [SUCCESS] Screenshot refreshed successfully
[[08:42:39]] [INFO] H9fy9qcFbZ=running
[[08:42:39]] [INFO] Executing action 348/576: Restart app: env[appid]
[[08:42:39]] [SUCCESS] Screenshot refreshed
[[08:42:39]] [INFO] Refreshing screenshot...
[[08:42:39]] [SUCCESS] Screenshot refreshed
[[08:42:39]] [INFO] Refreshing screenshot...
[[08:42:35]] [SUCCESS] Screenshot refreshed successfully
[[08:42:35]] [SUCCESS] Screenshot refreshed successfully
[[08:42:35]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:42:35]] [SUCCESS] Screenshot refreshed
[[08:42:35]] [INFO] Refreshing screenshot...
[[08:42:23]] [SUCCESS] Screenshot refreshed successfully
[[08:42:23]] [SUCCESS] Screenshot refreshed successfully
[[08:42:23]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:42:22]] [SUCCESS] Screenshot refreshed
[[08:42:22]] [INFO] Refreshing screenshot...
[[08:42:18]] [SUCCESS] Screenshot refreshed successfully
[[08:42:18]] [SUCCESS] Screenshot refreshed successfully
[[08:42:18]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:42:18]] [SUCCESS] Screenshot refreshed
[[08:42:18]] [INFO] Refreshing screenshot...
[[08:42:14]] [SUCCESS] Screenshot refreshed successfully
[[08:42:14]] [SUCCESS] Screenshot refreshed successfully
[[08:42:14]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:42:13]] [SUCCESS] Screenshot refreshed
[[08:42:13]] [INFO] Refreshing screenshot...
[[08:42:07]] [SUCCESS] Screenshot refreshed successfully
[[08:42:07]] [SUCCESS] Screenshot refreshed successfully
[[08:42:06]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:42:05]] [SUCCESS] Screenshot refreshed
[[08:42:05]] [INFO] Refreshing screenshot...
[[08:41:58]] [SUCCESS] Screenshot refreshed successfully
[[08:41:58]] [SUCCESS] Screenshot refreshed successfully
[[08:41:58]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:41:58]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:41:58]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:41:58]] [INFO] AeQaElnzUN=running
[[08:41:58]] [INFO] Executing action 347/576: cleanupSteps action
[[08:41:57]] [SUCCESS] Screenshot refreshed
[[08:41:57]] [INFO] Refreshing screenshot...
[[08:41:57]] [INFO] BracBsfa3Y=pass
[[08:41:52]] [SUCCESS] Screenshot refreshed successfully
[[08:41:52]] [SUCCESS] Screenshot refreshed successfully
[[08:41:52]] [INFO] BracBsfa3Y=running
[[08:41:52]] [INFO] Executing action 346/576: Tap on Text: "out"
[[08:41:51]] [SUCCESS] Screenshot refreshed
[[08:41:51]] [INFO] Refreshing screenshot...
[[08:41:51]] [INFO] s6tWdQ5URW=pass
[[08:41:44]] [SUCCESS] Screenshot refreshed successfully
[[08:41:44]] [SUCCESS] Screenshot refreshed successfully
[[08:41:44]] [INFO] s6tWdQ5URW=running
[[08:41:44]] [INFO] Executing action 345/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:41:43]] [SUCCESS] Screenshot refreshed
[[08:41:43]] [INFO] Refreshing screenshot...
[[08:41:43]] [INFO] wNGRrfUjpK=pass
[[08:41:39]] [SUCCESS] Screenshot refreshed successfully
[[08:41:39]] [SUCCESS] Screenshot refreshed successfully
[[08:41:39]] [INFO] wNGRrfUjpK=running
[[08:41:39]] [INFO] Executing action 344/576: Tap on image: env[device-back-img]
[[08:41:39]] [SUCCESS] Screenshot refreshed
[[08:41:39]] [INFO] Refreshing screenshot...
[[08:41:39]] [INFO] BracBsfa3Y=pass
[[08:41:34]] [SUCCESS] Screenshot refreshed successfully
[[08:41:34]] [SUCCESS] Screenshot refreshed successfully
[[08:41:34]] [INFO] BracBsfa3Y=running
[[08:41:34]] [INFO] Executing action 343/576: Tap on Text: "Customer"
[[08:41:33]] [SUCCESS] Screenshot refreshed
[[08:41:33]] [INFO] Refreshing screenshot...
[[08:41:33]] [INFO] H4WfwVU8YP=pass
[[08:41:29]] [SUCCESS] Screenshot refreshed successfully
[[08:41:29]] [SUCCESS] Screenshot refreshed successfully
[[08:41:28]] [INFO] H4WfwVU8YP=running
[[08:41:28]] [INFO] Executing action 342/576: Tap on image: banner-close-updated.png
[[08:41:28]] [SUCCESS] Screenshot refreshed
[[08:41:28]] [INFO] Refreshing screenshot...
[[08:41:28]] [INFO] ePyaYpttQA=pass
[[08:41:24]] [SUCCESS] Screenshot refreshed successfully
[[08:41:24]] [SUCCESS] Screenshot refreshed successfully
[[08:41:23]] [INFO] ePyaYpttQA=running
[[08:41:23]] [INFO] Executing action 341/576: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[08:41:23]] [SUCCESS] Screenshot refreshed
[[08:41:23]] [INFO] Refreshing screenshot...
[[08:41:23]] [INFO] BracBsfa3Y=pass
[[08:41:17]] [SUCCESS] Screenshot refreshed successfully
[[08:41:17]] [SUCCESS] Screenshot refreshed successfully
[[08:41:17]] [INFO] BracBsfa3Y=running
[[08:41:17]] [INFO] Executing action 340/576: Tap on Text: "Invite"
[[08:41:17]] [SUCCESS] Screenshot refreshed
[[08:41:17]] [INFO] Refreshing screenshot...
[[08:41:17]] [INFO] xVbCNStsOP=pass
[[08:41:12]] [SUCCESS] Screenshot refreshed successfully
[[08:41:12]] [SUCCESS] Screenshot refreshed successfully
[[08:41:12]] [INFO] xVbCNStsOP=running
[[08:41:12]] [INFO] Executing action 339/576: Tap on image: env[device-back-img]
[[08:41:12]] [SUCCESS] Screenshot refreshed
[[08:41:12]] [INFO] Refreshing screenshot...
[[08:41:12]] [INFO] 8kQkC2FGyZ=pass
[[08:41:09]] [SUCCESS] Screenshot refreshed successfully
[[08:41:09]] [SUCCESS] Screenshot refreshed successfully
[[08:41:08]] [INFO] 8kQkC2FGyZ=running
[[08:41:08]] [INFO] Executing action 338/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[08:41:08]] [SUCCESS] Screenshot refreshed
[[08:41:08]] [INFO] Refreshing screenshot...
[[08:41:08]] [INFO] PgjJCrKFYo=pass
[[08:41:02]] [SUCCESS] Screenshot refreshed successfully
[[08:41:02]] [SUCCESS] Screenshot refreshed successfully
[[08:41:02]] [INFO] PgjJCrKFYo=running
[[08:41:02]] [INFO] Executing action 337/576: Tap on Text: "VIC"
[[08:41:02]] [SUCCESS] Screenshot refreshed
[[08:41:02]] [INFO] Refreshing screenshot...
[[08:41:02]] [INFO] 3Si0csRNaw=pass
[[08:40:55]] [SUCCESS] Screenshot refreshed successfully
[[08:40:55]] [SUCCESS] Screenshot refreshed successfully
[[08:40:54]] [INFO] 3Si0csRNaw=running
[[08:40:54]] [INFO] Executing action 336/576: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[08:40:53]] [SUCCESS] Screenshot refreshed
[[08:40:53]] [INFO] Refreshing screenshot...
[[08:40:53]] [INFO] BracBsfa3Y=pass
[[08:40:48]] [SUCCESS] Screenshot refreshed successfully
[[08:40:48]] [SUCCESS] Screenshot refreshed successfully
[[08:40:48]] [INFO] BracBsfa3Y=running
[[08:40:48]] [INFO] Executing action 335/576: Tap on Text: "Nearby"
[[08:40:47]] [SUCCESS] Screenshot refreshed
[[08:40:47]] [INFO] Refreshing screenshot...
[[08:40:47]] [INFO] BracBsfa3Y=pass
[[08:40:43]] [SUCCESS] Screenshot refreshed successfully
[[08:40:43]] [SUCCESS] Screenshot refreshed successfully
[[08:40:43]] [INFO] BracBsfa3Y=running
[[08:40:43]] [INFO] Executing action 334/576: Tap on Text: "locator"
[[08:40:42]] [SUCCESS] Screenshot refreshed
[[08:40:42]] [INFO] Refreshing screenshot...
[[08:40:42]] [INFO] s6tWdQ5URW=pass
[[08:40:35]] [SUCCESS] Screenshot refreshed successfully
[[08:40:35]] [SUCCESS] Screenshot refreshed successfully
[[08:40:35]] [INFO] s6tWdQ5URW=running
[[08:40:35]] [INFO] Executing action 333/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:40:35]] [SUCCESS] Screenshot refreshed
[[08:40:35]] [INFO] Refreshing screenshot...
[[08:40:35]] [INFO] 2M0KHOVecv=pass
[[08:40:30]] [SUCCESS] Screenshot refreshed successfully
[[08:40:30]] [SUCCESS] Screenshot refreshed successfully
[[08:40:30]] [INFO] 2M0KHOVecv=running
[[08:40:30]] [INFO] Executing action 332/576: Check if element with accessibility_id="txtMy Flybuys card" exists
[[08:40:30]] [SUCCESS] Screenshot refreshed
[[08:40:30]] [INFO] Refreshing screenshot...
[[08:40:30]] [INFO] LBgsj3oLcu=pass
[[08:40:26]] [SUCCESS] Screenshot refreshed successfully
[[08:40:26]] [SUCCESS] Screenshot refreshed successfully
[[08:40:25]] [INFO] LBgsj3oLcu=running
[[08:40:25]] [INFO] Executing action 331/576: Tap on image: env[device-back-img]
[[08:40:25]] [SUCCESS] Screenshot refreshed
[[08:40:25]] [INFO] Refreshing screenshot...
[[08:40:25]] [INFO] biRyWs3nSs=pass
[[08:40:19]] [SUCCESS] Screenshot refreshed successfully
[[08:40:19]] [SUCCESS] Screenshot refreshed successfully
[[08:40:19]] [INFO] biRyWs3nSs=running
[[08:40:19]] [INFO] Executing action 330/576: Tap on element with accessibility_id: btnSaveFlybuysCard
[[08:40:18]] [SUCCESS] Screenshot refreshed
[[08:40:18]] [INFO] Refreshing screenshot...
[[08:40:18]] [INFO] 8cFGh3GD68=pass
[[08:40:13]] [SUCCESS] Screenshot refreshed successfully
[[08:40:13]] [SUCCESS] Screenshot refreshed successfully
[[08:40:12]] [INFO] 8cFGh3GD68=running
[[08:40:12]] [INFO] Executing action 329/576: Tap on element with accessibility_id: Done
[[08:40:12]] [SUCCESS] Screenshot refreshed
[[08:40:12]] [INFO] Refreshing screenshot...
[[08:40:12]] [INFO] sLe0Wurhgm=pass
[[08:40:09]] [SUCCESS] Screenshot refreshed successfully
[[08:40:09]] [SUCCESS] Screenshot refreshed successfully
[[08:40:09]] [INFO] sLe0Wurhgm=running
[[08:40:09]] [INFO] Executing action 328/576: Input text: "2791234567890"
[[08:40:08]] [SUCCESS] Screenshot refreshed
[[08:40:08]] [INFO] Refreshing screenshot...
[[08:40:08]] [INFO] Ey86YRVRzU=pass
[[08:40:02]] [SUCCESS] Screenshot refreshed successfully
[[08:40:02]] [SUCCESS] Screenshot refreshed successfully
[[08:40:02]] [INFO] Ey86YRVRzU=running
[[08:40:02]] [INFO] Executing action 327/576: Tap on element with accessibility_id: Flybuys barcode number
[[08:40:02]] [SUCCESS] Screenshot refreshed
[[08:40:02]] [INFO] Refreshing screenshot...
[[08:40:02]] [INFO] Gxhf3XGc6e=pass
[[08:39:56]] [SUCCESS] Screenshot refreshed successfully
[[08:39:56]] [SUCCESS] Screenshot refreshed successfully
[[08:39:56]] [INFO] Gxhf3XGc6e=running
[[08:39:56]] [INFO] Executing action 326/576: Tap on element with accessibility_id: btnLinkFlyBuys
[[08:39:55]] [SUCCESS] Screenshot refreshed
[[08:39:55]] [INFO] Refreshing screenshot...
[[08:39:55]] [INFO] BracBsfa3Y=pass
[[08:39:50]] [SUCCESS] Screenshot refreshed successfully
[[08:39:50]] [SUCCESS] Screenshot refreshed successfully
[[08:39:50]] [INFO] BracBsfa3Y=running
[[08:39:50]] [INFO] Executing action 325/576: Tap on Text: "Flybuys"
[[08:39:49]] [SUCCESS] Screenshot refreshed
[[08:39:49]] [INFO] Refreshing screenshot...
[[08:39:49]] [INFO] Ds5GfNVb3x=pass
[[08:39:44]] [SUCCESS] Screenshot refreshed successfully
[[08:39:44]] [SUCCESS] Screenshot refreshed successfully
[[08:39:44]] [INFO] Ds5GfNVb3x=running
[[08:39:44]] [INFO] Executing action 324/576: Tap on element with accessibility_id: btnRemove
[[08:39:43]] [SUCCESS] Screenshot refreshed
[[08:39:43]] [INFO] Refreshing screenshot...
[[08:39:43]] [INFO] 3ZFgwFaiXp=pass
[[08:39:38]] [SUCCESS] Screenshot refreshed successfully
[[08:39:38]] [SUCCESS] Screenshot refreshed successfully
[[08:39:37]] [INFO] 3ZFgwFaiXp=running
[[08:39:37]] [INFO] Executing action 323/576: Tap on element with accessibility_id: Remove card
[[08:39:37]] [SUCCESS] Screenshot refreshed
[[08:39:37]] [INFO] Refreshing screenshot...
[[08:39:37]] [INFO] 40hnWPsQ9P=pass
[[08:39:31]] [SUCCESS] Screenshot refreshed successfully
[[08:39:31]] [SUCCESS] Screenshot refreshed successfully
[[08:39:31]] [INFO] 40hnWPsQ9P=running
[[08:39:31]] [INFO] Executing action 322/576: Tap on element with accessibility_id: btneditFlybuysCard
[[08:39:30]] [SUCCESS] Screenshot refreshed
[[08:39:30]] [INFO] Refreshing screenshot...
[[08:39:30]] [INFO] 40hnWPsQ9P=pass
[[08:39:25]] [SUCCESS] Screenshot refreshed successfully
[[08:39:25]] [SUCCESS] Screenshot refreshed successfully
[[08:39:25]] [INFO] 40hnWPsQ9P=running
[[08:39:25]] [INFO] Executing action 321/576: Wait till accessibility_id=btneditFlybuysCard
[[08:39:25]] [SUCCESS] Screenshot refreshed
[[08:39:25]] [INFO] Refreshing screenshot...
[[08:39:25]] [INFO] BracBsfa3Y=pass
[[08:39:19]] [SUCCESS] Screenshot refreshed successfully
[[08:39:19]] [SUCCESS] Screenshot refreshed successfully
[[08:39:19]] [INFO] BracBsfa3Y=running
[[08:39:19]] [INFO] Executing action 320/576: Tap on Text: "Flybuys"
[[08:39:18]] [SUCCESS] Screenshot refreshed
[[08:39:18]] [INFO] Refreshing screenshot...
[[08:39:18]] [INFO] MkTFxfzubv=pass
[[08:39:15]] [SUCCESS] Screenshot refreshed successfully
[[08:39:15]] [SUCCESS] Screenshot refreshed successfully
[[08:39:14]] [INFO] MkTFxfzubv=running
[[08:39:14]] [INFO] Executing action 319/576: Tap on image: env[device-back-img]
[[08:39:14]] [SUCCESS] Screenshot refreshed
[[08:39:14]] [INFO] Refreshing screenshot...
[[08:39:14]] [INFO] EO3cMmdUyM=pass
[[08:39:10]] [SUCCESS] Screenshot refreshed successfully
[[08:39:10]] [SUCCESS] Screenshot refreshed successfully
[[08:39:10]] [INFO] EO3cMmdUyM=running
[[08:39:10]] [INFO] Executing action 318/576: Tap on image: env[device-back-img]
[[08:39:09]] [SUCCESS] Screenshot refreshed
[[08:39:09]] [INFO] Refreshing screenshot...
[[08:39:09]] [INFO] napKDohf3Z=pass
[[08:39:04]] [SUCCESS] Screenshot refreshed successfully
[[08:39:04]] [SUCCESS] Screenshot refreshed successfully
[[08:39:04]] [INFO] napKDohf3Z=running
[[08:39:04]] [INFO] Executing action 317/576: Tap on Text: "payment"
[[08:39:04]] [SUCCESS] Screenshot refreshed
[[08:39:04]] [INFO] Refreshing screenshot...
[[08:39:04]] [INFO] ekqt95ZRol=pass
[[08:38:59]] [SUCCESS] Screenshot refreshed successfully
[[08:38:59]] [SUCCESS] Screenshot refreshed successfully
[[08:38:59]] [INFO] ekqt95ZRol=running
[[08:38:59]] [INFO] Executing action 316/576: Tap on image: env[device-back-img]
[[08:38:59]] [SUCCESS] Screenshot refreshed
[[08:38:59]] [INFO] Refreshing screenshot...
[[08:38:59]] [INFO] 20qUCJgpE9=pass
[[08:38:55]] [SUCCESS] Screenshot refreshed successfully
[[08:38:55]] [SUCCESS] Screenshot refreshed successfully
[[08:38:54]] [INFO] 20qUCJgpE9=running
[[08:38:54]] [INFO] Executing action 315/576: Tap on Text: "address"
[[08:38:53]] [SUCCESS] Screenshot refreshed
[[08:38:53]] [INFO] Refreshing screenshot...
[[08:38:53]] [INFO] 6HR2weiXoT=pass
[[08:38:49]] [SUCCESS] Screenshot refreshed successfully
[[08:38:49]] [SUCCESS] Screenshot refreshed successfully
[[08:38:49]] [INFO] 6HR2weiXoT=running
[[08:38:49]] [INFO] Executing action 314/576: Tap on image: env[device-back-img]
[[08:38:49]] [SUCCESS] Screenshot refreshed
[[08:38:49]] [INFO] Refreshing screenshot...
[[08:38:49]] [INFO] 3hOTINBVMf=pass
[[08:38:44]] [SUCCESS] Screenshot refreshed successfully
[[08:38:44]] [SUCCESS] Screenshot refreshed successfully
[[08:38:44]] [INFO] 3hOTINBVMf=running
[[08:38:44]] [INFO] Executing action 313/576: Tap on Text: "details"
[[08:38:43]] [SUCCESS] Screenshot refreshed
[[08:38:43]] [INFO] Refreshing screenshot...
[[08:38:43]] [INFO] yJi0WxnERj=pass
[[08:38:39]] [SUCCESS] Screenshot refreshed successfully
[[08:38:39]] [SUCCESS] Screenshot refreshed successfully
[[08:38:39]] [INFO] yJi0WxnERj=running
[[08:38:39]] [INFO] Executing action 312/576: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[08:38:39]] [SUCCESS] Screenshot refreshed
[[08:38:39]] [INFO] Refreshing screenshot...
[[08:38:39]] [INFO] PbfHAtFQPP=pass
[[08:38:35]] [SUCCESS] Screenshot refreshed successfully
[[08:38:35]] [SUCCESS] Screenshot refreshed successfully
[[08:38:34]] [INFO] PbfHAtFQPP=running
[[08:38:34]] [INFO] Executing action 311/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:38:34]] [SUCCESS] Screenshot refreshed
[[08:38:34]] [INFO] Refreshing screenshot...
[[08:38:34]] [INFO] 6qZnk86hGg=pass
[[08:38:29]] [SUCCESS] Screenshot refreshed successfully
[[08:38:29]] [SUCCESS] Screenshot refreshed successfully
[[08:38:29]] [INFO] 6qZnk86hGg=running
[[08:38:29]] [INFO] Executing action 310/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:38:28]] [SUCCESS] Screenshot refreshed
[[08:38:28]] [INFO] Refreshing screenshot...
[[08:38:28]] [INFO] FAvQgIuHc1=pass
[[08:38:23]] [SUCCESS] Screenshot refreshed successfully
[[08:38:23]] [SUCCESS] Screenshot refreshed successfully
[[08:38:23]] [INFO] FAvQgIuHc1=running
[[08:38:23]] [INFO] Executing action 309/576: Tap on Text: "Return"
[[08:38:22]] [SUCCESS] Screenshot refreshed
[[08:38:22]] [INFO] Refreshing screenshot...
[[08:38:22]] [INFO] vmc01sHkbr=pass
[[08:38:16]] [SUCCESS] Screenshot refreshed successfully
[[08:38:16]] [SUCCESS] Screenshot refreshed successfully
[[08:38:16]] [INFO] vmc01sHkbr=running
[[08:38:16]] [INFO] Executing action 308/576: Wait for 5 ms
[[08:38:15]] [SUCCESS] Screenshot refreshed
[[08:38:15]] [INFO] Refreshing screenshot...
[[08:38:15]] [INFO] zeu0wd1vcF=pass
[[08:38:02]] [SUCCESS] Screenshot refreshed successfully
[[08:38:02]] [SUCCESS] Screenshot refreshed successfully
[[08:38:02]] [INFO] zeu0wd1vcF=running
[[08:38:02]] [INFO] Executing action 307/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:38:02]] [SUCCESS] Screenshot refreshed
[[08:38:02]] [INFO] Refreshing screenshot...
[[08:38:02]] [INFO] OwWeZes4aT=pass
[[08:37:58]] [SUCCESS] Screenshot refreshed successfully
[[08:37:58]] [SUCCESS] Screenshot refreshed successfully
[[08:37:57]] [INFO] OwWeZes4aT=running
[[08:37:57]] [INFO] Executing action 306/576: Tap on image: env[device-back-img]
[[08:37:57]] [SUCCESS] Screenshot refreshed
[[08:37:57]] [INFO] Refreshing screenshot...
[[08:37:57]] [INFO] aAaTtUE92h=pass
[[08:37:53]] [SUCCESS] Screenshot refreshed successfully
[[08:37:53]] [SUCCESS] Screenshot refreshed successfully
[[08:37:53]] [INFO] aAaTtUE92h=running
[[08:37:53]] [INFO] Executing action 305/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[08:37:53]] [SUCCESS] Screenshot refreshed
[[08:37:53]] [INFO] Refreshing screenshot...
[[08:37:53]] [INFO] 9iOZGMqAZK=pass
[[08:37:49]] [SUCCESS] Screenshot refreshed successfully
[[08:37:49]] [SUCCESS] Screenshot refreshed successfully
[[08:37:48]] [INFO] 9iOZGMqAZK=running
[[08:37:48]] [INFO] Executing action 304/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[08:37:48]] [SUCCESS] Screenshot refreshed
[[08:37:48]] [INFO] Refreshing screenshot...
[[08:37:48]] [INFO] mRTYzOFRRw=pass
[[08:37:45]] [SUCCESS] Screenshot refreshed successfully
[[08:37:45]] [SUCCESS] Screenshot refreshed successfully
[[08:37:45]] [INFO] mRTYzOFRRw=running
[[08:37:45]] [INFO] Executing action 303/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[08:37:44]] [SUCCESS] Screenshot refreshed
[[08:37:44]] [INFO] Refreshing screenshot...
[[08:37:44]] [INFO] 7g6MFJSGIO=pass
[[08:37:40]] [SUCCESS] Screenshot refreshed successfully
[[08:37:40]] [SUCCESS] Screenshot refreshed successfully
[[08:37:40]] [INFO] 7g6MFJSGIO=running
[[08:37:40]] [INFO] Executing action 302/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[08:37:40]] [SUCCESS] Screenshot refreshed
[[08:37:40]] [INFO] Refreshing screenshot...
[[08:37:40]] [INFO] zNwyPagPE1=pass
[[08:37:33]] [SUCCESS] Screenshot refreshed successfully
[[08:37:33]] [SUCCESS] Screenshot refreshed successfully
[[08:37:33]] [INFO] zNwyPagPE1=running
[[08:37:33]] [INFO] Executing action 301/576: Wait for 5 ms
[[08:37:33]] [SUCCESS] Screenshot refreshed
[[08:37:33]] [INFO] Refreshing screenshot...
[[08:37:33]] [INFO] qXsL3wzg6J=pass
[[08:37:29]] [SUCCESS] Screenshot refreshed successfully
[[08:37:29]] [SUCCESS] Screenshot refreshed successfully
[[08:37:29]] [INFO] qXsL3wzg6J=running
[[08:37:29]] [INFO] Executing action 300/576: Tap on image: env[device-back-img]
[[08:37:28]] [SUCCESS] Screenshot refreshed
[[08:37:28]] [INFO] Refreshing screenshot...
[[08:37:28]] [INFO] YuuQe2KupX=pass
[[08:37:23]] [INFO] YuuQe2KupX=running
[[08:37:23]] [INFO] Executing action 299/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[08:37:23]] [SUCCESS] Screenshot refreshed successfully
[[08:37:23]] [SUCCESS] Screenshot refreshed successfully
[[08:37:23]] [SUCCESS] Screenshot refreshed
[[08:37:23]] [INFO] Refreshing screenshot...
[[08:37:23]] [INFO] g0PE7Mofye=pass
[[08:37:17]] [SUCCESS] Screenshot refreshed successfully
[[08:37:17]] [SUCCESS] Screenshot refreshed successfully
[[08:37:17]] [INFO] g0PE7Mofye=running
[[08:37:17]] [INFO] Executing action 298/576: Tap on element with accessibility_id: Print order details
[[08:37:17]] [SUCCESS] Screenshot refreshed
[[08:37:17]] [INFO] Refreshing screenshot...
[[08:37:17]] [INFO] GgQaBLWYkb=pass
[[08:37:13]] [SUCCESS] Screenshot refreshed successfully
[[08:37:13]] [SUCCESS] Screenshot refreshed successfully
[[08:37:13]] [INFO] GgQaBLWYkb=running
[[08:37:13]] [INFO] Executing action 297/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[08:37:12]] [SUCCESS] Screenshot refreshed
[[08:37:12]] [INFO] Refreshing screenshot...
[[08:37:12]] [INFO] f3OrHHzTFN=pass
[[08:36:56]] [SUCCESS] Screenshot refreshed successfully
[[08:36:56]] [SUCCESS] Screenshot refreshed successfully
[[08:36:56]] [INFO] f3OrHHzTFN=running
[[08:36:56]] [INFO] Executing action 296/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[08:36:55]] [SUCCESS] Screenshot refreshed
[[08:36:55]] [INFO] Refreshing screenshot...
[[08:36:55]] [INFO] 7g6MFJSGIO=pass
[[08:36:51]] [SUCCESS] Screenshot refreshed successfully
[[08:36:51]] [SUCCESS] Screenshot refreshed successfully
[[08:36:51]] [INFO] 7g6MFJSGIO=running
[[08:36:51]] [INFO] Executing action 295/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[08:36:51]] [SUCCESS] Screenshot refreshed
[[08:36:51]] [INFO] Refreshing screenshot...
[[08:36:51]] [INFO] Z6g3sGuHTp=pass
[[08:36:44]] [SUCCESS] Screenshot refreshed successfully
[[08:36:44]] [SUCCESS] Screenshot refreshed successfully
[[08:36:44]] [INFO] Z6g3sGuHTp=running
[[08:36:44]] [INFO] Executing action 294/576: Wait for 5 ms
[[08:36:43]] [SUCCESS] Screenshot refreshed
[[08:36:43]] [INFO] Refreshing screenshot...
[[08:36:43]] [INFO] pFlYwTS53v=pass
[[08:36:39]] [SUCCESS] Screenshot refreshed successfully
[[08:36:39]] [SUCCESS] Screenshot refreshed successfully
[[08:36:39]] [INFO] pFlYwTS53v=running
[[08:36:39]] [INFO] Executing action 293/576: Tap on Text: "receipts"
[[08:36:38]] [SUCCESS] Screenshot refreshed
[[08:36:38]] [INFO] Refreshing screenshot...
[[08:36:38]] [INFO] V59u3l1wkM=pass
[[08:36:35]] [SUCCESS] Screenshot refreshed successfully
[[08:36:35]] [SUCCESS] Screenshot refreshed successfully
[[08:36:35]] [INFO] V59u3l1wkM=running
[[08:36:35]] [INFO] Executing action 292/576: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[08:36:34]] [SUCCESS] Screenshot refreshed
[[08:36:34]] [INFO] Refreshing screenshot...
[[08:36:34]] [INFO] sl3Wk1gK8X=pass
[[08:36:32]] [SUCCESS] Screenshot refreshed successfully
[[08:36:32]] [SUCCESS] Screenshot refreshed successfully
[[08:36:30]] [INFO] sl3Wk1gK8X=running
[[08:36:30]] [INFO] Executing action 291/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:36:30]] [SUCCESS] Screenshot refreshed
[[08:36:30]] [INFO] Refreshing screenshot...
[[08:36:29]] [SUCCESS] Screenshot refreshed
[[08:36:29]] [INFO] Refreshing screenshot...
[[08:36:25]] [SUCCESS] Screenshot refreshed successfully
[[08:36:25]] [SUCCESS] Screenshot refreshed successfully
[[08:36:24]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:36:23]] [SUCCESS] Screenshot refreshed
[[08:36:23]] [INFO] Refreshing screenshot...
[[08:36:19]] [SUCCESS] Screenshot refreshed successfully
[[08:36:19]] [SUCCESS] Screenshot refreshed successfully
[[08:36:19]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:36:19]] [SUCCESS] Screenshot refreshed
[[08:36:19]] [INFO] Refreshing screenshot...
[[08:36:14]] [SUCCESS] Screenshot refreshed successfully
[[08:36:14]] [SUCCESS] Screenshot refreshed successfully
[[08:36:14]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:36:13]] [SUCCESS] Screenshot refreshed
[[08:36:13]] [INFO] Refreshing screenshot...
[[08:36:09]] [SUCCESS] Screenshot refreshed successfully
[[08:36:09]] [SUCCESS] Screenshot refreshed successfully
[[08:36:09]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:36:09]] [SUCCESS] Screenshot refreshed
[[08:36:09]] [INFO] Refreshing screenshot...
[[08:36:03]] [SUCCESS] Screenshot refreshed successfully
[[08:36:03]] [SUCCESS] Screenshot refreshed successfully
[[08:36:03]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:36:03]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:36:03]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:36:03]] [INFO] vjK6GqOF3r=running
[[08:36:03]] [INFO] Executing action 290/576: Execute Test Case: Kmart-Signin (8 steps)
[[08:36:02]] [SUCCESS] Screenshot refreshed
[[08:36:02]] [INFO] Refreshing screenshot...
[[08:36:02]] [INFO] ly2oT3zqmf=pass
[[08:36:00]] [SUCCESS] Screenshot refreshed successfully
[[08:36:00]] [SUCCESS] Screenshot refreshed successfully
[[08:35:59]] [INFO] ly2oT3zqmf=running
[[08:35:59]] [INFO] Executing action 289/576: iOS Function: alert_accept
[[08:35:59]] [SUCCESS] Screenshot refreshed
[[08:35:59]] [INFO] Refreshing screenshot...
[[08:35:59]] [INFO] xAPeBnVHrT=pass
[[08:35:51]] [SUCCESS] Screenshot refreshed successfully
[[08:35:51]] [SUCCESS] Screenshot refreshed successfully
[[08:35:51]] [INFO] xAPeBnVHrT=running
[[08:35:51]] [INFO] Executing action 288/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:35:50]] [SUCCESS] Screenshot refreshed
[[08:35:50]] [INFO] Refreshing screenshot...
[[08:35:50]] [INFO] u6bRYZZFAv=pass
[[08:35:44]] [SUCCESS] Screenshot refreshed successfully
[[08:35:44]] [SUCCESS] Screenshot refreshed successfully
[[08:35:43]] [INFO] u6bRYZZFAv=running
[[08:35:43]] [INFO] Executing action 287/576: Wait for 5 ms
[[08:35:43]] [SUCCESS] Screenshot refreshed
[[08:35:43]] [INFO] Refreshing screenshot...
[[08:35:43]] [INFO] pjFNt3w5Fr=pass
[[08:35:30]] [SUCCESS] Screenshot refreshed successfully
[[08:35:30]] [SUCCESS] Screenshot refreshed successfully
[[08:35:29]] [INFO] pjFNt3w5Fr=running
[[08:35:29]] [INFO] Executing action 286/576: Restart app: env[appid]
[[08:35:29]] [SUCCESS] Screenshot refreshed
[[08:35:29]] [INFO] Refreshing screenshot...
[[08:35:28]] [SUCCESS] Screenshot refreshed
[[08:35:28]] [INFO] Refreshing screenshot...
[[08:35:25]] [SUCCESS] Screenshot refreshed successfully
[[08:35:25]] [SUCCESS] Screenshot refreshed successfully
[[08:35:25]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:35:24]] [SUCCESS] Screenshot refreshed
[[08:35:24]] [INFO] Refreshing screenshot...
[[08:35:11]] [SUCCESS] Screenshot refreshed successfully
[[08:35:11]] [SUCCESS] Screenshot refreshed successfully
[[08:35:11]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:35:11]] [SUCCESS] Screenshot refreshed
[[08:35:11]] [INFO] Refreshing screenshot...
[[08:35:07]] [SUCCESS] Screenshot refreshed successfully
[[08:35:07]] [SUCCESS] Screenshot refreshed successfully
[[08:35:07]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:35:07]] [SUCCESS] Screenshot refreshed
[[08:35:07]] [INFO] Refreshing screenshot...
[[08:35:03]] [SUCCESS] Screenshot refreshed successfully
[[08:35:03]] [SUCCESS] Screenshot refreshed successfully
[[08:35:03]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:35:02]] [SUCCESS] Screenshot refreshed
[[08:35:02]] [INFO] Refreshing screenshot...
[[08:34:56]] [SUCCESS] Screenshot refreshed successfully
[[08:34:56]] [SUCCESS] Screenshot refreshed successfully
[[08:34:55]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:34:55]] [SUCCESS] Screenshot refreshed
[[08:34:55]] [INFO] Refreshing screenshot...
[[08:34:49]] [SUCCESS] Screenshot refreshed successfully
[[08:34:49]] [SUCCESS] Screenshot refreshed successfully
[[08:34:48]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:34:48]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:34:48]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:34:48]] [INFO] PGvsG6rpU4=running
[[08:34:48]] [INFO] Executing action 285/576: cleanupSteps action
[[08:34:48]] [SUCCESS] Screenshot refreshed
[[08:34:48]] [INFO] Refreshing screenshot...
[[08:34:48]] [INFO] LzGkAcsQyE=pass
[[08:34:45]] [SUCCESS] Screenshot refreshed successfully
[[08:34:45]] [SUCCESS] Screenshot refreshed successfully
[[08:34:45]] [INFO] LzGkAcsQyE=running
[[08:34:45]] [INFO] Executing action 284/576: Terminate app: env[appid]
[[08:34:44]] [SUCCESS] Screenshot refreshed
[[08:34:44]] [INFO] Refreshing screenshot...
[[08:34:44]] [INFO] Bdhe5AoUlM=pass
[[08:34:40]] [SUCCESS] Screenshot refreshed successfully
[[08:34:40]] [SUCCESS] Screenshot refreshed successfully
[[08:34:40]] [INFO] Bdhe5AoUlM=running
[[08:34:40]] [INFO] Executing action 283/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:34:39]] [SUCCESS] Screenshot refreshed
[[08:34:39]] [INFO] Refreshing screenshot...
[[08:34:39]] [INFO] FciJcOsMsB=pass
[[08:34:32]] [SUCCESS] Screenshot refreshed successfully
[[08:34:32]] [SUCCESS] Screenshot refreshed successfully
[[08:34:32]] [INFO] FciJcOsMsB=running
[[08:34:32]] [INFO] Executing action 282/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:34:32]] [SUCCESS] Screenshot refreshed
[[08:34:32]] [INFO] Refreshing screenshot...
[[08:34:32]] [INFO] FARWZvOj0x=pass
[[08:34:28]] [SUCCESS] Screenshot refreshed successfully
[[08:34:28]] [SUCCESS] Screenshot refreshed successfully
[[08:34:27]] [INFO] FARWZvOj0x=running
[[08:34:27]] [INFO] Executing action 281/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:34:27]] [SUCCESS] Screenshot refreshed
[[08:34:27]] [INFO] Refreshing screenshot...
[[08:34:27]] [INFO] bZCkx4U9Gk=pass
[[08:34:21]] [INFO] bZCkx4U9Gk=running
[[08:34:21]] [INFO] Executing action 280/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:34:21]] [SUCCESS] Screenshot refreshed successfully
[[08:34:21]] [SUCCESS] Screenshot refreshed successfully
[[08:34:21]] [SUCCESS] Screenshot refreshed
[[08:34:21]] [INFO] Refreshing screenshot...
[[08:34:21]] [INFO] vwFwkK6ydQ=pass
[[08:34:17]] [SUCCESS] Screenshot refreshed successfully
[[08:34:17]] [SUCCESS] Screenshot refreshed successfully
[[08:34:16]] [INFO] vwFwkK6ydQ=running
[[08:34:16]] [INFO] Executing action 279/576: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[08:34:16]] [SUCCESS] Screenshot refreshed
[[08:34:16]] [INFO] Refreshing screenshot...
[[08:34:16]] [INFO] xLGm9FefWE=pass
[[08:34:12]] [SUCCESS] Screenshot refreshed successfully
[[08:34:12]] [SUCCESS] Screenshot refreshed successfully
[[08:34:12]] [INFO] xLGm9FefWE=running
[[08:34:12]] [INFO] Executing action 278/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[08:34:11]] [SUCCESS] Screenshot refreshed
[[08:34:11]] [INFO] Refreshing screenshot...
[[08:34:11]] [INFO] UtVRXwa86e=pass
[[08:34:05]] [SUCCESS] Screenshot refreshed successfully
[[08:34:05]] [SUCCESS] Screenshot refreshed successfully
[[08:34:04]] [INFO] UtVRXwa86e=running
[[08:34:04]] [INFO] Executing action 277/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Sign in with Google"]" is visible
[[08:34:04]] [SUCCESS] Screenshot refreshed
[[08:34:04]] [INFO] Refreshing screenshot...
[[08:34:04]] [INFO] SDtskxyVpg=pass
[[08:34:00]] [SUCCESS] Screenshot refreshed successfully
[[08:34:00]] [SUCCESS] Screenshot refreshed successfully
[[08:33:59]] [INFO] SDtskxyVpg=running
[[08:33:59]] [INFO] Executing action 276/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:33:59]] [SUCCESS] Screenshot refreshed
[[08:33:59]] [INFO] Refreshing screenshot...
[[08:33:59]] [INFO] 6HhScBaqQp=pass
[[08:33:57]] [SUCCESS] Screenshot refreshed successfully
[[08:33:57]] [SUCCESS] Screenshot refreshed successfully
[[08:33:56]] [INFO] 6HhScBaqQp=running
[[08:33:56]] [INFO] Executing action 275/576: iOS Function: alert_accept
[[08:33:56]] [SUCCESS] Screenshot refreshed
[[08:33:56]] [INFO] Refreshing screenshot...
[[08:33:56]] [INFO] quzlwPw42x=pass
[[08:33:50]] [SUCCESS] Screenshot refreshed successfully
[[08:33:50]] [SUCCESS] Screenshot refreshed successfully
[[08:33:49]] [INFO] quzlwPw42x=running
[[08:33:49]] [INFO] Executing action 274/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:33:49]] [SUCCESS] Screenshot refreshed
[[08:33:49]] [INFO] Refreshing screenshot...
[[08:33:49]] [INFO] jQYHQIvQ8l=pass
[[08:33:45]] [SUCCESS] Screenshot refreshed successfully
[[08:33:45]] [SUCCESS] Screenshot refreshed successfully
[[08:33:45]] [INFO] jQYHQIvQ8l=running
[[08:33:45]] [INFO] Executing action 273/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[08:33:44]] [SUCCESS] Screenshot refreshed
[[08:33:44]] [INFO] Refreshing screenshot...
[[08:33:44]] [INFO] ts3qyFxyMf=pass
[[08:33:40]] [SUCCESS] Screenshot refreshed successfully
[[08:33:40]] [SUCCESS] Screenshot refreshed successfully
[[08:33:40]] [INFO] ts3qyFxyMf=running
[[08:33:40]] [INFO] Executing action 272/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:33:39]] [SUCCESS] Screenshot refreshed
[[08:33:39]] [INFO] Refreshing screenshot...
[[08:33:39]] [INFO] FciJcOsMsB=pass
[[08:33:32]] [SUCCESS] Screenshot refreshed successfully
[[08:33:32]] [SUCCESS] Screenshot refreshed successfully
[[08:33:32]] [INFO] FciJcOsMsB=running
[[08:33:32]] [INFO] Executing action 271/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:33:32]] [SUCCESS] Screenshot refreshed
[[08:33:32]] [INFO] Refreshing screenshot...
[[08:33:32]] [INFO] CWkqGp5ndO=pass
[[08:33:28]] [SUCCESS] Screenshot refreshed successfully
[[08:33:28]] [SUCCESS] Screenshot refreshed successfully
[[08:33:28]] [INFO] CWkqGp5ndO=running
[[08:33:28]] [INFO] Executing action 270/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:33:27]] [SUCCESS] Screenshot refreshed
[[08:33:27]] [INFO] Refreshing screenshot...
[[08:33:27]] [INFO] KfMHchi8cx=pass
[[08:33:20]] [INFO] KfMHchi8cx=running
[[08:33:20]] [INFO] Executing action 269/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:33:20]] [SUCCESS] Screenshot refreshed successfully
[[08:33:20]] [SUCCESS] Screenshot refreshed successfully
[[08:33:19]] [SUCCESS] Screenshot refreshed
[[08:33:19]] [INFO] Refreshing screenshot...
[[08:33:19]] [INFO] zsVeGHiIgX=pass
[[08:33:16]] [INFO] zsVeGHiIgX=running
[[08:33:16]] [INFO] Executing action 268/576: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[08:33:16]] [SUCCESS] Screenshot refreshed successfully
[[08:33:16]] [SUCCESS] Screenshot refreshed successfully
[[08:33:16]] [SUCCESS] Screenshot refreshed
[[08:33:16]] [INFO] Refreshing screenshot...
[[08:33:16]] [INFO] 5nsUXQ5L7u=pass
[[08:33:13]] [INFO] 5nsUXQ5L7u=running
[[08:33:13]] [INFO] Executing action 267/576: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[08:33:13]] [SUCCESS] Screenshot refreshed successfully
[[08:33:13]] [SUCCESS] Screenshot refreshed successfully
[[08:33:12]] [SUCCESS] Screenshot refreshed
[[08:33:12]] [INFO] Refreshing screenshot...
[[08:33:12]] [INFO] iSckENpXrN=pass
[[08:33:09]] [INFO] iSckENpXrN=running
[[08:33:09]] [INFO] Executing action 266/576: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[08:33:09]] [SUCCESS] Screenshot refreshed successfully
[[08:33:09]] [SUCCESS] Screenshot refreshed successfully
[[08:33:09]] [SUCCESS] Screenshot refreshed
[[08:33:09]] [INFO] Refreshing screenshot...
[[08:33:09]] [INFO] J7BPGVnRJI=pass
[[08:33:06]] [INFO] J7BPGVnRJI=running
[[08:33:06]] [INFO] Executing action 265/576: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[08:33:05]] [SUCCESS] Screenshot refreshed successfully
[[08:33:05]] [SUCCESS] Screenshot refreshed successfully
[[08:33:05]] [SUCCESS] Screenshot refreshed
[[08:33:05]] [INFO] Refreshing screenshot...
[[08:33:05]] [INFO] 0pwZCYAtOv=pass
[[08:33:02]] [INFO] 0pwZCYAtOv=running
[[08:33:02]] [INFO] Executing action 264/576: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[08:33:02]] [SUCCESS] Screenshot refreshed successfully
[[08:33:02]] [SUCCESS] Screenshot refreshed successfully
[[08:33:01]] [SUCCESS] Screenshot refreshed
[[08:33:01]] [INFO] Refreshing screenshot...
[[08:33:01]] [INFO] soKM0KayFJ=pass
[[08:32:58]] [INFO] soKM0KayFJ=running
[[08:32:58]] [INFO] Executing action 263/576: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[08:32:58]] [SUCCESS] Screenshot refreshed successfully
[[08:32:58]] [SUCCESS] Screenshot refreshed successfully
[[08:32:58]] [SUCCESS] Screenshot refreshed
[[08:32:58]] [INFO] Refreshing screenshot...
[[08:32:58]] [INFO] hnH3ayslCh=pass
[[08:32:54]] [INFO] hnH3ayslCh=running
[[08:32:54]] [INFO] Executing action 262/576: Tap on Text: "Passcode"
[[08:32:54]] [SUCCESS] Screenshot refreshed successfully
[[08:32:54]] [SUCCESS] Screenshot refreshed successfully
[[08:32:54]] [SUCCESS] Screenshot refreshed
[[08:32:54]] [INFO] Refreshing screenshot...
[[08:32:54]] [INFO] CzVeOTdAX9=pass
[[08:32:42]] [SUCCESS] Screenshot refreshed successfully
[[08:32:42]] [SUCCESS] Screenshot refreshed successfully
[[08:32:42]] [INFO] CzVeOTdAX9=running
[[08:32:42]] [INFO] Executing action 261/576: Wait for 10 ms
[[08:32:42]] [SUCCESS] Screenshot refreshed
[[08:32:42]] [INFO] Refreshing screenshot...
[[08:32:42]] [INFO] NL2gtj6qIu=pass
[[08:32:37]] [SUCCESS] Screenshot refreshed successfully
[[08:32:37]] [SUCCESS] Screenshot refreshed successfully
[[08:32:37]] [INFO] NL2gtj6qIu=running
[[08:32:37]] [INFO] Executing action 260/576: Tap on Text: "Apple"
[[08:32:36]] [SUCCESS] Screenshot refreshed
[[08:32:36]] [INFO] Refreshing screenshot...
[[08:32:36]] [INFO] VsSlyhXuVD=pass
[[08:32:33]] [SUCCESS] Screenshot refreshed successfully
[[08:32:33]] [SUCCESS] Screenshot refreshed successfully
[[08:32:31]] [INFO] VsSlyhXuVD=running
[[08:32:31]] [INFO] Executing action 259/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:32:31]] [SUCCESS] Screenshot refreshed
[[08:32:31]] [INFO] Refreshing screenshot...
[[08:32:31]] [INFO] CJ88OgjKXp=pass
[[08:32:27]] [SUCCESS] Screenshot refreshed successfully
[[08:32:27]] [SUCCESS] Screenshot refreshed successfully
[[08:32:27]] [INFO] CJ88OgjKXp=running
[[08:32:27]] [INFO] Executing action 258/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:32:26]] [SUCCESS] Screenshot refreshed
[[08:32:26]] [INFO] Refreshing screenshot...
[[08:32:26]] [INFO] AYiwFSLTBD=pass
[[08:32:24]] [SUCCESS] Screenshot refreshed successfully
[[08:32:24]] [SUCCESS] Screenshot refreshed successfully
[[08:32:24]] [INFO] AYiwFSLTBD=running
[[08:32:24]] [INFO] Executing action 257/576: iOS Function: alert_accept
[[08:32:23]] [SUCCESS] Screenshot refreshed
[[08:32:23]] [INFO] Refreshing screenshot...
[[08:32:23]] [INFO] HJzOYZNnGr=pass
[[08:32:17]] [SUCCESS] Screenshot refreshed successfully
[[08:32:17]] [SUCCESS] Screenshot refreshed successfully
[[08:32:17]] [INFO] HJzOYZNnGr=running
[[08:32:17]] [INFO] Executing action 256/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:32:16]] [SUCCESS] Screenshot refreshed
[[08:32:16]] [INFO] Refreshing screenshot...
[[08:32:16]] [INFO] taf19mtrUT=pass
[[08:32:13]] [SUCCESS] Screenshot refreshed successfully
[[08:32:13]] [SUCCESS] Screenshot refreshed successfully
[[08:32:12]] [INFO] taf19mtrUT=running
[[08:32:12]] [INFO] Executing action 255/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[08:32:12]] [SUCCESS] Screenshot refreshed
[[08:32:12]] [INFO] Refreshing screenshot...
[[08:32:12]] [INFO] oiPcknTonJ=pass
[[08:32:07]] [SUCCESS] Screenshot refreshed successfully
[[08:32:07]] [SUCCESS] Screenshot refreshed successfully
[[08:32:07]] [INFO] oiPcknTonJ=running
[[08:32:07]] [INFO] Executing action 254/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:32:07]] [SUCCESS] Screenshot refreshed
[[08:32:07]] [INFO] Refreshing screenshot...
[[08:32:07]] [INFO] FciJcOsMsB=pass
[[08:32:02]] [SUCCESS] Screenshot refreshed successfully
[[08:32:02]] [SUCCESS] Screenshot refreshed successfully
[[08:32:01]] [INFO] FciJcOsMsB=running
[[08:32:01]] [INFO] Executing action 253/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:32:01]] [SUCCESS] Screenshot refreshed
[[08:32:01]] [INFO] Refreshing screenshot...
[[08:32:01]] [INFO] 2qOXZcEmK8=pass
[[08:31:57]] [SUCCESS] Screenshot refreshed successfully
[[08:31:57]] [SUCCESS] Screenshot refreshed successfully
[[08:31:57]] [INFO] 2qOXZcEmK8=running
[[08:31:57]] [INFO] Executing action 252/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:31:56]] [SUCCESS] Screenshot refreshed
[[08:31:56]] [INFO] Refreshing screenshot...
[[08:31:56]] [INFO] M6HdLxu76S=pass
[[08:31:52]] [SUCCESS] Screenshot refreshed successfully
[[08:31:52]] [SUCCESS] Screenshot refreshed successfully
[[08:31:52]] [INFO] M6HdLxu76S=running
[[08:31:52]] [INFO] Executing action 251/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:31:51]] [SUCCESS] Screenshot refreshed
[[08:31:51]] [INFO] Refreshing screenshot...
[[08:31:51]] [INFO] pCPTAtSZbf=pass
[[08:31:47]] [SUCCESS] Screenshot refreshed successfully
[[08:31:47]] [SUCCESS] Screenshot refreshed successfully
[[08:31:47]] [INFO] pCPTAtSZbf=running
[[08:31:47]] [INFO] Executing action 250/576: iOS Function: text - Text: "Wonderbaby@5"
[[08:31:46]] [SUCCESS] Screenshot refreshed
[[08:31:46]] [INFO] Refreshing screenshot...
[[08:31:46]] [INFO] DaVBARRwft=pass
[[08:31:42]] [SUCCESS] Screenshot refreshed successfully
[[08:31:42]] [SUCCESS] Screenshot refreshed successfully
[[08:31:41]] [INFO] DaVBARRwft=running
[[08:31:41]] [INFO] Executing action 249/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[08:31:41]] [SUCCESS] Screenshot refreshed
[[08:31:41]] [INFO] Refreshing screenshot...
[[08:31:41]] [INFO] e1RoZWCZJb=pass
[[08:31:36]] [SUCCESS] Screenshot refreshed successfully
[[08:31:36]] [SUCCESS] Screenshot refreshed successfully
[[08:31:36]] [INFO] e1RoZWCZJb=running
[[08:31:36]] [INFO] Executing action 248/576: iOS Function: text - Text: "<EMAIL>"
[[08:31:36]] [SUCCESS] Screenshot refreshed
[[08:31:36]] [INFO] Refreshing screenshot...
[[08:31:36]] [INFO] y8ZMTkG38M=pass
[[08:31:32]] [SUCCESS] Screenshot refreshed successfully
[[08:31:32]] [SUCCESS] Screenshot refreshed successfully
[[08:31:31]] [INFO] y8ZMTkG38M=running
[[08:31:31]] [INFO] Executing action 247/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[08:31:31]] [SUCCESS] Screenshot refreshed
[[08:31:31]] [INFO] Refreshing screenshot...
[[08:31:31]] [INFO] UUhQjmzfO2=pass
[[08:31:25]] [SUCCESS] Screenshot refreshed successfully
[[08:31:25]] [SUCCESS] Screenshot refreshed successfully
[[08:31:25]] [INFO] UUhQjmzfO2=running
[[08:31:25]] [INFO] Executing action 246/576: Tap on Text: "OnePass"
[[08:31:25]] [SUCCESS] Screenshot refreshed
[[08:31:25]] [INFO] Refreshing screenshot...
[[08:31:25]] [INFO] FciJcOsMsB=pass
[[08:31:20]] [SUCCESS] Screenshot refreshed successfully
[[08:31:20]] [SUCCESS] Screenshot refreshed successfully
[[08:31:20]] [INFO] FciJcOsMsB=running
[[08:31:20]] [INFO] Executing action 245/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:31:19]] [SUCCESS] Screenshot refreshed
[[08:31:19]] [INFO] Refreshing screenshot...
[[08:31:19]] [INFO] NCyuT8W5Xz=pass
[[08:31:16]] [SUCCESS] Screenshot refreshed successfully
[[08:31:16]] [SUCCESS] Screenshot refreshed successfully
[[08:31:16]] [INFO] NCyuT8W5Xz=running
[[08:31:16]] [INFO] Executing action 244/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:31:15]] [SUCCESS] Screenshot refreshed
[[08:31:15]] [INFO] Refreshing screenshot...
[[08:31:15]] [INFO] 2kwu2VBmuZ=pass
[[08:31:13]] [SUCCESS] Screenshot refreshed successfully
[[08:31:13]] [SUCCESS] Screenshot refreshed successfully
[[08:31:12]] [INFO] 2kwu2VBmuZ=running
[[08:31:12]] [INFO] Executing action 243/576: iOS Function: alert_accept
[[08:31:12]] [SUCCESS] Screenshot refreshed
[[08:31:12]] [INFO] Refreshing screenshot...
[[08:31:12]] [INFO] cJDpd7aK3d=pass
[[08:31:06]] [SUCCESS] Screenshot refreshed successfully
[[08:31:06]] [SUCCESS] Screenshot refreshed successfully
[[08:31:06]] [INFO] cJDpd7aK3d=running
[[08:31:06]] [INFO] Executing action 242/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:31:05]] [SUCCESS] Screenshot refreshed
[[08:31:05]] [INFO] Refreshing screenshot...
[[08:31:05]] [INFO] FlEukNkjlS=pass
[[08:31:01]] [SUCCESS] Screenshot refreshed successfully
[[08:31:01]] [SUCCESS] Screenshot refreshed successfully
[[08:31:01]] [INFO] FlEukNkjlS=running
[[08:31:01]] [INFO] Executing action 241/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[08:31:00]] [SUCCESS] Screenshot refreshed
[[08:31:00]] [INFO] Refreshing screenshot...
[[08:31:00]] [INFO] LlRfimKPrn=pass
[[08:30:56]] [SUCCESS] Screenshot refreshed successfully
[[08:30:56]] [SUCCESS] Screenshot refreshed successfully
[[08:30:56]] [INFO] LlRfimKPrn=running
[[08:30:56]] [INFO] Executing action 240/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:30:55]] [SUCCESS] Screenshot refreshed
[[08:30:55]] [INFO] Refreshing screenshot...
[[08:30:55]] [INFO] FciJcOsMsB=pass
[[08:30:48]] [SUCCESS] Screenshot refreshed successfully
[[08:30:48]] [SUCCESS] Screenshot refreshed successfully
[[08:30:48]] [INFO] FciJcOsMsB=running
[[08:30:48]] [INFO] Executing action 239/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:30:48]] [SUCCESS] Screenshot refreshed
[[08:30:48]] [INFO] Refreshing screenshot...
[[08:30:48]] [INFO] 08NzsvhQXK=pass
[[08:30:44]] [SUCCESS] Screenshot refreshed successfully
[[08:30:44]] [SUCCESS] Screenshot refreshed successfully
[[08:30:44]] [INFO] 08NzsvhQXK=running
[[08:30:44]] [INFO] Executing action 238/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:30:43]] [SUCCESS] Screenshot refreshed
[[08:30:43]] [INFO] Refreshing screenshot...
[[08:30:43]] [INFO] IsGWxLFpIn=pass
[[08:30:40]] [SUCCESS] Screenshot refreshed successfully
[[08:30:40]] [SUCCESS] Screenshot refreshed successfully
[[08:30:40]] [INFO] IsGWxLFpIn=running
[[08:30:40]] [INFO] Executing action 237/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:30:39]] [SUCCESS] Screenshot refreshed
[[08:30:39]] [INFO] Refreshing screenshot...
[[08:30:39]] [INFO] dyECdbRifp=pass
[[08:30:34]] [SUCCESS] Screenshot refreshed successfully
[[08:30:34]] [SUCCESS] Screenshot refreshed successfully
[[08:30:34]] [INFO] dyECdbRifp=running
[[08:30:34]] [INFO] Executing action 236/576: iOS Function: text - Text: "Wonderbaby@5"
[[08:30:34]] [SUCCESS] Screenshot refreshed
[[08:30:34]] [INFO] Refreshing screenshot...
[[08:30:34]] [INFO] I5bRbYY1hD=pass
[[08:30:29]] [SUCCESS] Screenshot refreshed successfully
[[08:30:29]] [SUCCESS] Screenshot refreshed successfully
[[08:30:29]] [INFO] I5bRbYY1hD=running
[[08:30:29]] [INFO] Executing action 235/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:30:28]] [SUCCESS] Screenshot refreshed
[[08:30:28]] [INFO] Refreshing screenshot...
[[08:30:28]] [INFO] WMl5g82CCq=pass
[[08:30:23]] [SUCCESS] Screenshot refreshed successfully
[[08:30:23]] [SUCCESS] Screenshot refreshed successfully
[[08:30:23]] [INFO] WMl5g82CCq=running
[[08:30:23]] [INFO] Executing action 234/576: iOS Function: text - Text: "<EMAIL>"
[[08:30:22]] [SUCCESS] Screenshot refreshed
[[08:30:22]] [INFO] Refreshing screenshot...
[[08:30:22]] [INFO] 8OsQmoVYqW=pass
[[08:30:18]] [SUCCESS] Screenshot refreshed successfully
[[08:30:18]] [SUCCESS] Screenshot refreshed successfully
[[08:30:18]] [INFO] 8OsQmoVYqW=running
[[08:30:18]] [INFO] Executing action 233/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:30:17]] [SUCCESS] Screenshot refreshed
[[08:30:17]] [INFO] Refreshing screenshot...
[[08:30:17]] [INFO] ImienLpJEN=pass
[[08:30:14]] [SUCCESS] Screenshot refreshed successfully
[[08:30:14]] [SUCCESS] Screenshot refreshed successfully
[[08:30:14]] [INFO] ImienLpJEN=running
[[08:30:14]] [INFO] Executing action 232/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:30:13]] [SUCCESS] Screenshot refreshed
[[08:30:13]] [INFO] Refreshing screenshot...
[[08:30:13]] [INFO] q4hPXCBtx4=pass
[[08:30:11]] [SUCCESS] Screenshot refreshed successfully
[[08:30:11]] [SUCCESS] Screenshot refreshed successfully
[[08:30:10]] [INFO] q4hPXCBtx4=running
[[08:30:10]] [INFO] Executing action 231/576: iOS Function: alert_accept
[[08:30:10]] [SUCCESS] Screenshot refreshed
[[08:30:10]] [INFO] Refreshing screenshot...
[[08:30:10]] [INFO] 2cTZvK1psn=pass
[[08:30:04]] [SUCCESS] Screenshot refreshed successfully
[[08:30:04]] [SUCCESS] Screenshot refreshed successfully
[[08:30:04]] [INFO] 2cTZvK1psn=running
[[08:30:04]] [INFO] Executing action 230/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:30:03]] [SUCCESS] Screenshot refreshed
[[08:30:03]] [INFO] Refreshing screenshot...
[[08:30:03]] [INFO] Vxt7QOYeDD=pass
[[08:29:50]] [SUCCESS] Screenshot refreshed successfully
[[08:29:50]] [SUCCESS] Screenshot refreshed successfully
[[08:29:49]] [INFO] Vxt7QOYeDD=running
[[08:29:49]] [INFO] Executing action 229/576: Restart app: env[appid]
[[08:29:49]] [SUCCESS] Screenshot refreshed
[[08:29:49]] [INFO] Refreshing screenshot...
[[08:29:49]] [SUCCESS] Screenshot refreshed
[[08:29:49]] [INFO] Refreshing screenshot...
[[08:29:45]] [SUCCESS] Screenshot refreshed successfully
[[08:29:45]] [SUCCESS] Screenshot refreshed successfully
[[08:29:45]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:29:45]] [SUCCESS] Screenshot refreshed
[[08:29:45]] [INFO] Refreshing screenshot...
[[08:29:32]] [SUCCESS] Screenshot refreshed successfully
[[08:29:32]] [SUCCESS] Screenshot refreshed successfully
[[08:29:32]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:29:32]] [SUCCESS] Screenshot refreshed
[[08:29:32]] [INFO] Refreshing screenshot...
[[08:29:28]] [SUCCESS] Screenshot refreshed successfully
[[08:29:28]] [SUCCESS] Screenshot refreshed successfully
[[08:29:28]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:29:28]] [SUCCESS] Screenshot refreshed
[[08:29:28]] [INFO] Refreshing screenshot...
[[08:29:23]] [SUCCESS] Screenshot refreshed successfully
[[08:29:23]] [SUCCESS] Screenshot refreshed successfully
[[08:29:23]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:29:22]] [SUCCESS] Screenshot refreshed
[[08:29:22]] [INFO] Refreshing screenshot...
[[08:29:16]] [SUCCESS] Screenshot refreshed successfully
[[08:29:16]] [SUCCESS] Screenshot refreshed successfully
[[08:29:15]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:29:15]] [SUCCESS] Screenshot refreshed
[[08:29:15]] [INFO] Refreshing screenshot...
[[08:29:08]] [SUCCESS] Screenshot refreshed successfully
[[08:29:08]] [SUCCESS] Screenshot refreshed successfully
[[08:29:08]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:29:08]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:29:08]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:29:08]] [INFO] DYWpUY7xB6=running
[[08:29:08]] [INFO] Executing action 228/576: cleanupSteps action
[[08:29:07]] [SUCCESS] Screenshot refreshed
[[08:29:07]] [INFO] Refreshing screenshot...
[[08:29:07]] [INFO] OyUowAaBzD=pass
[[08:29:03]] [SUCCESS] Screenshot refreshed successfully
[[08:29:03]] [SUCCESS] Screenshot refreshed successfully
[[08:29:03]] [INFO] OyUowAaBzD=running
[[08:29:03]] [INFO] Executing action 227/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:29:02]] [SUCCESS] Screenshot refreshed
[[08:29:02]] [INFO] Refreshing screenshot...
[[08:29:02]] [INFO] Ob26qqcA0p=pass
[[08:28:55]] [SUCCESS] Screenshot refreshed successfully
[[08:28:55]] [SUCCESS] Screenshot refreshed successfully
[[08:28:55]] [INFO] Ob26qqcA0p=running
[[08:28:55]] [INFO] Executing action 226/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:28:55]] [SUCCESS] Screenshot refreshed
[[08:28:55]] [INFO] Refreshing screenshot...
[[08:28:55]] [INFO] k3mu9Mt7Ec=pass
[[08:28:51]] [SUCCESS] Screenshot refreshed successfully
[[08:28:51]] [SUCCESS] Screenshot refreshed successfully
[[08:28:51]] [INFO] k3mu9Mt7Ec=running
[[08:28:51]] [INFO] Executing action 225/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:28:50]] [SUCCESS] Screenshot refreshed
[[08:28:50]] [INFO] Refreshing screenshot...
[[08:28:50]] [INFO] yhmzeynQyu=pass
[[08:28:46]] [SUCCESS] Screenshot refreshed successfully
[[08:28:46]] [SUCCESS] Screenshot refreshed successfully
[[08:28:46]] [INFO] yhmzeynQyu=running
[[08:28:46]] [INFO] Executing action 224/576: Tap on Text: "Remove"
[[08:28:46]] [SUCCESS] Screenshot refreshed
[[08:28:46]] [INFO] Refreshing screenshot...
[[08:28:46]] [INFO] Q0fomJIDoQ=pass
[[08:28:40]] [SUCCESS] Screenshot refreshed successfully
[[08:28:40]] [SUCCESS] Screenshot refreshed successfully
[[08:28:40]] [INFO] Q0fomJIDoQ=running
[[08:28:40]] [INFO] Executing action 223/576: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[08:28:40]] [SUCCESS] Screenshot refreshed
[[08:28:40]] [INFO] Refreshing screenshot...
[[08:28:40]] [INFO] yhmzeynQyu=pass
[[08:28:35]] [SUCCESS] Screenshot refreshed successfully
[[08:28:35]] [SUCCESS] Screenshot refreshed successfully
[[08:28:35]] [INFO] yhmzeynQyu=running
[[08:28:35]] [INFO] Executing action 222/576: Tap on Text: "Remove"
[[08:28:35]] [SUCCESS] Screenshot refreshed
[[08:28:35]] [INFO] Refreshing screenshot...
[[08:28:35]] [INFO] Q0fomJIDoQ=pass
[[08:28:29]] [SUCCESS] Screenshot refreshed successfully
[[08:28:29]] [SUCCESS] Screenshot refreshed successfully
[[08:28:29]] [INFO] Q0fomJIDoQ=running
[[08:28:29]] [INFO] Executing action 221/576: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[08:28:28]] [SUCCESS] Screenshot refreshed
[[08:28:28]] [INFO] Refreshing screenshot...
[[08:28:28]] [INFO] F1olhgKhUt=pass
[[08:28:25]] [SUCCESS] Screenshot refreshed successfully
[[08:28:25]] [SUCCESS] Screenshot refreshed successfully
[[08:28:24]] [INFO] F1olhgKhUt=running
[[08:28:24]] [INFO] Executing action 220/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[08:28:24]] [SUCCESS] Screenshot refreshed
[[08:28:24]] [INFO] Refreshing screenshot...
[[08:28:24]] [INFO] 8umPSX0vrr=pass
[[08:28:19]] [SUCCESS] Screenshot refreshed successfully
[[08:28:19]] [SUCCESS] Screenshot refreshed successfully
[[08:28:19]] [INFO] 8umPSX0vrr=running
[[08:28:19]] [INFO] Executing action 219/576: Tap on image: banner-close-updated.png
[[08:28:19]] [SUCCESS] Screenshot refreshed
[[08:28:19]] [INFO] Refreshing screenshot...
[[08:28:19]] [INFO] pr9o8Zsm5p=pass
[[08:28:15]] [SUCCESS] Screenshot refreshed successfully
[[08:28:15]] [SUCCESS] Screenshot refreshed successfully
[[08:28:15]] [INFO] pr9o8Zsm5p=running
[[08:28:15]] [INFO] Executing action 218/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[08:28:14]] [SUCCESS] Screenshot refreshed
[[08:28:14]] [INFO] Refreshing screenshot...
[[08:28:14]] [INFO] Qbg9bipTGs=pass
[[08:28:11]] [SUCCESS] Screenshot refreshed successfully
[[08:28:11]] [SUCCESS] Screenshot refreshed successfully
[[08:28:11]] [INFO] Qbg9bipTGs=running
[[08:28:11]] [INFO] Executing action 217/576: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[08:28:10]] [SUCCESS] Screenshot refreshed
[[08:28:10]] [INFO] Refreshing screenshot...
[[08:28:10]] [INFO] Ob26qqcA0p=pass
[[08:28:06]] [SUCCESS] Screenshot refreshed successfully
[[08:28:06]] [SUCCESS] Screenshot refreshed successfully
[[08:28:06]] [INFO] Ob26qqcA0p=running
[[08:28:06]] [INFO] Executing action 216/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:28:05]] [SUCCESS] Screenshot refreshed
[[08:28:05]] [INFO] Refreshing screenshot...
[[08:28:05]] [INFO] lWIRxRm6HE=pass
[[08:28:01]] [SUCCESS] Screenshot refreshed successfully
[[08:28:01]] [SUCCESS] Screenshot refreshed successfully
[[08:28:00]] [INFO] lWIRxRm6HE=running
[[08:28:00]] [INFO] Executing action 215/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:28:00]] [SUCCESS] Screenshot refreshed
[[08:28:00]] [INFO] Refreshing screenshot...
[[08:28:00]] [INFO] uOt2cFGhGr=pass
[[08:27:56]] [SUCCESS] Screenshot refreshed successfully
[[08:27:56]] [SUCCESS] Screenshot refreshed successfully
[[08:27:56]] [INFO] uOt2cFGhGr=running
[[08:27:56]] [INFO] Executing action 214/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:27:56]] [SUCCESS] Screenshot refreshed
[[08:27:56]] [INFO] Refreshing screenshot...
[[08:27:56]] [INFO] Q0fomJIDoQ=pass
[[08:27:52]] [SUCCESS] Screenshot refreshed successfully
[[08:27:52]] [SUCCESS] Screenshot refreshed successfully
[[08:27:52]] [INFO] Q0fomJIDoQ=running
[[08:27:52]] [INFO] Executing action 213/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[08:27:51]] [SUCCESS] Screenshot refreshed
[[08:27:51]] [INFO] Refreshing screenshot...
[[08:27:51]] [INFO] yhmzeynQyu=pass
[[08:27:47]] [SUCCESS] Screenshot refreshed successfully
[[08:27:47]] [SUCCESS] Screenshot refreshed successfully
[[08:27:47]] [INFO] yhmzeynQyu=running
[[08:27:47]] [INFO] Executing action 212/576: Tap on Text: "Remove"
[[08:27:46]] [SUCCESS] Screenshot refreshed
[[08:27:46]] [INFO] Refreshing screenshot...
[[08:27:46]] [INFO] Q0fomJIDoQ=pass
[[08:27:42]] [SUCCESS] Screenshot refreshed successfully
[[08:27:42]] [SUCCESS] Screenshot refreshed successfully
[[08:27:42]] [INFO] Q0fomJIDoQ=running
[[08:27:42]] [INFO] Executing action 211/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[08:27:42]] [SUCCESS] Screenshot refreshed
[[08:27:42]] [INFO] Refreshing screenshot...
[[08:27:42]] [INFO] y4i304JeJj=pass
[[08:27:37]] [SUCCESS] Screenshot refreshed successfully
[[08:27:37]] [SUCCESS] Screenshot refreshed successfully
[[08:27:37]] [INFO] y4i304JeJj=running
[[08:27:37]] [INFO] Executing action 210/576: Tap on Text: "Move"
[[08:27:37]] [SUCCESS] Screenshot refreshed
[[08:27:37]] [INFO] Refreshing screenshot...
[[08:27:37]] [INFO] Q0fomJIDoQ=pass
[[08:27:33]] [SUCCESS] Screenshot refreshed successfully
[[08:27:33]] [SUCCESS] Screenshot refreshed successfully
[[08:27:33]] [INFO] Q0fomJIDoQ=running
[[08:27:33]] [INFO] Executing action 209/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[08:27:32]] [SUCCESS] Screenshot refreshed
[[08:27:32]] [INFO] Refreshing screenshot...
[[08:27:32]] [INFO] Q0fomJIDoQ=pass
[[08:27:29]] [SUCCESS] Screenshot refreshed successfully
[[08:27:29]] [SUCCESS] Screenshot refreshed successfully
[[08:27:29]] [INFO] Q0fomJIDoQ=running
[[08:27:29]] [INFO] Executing action 208/576: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[08:27:28]] [SUCCESS] Screenshot refreshed
[[08:27:28]] [INFO] Refreshing screenshot...
[[08:27:28]] [INFO] F1olhgKhUt=pass
[[08:27:24]] [SUCCESS] Screenshot refreshed successfully
[[08:27:24]] [SUCCESS] Screenshot refreshed successfully
[[08:27:23]] [INFO] F1olhgKhUt=running
[[08:27:23]] [INFO] Executing action 207/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[08:27:23]] [SUCCESS] Screenshot refreshed
[[08:27:23]] [INFO] Refreshing screenshot...
[[08:27:23]] [INFO] WbxRVpWtjw=pass
[[08:27:18]] [SUCCESS] Screenshot refreshed successfully
[[08:27:18]] [SUCCESS] Screenshot refreshed successfully
[[08:27:18]] [INFO] WbxRVpWtjw=running
[[08:27:18]] [INFO] Executing action 206/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[08:27:18]] [SUCCESS] Screenshot refreshed
[[08:27:18]] [INFO] Refreshing screenshot...
[[08:27:18]] [INFO] H3IAmq3r3i=pass
[[08:27:11]] [SUCCESS] Screenshot refreshed successfully
[[08:27:11]] [SUCCESS] Screenshot refreshed successfully
[[08:27:10]] [INFO] H3IAmq3r3i=running
[[08:27:10]] [INFO] Executing action 205/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[08:27:10]] [SUCCESS] Screenshot refreshed
[[08:27:10]] [INFO] Refreshing screenshot...
[[08:27:10]] [INFO] uOt2cFGhGr=pass
[[08:27:06]] [SUCCESS] Screenshot refreshed successfully
[[08:27:06]] [SUCCESS] Screenshot refreshed successfully
[[08:27:06]] [INFO] uOt2cFGhGr=running
[[08:27:06]] [INFO] Executing action 204/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:27:05]] [SUCCESS] Screenshot refreshed
[[08:27:05]] [INFO] Refreshing screenshot...
[[08:27:05]] [INFO] eLxHVWKeDQ=pass
[[08:27:02]] [SUCCESS] Screenshot refreshed successfully
[[08:27:02]] [SUCCESS] Screenshot refreshed successfully
[[08:27:01]] [INFO] eLxHVWKeDQ=running
[[08:27:01]] [INFO] Executing action 203/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:27:01]] [SUCCESS] Screenshot refreshed
[[08:27:01]] [INFO] Refreshing screenshot...
[[08:27:01]] [INFO] ghzdMuwrHj=pass
[[08:26:56]] [SUCCESS] Screenshot refreshed successfully
[[08:26:56]] [SUCCESS] Screenshot refreshed successfully
[[08:26:56]] [INFO] ghzdMuwrHj=running
[[08:26:56]] [INFO] Executing action 202/576: iOS Function: text - Text: "P_43386093"
[[08:26:56]] [SUCCESS] Screenshot refreshed
[[08:26:56]] [INFO] Refreshing screenshot...
[[08:26:56]] [INFO] fMzoZJg9I7=pass
[[08:26:51]] [SUCCESS] Screenshot refreshed successfully
[[08:26:51]] [SUCCESS] Screenshot refreshed successfully
[[08:26:50]] [INFO] fMzoZJg9I7=running
[[08:26:50]] [INFO] Executing action 201/576: Tap on Text: "Find"
[[08:26:50]] [SUCCESS] Screenshot refreshed
[[08:26:50]] [INFO] Refreshing screenshot...
[[08:26:50]] [INFO] j1JjmfPRaE=pass
[[08:26:45]] [SUCCESS] Screenshot refreshed successfully
[[08:26:45]] [SUCCESS] Screenshot refreshed successfully
[[08:26:44]] [INFO] j1JjmfPRaE=running
[[08:26:44]] [INFO] Executing action 200/576: Restart app: env[appid]
[[08:26:44]] [SUCCESS] Screenshot refreshed
[[08:26:44]] [INFO] Refreshing screenshot...
[[08:26:44]] [INFO] WbxRVpWtjw=pass
[[08:26:40]] [SUCCESS] Screenshot refreshed successfully
[[08:26:40]] [SUCCESS] Screenshot refreshed successfully
[[08:26:40]] [INFO] WbxRVpWtjw=running
[[08:26:40]] [INFO] Executing action 199/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[08:26:39]] [SUCCESS] Screenshot refreshed
[[08:26:39]] [INFO] Refreshing screenshot...
[[08:26:39]] [INFO] H3IAmq3r3i=pass
[[08:26:33]] [SUCCESS] Screenshot refreshed successfully
[[08:26:33]] [SUCCESS] Screenshot refreshed successfully
[[08:26:33]] [INFO] H3IAmq3r3i=running
[[08:26:33]] [INFO] Executing action 198/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[08:26:32]] [SUCCESS] Screenshot refreshed
[[08:26:32]] [INFO] Refreshing screenshot...
[[08:26:32]] [INFO] ITHvSyXXmu=pass
[[08:26:29]] [SUCCESS] Screenshot refreshed successfully
[[08:26:29]] [SUCCESS] Screenshot refreshed successfully
[[08:26:28]] [INFO] ITHvSyXXmu=running
[[08:26:28]] [INFO] Executing action 197/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:26:28]] [SUCCESS] Screenshot refreshed
[[08:26:28]] [INFO] Refreshing screenshot...
[[08:26:28]] [INFO] eLxHVWKeDQ=pass
[[08:26:12]] [SUCCESS] Screenshot refreshed successfully
[[08:26:12]] [SUCCESS] Screenshot refreshed successfully
[[08:26:12]] [INFO] eLxHVWKeDQ=running
[[08:26:12]] [INFO] Executing action 196/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[08:26:11]] [SUCCESS] Screenshot refreshed
[[08:26:11]] [INFO] Refreshing screenshot...
[[08:26:11]] [INFO] WbxRVpWtjw=pass
[[08:26:07]] [SUCCESS] Screenshot refreshed successfully
[[08:26:07]] [SUCCESS] Screenshot refreshed successfully
[[08:26:07]] [INFO] WbxRVpWtjw=running
[[08:26:07]] [INFO] Executing action 195/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[08:26:06]] [SUCCESS] Screenshot refreshed
[[08:26:06]] [INFO] Refreshing screenshot...
[[08:26:06]] [INFO] H3IAmq3r3i=pass
[[08:25:59]] [SUCCESS] Screenshot refreshed successfully
[[08:25:59]] [SUCCESS] Screenshot refreshed successfully
[[08:25:59]] [INFO] H3IAmq3r3i=running
[[08:25:59]] [INFO] Executing action 194/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[08:25:58]] [SUCCESS] Screenshot refreshed
[[08:25:58]] [INFO] Refreshing screenshot...
[[08:25:58]] [INFO] ITHvSyXXmu=pass
[[08:25:53]] [SUCCESS] Screenshot refreshed successfully
[[08:25:53]] [SUCCESS] Screenshot refreshed successfully
[[08:25:53]] [INFO] ITHvSyXXmu=running
[[08:25:53]] [INFO] Executing action 193/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:25:53]] [SUCCESS] Screenshot refreshed
[[08:25:53]] [INFO] Refreshing screenshot...
[[08:25:53]] [INFO] eLxHVWKeDQ=pass
[[08:25:49]] [SUCCESS] Screenshot refreshed successfully
[[08:25:49]] [SUCCESS] Screenshot refreshed successfully
[[08:25:48]] [INFO] eLxHVWKeDQ=running
[[08:25:48]] [INFO] Executing action 192/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:25:48]] [SUCCESS] Screenshot refreshed
[[08:25:48]] [INFO] Refreshing screenshot...
[[08:25:48]] [INFO] nAB6Q8LAdv=pass
[[08:25:44]] [SUCCESS] Screenshot refreshed successfully
[[08:25:44]] [SUCCESS] Screenshot refreshed successfully
[[08:25:44]] [INFO] nAB6Q8LAdv=running
[[08:25:44]] [INFO] Executing action 191/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:25:43]] [SUCCESS] Screenshot refreshed
[[08:25:43]] [INFO] Refreshing screenshot...
[[08:25:43]] [INFO] sc2KH9bG6H=pass
[[08:25:39]] [SUCCESS] Screenshot refreshed successfully
[[08:25:39]] [SUCCESS] Screenshot refreshed successfully
[[08:25:39]] [INFO] sc2KH9bG6H=running
[[08:25:39]] [INFO] Executing action 190/576: iOS Function: text - Text: "Uno card"
[[08:25:38]] [SUCCESS] Screenshot refreshed
[[08:25:38]] [INFO] Refreshing screenshot...
[[08:25:38]] [INFO] rqLJpAP0mA=pass
[[08:25:33]] [SUCCESS] Screenshot refreshed successfully
[[08:25:33]] [SUCCESS] Screenshot refreshed successfully
[[08:25:33]] [INFO] rqLJpAP0mA=running
[[08:25:33]] [INFO] Executing action 189/576: Tap on Text: "Find"
[[08:25:32]] [SUCCESS] Screenshot refreshed
[[08:25:32]] [INFO] Refreshing screenshot...
[[08:25:32]] [INFO] yiKyF5FJwN=pass
[[08:25:28]] [SUCCESS] Screenshot refreshed successfully
[[08:25:28]] [SUCCESS] Screenshot refreshed successfully
[[08:25:28]] [INFO] yiKyF5FJwN=running
[[08:25:28]] [INFO] Executing action 188/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:25:27]] [SUCCESS] Screenshot refreshed
[[08:25:27]] [INFO] Refreshing screenshot...
[[08:25:27]] [INFO] YqMEb5Jr6o=pass
[[08:25:22]] [SUCCESS] Screenshot refreshed successfully
[[08:25:22]] [SUCCESS] Screenshot refreshed successfully
[[08:25:22]] [INFO] YqMEb5Jr6o=running
[[08:25:22]] [INFO] Executing action 187/576: iOS Function: text - Text: "Wonderbaby@6"
[[08:25:21]] [SUCCESS] Screenshot refreshed
[[08:25:21]] [INFO] Refreshing screenshot...
[[08:25:21]] [INFO] T3MmUw30SF=pass
[[08:25:17]] [SUCCESS] Screenshot refreshed successfully
[[08:25:17]] [SUCCESS] Screenshot refreshed successfully
[[08:25:17]] [INFO] T3MmUw30SF=running
[[08:25:17]] [INFO] Executing action 186/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:25:16]] [SUCCESS] Screenshot refreshed
[[08:25:16]] [INFO] Refreshing screenshot...
[[08:25:16]] [INFO] 3FBGGKUMbh=pass
[[08:25:11]] [SUCCESS] Screenshot refreshed successfully
[[08:25:11]] [SUCCESS] Screenshot refreshed successfully
[[08:25:11]] [INFO] 3FBGGKUMbh=running
[[08:25:11]] [INFO] Executing action 185/576: iOS Function: text - Text: "env[uname-op]"
[[08:25:11]] [SUCCESS] Screenshot refreshed
[[08:25:11]] [INFO] Refreshing screenshot...
[[08:25:11]] [INFO] LDkFLWks00=pass
[[08:25:07]] [SUCCESS] Screenshot refreshed successfully
[[08:25:07]] [SUCCESS] Screenshot refreshed successfully
[[08:25:07]] [INFO] LDkFLWks00=running
[[08:25:07]] [INFO] Executing action 184/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:25:06]] [SUCCESS] Screenshot refreshed
[[08:25:06]] [INFO] Refreshing screenshot...
[[08:25:06]] [INFO] 3caMBvQX7k=pass
[[08:25:03]] [SUCCESS] Screenshot refreshed successfully
[[08:25:03]] [SUCCESS] Screenshot refreshed successfully
[[08:25:02]] [INFO] 3caMBvQX7k=running
[[08:25:02]] [INFO] Executing action 183/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:25:02]] [SUCCESS] Screenshot refreshed
[[08:25:02]] [INFO] Refreshing screenshot...
[[08:25:02]] [INFO] yUJyVO5Wev=pass
[[08:25:00]] [SUCCESS] Screenshot refreshed successfully
[[08:25:00]] [SUCCESS] Screenshot refreshed successfully
[[08:24:59]] [INFO] yUJyVO5Wev=running
[[08:24:59]] [INFO] Executing action 182/576: iOS Function: alert_accept
[[08:24:59]] [SUCCESS] Screenshot refreshed
[[08:24:59]] [INFO] Refreshing screenshot...
[[08:24:59]] [INFO] rkL0oz4kiL=pass
[[08:24:51]] [SUCCESS] Screenshot refreshed successfully
[[08:24:51]] [SUCCESS] Screenshot refreshed successfully
[[08:24:51]] [INFO] rkL0oz4kiL=running
[[08:24:51]] [INFO] Executing action 181/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:24:50]] [SUCCESS] Screenshot refreshed
[[08:24:50]] [INFO] Refreshing screenshot...
[[08:24:50]] [INFO] HotUJOd6oB=pass
[[08:24:37]] [SUCCESS] Screenshot refreshed successfully
[[08:24:37]] [SUCCESS] Screenshot refreshed successfully
[[08:24:36]] [INFO] HotUJOd6oB=running
[[08:24:36]] [INFO] Executing action 180/576: Restart app: env[appid]
[[08:24:36]] [SUCCESS] Screenshot refreshed
[[08:24:36]] [INFO] Refreshing screenshot...
[[08:24:36]] [SUCCESS] Screenshot refreshed
[[08:24:36]] [INFO] Refreshing screenshot...
[[08:24:32]] [SUCCESS] Screenshot refreshed successfully
[[08:24:32]] [SUCCESS] Screenshot refreshed successfully
[[08:24:32]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:24:32]] [SUCCESS] Screenshot refreshed
[[08:24:32]] [INFO] Refreshing screenshot...
[[08:24:18]] [SUCCESS] Screenshot refreshed successfully
[[08:24:18]] [SUCCESS] Screenshot refreshed successfully
[[08:24:18]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:24:18]] [SUCCESS] Screenshot refreshed
[[08:24:18]] [INFO] Refreshing screenshot...
[[08:24:14]] [SUCCESS] Screenshot refreshed successfully
[[08:24:14]] [SUCCESS] Screenshot refreshed successfully
[[08:24:14]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:24:14]] [SUCCESS] Screenshot refreshed
[[08:24:14]] [INFO] Refreshing screenshot...
[[08:24:10]] [SUCCESS] Screenshot refreshed successfully
[[08:24:10]] [SUCCESS] Screenshot refreshed successfully
[[08:24:09]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:24:09]] [SUCCESS] Screenshot refreshed
[[08:24:09]] [INFO] Refreshing screenshot...
[[08:24:02]] [SUCCESS] Screenshot refreshed successfully
[[08:24:02]] [SUCCESS] Screenshot refreshed successfully
[[08:24:02]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:24:01]] [SUCCESS] Screenshot refreshed
[[08:24:01]] [INFO] Refreshing screenshot...
[[08:23:55]] [SUCCESS] Screenshot refreshed successfully
[[08:23:55]] [SUCCESS] Screenshot refreshed successfully
[[08:23:54]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:23:54]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:23:54]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:23:54]] [INFO] IR7wnjW7C8=running
[[08:23:54]] [INFO] Executing action 179/576: cleanupSteps action
[[08:23:54]] [SUCCESS] Screenshot refreshed
[[08:23:54]] [INFO] Refreshing screenshot...
[[08:23:54]] [INFO] 7WYExJTqjp=pass
[[08:23:49]] [SUCCESS] Screenshot refreshed successfully
[[08:23:49]] [SUCCESS] Screenshot refreshed successfully
[[08:23:49]] [INFO] 7WYExJTqjp=running
[[08:23:49]] [INFO] Executing action 178/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:23:49]] [SUCCESS] Screenshot refreshed
[[08:23:49]] [INFO] Refreshing screenshot...
[[08:23:49]] [INFO] 4WfPFN961S=pass
[[08:23:42]] [SUCCESS] Screenshot refreshed successfully
[[08:23:42]] [SUCCESS] Screenshot refreshed successfully
[[08:23:42]] [INFO] 4WfPFN961S=running
[[08:23:42]] [INFO] Executing action 177/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:23:42]] [SUCCESS] Screenshot refreshed
[[08:23:42]] [INFO] Refreshing screenshot...
[[08:23:42]] [INFO] NurQsFoMkE=pass
[[08:23:38]] [SUCCESS] Screenshot refreshed successfully
[[08:23:38]] [SUCCESS] Screenshot refreshed successfully
[[08:23:37]] [INFO] NurQsFoMkE=running
[[08:23:37]] [INFO] Executing action 176/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:23:37]] [SUCCESS] Screenshot refreshed
[[08:23:37]] [INFO] Refreshing screenshot...
[[08:23:37]] [INFO] CkfAScJNq8=pass
[[08:23:33]] [SUCCESS] Screenshot refreshed successfully
[[08:23:33]] [SUCCESS] Screenshot refreshed successfully
[[08:23:33]] [INFO] CkfAScJNq8=running
[[08:23:33]] [INFO] Executing action 175/576: Tap on image: env[closebtnimage]
[[08:23:32]] [SUCCESS] Screenshot refreshed
[[08:23:32]] [INFO] Refreshing screenshot...
[[08:23:32]] [INFO] 1NWfFsDiTQ=pass
[[08:23:28]] [SUCCESS] Screenshot refreshed successfully
[[08:23:28]] [SUCCESS] Screenshot refreshed successfully
[[08:23:28]] [INFO] 1NWfFsDiTQ=running
[[08:23:28]] [INFO] Executing action 174/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:23:28]] [SUCCESS] Screenshot refreshed
[[08:23:28]] [INFO] Refreshing screenshot...
[[08:23:28]] [INFO] tufIibCj03=pass
[[08:23:23]] [SUCCESS] Screenshot refreshed successfully
[[08:23:23]] [SUCCESS] Screenshot refreshed successfully
[[08:23:23]] [INFO] tufIibCj03=running
[[08:23:23]] [INFO] Executing action 173/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:23:22]] [SUCCESS] Screenshot refreshed
[[08:23:22]] [INFO] Refreshing screenshot...
[[08:23:22]] [INFO] XryN8qR1DX=pass
[[08:23:19]] [SUCCESS] Screenshot refreshed successfully
[[08:23:19]] [SUCCESS] Screenshot refreshed successfully
[[08:23:18]] [INFO] XryN8qR1DX=running
[[08:23:18]] [INFO] Executing action 172/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:23:18]] [SUCCESS] Screenshot refreshed
[[08:23:18]] [INFO] Refreshing screenshot...
[[08:23:18]] [INFO] CkfAScJNq8=pass
[[08:23:14]] [SUCCESS] Screenshot refreshed successfully
[[08:23:14]] [SUCCESS] Screenshot refreshed successfully
[[08:23:13]] [INFO] CkfAScJNq8=running
[[08:23:13]] [INFO] Executing action 171/576: Tap on image: env[closebtnimage]
[[08:23:13]] [SUCCESS] Screenshot refreshed
[[08:23:13]] [INFO] Refreshing screenshot...
[[08:23:13]] [SUCCESS] Screenshot refreshed successfully
[[08:23:13]] [SUCCESS] Screenshot refreshed successfully
[[08:23:13]] [SUCCESS] Screenshot refreshed
[[08:23:13]] [INFO] Refreshing screenshot...
[[08:23:08]] [SUCCESS] Screenshot refreshed successfully
[[08:23:08]] [SUCCESS] Screenshot refreshed successfully
[[08:23:08]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:23:08]] [SUCCESS] Screenshot refreshed
[[08:23:08]] [INFO] Refreshing screenshot...
[[08:23:04]] [SUCCESS] Screenshot refreshed successfully
[[08:23:04]] [SUCCESS] Screenshot refreshed successfully
[[08:23:04]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:23:03]] [SUCCESS] Screenshot refreshed
[[08:23:03]] [INFO] Refreshing screenshot...
[[08:22:58]] [SUCCESS] Screenshot refreshed successfully
[[08:22:58]] [SUCCESS] Screenshot refreshed successfully
[[08:22:58]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:22:58]] [SUCCESS] Screenshot refreshed
[[08:22:58]] [INFO] Refreshing screenshot...
[[08:22:53]] [SUCCESS] Screenshot refreshed successfully
[[08:22:53]] [SUCCESS] Screenshot refreshed successfully
[[08:22:53]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:22:52]] [SUCCESS] Screenshot refreshed
[[08:22:52]] [INFO] Refreshing screenshot...
[[08:22:47]] [SUCCESS] Screenshot refreshed successfully
[[08:22:47]] [SUCCESS] Screenshot refreshed successfully
[[08:22:47]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:22:47]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:22:46]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:22:46]] [INFO] mWOCt0aAWW=running
[[08:22:46]] [INFO] Executing action 170/576: Execute Test Case: Kmart-Signin (5 steps)
[[08:22:46]] [SUCCESS] Screenshot refreshed
[[08:22:46]] [INFO] Refreshing screenshot...
[[08:22:46]] [INFO] q9ZiyYoE5B=pass
[[08:22:44]] [SUCCESS] Screenshot refreshed successfully
[[08:22:44]] [SUCCESS] Screenshot refreshed successfully
[[08:22:43]] [INFO] q9ZiyYoE5B=running
[[08:22:43]] [INFO] Executing action 169/576: iOS Function: alert_accept
[[08:22:43]] [SUCCESS] Screenshot refreshed
[[08:22:43]] [INFO] Refreshing screenshot...
[[08:22:43]] [INFO] STEdg5jOU8=pass
[[08:22:38]] [SUCCESS] Screenshot refreshed successfully
[[08:22:38]] [SUCCESS] Screenshot refreshed successfully
[[08:22:38]] [INFO] STEdg5jOU8=running
[[08:22:38]] [INFO] Executing action 168/576: Tap on Text: "in"
[[08:22:37]] [SUCCESS] Screenshot refreshed
[[08:22:37]] [INFO] Refreshing screenshot...
[[08:22:37]] [INFO] LDH2hlTZT9=pass
[[08:22:30]] [SUCCESS] Screenshot refreshed successfully
[[08:22:30]] [SUCCESS] Screenshot refreshed successfully
[[08:22:30]] [INFO] LDH2hlTZT9=running
[[08:22:30]] [INFO] Executing action 167/576: Wait for 5 ms
[[08:22:30]] [SUCCESS] Screenshot refreshed
[[08:22:30]] [INFO] Refreshing screenshot...
[[08:22:30]] [INFO] 5Dk9h5bQWl=pass
[[08:22:23]] [SUCCESS] Screenshot refreshed successfully
[[08:22:23]] [SUCCESS] Screenshot refreshed successfully
[[08:22:23]] [INFO] 5Dk9h5bQWl=running
[[08:22:23]] [INFO] Executing action 166/576: Tap on element with accessibility_id: Continue to details
[[08:22:23]] [SUCCESS] Screenshot refreshed
[[08:22:23]] [INFO] Refreshing screenshot...
[[08:22:23]] [INFO] VMzFZ2uTwl=pass
[[08:22:16]] [SUCCESS] Screenshot refreshed successfully
[[08:22:16]] [SUCCESS] Screenshot refreshed successfully
[[08:22:14]] [INFO] VMzFZ2uTwl=running
[[08:22:14]] [INFO] Executing action 165/576: Swipe up till element accessibilityid: "Continue to details" is visible
[[08:22:14]] [SUCCESS] Screenshot refreshed
[[08:22:14]] [INFO] Refreshing screenshot...
[[08:22:14]] [SUCCESS] Screenshot refreshed
[[08:22:14]] [INFO] Refreshing screenshot...
[[08:22:10]] [INFO] Executing Multi Step action step 6/6: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:22:10]] [SUCCESS] Screenshot refreshed successfully
[[08:22:10]] [SUCCESS] Screenshot refreshed successfully
[[08:22:10]] [SUCCESS] Screenshot refreshed
[[08:22:10]] [INFO] Refreshing screenshot...
[[08:22:06]] [SUCCESS] Screenshot refreshed successfully
[[08:22:06]] [SUCCESS] Screenshot refreshed successfully
[[08:22:06]] [INFO] Executing Multi Step action step 5/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:22:05]] [SUCCESS] Screenshot refreshed
[[08:22:05]] [INFO] Refreshing screenshot...
[[08:22:00]] [SUCCESS] Screenshot refreshed successfully
[[08:22:00]] [SUCCESS] Screenshot refreshed successfully
[[08:22:00]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[08:21:59]] [SUCCESS] Screenshot refreshed
[[08:21:59]] [INFO] Refreshing screenshot...
[[08:21:56]] [SUCCESS] Screenshot refreshed successfully
[[08:21:56]] [SUCCESS] Screenshot refreshed successfully
[[08:21:55]] [INFO] Executing Multi Step action step 3/6: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:21:55]] [SUCCESS] Screenshot refreshed
[[08:21:55]] [INFO] Refreshing screenshot...
[[08:21:50]] [SUCCESS] Screenshot refreshed successfully
[[08:21:50]] [SUCCESS] Screenshot refreshed successfully
[[08:21:50]] [INFO] Executing Multi Step action step 2/6: iOS Function: text - Text: "Notebook"
[[08:21:50]] [SUCCESS] Screenshot refreshed
[[08:21:50]] [INFO] Refreshing screenshot...
[[08:21:42]] [SUCCESS] Screenshot refreshed successfully
[[08:21:42]] [SUCCESS] Screenshot refreshed successfully
[[08:21:42]] [INFO] Executing Multi Step action step 1/6: Tap on Text: "Find"
[[08:21:42]] [INFO] Loaded 6 steps from test case: Search and Add (Notebooks)
[[08:21:42]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[08:21:42]] [INFO] 8HTspxuvVG=running
[[08:21:42]] [INFO] Executing action 164/576: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[08:21:41]] [SUCCESS] Screenshot refreshed
[[08:21:41]] [INFO] Refreshing screenshot...
[[08:21:41]] [INFO] NurQsFoMkE=pass
[[08:21:37]] [SUCCESS] Screenshot refreshed successfully
[[08:21:37]] [SUCCESS] Screenshot refreshed successfully
[[08:21:37]] [INFO] NurQsFoMkE=running
[[08:21:37]] [INFO] Executing action 163/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:21:36]] [SUCCESS] Screenshot refreshed
[[08:21:36]] [INFO] Refreshing screenshot...
[[08:21:36]] [INFO] 7QpmNS6hif=pass
[[08:21:32]] [SUCCESS] Screenshot refreshed successfully
[[08:21:32]] [SUCCESS] Screenshot refreshed successfully
[[08:21:31]] [INFO] 7QpmNS6hif=running
[[08:21:31]] [INFO] Executing action 162/576: Restart app: env[appid]
[[08:21:30]] [SUCCESS] Screenshot refreshed
[[08:21:30]] [INFO] Refreshing screenshot...
[[08:21:30]] [INFO] 7WYExJTqjp=pass
[[08:21:26]] [SUCCESS] Screenshot refreshed successfully
[[08:21:26]] [SUCCESS] Screenshot refreshed successfully
[[08:21:26]] [INFO] 7WYExJTqjp=running
[[08:21:26]] [INFO] Executing action 161/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:21:26]] [SUCCESS] Screenshot refreshed
[[08:21:26]] [INFO] Refreshing screenshot...
[[08:21:26]] [INFO] 4WfPFN961S=pass
[[08:21:19]] [SUCCESS] Screenshot refreshed successfully
[[08:21:19]] [SUCCESS] Screenshot refreshed successfully
[[08:21:19]] [INFO] 4WfPFN961S=running
[[08:21:19]] [INFO] Executing action 160/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:21:18]] [SUCCESS] Screenshot refreshed
[[08:21:18]] [INFO] Refreshing screenshot...
[[08:21:18]] [INFO] NurQsFoMkE=pass
[[08:21:14]] [SUCCESS] Screenshot refreshed successfully
[[08:21:14]] [SUCCESS] Screenshot refreshed successfully
[[08:21:14]] [INFO] NurQsFoMkE=running
[[08:21:14]] [INFO] Executing action 159/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:21:13]] [SUCCESS] Screenshot refreshed
[[08:21:13]] [INFO] Refreshing screenshot...
[[08:21:13]] [INFO] CkfAScJNq8=pass
[[08:21:09]] [SUCCESS] Screenshot refreshed successfully
[[08:21:09]] [SUCCESS] Screenshot refreshed successfully
[[08:21:09]] [INFO] CkfAScJNq8=running
[[08:21:09]] [INFO] Executing action 158/576: Tap on image: env[closebtnimage]
[[08:21:08]] [SUCCESS] Screenshot refreshed
[[08:21:08]] [INFO] Refreshing screenshot...
[[08:21:08]] [INFO] 1NWfFsDiTQ=pass
[[08:21:04]] [SUCCESS] Screenshot refreshed successfully
[[08:21:04]] [SUCCESS] Screenshot refreshed successfully
[[08:21:04]] [INFO] 1NWfFsDiTQ=running
[[08:21:04]] [INFO] Executing action 157/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:21:04]] [SUCCESS] Screenshot refreshed
[[08:21:04]] [INFO] Refreshing screenshot...
[[08:21:04]] [INFO] tufIibCj03=pass
[[08:20:59]] [SUCCESS] Screenshot refreshed successfully
[[08:20:59]] [SUCCESS] Screenshot refreshed successfully
[[08:20:59]] [INFO] tufIibCj03=running
[[08:20:59]] [INFO] Executing action 156/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:20:58]] [SUCCESS] Screenshot refreshed
[[08:20:58]] [INFO] Refreshing screenshot...
[[08:20:58]] [INFO] g8u66qfKkX=pass
[[08:20:55]] [SUCCESS] Screenshot refreshed successfully
[[08:20:55]] [SUCCESS] Screenshot refreshed successfully
[[08:20:55]] [INFO] g8u66qfKkX=running
[[08:20:55]] [INFO] Executing action 155/576: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:20:54]] [SUCCESS] Screenshot refreshed
[[08:20:54]] [INFO] Refreshing screenshot...
[[08:20:54]] [INFO] XryN8qR1DX=pass
[[08:20:51]] [SUCCESS] Screenshot refreshed successfully
[[08:20:51]] [SUCCESS] Screenshot refreshed successfully
[[08:20:50]] [INFO] XryN8qR1DX=running
[[08:20:50]] [INFO] Executing action 154/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:20:50]] [SUCCESS] Screenshot refreshed
[[08:20:50]] [INFO] Refreshing screenshot...
[[08:20:50]] [INFO] CkfAScJNq8=pass
[[08:20:45]] [SUCCESS] Screenshot refreshed successfully
[[08:20:45]] [SUCCESS] Screenshot refreshed successfully
[[08:20:45]] [INFO] CkfAScJNq8=running
[[08:20:45]] [INFO] Executing action 153/576: Tap on image: env[closebtnimage]
[[08:20:45]] [SUCCESS] Screenshot refreshed
[[08:20:45]] [INFO] Refreshing screenshot...
[[08:20:45]] [INFO] g8u66qfKkX=pass
[[08:20:37]] [INFO] g8u66qfKkX=running
[[08:20:37]] [INFO] Executing action 152/576: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:20:37]] [SUCCESS] Screenshot refreshed successfully
[[08:20:37]] [SUCCESS] Screenshot refreshed successfully
[[08:20:37]] [SUCCESS] Screenshot refreshed
[[08:20:37]] [INFO] Refreshing screenshot...
[[08:20:37]] [INFO] pCPTAtSZbf=pass
[[08:20:32]] [SUCCESS] Screenshot refreshed successfully
[[08:20:32]] [SUCCESS] Screenshot refreshed successfully
[[08:20:32]] [INFO] pCPTAtSZbf=running
[[08:20:32]] [INFO] Executing action 151/576: iOS Function: text - Text: "Wonderbaby@5"
[[08:20:32]] [SUCCESS] Screenshot refreshed
[[08:20:32]] [INFO] Refreshing screenshot...
[[08:20:32]] [INFO] DaVBARRwft=pass
[[08:20:27]] [SUCCESS] Screenshot refreshed successfully
[[08:20:27]] [SUCCESS] Screenshot refreshed successfully
[[08:20:27]] [INFO] DaVBARRwft=running
[[08:20:27]] [INFO] Executing action 150/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[08:20:27]] [SUCCESS] Screenshot refreshed
[[08:20:27]] [INFO] Refreshing screenshot...
[[08:20:27]] [INFO] e1RoZWCZJb=pass
[[08:20:21]] [SUCCESS] Screenshot refreshed successfully
[[08:20:21]] [SUCCESS] Screenshot refreshed successfully
[[08:20:21]] [INFO] e1RoZWCZJb=running
[[08:20:21]] [INFO] Executing action 149/576: iOS Function: text - Text: "<EMAIL>"
[[08:20:21]] [SUCCESS] Screenshot refreshed
[[08:20:21]] [INFO] Refreshing screenshot...
[[08:20:21]] [INFO] 50Z2jrodNd=pass
[[08:20:17]] [SUCCESS] Screenshot refreshed successfully
[[08:20:17]] [SUCCESS] Screenshot refreshed successfully
[[08:20:16]] [INFO] 50Z2jrodNd=running
[[08:20:16]] [INFO] Executing action 148/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:20:16]] [SUCCESS] Screenshot refreshed
[[08:20:16]] [INFO] Refreshing screenshot...
[[08:20:16]] [INFO] q9ZiyYoE5B=pass
[[08:20:14]] [SUCCESS] Screenshot refreshed successfully
[[08:20:14]] [SUCCESS] Screenshot refreshed successfully
[[08:20:13]] [INFO] q9ZiyYoE5B=running
[[08:20:13]] [INFO] Executing action 147/576: iOS Function: alert_accept
[[08:20:13]] [SUCCESS] Screenshot refreshed
[[08:20:13]] [INFO] Refreshing screenshot...
[[08:20:13]] [INFO] 6PL8P3rT57=pass
[[08:20:08]] [SUCCESS] Screenshot refreshed successfully
[[08:20:08]] [SUCCESS] Screenshot refreshed successfully
[[08:20:08]] [INFO] 6PL8P3rT57=running
[[08:20:08]] [INFO] Executing action 146/576: Tap on Text: "Sign"
[[08:20:07]] [SUCCESS] Screenshot refreshed
[[08:20:07]] [INFO] Refreshing screenshot...
[[08:20:07]] [INFO] 2YGctqXNED=pass
[[08:20:00]] [SUCCESS] Screenshot refreshed successfully
[[08:20:00]] [SUCCESS] Screenshot refreshed successfully
[[08:20:00]] [INFO] 2YGctqXNED=running
[[08:20:00]] [INFO] Executing action 145/576: Tap on element with accessibility_id: Continue to details
[[08:20:00]] [SUCCESS] Screenshot refreshed
[[08:20:00]] [INFO] Refreshing screenshot...
[[08:20:00]] [INFO] 2YGctqXNED=pass
[[08:19:51]] [SUCCESS] Screenshot refreshed successfully
[[08:19:51]] [SUCCESS] Screenshot refreshed successfully
[[08:19:51]] [INFO] 2YGctqXNED=running
[[08:19:51]] [INFO] Executing action 144/576: Swipe up till element accessibilityid: "Continue to details" is visible
[[08:19:51]] [SUCCESS] Screenshot refreshed
[[08:19:51]] [INFO] Refreshing screenshot...
[[08:19:51]] [INFO] tufIibCj03=pass
[[08:19:47]] [SUCCESS] Screenshot refreshed successfully
[[08:19:47]] [SUCCESS] Screenshot refreshed successfully
[[08:19:47]] [INFO] tufIibCj03=running
[[08:19:47]] [INFO] Executing action 143/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:19:46]] [SUCCESS] Screenshot refreshed
[[08:19:46]] [INFO] Refreshing screenshot...
[[08:19:46]] [INFO] g8u66qfKkX=pass
[[08:19:43]] [INFO] g8u66qfKkX=running
[[08:19:43]] [INFO] Executing action 142/576: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:19:43]] [SUCCESS] Screenshot refreshed successfully
[[08:19:43]] [SUCCESS] Screenshot refreshed successfully
[[08:19:42]] [SUCCESS] Screenshot refreshed
[[08:19:42]] [INFO] Refreshing screenshot...
[[08:19:42]] [INFO] XryN8qR1DX=pass
[[08:19:38]] [SUCCESS] Screenshot refreshed successfully
[[08:19:38]] [SUCCESS] Screenshot refreshed successfully
[[08:19:38]] [INFO] XryN8qR1DX=running
[[08:19:38]] [INFO] Executing action 141/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:19:37]] [SUCCESS] Screenshot refreshed
[[08:19:37]] [INFO] Refreshing screenshot...
[[08:19:37]] [INFO] K2w9XUGwnb=pass
[[08:19:29]] [SUCCESS] Screenshot refreshed successfully
[[08:19:29]] [SUCCESS] Screenshot refreshed successfully
[[08:19:29]] [INFO] K2w9XUGwnb=running
[[08:19:29]] [INFO] Executing action 140/576: Tap on element with accessibility_id: Add to bag
[[08:19:28]] [SUCCESS] Screenshot refreshed
[[08:19:28]] [INFO] Refreshing screenshot...
[[08:19:28]] [INFO] BTYxjEaZEk=pass
[[08:19:23]] [SUCCESS] Screenshot refreshed successfully
[[08:19:23]] [SUCCESS] Screenshot refreshed successfully
[[08:19:23]] [INFO] BTYxjEaZEk=running
[[08:19:23]] [INFO] Executing action 139/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:19:23]] [SUCCESS] Screenshot refreshed
[[08:19:23]] [INFO] Refreshing screenshot...
[[08:19:23]] [INFO] YC6bBrKQgq=pass
[[08:19:19]] [SUCCESS] Screenshot refreshed successfully
[[08:19:19]] [SUCCESS] Screenshot refreshed successfully
[[08:19:19]] [INFO] YC6bBrKQgq=running
[[08:19:19]] [INFO] Executing action 138/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:19:18]] [SUCCESS] Screenshot refreshed
[[08:19:18]] [INFO] Refreshing screenshot...
[[08:19:18]] [INFO] aRgHcQcLDP=pass
[[08:19:14]] [SUCCESS] Screenshot refreshed successfully
[[08:19:14]] [SUCCESS] Screenshot refreshed successfully
[[08:19:14]] [INFO] aRgHcQcLDP=running
[[08:19:14]] [INFO] Executing action 137/576: iOS Function: text - Text: "uno card"
[[08:19:13]] [SUCCESS] Screenshot refreshed
[[08:19:13]] [INFO] Refreshing screenshot...
[[08:19:13]] [INFO] 4PZC1vVWJW=pass
[[08:19:08]] [SUCCESS] Screenshot refreshed successfully
[[08:19:08]] [SUCCESS] Screenshot refreshed successfully
[[08:19:08]] [INFO] 4PZC1vVWJW=running
[[08:19:08]] [INFO] Executing action 136/576: Tap on Text: "Find"
[[08:19:07]] [SUCCESS] Screenshot refreshed
[[08:19:07]] [INFO] Refreshing screenshot...
[[08:19:07]] [INFO] XryN8qR1DX=pass
[[08:19:03]] [SUCCESS] Screenshot refreshed successfully
[[08:19:03]] [SUCCESS] Screenshot refreshed successfully
[[08:19:03]] [INFO] XryN8qR1DX=running
[[08:19:03]] [INFO] Executing action 135/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:19:02]] [SUCCESS] Screenshot refreshed
[[08:19:02]] [INFO] Refreshing screenshot...
[[08:19:02]] [INFO] 7WYExJTqjp=pass
[[08:18:58]] [SUCCESS] Screenshot refreshed successfully
[[08:18:58]] [SUCCESS] Screenshot refreshed successfully
[[08:18:58]] [INFO] 7WYExJTqjp=running
[[08:18:58]] [INFO] Executing action 134/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:18:57]] [SUCCESS] Screenshot refreshed
[[08:18:57]] [INFO] Refreshing screenshot...
[[08:18:57]] [INFO] 4WfPFN961S=pass
[[08:18:50]] [SUCCESS] Screenshot refreshed successfully
[[08:18:50]] [SUCCESS] Screenshot refreshed successfully
[[08:18:50]] [INFO] 4WfPFN961S=running
[[08:18:50]] [INFO] Executing action 133/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:18:50]] [SUCCESS] Screenshot refreshed
[[08:18:50]] [INFO] Refreshing screenshot...
[[08:18:50]] [INFO] NurQsFoMkE=pass
[[08:18:48]] [SUCCESS] Screenshot refreshed successfully
[[08:18:48]] [SUCCESS] Screenshot refreshed successfully
[[08:18:46]] [INFO] NurQsFoMkE=running
[[08:18:46]] [INFO] Executing action 132/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:18:46]] [SUCCESS] Screenshot refreshed
[[08:18:46]] [INFO] Refreshing screenshot...
[[08:18:45]] [SUCCESS] Screenshot refreshed
[[08:18:45]] [INFO] Refreshing screenshot...
[[08:18:41]] [SUCCESS] Screenshot refreshed successfully
[[08:18:41]] [SUCCESS] Screenshot refreshed successfully
[[08:18:41]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:18:40]] [SUCCESS] Screenshot refreshed
[[08:18:40]] [INFO] Refreshing screenshot...
[[08:18:36]] [SUCCESS] Screenshot refreshed successfully
[[08:18:36]] [SUCCESS] Screenshot refreshed successfully
[[08:18:36]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:18:36]] [SUCCESS] Screenshot refreshed
[[08:18:36]] [INFO] Refreshing screenshot...
[[08:18:30]] [SUCCESS] Screenshot refreshed successfully
[[08:18:30]] [SUCCESS] Screenshot refreshed successfully
[[08:18:30]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:18:30]] [SUCCESS] Screenshot refreshed
[[08:18:30]] [INFO] Refreshing screenshot...
[[08:18:26]] [SUCCESS] Screenshot refreshed successfully
[[08:18:26]] [SUCCESS] Screenshot refreshed successfully
[[08:18:25]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:18:25]] [SUCCESS] Screenshot refreshed
[[08:18:25]] [INFO] Refreshing screenshot...
[[08:18:19]] [SUCCESS] Screenshot refreshed successfully
[[08:18:19]] [SUCCESS] Screenshot refreshed successfully
[[08:18:19]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:18:19]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:18:19]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:18:19]] [INFO] mWOCt0aAWW=running
[[08:18:19]] [INFO] Executing action 131/576: Execute Test Case: Kmart-Signin (5 steps)
[[08:18:19]] [SUCCESS] Screenshot refreshed
[[08:18:19]] [INFO] Refreshing screenshot...
[[08:18:19]] [INFO] byEe7qbCpq=pass
[[08:18:16]] [SUCCESS] Screenshot refreshed successfully
[[08:18:16]] [SUCCESS] Screenshot refreshed successfully
[[08:18:16]] [INFO] byEe7qbCpq=running
[[08:18:16]] [INFO] Executing action 130/576: iOS Function: alert_accept
[[08:18:15]] [SUCCESS] Screenshot refreshed
[[08:18:15]] [INFO] Refreshing screenshot...
[[08:18:15]] [INFO] L6wTorOX8B=pass
[[08:18:11]] [SUCCESS] Screenshot refreshed successfully
[[08:18:11]] [SUCCESS] Screenshot refreshed successfully
[[08:18:11]] [INFO] L6wTorOX8B=running
[[08:18:11]] [INFO] Executing action 129/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[08:18:11]] [SUCCESS] Screenshot refreshed
[[08:18:11]] [INFO] Refreshing screenshot...
[[08:18:11]] [INFO] XryN8qR1DX=pass
[[08:18:07]] [SUCCESS] Screenshot refreshed successfully
[[08:18:07]] [SUCCESS] Screenshot refreshed successfully
[[08:18:07]] [INFO] XryN8qR1DX=running
[[08:18:07]] [INFO] Executing action 128/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:18:06]] [SUCCESS] Screenshot refreshed
[[08:18:06]] [INFO] Refreshing screenshot...
[[08:18:06]] [INFO] lCSewtjn1z=pass
[[08:18:01]] [SUCCESS] Screenshot refreshed successfully
[[08:18:01]] [SUCCESS] Screenshot refreshed successfully
[[08:18:01]] [INFO] lCSewtjn1z=running
[[08:18:01]] [INFO] Executing action 127/576: Restart app: env[appid]
[[08:18:00]] [SUCCESS] Screenshot refreshed
[[08:18:00]] [INFO] Refreshing screenshot...
[[08:18:00]] [INFO] IJh702cxG0=pass
[[08:17:56]] [SUCCESS] Screenshot refreshed successfully
[[08:17:56]] [SUCCESS] Screenshot refreshed successfully
[[08:17:56]] [INFO] IJh702cxG0=running
[[08:17:56]] [INFO] Executing action 126/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:17:55]] [SUCCESS] Screenshot refreshed
[[08:17:55]] [INFO] Refreshing screenshot...
[[08:17:55]] [INFO] 4WfPFN961S=pass
[[08:17:49]] [SUCCESS] Screenshot refreshed successfully
[[08:17:49]] [SUCCESS] Screenshot refreshed successfully
[[08:17:49]] [INFO] 4WfPFN961S=running
[[08:17:49]] [INFO] Executing action 125/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:17:48]] [SUCCESS] Screenshot refreshed
[[08:17:48]] [INFO] Refreshing screenshot...
[[08:17:48]] [INFO] AOcOOSuOsB=pass
[[08:17:44]] [SUCCESS] Screenshot refreshed successfully
[[08:17:44]] [SUCCESS] Screenshot refreshed successfully
[[08:17:44]] [INFO] AOcOOSuOsB=running
[[08:17:44]] [INFO] Executing action 124/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:17:43]] [SUCCESS] Screenshot refreshed
[[08:17:43]] [INFO] Refreshing screenshot...
[[08:17:43]] [INFO] AOcOOSuOsB=pass
[[08:17:38]] [SUCCESS] Screenshot refreshed successfully
[[08:17:38]] [SUCCESS] Screenshot refreshed successfully
[[08:17:37]] [INFO] AOcOOSuOsB=running
[[08:17:37]] [INFO] Executing action 123/576: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:17:37]] [SUCCESS] Screenshot refreshed
[[08:17:37]] [INFO] Refreshing screenshot...
[[08:17:37]] [INFO] N2yjynioko=pass
[[08:17:32]] [SUCCESS] Screenshot refreshed successfully
[[08:17:32]] [SUCCESS] Screenshot refreshed successfully
[[08:17:32]] [INFO] N2yjynioko=running
[[08:17:32]] [INFO] Executing action 122/576: iOS Function: text - Text: "Wonderbaby@5"
[[08:17:32]] [SUCCESS] Screenshot refreshed
[[08:17:32]] [INFO] Refreshing screenshot...
[[08:17:32]] [INFO] SHaIduBnay=pass
[[08:17:27]] [SUCCESS] Screenshot refreshed successfully
[[08:17:27]] [SUCCESS] Screenshot refreshed successfully
[[08:17:27]] [INFO] SHaIduBnay=running
[[08:17:27]] [INFO] Executing action 121/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[08:17:27]] [SUCCESS] Screenshot refreshed
[[08:17:27]] [INFO] Refreshing screenshot...
[[08:17:27]] [INFO] wuIMlAwYVA=pass
[[08:17:21]] [SUCCESS] Screenshot refreshed successfully
[[08:17:21]] [SUCCESS] Screenshot refreshed successfully
[[08:17:21]] [INFO] wuIMlAwYVA=running
[[08:17:21]] [INFO] Executing action 120/576: iOS Function: text - Text: "env[uname1]"
[[08:17:21]] [SUCCESS] Screenshot refreshed
[[08:17:21]] [INFO] Refreshing screenshot...
[[08:17:21]] [INFO] 50Z2jrodNd=pass
[[08:17:16]] [SUCCESS] Screenshot refreshed successfully
[[08:17:16]] [SUCCESS] Screenshot refreshed successfully
[[08:17:16]] [INFO] 50Z2jrodNd=running
[[08:17:16]] [INFO] Executing action 119/576: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[08:17:16]] [SUCCESS] Screenshot refreshed
[[08:17:16]] [INFO] Refreshing screenshot...
[[08:17:16]] [INFO] VK2oI6mXSB=pass
[[08:17:12]] [SUCCESS] Screenshot refreshed successfully
[[08:17:12]] [SUCCESS] Screenshot refreshed successfully
[[08:17:12]] [INFO] VK2oI6mXSB=running
[[08:17:12]] [INFO] Executing action 118/576: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[08:17:11]] [SUCCESS] Screenshot refreshed
[[08:17:11]] [INFO] Refreshing screenshot...
[[08:17:11]] [INFO] q9ZiyYoE5B=pass
[[08:17:09]] [SUCCESS] Screenshot refreshed successfully
[[08:17:09]] [SUCCESS] Screenshot refreshed successfully
[[08:17:09]] [INFO] q9ZiyYoE5B=running
[[08:17:09]] [INFO] Executing action 117/576: iOS Function: alert_accept
[[08:17:08]] [SUCCESS] Screenshot refreshed
[[08:17:08]] [INFO] Refreshing screenshot...
[[08:17:08]] [INFO] 4PZC1vVWJW=pass
[[08:17:03]] [SUCCESS] Screenshot refreshed successfully
[[08:17:03]] [SUCCESS] Screenshot refreshed successfully
[[08:17:03]] [INFO] 4PZC1vVWJW=running
[[08:17:03]] [INFO] Executing action 116/576: Tap on Text: "Sign"
[[08:17:02]] [SUCCESS] Screenshot refreshed
[[08:17:02]] [INFO] Refreshing screenshot...
[[08:17:02]] [INFO] 2YGctqXNED=pass
[[08:16:52]] [SUCCESS] Screenshot refreshed successfully
[[08:16:52]] [SUCCESS] Screenshot refreshed successfully
[[08:16:52]] [INFO] 2YGctqXNED=running
[[08:16:52]] [INFO] Executing action 115/576: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[08:16:51]] [SUCCESS] Screenshot refreshed
[[08:16:51]] [INFO] Refreshing screenshot...
[[08:16:51]] [INFO] 6zUBxjSFym=pass
[[08:16:48]] [SUCCESS] Screenshot refreshed successfully
[[08:16:48]] [SUCCESS] Screenshot refreshed successfully
[[08:16:47]] [INFO] 6zUBxjSFym=running
[[08:16:47]] [INFO] Executing action 114/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:16:47]] [SUCCESS] Screenshot refreshed
[[08:16:47]] [INFO] Refreshing screenshot...
[[08:16:47]] [INFO] BTYxjEaZEk=pass
[[08:16:43]] [SUCCESS] Screenshot refreshed successfully
[[08:16:43]] [SUCCESS] Screenshot refreshed successfully
[[08:16:42]] [INFO] BTYxjEaZEk=running
[[08:16:42]] [INFO] Executing action 113/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:16:42]] [SUCCESS] Screenshot refreshed
[[08:16:42]] [INFO] Refreshing screenshot...
[[08:16:42]] [INFO] YC6bBrKQgq=pass
[[08:16:38]] [SUCCESS] Screenshot refreshed successfully
[[08:16:38]] [SUCCESS] Screenshot refreshed successfully
[[08:16:38]] [INFO] YC6bBrKQgq=running
[[08:16:38]] [INFO] Executing action 112/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:16:37]] [SUCCESS] Screenshot refreshed
[[08:16:37]] [INFO] Refreshing screenshot...
[[08:16:37]] [INFO] aRgHcQcLDP=pass
[[08:16:33]] [SUCCESS] Screenshot refreshed successfully
[[08:16:33]] [SUCCESS] Screenshot refreshed successfully
[[08:16:33]] [INFO] aRgHcQcLDP=running
[[08:16:33]] [INFO] Executing action 111/576: iOS Function: text - Text: "uno card"
[[08:16:32]] [SUCCESS] Screenshot refreshed
[[08:16:32]] [INFO] Refreshing screenshot...
[[08:16:32]] [INFO] 4PZC1vVWJW=pass
[[08:16:27]] [SUCCESS] Screenshot refreshed successfully
[[08:16:27]] [SUCCESS] Screenshot refreshed successfully
[[08:16:27]] [INFO] 4PZC1vVWJW=running
[[08:16:27]] [INFO] Executing action 110/576: Tap on Text: "Find"
[[08:16:26]] [SUCCESS] Screenshot refreshed
[[08:16:26]] [INFO] Refreshing screenshot...
[[08:16:26]] [INFO] lCSewtjn1z=pass
[[08:16:21]] [SUCCESS] Screenshot refreshed successfully
[[08:16:21]] [SUCCESS] Screenshot refreshed successfully
[[08:16:21]] [INFO] lCSewtjn1z=running
[[08:16:21]] [INFO] Executing action 109/576: Restart app: env[appid]
[[08:16:20]] [SUCCESS] Screenshot refreshed
[[08:16:20]] [INFO] Refreshing screenshot...
[[08:16:20]] [INFO] A1Wz7p1iVG=pass
[[08:16:16]] [SUCCESS] Screenshot refreshed successfully
[[08:16:16]] [SUCCESS] Screenshot refreshed successfully
[[08:16:16]] [INFO] A1Wz7p1iVG=running
[[08:16:16]] [INFO] Executing action 108/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:16:15]] [SUCCESS] Screenshot refreshed
[[08:16:15]] [INFO] Refreshing screenshot...
[[08:16:15]] [INFO] ehyLmdZWP2=pass
[[08:16:08]] [SUCCESS] Screenshot refreshed successfully
[[08:16:08]] [SUCCESS] Screenshot refreshed successfully
[[08:16:08]] [INFO] ehyLmdZWP2=running
[[08:16:08]] [INFO] Executing action 107/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:16:08]] [SUCCESS] Screenshot refreshed
[[08:16:08]] [INFO] Refreshing screenshot...
[[08:16:08]] [INFO] ydRnBBO1vR=pass
[[08:16:05]] [SUCCESS] Screenshot refreshed successfully
[[08:16:05]] [SUCCESS] Screenshot refreshed successfully
[[08:16:04]] [INFO] ydRnBBO1vR=running
[[08:16:04]] [INFO] Executing action 106/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:16:03]] [SUCCESS] Screenshot refreshed
[[08:16:03]] [INFO] Refreshing screenshot...
[[08:16:03]] [INFO] quZwUwj3a8=pass
[[08:15:59]] [SUCCESS] Screenshot refreshed successfully
[[08:15:59]] [SUCCESS] Screenshot refreshed successfully
[[08:15:59]] [INFO] quZwUwj3a8=running
[[08:15:59]] [INFO] Executing action 105/576: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[08:15:58]] [SUCCESS] Screenshot refreshed
[[08:15:58]] [INFO] Refreshing screenshot...
[[08:15:58]] [INFO] FHRlQXe58T=pass
[[08:15:54]] [SUCCESS] Screenshot refreshed successfully
[[08:15:54]] [SUCCESS] Screenshot refreshed successfully
[[08:15:53]] [INFO] FHRlQXe58T=running
[[08:15:53]] [INFO] Executing action 104/576: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:15:53]] [SUCCESS] Screenshot refreshed
[[08:15:53]] [INFO] Refreshing screenshot...
[[08:15:53]] [INFO] FHRlQXe58T=pass
[[08:15:48]] [SUCCESS] Screenshot refreshed successfully
[[08:15:48]] [SUCCESS] Screenshot refreshed successfully
[[08:15:48]] [INFO] FHRlQXe58T=running
[[08:15:48]] [INFO] Executing action 103/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtStart Shopping"]
[[08:15:48]] [SUCCESS] Screenshot refreshed
[[08:15:48]] [INFO] Refreshing screenshot...
[[08:15:48]] [INFO] N9sXy9WltY=pass
[[08:15:44]] [SUCCESS] Screenshot refreshed successfully
[[08:15:44]] [SUCCESS] Screenshot refreshed successfully
[[08:15:44]] [INFO] N9sXy9WltY=running
[[08:15:44]] [INFO] Executing action 102/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtStart Shopping"]" exists
[[08:15:43]] [SUCCESS] Screenshot refreshed
[[08:15:43]] [INFO] Refreshing screenshot...
[[08:15:43]] [INFO] 8uojw2klHA=pass
[[08:15:39]] [SUCCESS] Screenshot refreshed successfully
[[08:15:39]] [SUCCESS] Screenshot refreshed successfully
[[08:15:38]] [INFO] 8uojw2klHA=running
[[08:15:38]] [INFO] Executing action 101/576: iOS Function: text - Text: "env[pwd]"
[[08:15:38]] [SUCCESS] Screenshot refreshed
[[08:15:38]] [INFO] Refreshing screenshot...
[[08:15:38]] [INFO] SHaIduBnay=pass
[[08:15:34]] [SUCCESS] Screenshot refreshed successfully
[[08:15:34]] [SUCCESS] Screenshot refreshed successfully
[[08:15:34]] [INFO] SHaIduBnay=running
[[08:15:34]] [INFO] Executing action 100/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:15:33]] [SUCCESS] Screenshot refreshed
[[08:15:33]] [INFO] Refreshing screenshot...
[[08:15:33]] [INFO] TGoXyeQtB7=pass
[[08:15:28]] [SUCCESS] Screenshot refreshed successfully
[[08:15:28]] [SUCCESS] Screenshot refreshed successfully
[[08:15:28]] [INFO] TGoXyeQtB7=running
[[08:15:28]] [INFO] Executing action 99/576: iOS Function: text - Text: "env[uname]"
[[08:15:28]] [SUCCESS] Screenshot refreshed
[[08:15:28]] [INFO] Refreshing screenshot...
[[08:15:28]] [INFO] rLCI6NVxSc=pass
[[08:15:23]] [SUCCESS] Screenshot refreshed successfully
[[08:15:23]] [SUCCESS] Screenshot refreshed successfully
[[08:15:23]] [INFO] rLCI6NVxSc=running
[[08:15:23]] [INFO] Executing action 98/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:15:22]] [SUCCESS] Screenshot refreshed
[[08:15:22]] [INFO] Refreshing screenshot...
[[08:15:22]] [INFO] 6mHVWI3j5e=pass
[[08:15:19]] [SUCCESS] Screenshot refreshed successfully
[[08:15:19]] [SUCCESS] Screenshot refreshed successfully
[[08:15:19]] [INFO] 6mHVWI3j5e=running
[[08:15:19]] [INFO] Executing action 97/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:15:18]] [SUCCESS] Screenshot refreshed
[[08:15:18]] [INFO] Refreshing screenshot...
[[08:15:18]] [INFO] rJVGLpLWM3=pass
[[08:15:15]] [SUCCESS] Screenshot refreshed successfully
[[08:15:15]] [SUCCESS] Screenshot refreshed successfully
[[08:15:15]] [INFO] rJVGLpLWM3=running
[[08:15:15]] [INFO] Executing action 96/576: iOS Function: alert_accept
[[08:15:15]] [SUCCESS] Screenshot refreshed
[[08:15:15]] [INFO] Refreshing screenshot...
[[08:15:15]] [INFO] WlISsMf9QA=pass
[[08:15:11]] [SUCCESS] Screenshot refreshed successfully
[[08:15:11]] [SUCCESS] Screenshot refreshed successfully
[[08:15:11]] [INFO] WlISsMf9QA=running
[[08:15:11]] [INFO] Executing action 95/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[08:15:10]] [SUCCESS] Screenshot refreshed
[[08:15:10]] [INFO] Refreshing screenshot...
[[08:15:10]] [INFO] IvqPpScAJa=pass
[[08:15:07]] [SUCCESS] Screenshot refreshed successfully
[[08:15:07]] [SUCCESS] Screenshot refreshed successfully
[[08:15:06]] [INFO] IvqPpScAJa=running
[[08:15:06]] [INFO] Executing action 94/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[08:15:06]] [SUCCESS] Screenshot refreshed
[[08:15:06]] [INFO] Refreshing screenshot...
[[08:15:06]] [INFO] bGo3feCwBQ=pass
[[08:15:02]] [SUCCESS] Screenshot refreshed successfully
[[08:15:02]] [SUCCESS] Screenshot refreshed successfully
[[08:15:01]] [INFO] bGo3feCwBQ=running
[[08:15:01]] [INFO] Executing action 93/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:15:01]] [SUCCESS] Screenshot refreshed
[[08:15:01]] [INFO] Refreshing screenshot...
[[08:15:01]] [INFO] 4WfPFN961S=pass
[[08:14:55]] [SUCCESS] Screenshot refreshed successfully
[[08:14:55]] [SUCCESS] Screenshot refreshed successfully
[[08:14:54]] [INFO] 4WfPFN961S=running
[[08:14:54]] [INFO] Executing action 92/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:14:53]] [SUCCESS] Screenshot refreshed
[[08:14:53]] [INFO] Refreshing screenshot...
[[08:14:53]] [INFO] F0gZF1jEnT=pass
[[08:14:50]] [SUCCESS] Screenshot refreshed successfully
[[08:14:50]] [SUCCESS] Screenshot refreshed successfully
[[08:14:49]] [INFO] F0gZF1jEnT=running
[[08:14:49]] [INFO] Executing action 91/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:14:49]] [SUCCESS] Screenshot refreshed
[[08:14:49]] [INFO] Refreshing screenshot...
[[08:14:49]] [INFO] EDHl0X27Wi=pass
[[08:14:45]] [INFO] EDHl0X27Wi=running
[[08:14:45]] [INFO] Executing action 90/576: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[08:14:45]] [SUCCESS] Screenshot refreshed successfully
[[08:14:45]] [SUCCESS] Screenshot refreshed successfully
[[08:14:44]] [SUCCESS] Screenshot refreshed
[[08:14:44]] [INFO] Refreshing screenshot...
[[08:14:44]] [INFO] j8NXU87gV3=pass
[[08:14:39]] [INFO] j8NXU87gV3=running
[[08:14:39]] [INFO] Executing action 89/576: iOS Function: text - Text: "env[pwd]"
[[08:14:39]] [SUCCESS] Screenshot refreshed successfully
[[08:14:38]] [SUCCESS] Screenshot refreshed successfully
[[08:14:37]] [SUCCESS] Screenshot refreshed
[[08:14:37]] [INFO] Refreshing screenshot...
[[08:14:37]] [INFO] dpVaKL19uc=pass
[[08:14:31]] [INFO] dpVaKL19uc=running
[[08:14:31]] [INFO] Executing action 88/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:14:21]] [SUCCESS] Screenshot refreshed successfully
[[08:14:21]] [SUCCESS] Screenshot refreshed successfully
[[08:14:20]] [SUCCESS] Screenshot refreshed
[[08:14:20]] [INFO] Refreshing screenshot...
[[08:14:20]] [INFO] eOm1WExcrK=pass
[[08:14:15]] [INFO] eOm1WExcrK=running
[[08:14:15]] [INFO] Executing action 87/576: iOS Function: text - Text: "env[uname]"
[[08:14:12]] [SUCCESS] Screenshot refreshed successfully
[[08:14:12]] [SUCCESS] Screenshot refreshed successfully
[[08:14:12]] [SUCCESS] Screenshot refreshed
[[08:14:12]] [INFO] Refreshing screenshot...
[[08:14:12]] [INFO] 50Z2jrodNd=pass
[[08:14:08]] [SUCCESS] Screenshot refreshed successfully
[[08:14:08]] [SUCCESS] Screenshot refreshed successfully
[[08:14:08]] [INFO] 50Z2jrodNd=running
[[08:14:08]] [INFO] Executing action 86/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:14:07]] [SUCCESS] Screenshot refreshed
[[08:14:07]] [INFO] Refreshing screenshot...
[[08:14:07]] [INFO] eJnHS9n9VL=pass
[[08:14:04]] [SUCCESS] Screenshot refreshed successfully
[[08:14:04]] [SUCCESS] Screenshot refreshed successfully
[[08:14:03]] [INFO] eJnHS9n9VL=running
[[08:14:03]] [INFO] Executing action 85/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:14:03]] [SUCCESS] Screenshot refreshed
[[08:14:03]] [INFO] Refreshing screenshot...
[[08:14:03]] [INFO] XuLgjNG74w=pass
[[08:14:01]] [SUCCESS] Screenshot refreshed successfully
[[08:14:01]] [SUCCESS] Screenshot refreshed successfully
[[08:14:00]] [INFO] XuLgjNG74w=running
[[08:14:00]] [INFO] Executing action 84/576: iOS Function: alert_accept
[[08:14:00]] [SUCCESS] Screenshot refreshed
[[08:14:00]] [INFO] Refreshing screenshot...
[[08:14:00]] [INFO] qA1ap4n1m4=pass
[[08:13:52]] [SUCCESS] Screenshot refreshed successfully
[[08:13:52]] [SUCCESS] Screenshot refreshed successfully
[[08:13:52]] [INFO] qA1ap4n1m4=running
[[08:13:52]] [INFO] Executing action 83/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:13:51]] [SUCCESS] Screenshot refreshed
[[08:13:51]] [INFO] Refreshing screenshot...
[[08:13:51]] [INFO] JXFxYCr98V=pass
[[08:13:38]] [SUCCESS] Screenshot refreshed successfully
[[08:13:38]] [SUCCESS] Screenshot refreshed successfully
[[08:13:37]] [INFO] JXFxYCr98V=running
[[08:13:37]] [INFO] Executing action 82/576: Restart app: env[appid]
[[08:13:37]] [SUCCESS] Screenshot refreshed
[[08:13:37]] [INFO] Refreshing screenshot...
[[08:13:36]] [SUCCESS] Screenshot refreshed
[[08:13:36]] [INFO] Refreshing screenshot...
[[08:13:33]] [SUCCESS] Screenshot refreshed successfully
[[08:13:33]] [SUCCESS] Screenshot refreshed successfully
[[08:13:33]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:13:33]] [SUCCESS] Screenshot refreshed
[[08:13:33]] [INFO] Refreshing screenshot...
[[08:13:20]] [SUCCESS] Screenshot refreshed successfully
[[08:13:20]] [SUCCESS] Screenshot refreshed successfully
[[08:13:20]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:13:20]] [SUCCESS] Screenshot refreshed
[[08:13:20]] [INFO] Refreshing screenshot...
[[08:13:16]] [SUCCESS] Screenshot refreshed successfully
[[08:13:16]] [SUCCESS] Screenshot refreshed successfully
[[08:13:16]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:13:15]] [SUCCESS] Screenshot refreshed
[[08:13:15]] [INFO] Refreshing screenshot...
[[08:13:12]] [SUCCESS] Screenshot refreshed successfully
[[08:13:12]] [SUCCESS] Screenshot refreshed successfully
[[08:13:11]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:13:11]] [SUCCESS] Screenshot refreshed
[[08:13:11]] [INFO] Refreshing screenshot...
[[08:13:04]] [SUCCESS] Screenshot refreshed successfully
[[08:13:04]] [SUCCESS] Screenshot refreshed successfully
[[08:13:04]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:13:03]] [SUCCESS] Screenshot refreshed
[[08:13:03]] [INFO] Refreshing screenshot...
[[08:12:57]] [SUCCESS] Screenshot refreshed successfully
[[08:12:57]] [SUCCESS] Screenshot refreshed successfully
[[08:12:56]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:12:56]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:12:56]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:12:56]] [INFO] hbIlJIWlVN=running
[[08:12:56]] [INFO] Executing action 81/576: cleanupSteps action
[[08:12:56]] [SUCCESS] Screenshot refreshed
[[08:12:56]] [INFO] Refreshing screenshot...
[[08:12:56]] [SUCCESS] Screenshot refreshed
[[08:12:56]] [INFO] Refreshing screenshot...
[[08:12:51]] [SUCCESS] Screenshot refreshed successfully
[[08:12:51]] [SUCCESS] Screenshot refreshed successfully
[[08:12:51]] [INFO] Executing Multi Step action step 34/34: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[08:12:51]] [SUCCESS] Screenshot refreshed
[[08:12:51]] [INFO] Refreshing screenshot...
[[08:12:47]] [INFO] Executing Multi Step action step 33/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:12:47]] [SUCCESS] Screenshot refreshed successfully
[[08:12:47]] [SUCCESS] Screenshot refreshed successfully
[[08:12:46]] [SUCCESS] Screenshot refreshed
[[08:12:46]] [INFO] Refreshing screenshot...
[[08:12:43]] [SUCCESS] Screenshot refreshed successfully
[[08:12:43]] [SUCCESS] Screenshot refreshed successfully
[[08:12:42]] [INFO] Executing Multi Step action step 32/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:12:42]] [SUCCESS] Screenshot refreshed
[[08:12:42]] [INFO] Refreshing screenshot...
[[08:12:37]] [SUCCESS] Screenshot refreshed successfully
[[08:12:37]] [SUCCESS] Screenshot refreshed successfully
[[08:12:37]] [INFO] Executing Multi Step action step 31/34: Tap on image: banner-close-updated.png
[[08:12:36]] [SUCCESS] Screenshot refreshed
[[08:12:36]] [INFO] Refreshing screenshot...
[[08:12:26]] [SUCCESS] Screenshot refreshed successfully
[[08:12:26]] [SUCCESS] Screenshot refreshed successfully
[[08:12:26]] [INFO] Executing Multi Step action step 30/34: Swipe from (50%, 70%) to (50%, 30%)
[[08:12:26]] [SUCCESS] Screenshot refreshed
[[08:12:26]] [INFO] Refreshing screenshot...
[[08:12:22]] [SUCCESS] Screenshot refreshed successfully
[[08:12:22]] [SUCCESS] Screenshot refreshed successfully
[[08:12:22]] [INFO] Executing Multi Step action step 29/34: Tap on image: env[delivery-address-img]
[[08:12:21]] [SUCCESS] Screenshot refreshed
[[08:12:21]] [INFO] Refreshing screenshot...
[[08:12:17]] [SUCCESS] Screenshot refreshed successfully
[[08:12:17]] [SUCCESS] Screenshot refreshed successfully
[[08:12:17]] [INFO] Executing Multi Step action step 28/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[08:12:16]] [SUCCESS] Screenshot refreshed
[[08:12:16]] [INFO] Refreshing screenshot...
[[08:12:09]] [SUCCESS] Screenshot refreshed successfully
[[08:12:09]] [SUCCESS] Screenshot refreshed successfully
[[08:12:09]] [INFO] Executing Multi Step action step 27/34: Tap and Type at (54, 314): "305 238 Flinders"
[[08:12:09]] [SUCCESS] Screenshot refreshed
[[08:12:09]] [INFO] Refreshing screenshot...
[[08:12:04]] [SUCCESS] Screenshot refreshed successfully
[[08:12:04]] [SUCCESS] Screenshot refreshed successfully
[[08:12:04]] [INFO] Executing Multi Step action step 26/34: Tap on Text: "address"
[[08:12:03]] [SUCCESS] Screenshot refreshed
[[08:12:03]] [INFO] Refreshing screenshot...
[[08:11:59]] [SUCCESS] Screenshot refreshed successfully
[[08:11:59]] [SUCCESS] Screenshot refreshed successfully
[[08:11:59]] [INFO] Executing Multi Step action step 25/34: iOS Function: text - Text: " "
[[08:11:58]] [SUCCESS] Screenshot refreshed
[[08:11:58]] [INFO] Refreshing screenshot...
[[08:11:55]] [SUCCESS] Screenshot refreshed successfully
[[08:11:55]] [SUCCESS] Screenshot refreshed successfully
[[08:11:54]] [INFO] Executing Multi Step action step 24/34: textClear action
[[08:11:53]] [SUCCESS] Screenshot refreshed
[[08:11:53]] [INFO] Refreshing screenshot...
[[08:11:49]] [SUCCESS] Screenshot refreshed successfully
[[08:11:49]] [SUCCESS] Screenshot refreshed successfully
[[08:11:49]] [INFO] Executing Multi Step action step 23/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[08:11:49]] [SUCCESS] Screenshot refreshed
[[08:11:49]] [INFO] Refreshing screenshot...
[[08:11:44]] [SUCCESS] Screenshot refreshed successfully
[[08:11:44]] [SUCCESS] Screenshot refreshed successfully
[[08:11:44]] [INFO] Executing Multi Step action step 22/34: textClear action
[[08:11:44]] [SUCCESS] Screenshot refreshed
[[08:11:44]] [INFO] Refreshing screenshot...
[[08:11:40]] [SUCCESS] Screenshot refreshed successfully
[[08:11:40]] [SUCCESS] Screenshot refreshed successfully
[[08:11:40]] [INFO] Executing Multi Step action step 21/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:11:39]] [SUCCESS] Screenshot refreshed
[[08:11:39]] [INFO] Refreshing screenshot...
[[08:11:35]] [SUCCESS] Screenshot refreshed successfully
[[08:11:35]] [SUCCESS] Screenshot refreshed successfully
[[08:11:35]] [INFO] Executing Multi Step action step 20/34: textClear action
[[08:11:35]] [SUCCESS] Screenshot refreshed
[[08:11:35]] [INFO] Refreshing screenshot...
[[08:11:30]] [SUCCESS] Screenshot refreshed successfully
[[08:11:30]] [SUCCESS] Screenshot refreshed successfully
[[08:11:30]] [INFO] Executing Multi Step action step 19/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[08:11:29]] [SUCCESS] Screenshot refreshed
[[08:11:29]] [INFO] Refreshing screenshot...
[[08:11:25]] [SUCCESS] Screenshot refreshed successfully
[[08:11:25]] [SUCCESS] Screenshot refreshed successfully
[[08:11:25]] [INFO] Executing Multi Step action step 18/34: textClear action
[[08:11:24]] [SUCCESS] Screenshot refreshed
[[08:11:24]] [INFO] Refreshing screenshot...
[[08:11:20]] [SUCCESS] Screenshot refreshed successfully
[[08:11:20]] [SUCCESS] Screenshot refreshed successfully
[[08:11:20]] [INFO] Executing Multi Step action step 17/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[08:11:20]] [SUCCESS] Screenshot refreshed
[[08:11:20]] [INFO] Refreshing screenshot...
[[08:11:16]] [SUCCESS] Screenshot refreshed successfully
[[08:11:16]] [SUCCESS] Screenshot refreshed successfully
[[08:11:16]] [INFO] Executing Multi Step action step 16/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[08:11:15]] [SUCCESS] Screenshot refreshed
[[08:11:15]] [INFO] Refreshing screenshot...
[[08:10:56]] [SUCCESS] Screenshot refreshed successfully
[[08:10:56]] [SUCCESS] Screenshot refreshed successfully
[[08:10:55]] [INFO] Executing Multi Step action step 15/34: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[08:10:55]] [SUCCESS] Screenshot refreshed
[[08:10:55]] [INFO] Refreshing screenshot...
[[08:10:51]] [SUCCESS] Screenshot refreshed successfully
[[08:10:51]] [SUCCESS] Screenshot refreshed successfully
[[08:10:51]] [INFO] Executing Multi Step action step 14/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[08:10:50]] [SUCCESS] Screenshot refreshed
[[08:10:50]] [INFO] Refreshing screenshot...
[[08:10:47]] [INFO] Executing Multi Step action step 13/34: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[08:10:47]] [SUCCESS] Screenshot refreshed successfully
[[08:10:47]] [SUCCESS] Screenshot refreshed successfully
[[08:10:46]] [SUCCESS] Screenshot refreshed
[[08:10:46]] [INFO] Refreshing screenshot...
[[08:10:43]] [SUCCESS] Screenshot refreshed successfully
[[08:10:43]] [SUCCESS] Screenshot refreshed successfully
[[08:10:42]] [INFO] Executing Multi Step action step 12/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:10:42]] [SUCCESS] Screenshot refreshed
[[08:10:42]] [INFO] Refreshing screenshot...
[[08:10:35]] [SUCCESS] Screenshot refreshed successfully
[[08:10:35]] [SUCCESS] Screenshot refreshed successfully
[[08:10:35]] [INFO] Executing Multi Step action step 11/34: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[08:10:34]] [SUCCESS] Screenshot refreshed
[[08:10:34]] [INFO] Refreshing screenshot...
[[08:10:30]] [SUCCESS] Screenshot refreshed successfully
[[08:10:30]] [SUCCESS] Screenshot refreshed successfully
[[08:10:30]] [INFO] Executing Multi Step action step 10/34: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:10:29]] [SUCCESS] Screenshot refreshed
[[08:10:29]] [INFO] Refreshing screenshot...
[[08:10:25]] [SUCCESS] Screenshot refreshed successfully
[[08:10:25]] [SUCCESS] Screenshot refreshed successfully
[[08:10:25]] [INFO] Executing Multi Step action step 9/34: iOS Function: text - Text: "Uno card"
[[08:10:25]] [SUCCESS] Screenshot refreshed
[[08:10:25]] [INFO] Refreshing screenshot...
[[08:10:19]] [SUCCESS] Screenshot refreshed successfully
[[08:10:19]] [SUCCESS] Screenshot refreshed successfully
[[08:10:19]] [INFO] Executing Multi Step action step 8/34: Tap on Text: "Find"
[[08:10:18]] [SUCCESS] Screenshot refreshed
[[08:10:18]] [INFO] Refreshing screenshot...
[[08:09:56]] [SUCCESS] Screenshot refreshed successfully
[[08:09:56]] [SUCCESS] Screenshot refreshed successfully
[[08:09:55]] [INFO] Executing Multi Step action step 7/34: If exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]" (timeout: 20s) → Then tap at (0, 0)
[[08:09:55]] [SUCCESS] Screenshot refreshed
[[08:09:55]] [INFO] Refreshing screenshot...
[[08:09:42]] [SUCCESS] Screenshot refreshed successfully
[[08:09:42]] [SUCCESS] Screenshot refreshed successfully
[[08:09:42]] [INFO] Executing Multi Step action step 6/34: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[08:09:41]] [SUCCESS] Screenshot refreshed
[[08:09:41]] [INFO] Refreshing screenshot...
[[08:09:14]] [SUCCESS] Screenshot refreshed successfully
[[08:09:14]] [SUCCESS] Screenshot refreshed successfully
[[08:09:13]] [INFO] Executing Multi Step action step 5/34: If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 25s) → Then click element: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[08:09:13]] [SUCCESS] Screenshot refreshed
[[08:09:13]] [INFO] Refreshing screenshot...
[[08:09:08]] [SUCCESS] Screenshot refreshed successfully
[[08:09:08]] [SUCCESS] Screenshot refreshed successfully
[[08:09:07]] [INFO] Executing Multi Step action step 4/34: Tap on Text: "Save"
[[08:09:07]] [SUCCESS] Screenshot refreshed
[[08:09:07]] [INFO] Refreshing screenshot...
[[08:09:03]] [SUCCESS] Screenshot refreshed successfully
[[08:09:03]] [SUCCESS] Screenshot refreshed successfully
[[08:09:01]] [INFO] Executing Multi Step action step 3/34: Tap on element with accessibility_id: btnCurrentLocationButton
[[08:09:00]] [SUCCESS] Screenshot refreshed
[[08:09:00]] [INFO] Refreshing screenshot...
[[08:08:56]] [SUCCESS] Screenshot refreshed successfully
[[08:08:56]] [SUCCESS] Screenshot refreshed successfully
[[08:08:56]] [INFO] Executing Multi Step action step 2/34: Wait till accessibility_id=btnCurrentLocationButton
[[08:08:55]] [SUCCESS] Screenshot refreshed
[[08:08:55]] [INFO] Refreshing screenshot...
[[08:08:48]] [SUCCESS] Screenshot refreshed successfully
[[08:08:48]] [SUCCESS] Screenshot refreshed successfully
[[08:08:48]] [INFO] Executing Multi Step action step 1/34: Tap on Text: "Edit"
[[08:08:48]] [INFO] Loaded 34 steps from test case: Delivery  Buy
[[08:08:48]] [INFO] Loading steps for multiStep action: Delivery  Buy
[[08:08:48]] [INFO] aI4Cfo88Pv=running
[[08:08:48]] [INFO] Executing action 80/576: Execute Test Case: Delivery  Buy (34 steps)
[[08:08:47]] [SUCCESS] Screenshot refreshed
[[08:08:47]] [INFO] Refreshing screenshot...
[[08:08:47]] [INFO] cKNu2QoRC1=pass
[[08:08:43]] [SUCCESS] Screenshot refreshed successfully
[[08:08:43]] [SUCCESS] Screenshot refreshed successfully
[[08:08:43]] [INFO] cKNu2QoRC1=running
[[08:08:43]] [INFO] Executing action 79/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:08:42]] [SUCCESS] Screenshot refreshed
[[08:08:42]] [INFO] Refreshing screenshot...
[[08:08:42]] [INFO] OyUowAaBzD=pass
[[08:08:38]] [SUCCESS] Screenshot refreshed successfully
[[08:08:38]] [SUCCESS] Screenshot refreshed successfully
[[08:08:38]] [INFO] OyUowAaBzD=running
[[08:08:38]] [INFO] Executing action 78/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[08:08:38]] [SUCCESS] Screenshot refreshed
[[08:08:38]] [INFO] Refreshing screenshot...
[[08:08:38]] [INFO] Ob26qqcA0p=pass
[[08:08:30]] [SUCCESS] Screenshot refreshed successfully
[[08:08:30]] [SUCCESS] Screenshot refreshed successfully
[[08:08:30]] [INFO] Ob26qqcA0p=running
[[08:08:30]] [INFO] Executing action 77/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:08:30]] [SUCCESS] Screenshot refreshed
[[08:08:30]] [INFO] Refreshing screenshot...
[[08:08:30]] [INFO] k3mu9Mt7Ec=pass
[[08:08:26]] [SUCCESS] Screenshot refreshed successfully
[[08:08:26]] [SUCCESS] Screenshot refreshed successfully
[[08:08:25]] [INFO] k3mu9Mt7Ec=running
[[08:08:25]] [INFO] Executing action 76/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:08:25]] [SUCCESS] Screenshot refreshed
[[08:08:25]] [INFO] Refreshing screenshot...
[[08:08:25]] [INFO] 8umPSX0vrr=pass
[[08:08:20]] [SUCCESS] Screenshot refreshed successfully
[[08:08:20]] [SUCCESS] Screenshot refreshed successfully
[[08:08:20]] [INFO] 8umPSX0vrr=running
[[08:08:20]] [INFO] Executing action 75/576: Tap on image: banner-close-updated.png
[[08:08:19]] [SUCCESS] Screenshot refreshed
[[08:08:19]] [INFO] Refreshing screenshot...
[[08:08:19]] [INFO] pr9o8Zsm5p=pass
[[08:08:15]] [SUCCESS] Screenshot refreshed successfully
[[08:08:15]] [SUCCESS] Screenshot refreshed successfully
[[08:08:15]] [INFO] pr9o8Zsm5p=running
[[08:08:15]] [INFO] Executing action 74/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:08:15]] [SUCCESS] Screenshot refreshed
[[08:08:15]] [INFO] Refreshing screenshot...
[[08:08:15]] [INFO] Qbg9bipTGs=pass
[[08:08:10]] [SUCCESS] Screenshot refreshed successfully
[[08:08:10]] [SUCCESS] Screenshot refreshed successfully
[[08:08:10]] [INFO] Qbg9bipTGs=running
[[08:08:10]] [INFO] Executing action 73/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:08:09]] [SUCCESS] Screenshot refreshed
[[08:08:09]] [INFO] Refreshing screenshot...
[[08:08:09]] [INFO] qjj0i3rcUh=pass
[[08:08:05]] [SUCCESS] Screenshot refreshed successfully
[[08:08:05]] [SUCCESS] Screenshot refreshed successfully
[[08:08:05]] [INFO] qjj0i3rcUh=running
[[08:08:05]] [INFO] Executing action 72/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[08:08:05]] [SUCCESS] Screenshot refreshed
[[08:08:05]] [INFO] Refreshing screenshot...
[[08:08:05]] [INFO] lWIRxRm6HE=pass
[[08:08:00]] [SUCCESS] Screenshot refreshed successfully
[[08:08:00]] [SUCCESS] Screenshot refreshed successfully
[[08:08:00]] [INFO] lWIRxRm6HE=running
[[08:08:00]] [INFO] Executing action 71/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:08:00]] [SUCCESS] Screenshot refreshed
[[08:08:00]] [INFO] Refreshing screenshot...
[[08:08:00]] [INFO] Q0fomJIDoQ=pass
[[08:07:55]] [SUCCESS] Screenshot refreshed successfully
[[08:07:55]] [SUCCESS] Screenshot refreshed successfully
[[08:07:55]] [INFO] Q0fomJIDoQ=running
[[08:07:55]] [INFO] Executing action 70/576: Tap on image: banner-close-updated.png
[[08:07:55]] [SUCCESS] Screenshot refreshed
[[08:07:55]] [INFO] Refreshing screenshot...
[[08:07:55]] [INFO] 7SpDO20tS2=pass
[[08:07:43]] [SUCCESS] Screenshot refreshed successfully
[[08:07:43]] [SUCCESS] Screenshot refreshed successfully
[[08:07:43]] [INFO] 7SpDO20tS2=running
[[08:07:43]] [INFO] Executing action 69/576: Wait for 10 ms
[[08:07:42]] [SUCCESS] Screenshot refreshed
[[08:07:42]] [INFO] Refreshing screenshot...
[[08:07:42]] [INFO] FKZs2qCWoU=pass
[[08:07:37]] [SUCCESS] Screenshot refreshed successfully
[[08:07:37]] [SUCCESS] Screenshot refreshed successfully
[[08:07:37]] [INFO] FKZs2qCWoU=running
[[08:07:37]] [INFO] Executing action 68/576: Tap on Text: "Brunswick"
[[08:07:37]] [SUCCESS] Screenshot refreshed
[[08:07:37]] [INFO] Refreshing screenshot...
[[08:07:37]] [INFO] Qbg9bipTGs=pass
[[08:07:32]] [SUCCESS] Screenshot refreshed successfully
[[08:07:32]] [SUCCESS] Screenshot refreshed successfully
[[08:07:32]] [INFO] Qbg9bipTGs=running
[[08:07:32]] [INFO] Executing action 67/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:07:32]] [SUCCESS] Screenshot refreshed
[[08:07:32]] [INFO] Refreshing screenshot...
[[08:07:32]] [INFO] qjj0i3rcUh=pass
[[08:07:28]] [SUCCESS] Screenshot refreshed successfully
[[08:07:28]] [SUCCESS] Screenshot refreshed successfully
[[08:07:27]] [INFO] qjj0i3rcUh=running
[[08:07:27]] [INFO] Executing action 66/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[08:07:27]] [SUCCESS] Screenshot refreshed
[[08:07:27]] [INFO] Refreshing screenshot...
[[08:07:27]] [INFO] uM5FOSrU5U=pass
[[08:07:23]] [INFO] uM5FOSrU5U=running
[[08:07:23]] [INFO] Executing action 65/576: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[08:07:23]] [SUCCESS] Screenshot refreshed successfully
[[08:07:23]] [SUCCESS] Screenshot refreshed successfully
[[08:07:22]] [SUCCESS] Screenshot refreshed
[[08:07:22]] [INFO] Refreshing screenshot...
[[08:07:22]] [INFO] F1olhgKhUt=pass
[[08:07:19]] [SUCCESS] Screenshot refreshed successfully
[[08:07:19]] [SUCCESS] Screenshot refreshed successfully
[[08:07:18]] [INFO] F1olhgKhUt=running
[[08:07:18]] [INFO] Executing action 64/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:07:18]] [SUCCESS] Screenshot refreshed
[[08:07:18]] [INFO] Refreshing screenshot...
[[08:07:18]] [INFO] jY0oPjKbuS=pass
[[08:07:15]] [SUCCESS] Screenshot refreshed successfully
[[08:07:15]] [SUCCESS] Screenshot refreshed successfully
[[08:07:14]] [INFO] jY0oPjKbuS=running
[[08:07:14]] [INFO] Executing action 63/576: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[08:07:14]] [SUCCESS] Screenshot refreshed
[[08:07:14]] [INFO] Refreshing screenshot...
[[08:07:14]] [INFO] FnrbyHq7bU=pass
[[08:07:08]] [SUCCESS] Screenshot refreshed successfully
[[08:07:08]] [SUCCESS] Screenshot refreshed successfully
[[08:07:07]] [INFO] FnrbyHq7bU=running
[[08:07:07]] [INFO] Executing action 62/576: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[08:07:07]] [SUCCESS] Screenshot refreshed
[[08:07:07]] [INFO] Refreshing screenshot...
[[08:07:07]] [INFO] nAB6Q8LAdv=pass
[[08:07:03]] [SUCCESS] Screenshot refreshed successfully
[[08:07:03]] [SUCCESS] Screenshot refreshed successfully
[[08:07:03]] [INFO] nAB6Q8LAdv=running
[[08:07:03]] [INFO] Executing action 61/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:07:02]] [SUCCESS] Screenshot refreshed
[[08:07:02]] [INFO] Refreshing screenshot...
[[08:07:02]] [INFO] sc2KH9bG6H=pass
[[08:06:57]] [SUCCESS] Screenshot refreshed successfully
[[08:06:57]] [SUCCESS] Screenshot refreshed successfully
[[08:06:57]] [INFO] sc2KH9bG6H=running
[[08:06:57]] [INFO] Executing action 60/576: iOS Function: text - Text: "Uno card"
[[08:06:57]] [SUCCESS] Screenshot refreshed
[[08:06:57]] [INFO] Refreshing screenshot...
[[08:06:57]] [INFO] ZBXCQNlT8z=pass
[[08:06:52]] [SUCCESS] Screenshot refreshed successfully
[[08:06:52]] [SUCCESS] Screenshot refreshed successfully
[[08:06:51]] [INFO] ZBXCQNlT8z=running
[[08:06:51]] [INFO] Executing action 59/576: Tap on Text: "Find"
[[08:06:51]] [SUCCESS] Screenshot refreshed
[[08:06:51]] [INFO] Refreshing screenshot...
[[08:06:50]] [SUCCESS] Screenshot refreshed
[[08:06:50]] [INFO] Refreshing screenshot...
[[08:06:45]] [SUCCESS] Screenshot refreshed successfully
[[08:06:45]] [SUCCESS] Screenshot refreshed successfully
[[08:06:45]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[08:06:45]] [SUCCESS] Screenshot refreshed
[[08:06:45]] [INFO] Refreshing screenshot...
[[08:06:41]] [SUCCESS] Screenshot refreshed successfully
[[08:06:41]] [SUCCESS] Screenshot refreshed successfully
[[08:06:41]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:06:40]] [SUCCESS] Screenshot refreshed
[[08:06:40]] [INFO] Refreshing screenshot...
[[08:06:35]] [SUCCESS] Screenshot refreshed successfully
[[08:06:35]] [SUCCESS] Screenshot refreshed successfully
[[08:06:35]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[08:06:35]] [SUCCESS] Screenshot refreshed
[[08:06:35]] [INFO] Refreshing screenshot...
[[08:06:30]] [SUCCESS] Screenshot refreshed successfully
[[08:06:30]] [SUCCESS] Screenshot refreshed successfully
[[08:06:30]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:06:29]] [SUCCESS] Screenshot refreshed
[[08:06:29]] [INFO] Refreshing screenshot...
[[08:06:25]] [SUCCESS] Screenshot refreshed successfully
[[08:06:25]] [SUCCESS] Screenshot refreshed successfully
[[08:06:24]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:06:24]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[08:06:24]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[08:06:24]] [INFO] El6k4IPZly=running
[[08:06:24]] [INFO] Executing action 58/576: Execute Test Case: Kmart-Signin (8 steps)
[[08:06:23]] [SUCCESS] Screenshot refreshed
[[08:06:23]] [INFO] Refreshing screenshot...
[[08:06:23]] [INFO] 3caMBvQX7k=pass
[[08:06:20]] [SUCCESS] Screenshot refreshed successfully
[[08:06:20]] [SUCCESS] Screenshot refreshed successfully
[[08:06:20]] [INFO] 3caMBvQX7k=running
[[08:06:20]] [INFO] Executing action 57/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:06:19]] [SUCCESS] Screenshot refreshed
[[08:06:19]] [INFO] Refreshing screenshot...
[[08:06:19]] [INFO] yUJyVO5Wev=pass
[[08:06:17]] [SUCCESS] Screenshot refreshed successfully
[[08:06:17]] [SUCCESS] Screenshot refreshed successfully
[[08:06:16]] [INFO] yUJyVO5Wev=running
[[08:06:16]] [INFO] Executing action 56/576: iOS Function: alert_accept
[[08:06:16]] [SUCCESS] Screenshot refreshed
[[08:06:16]] [INFO] Refreshing screenshot...
[[08:06:16]] [INFO] rkL0oz4kiL=pass
[[08:06:09]] [SUCCESS] Screenshot refreshed successfully
[[08:06:09]] [SUCCESS] Screenshot refreshed successfully
[[08:06:08]] [INFO] rkL0oz4kiL=running
[[08:06:08]] [INFO] Executing action 55/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:06:08]] [SUCCESS] Screenshot refreshed
[[08:06:08]] [INFO] Refreshing screenshot...
[[08:06:08]] [INFO] HotUJOd6oB=pass
[[08:05:55]] [SUCCESS] Screenshot refreshed successfully
[[08:05:55]] [SUCCESS] Screenshot refreshed successfully
[[08:05:53]] [INFO] HotUJOd6oB=running
[[08:05:53]] [INFO] Executing action 54/576: Restart app: env[appid]
[[08:05:53]] [SUCCESS] Screenshot refreshed
[[08:05:53]] [INFO] Refreshing screenshot...
[[08:05:53]] [SUCCESS] Screenshot refreshed
[[08:05:53]] [INFO] Refreshing screenshot...
[[08:05:50]] [SUCCESS] Screenshot refreshed successfully
[[08:05:50]] [SUCCESS] Screenshot refreshed successfully
[[08:05:50]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[08:05:49]] [SUCCESS] Screenshot refreshed
[[08:05:49]] [INFO] Refreshing screenshot...
[[08:05:44]] [SUCCESS] Screenshot refreshed successfully
[[08:05:44]] [SUCCESS] Screenshot refreshed successfully
[[08:05:44]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[08:05:43]] [SUCCESS] Screenshot refreshed
[[08:05:43]] [INFO] Refreshing screenshot...
[[08:05:40]] [SUCCESS] Screenshot refreshed successfully
[[08:05:40]] [SUCCESS] Screenshot refreshed successfully
[[08:05:39]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[08:05:39]] [SUCCESS] Screenshot refreshed
[[08:05:39]] [INFO] Refreshing screenshot...
[[08:05:36]] [SUCCESS] Screenshot refreshed successfully
[[08:05:36]] [SUCCESS] Screenshot refreshed successfully
[[08:05:35]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:05:35]] [SUCCESS] Screenshot refreshed
[[08:05:35]] [INFO] Refreshing screenshot...
[[08:05:28]] [SUCCESS] Screenshot refreshed successfully
[[08:05:28]] [SUCCESS] Screenshot refreshed successfully
[[08:05:28]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[08:05:27]] [SUCCESS] Screenshot refreshed
[[08:05:27]] [INFO] Refreshing screenshot...
[[08:05:22]] [SUCCESS] Screenshot refreshed successfully
[[08:05:22]] [SUCCESS] Screenshot refreshed successfully
[[08:05:21]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[08:05:21]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[08:05:21]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[08:05:21]] [INFO] vKo6Ox3YrP=running
[[08:05:21]] [INFO] Executing action 53/576: cleanupSteps action
[[08:05:20]] [SUCCESS] Screenshot refreshed
[[08:05:20]] [INFO] Refreshing screenshot...
[[08:05:20]] [INFO] x4yLCZHaCR=pass
[[08:05:17]] [SUCCESS] Screenshot refreshed successfully
[[08:05:17]] [SUCCESS] Screenshot refreshed successfully
[[08:05:17]] [INFO] x4yLCZHaCR=running
[[08:05:17]] [INFO] Executing action 52/576: Terminate app: env[appid]
[[08:05:17]] [SUCCESS] Screenshot refreshed
[[08:05:17]] [INFO] Refreshing screenshot...
[[08:05:17]] [INFO] 2p13JoJbbA=pass
[[08:05:13]] [SUCCESS] Screenshot refreshed successfully
[[08:05:13]] [SUCCESS] Screenshot refreshed successfully
[[08:05:13]] [INFO] 2p13JoJbbA=running
[[08:05:13]] [INFO] Executing action 51/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:05:12]] [SUCCESS] Screenshot refreshed
[[08:05:12]] [INFO] Refreshing screenshot...
[[08:05:12]] [INFO] 2p13JoJbbA=pass
[[08:05:08]] [SUCCESS] Screenshot refreshed successfully
[[08:05:08]] [SUCCESS] Screenshot refreshed successfully
[[08:05:08]] [INFO] 2p13JoJbbA=running
[[08:05:08]] [INFO] Executing action 50/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:05:07]] [SUCCESS] Screenshot refreshed
[[08:05:07]] [INFO] Refreshing screenshot...
[[08:05:07]] [INFO] nyBidG0kHp=pass
[[08:05:00]] [INFO] nyBidG0kHp=running
[[08:05:00]] [INFO] Executing action 49/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[08:05:00]] [SUCCESS] Screenshot refreshed successfully
[[08:05:00]] [SUCCESS] Screenshot refreshed successfully
[[08:05:00]] [SUCCESS] Screenshot refreshed
[[08:05:00]] [INFO] Refreshing screenshot...
[[08:05:00]] [INFO] F4NGh9HrLw=pass
[[08:04:55]] [SUCCESS] Screenshot refreshed successfully
[[08:04:55]] [SUCCESS] Screenshot refreshed successfully
[[08:04:55]] [INFO] F4NGh9HrLw=running
[[08:04:55]] [INFO] Executing action 48/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[08:04:55]] [SUCCESS] Screenshot refreshed
[[08:04:55]] [INFO] Refreshing screenshot...
[[08:04:55]] [INFO] VtMfqK1V9t=pass
[[08:04:47]] [SUCCESS] Screenshot refreshed successfully
[[08:04:47]] [SUCCESS] Screenshot refreshed successfully
[[08:04:47]] [INFO] VtMfqK1V9t=running
[[08:04:47]] [INFO] Executing action 47/576: Tap on element with accessibility_id: Add to bag
[[08:04:46]] [SUCCESS] Screenshot refreshed
[[08:04:46]] [INFO] Refreshing screenshot...
[[08:04:46]] [INFO] NOnuFzXy63=pass
[[08:04:42]] [SUCCESS] Screenshot refreshed successfully
[[08:04:42]] [SUCCESS] Screenshot refreshed successfully
[[08:04:42]] [INFO] NOnuFzXy63=running
[[08:04:42]] [INFO] Executing action 46/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:04:41]] [SUCCESS] Screenshot refreshed
[[08:04:41]] [INFO] Refreshing screenshot...
[[08:04:41]] [INFO] kz9lnCdwoH=pass
[[08:04:37]] [SUCCESS] Screenshot refreshed successfully
[[08:04:37]] [SUCCESS] Screenshot refreshed successfully
[[08:04:37]] [INFO] kz9lnCdwoH=running
[[08:04:37]] [INFO] Executing action 45/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[08:04:37]] [SUCCESS] Screenshot refreshed
[[08:04:37]] [INFO] Refreshing screenshot...
[[08:04:37]] [INFO] kz9lnCdwoH=pass
[[08:04:33]] [SUCCESS] Screenshot refreshed successfully
[[08:04:33]] [SUCCESS] Screenshot refreshed successfully
[[08:04:33]] [INFO] kz9lnCdwoH=running
[[08:04:33]] [INFO] Executing action 44/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:04:32]] [SUCCESS] Screenshot refreshed
[[08:04:32]] [INFO] Refreshing screenshot...
[[08:04:32]] [INFO] qIF9CVPc56=pass
[[08:04:28]] [SUCCESS] Screenshot refreshed successfully
[[08:04:28]] [SUCCESS] Screenshot refreshed successfully
[[08:04:28]] [INFO] qIF9CVPc56=running
[[08:04:28]] [INFO] Executing action 43/576: iOS Function: text - Text: "mat"
[[08:04:28]] [SUCCESS] Screenshot refreshed
[[08:04:28]] [INFO] Refreshing screenshot...
[[08:04:28]] [INFO] yEga5MkcRe=pass
[[08:04:23]] [SUCCESS] Screenshot refreshed successfully
[[08:04:23]] [SUCCESS] Screenshot refreshed successfully
[[08:04:23]] [INFO] yEga5MkcRe=running
[[08:04:23]] [INFO] Executing action 42/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[08:04:23]] [SUCCESS] Screenshot refreshed
[[08:04:23]] [INFO] Refreshing screenshot...
[[08:04:23]] [INFO] F4NGh9HrLw=pass
[[08:04:19]] [SUCCESS] Screenshot refreshed successfully
[[08:04:19]] [SUCCESS] Screenshot refreshed successfully
[[08:04:18]] [INFO] F4NGh9HrLw=running
[[08:04:18]] [INFO] Executing action 41/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[08:04:18]] [SUCCESS] Screenshot refreshed
[[08:04:18]] [INFO] Refreshing screenshot...
[[08:04:18]] [INFO] kz9lnCdwoH=pass
[[08:04:14]] [SUCCESS] Screenshot refreshed successfully
[[08:04:14]] [SUCCESS] Screenshot refreshed successfully
[[08:04:13]] [INFO] kz9lnCdwoH=running
[[08:04:13]] [INFO] Executing action 40/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[08:04:13]] [SUCCESS] Screenshot refreshed
[[08:04:13]] [INFO] Refreshing screenshot...
[[08:04:13]] [INFO] kz9lnCdwoH=pass
[[08:04:09]] [SUCCESS] Screenshot refreshed successfully
[[08:04:09]] [SUCCESS] Screenshot refreshed successfully
[[08:04:09]] [INFO] kz9lnCdwoH=running
[[08:04:09]] [INFO] Executing action 39/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:04:09]] [SUCCESS] Screenshot refreshed
[[08:04:09]] [INFO] Refreshing screenshot...
[[08:04:09]] [INFO] JRheDTvpJf=pass
[[08:04:05]] [SUCCESS] Screenshot refreshed successfully
[[08:04:05]] [SUCCESS] Screenshot refreshed successfully
[[08:04:04]] [INFO] JRheDTvpJf=running
[[08:04:04]] [INFO] Executing action 38/576: iOS Function: text - Text: "Kid toy"
[[08:04:04]] [SUCCESS] Screenshot refreshed
[[08:04:04]] [INFO] Refreshing screenshot...
[[08:04:04]] [INFO] yEga5MkcRe=pass
[[08:04:00]] [SUCCESS] Screenshot refreshed successfully
[[08:04:00]] [SUCCESS] Screenshot refreshed successfully
[[08:04:00]] [INFO] yEga5MkcRe=running
[[08:04:00]] [INFO] Executing action 37/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[08:03:59]] [SUCCESS] Screenshot refreshed
[[08:03:59]] [INFO] Refreshing screenshot...
[[08:03:59]] [INFO] F4NGh9HrLw=pass
[[08:03:56]] [SUCCESS] Screenshot refreshed successfully
[[08:03:56]] [SUCCESS] Screenshot refreshed successfully
[[08:03:55]] [INFO] F4NGh9HrLw=running
[[08:03:55]] [INFO] Executing action 36/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[08:03:55]] [SUCCESS] Screenshot refreshed
[[08:03:55]] [INFO] Refreshing screenshot...
[[08:03:55]] [INFO] XPEr3w6Zof=pass
[[08:03:49]] [SUCCESS] Screenshot refreshed successfully
[[08:03:49]] [SUCCESS] Screenshot refreshed successfully
[[08:03:49]] [INFO] XPEr3w6Zof=running
[[08:03:49]] [INFO] Executing action 35/576: Restart app: env[appid]
[[08:03:48]] [SUCCESS] Screenshot refreshed
[[08:03:48]] [INFO] Refreshing screenshot...
[[08:03:48]] [INFO] PiQRBWBe3E=pass
[[08:03:44]] [SUCCESS] Screenshot refreshed successfully
[[08:03:44]] [SUCCESS] Screenshot refreshed successfully
[[08:03:44]] [INFO] PiQRBWBe3E=running
[[08:03:44]] [INFO] Executing action 34/576: Tap on image: env[device-back-img]
[[08:03:44]] [SUCCESS] Screenshot refreshed
[[08:03:44]] [INFO] Refreshing screenshot...
[[08:03:44]] [INFO] GWoppouz1l=pass
[[08:03:41]] [SUCCESS] Screenshot refreshed successfully
[[08:03:41]] [SUCCESS] Screenshot refreshed successfully
[[08:03:41]] [INFO] GWoppouz1l=running
[[08:03:41]] [INFO] Executing action 33/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[08:03:40]] [SUCCESS] Screenshot refreshed
[[08:03:40]] [INFO] Refreshing screenshot...
[[08:03:40]] [INFO] B6GDXWAmWp=pass
[[08:03:36]] [SUCCESS] Screenshot refreshed successfully
[[08:03:36]] [SUCCESS] Screenshot refreshed successfully
[[08:03:36]] [INFO] B6GDXWAmWp=running
[[08:03:36]] [INFO] Executing action 32/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[08:03:35]] [SUCCESS] Screenshot refreshed
[[08:03:35]] [INFO] Refreshing screenshot...
[[08:03:35]] [INFO] mtYqeDttRc=pass
[[08:03:32]] [SUCCESS] Screenshot refreshed successfully
[[08:03:32]] [SUCCESS] Screenshot refreshed successfully
[[08:03:31]] [INFO] mtYqeDttRc=running
[[08:03:31]] [INFO] Executing action 31/576: Tap on image: env[paypal-close-img]
[[08:03:30]] [SUCCESS] Screenshot refreshed
[[08:03:30]] [INFO] Refreshing screenshot...
[[08:03:30]] [INFO] q6cKxgMAIn=pass
[[08:03:23]] [SUCCESS] Screenshot refreshed successfully
[[08:03:23]] [SUCCESS] Screenshot refreshed successfully
[[08:03:23]] [INFO] q6cKxgMAIn=running
[[08:03:23]] [INFO] Executing action 30/576: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[08:03:23]] [SUCCESS] Screenshot refreshed
[[08:03:23]] [INFO] Refreshing screenshot...
[[08:03:23]] [INFO] KRQDBv2D3A=pass
[[08:03:19]] [SUCCESS] Screenshot refreshed successfully
[[08:03:19]] [SUCCESS] Screenshot refreshed successfully
[[08:03:19]] [INFO] KRQDBv2D3A=running
[[08:03:19]] [INFO] Executing action 29/576: Tap on image: env[device-back-img]
[[08:03:18]] [SUCCESS] Screenshot refreshed
[[08:03:18]] [INFO] Refreshing screenshot...
[[08:03:18]] [INFO] P4b2BITpCf=pass
[[08:03:15]] [SUCCESS] Screenshot refreshed successfully
[[08:03:15]] [SUCCESS] Screenshot refreshed successfully
[[08:03:15]] [INFO] P4b2BITpCf=running
[[08:03:15]] [INFO] Executing action 28/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[08:03:14]] [SUCCESS] Screenshot refreshed
[[08:03:14]] [INFO] Refreshing screenshot...
[[08:03:14]] [INFO] inrxgdWzXr=pass
[[08:03:08]] [SUCCESS] Screenshot refreshed successfully
[[08:03:08]] [SUCCESS] Screenshot refreshed successfully
[[08:03:08]] [INFO] inrxgdWzXr=running
[[08:03:08]] [INFO] Executing action 27/576: Tap on element with accessibility_id: Learn more about Zip
[[08:03:08]] [SUCCESS] Screenshot refreshed
[[08:03:08]] [INFO] Refreshing screenshot...
[[08:03:08]] [INFO] Et3kvnFdxh=pass
[[08:03:04]] [SUCCESS] Screenshot refreshed successfully
[[08:03:04]] [SUCCESS] Screenshot refreshed successfully
[[08:03:04]] [INFO] Et3kvnFdxh=running
[[08:03:04]] [INFO] Executing action 26/576: Tap on image: env[device-back-img]
[[08:03:04]] [INFO] Skipping disabled action 25/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[08:03:03]] [SUCCESS] Screenshot refreshed
[[08:03:03]] [INFO] Refreshing screenshot...
[[08:03:03]] [INFO] pk2DLZFBmx=pass
[[08:02:57]] [SUCCESS] Screenshot refreshed successfully
[[08:02:57]] [SUCCESS] Screenshot refreshed successfully
[[08:02:57]] [INFO] pk2DLZFBmx=running
[[08:02:57]] [INFO] Executing action 24/576: Tap on element with accessibility_id: Learn more about AfterPay
[[08:02:56]] [SUCCESS] Screenshot refreshed
[[08:02:56]] [INFO] Refreshing screenshot...
[[08:02:56]] [INFO] ShJSdXvmVL=pass
[[08:02:49]] [SUCCESS] Screenshot refreshed successfully
[[08:02:49]] [SUCCESS] Screenshot refreshed successfully
[[08:02:48]] [INFO] ShJSdXvmVL=running
[[08:02:48]] [INFO] Executing action 23/576: Swipe up till element accessibilityid: "Learn more about AfterPay" is visible
[[08:02:48]] [SUCCESS] Screenshot refreshed
[[08:02:48]] [INFO] Refreshing screenshot...
[[08:02:48]] [INFO] sHQtYzpI4s=pass
[[08:02:43]] [SUCCESS] Screenshot refreshed successfully
[[08:02:43]] [SUCCESS] Screenshot refreshed successfully
[[08:02:42]] [INFO] sHQtYzpI4s=running
[[08:02:42]] [INFO] Executing action 22/576: Tap on image: env[closebtnimage]
[[08:02:42]] [SUCCESS] Screenshot refreshed
[[08:02:42]] [INFO] Refreshing screenshot...
[[08:02:42]] [INFO] 83tV9A4NOn=pass
[[08:02:38]] [SUCCESS] Screenshot refreshed successfully
[[08:02:38]] [SUCCESS] Screenshot refreshed successfully
[[08:02:38]] [INFO] 83tV9A4NOn=running
[[08:02:38]] [INFO] Executing action 21/576: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[08:02:37]] [SUCCESS] Screenshot refreshed
[[08:02:37]] [INFO] Refreshing screenshot...
[[08:02:37]] [INFO] dCqKBG3e7u=pass
[[08:02:32]] [SUCCESS] Screenshot refreshed successfully
[[08:02:32]] [SUCCESS] Screenshot refreshed successfully
[[08:02:32]] [INFO] dCqKBG3e7u=running
[[08:02:32]] [INFO] Executing action 20/576: Tap on image: env[product-share-img]
[[08:02:32]] [SUCCESS] Screenshot refreshed
[[08:02:32]] [INFO] Refreshing screenshot...
[[08:02:32]] [INFO] kAQ1yIIw3h=pass
[[08:01:56]] [SUCCESS] Screenshot refreshed successfully
[[08:01:56]] [SUCCESS] Screenshot refreshed successfully
[[08:01:55]] [INFO] kAQ1yIIw3h=running
[[08:01:55]] [INFO] Executing action 19/576: Tap on element with xpath:  (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::* with fallback: Coordinates (98, 308)
[[08:01:55]] [SUCCESS] Screenshot refreshed
[[08:01:55]] [INFO] Refreshing screenshot...
[[08:01:55]] [INFO] OmKfD9iBjD=pass
[[08:01:51]] [SUCCESS] Screenshot refreshed successfully
[[08:01:51]] [SUCCESS] Screenshot refreshed successfully
[[08:01:51]] [INFO] OmKfD9iBjD=running
[[08:01:51]] [INFO] Executing action 18/576: Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::*
[[08:01:50]] [SUCCESS] Screenshot refreshed
[[08:01:50]] [INFO] Refreshing screenshot...
[[08:01:50]] [INFO] dMl1PH9Dlc=pass
[[08:01:39]] [SUCCESS] Screenshot refreshed successfully
[[08:01:39]] [SUCCESS] Screenshot refreshed successfully
[[08:01:38]] [INFO] dMl1PH9Dlc=running
[[08:01:38]] [INFO] Executing action 17/576: Wait for 10 ms
[[08:01:38]] [SUCCESS] Screenshot refreshed
[[08:01:38]] [INFO] Refreshing screenshot...
[[08:01:38]] [INFO] eHLWiRoqqS=pass
[[08:01:33]] [SUCCESS] Screenshot refreshed successfully
[[08:01:33]] [SUCCESS] Screenshot refreshed successfully
[[08:01:33]] [INFO] eHLWiRoqqS=running
[[08:01:33]] [INFO] Executing action 16/576: Swipe from (50%, 70%) to (50%, 30%)
[[08:01:32]] [SUCCESS] Screenshot refreshed
[[08:01:32]] [INFO] Refreshing screenshot...
[[08:01:32]] [INFO] huUnpMMjVR=pass
[[08:01:28]] [SUCCESS] Screenshot refreshed successfully
[[08:01:28]] [SUCCESS] Screenshot refreshed successfully
[[08:01:27]] [INFO] huUnpMMjVR=running
[[08:01:27]] [INFO] Executing action 15/576: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[08:01:27]] [SUCCESS] Screenshot refreshed
[[08:01:27]] [INFO] Refreshing screenshot...
[[08:01:27]] [INFO] XmAxcBtFI0=pass
[[08:01:23]] [SUCCESS] Screenshot refreshed successfully
[[08:01:23]] [SUCCESS] Screenshot refreshed successfully
[[08:01:23]] [INFO] XmAxcBtFI0=running
[[08:01:23]] [INFO] Executing action 14/576: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[08:01:22]] [SUCCESS] Screenshot refreshed
[[08:01:22]] [INFO] Refreshing screenshot...
[[08:01:22]] [INFO] ktAufkDJnF=pass
[[08:01:18]] [SUCCESS] Screenshot refreshed successfully
[[08:01:18]] [SUCCESS] Screenshot refreshed successfully
[[08:01:18]] [INFO] ktAufkDJnF=running
[[08:01:18]] [INFO] Executing action 13/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show")]
[[08:01:18]] [SUCCESS] Screenshot refreshed
[[08:01:18]] [INFO] Refreshing screenshot...
[[08:01:18]] [INFO] a50JhCx0ir=pass
[[08:01:14]] [SUCCESS] Screenshot refreshed successfully
[[08:01:14]] [SUCCESS] Screenshot refreshed successfully
[[08:01:14]] [INFO] a50JhCx0ir=running
[[08:01:14]] [INFO] Executing action 12/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[08:01:13]] [SUCCESS] Screenshot refreshed
[[08:01:13]] [INFO] Refreshing screenshot...
[[08:01:13]] [INFO] Y1O1clhMSJ=pass
[[08:01:09]] [SUCCESS] Screenshot refreshed successfully
[[08:01:09]] [SUCCESS] Screenshot refreshed successfully
[[08:01:09]] [INFO] Y1O1clhMSJ=running
[[08:01:09]] [INFO] Executing action 11/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[08:01:09]] [SUCCESS] Screenshot refreshed
[[08:01:09]] [INFO] Refreshing screenshot...
[[08:01:09]] [INFO] lYPskZt0Ya=pass
[[08:01:05]] [SUCCESS] Screenshot refreshed successfully
[[08:01:05]] [SUCCESS] Screenshot refreshed successfully
[[08:01:05]] [INFO] lYPskZt0Ya=running
[[08:01:05]] [INFO] Executing action 10/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:01:04]] [SUCCESS] Screenshot refreshed
[[08:01:04]] [INFO] Refreshing screenshot...
[[08:01:04]] [INFO] xUbWFa8Ok2=pass
[[08:01:00]] [SUCCESS] Screenshot refreshed successfully
[[08:01:00]] [SUCCESS] Screenshot refreshed successfully
[[08:01:00]] [INFO] xUbWFa8Ok2=running
[[08:01:00]] [INFO] Executing action 9/576: Tap on Text: "Latest"
[[08:00:59]] [SUCCESS] Screenshot refreshed
[[08:00:59]] [INFO] Refreshing screenshot...
[[08:00:59]] [INFO] RbNtEW6N9T=pass
[[08:00:55]] [SUCCESS] Screenshot refreshed successfully
[[08:00:55]] [SUCCESS] Screenshot refreshed successfully
[[08:00:55]] [INFO] RbNtEW6N9T=running
[[08:00:55]] [INFO] Executing action 8/576: Tap on Text: "Toys"
[[08:00:54]] [SUCCESS] Screenshot refreshed
[[08:00:54]] [INFO] Refreshing screenshot...
[[08:00:54]] [INFO] ltDXyWvtEz=pass
[[08:00:50]] [SUCCESS] Screenshot refreshed successfully
[[08:00:50]] [SUCCESS] Screenshot refreshed successfully
[[08:00:49]] [INFO] ltDXyWvtEz=running
[[08:00:49]] [INFO] Executing action 7/576: Tap on image: env[device-back-img]
[[08:00:49]] [SUCCESS] Screenshot refreshed
[[08:00:49]] [INFO] Refreshing screenshot...
[[08:00:49]] [INFO] QPKR6jUF9O=pass
[[08:00:46]] [SUCCESS] Screenshot refreshed successfully
[[08:00:46]] [SUCCESS] Screenshot refreshed successfully
[[08:00:46]] [INFO] QPKR6jUF9O=running
[[08:00:46]] [INFO] Executing action 6/576: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[08:00:46]] [SUCCESS] Screenshot refreshed
[[08:00:46]] [INFO] Refreshing screenshot...
[[08:00:46]] [INFO] vfwUVEyq6X=pass
[[08:00:43]] [SUCCESS] Screenshot refreshed successfully
[[08:00:43]] [SUCCESS] Screenshot refreshed successfully
[[08:00:43]] [INFO] vfwUVEyq6X=running
[[08:00:43]] [INFO] Executing action 5/576: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[08:00:42]] [SUCCESS] Screenshot refreshed
[[08:00:42]] [INFO] Refreshing screenshot...
[[08:00:42]] [INFO] Xr6F8gdd8q=pass
[[08:00:38]] [SUCCESS] Screenshot refreshed successfully
[[08:00:38]] [SUCCESS] Screenshot refreshed successfully
[[08:00:38]] [INFO] Xr6F8gdd8q=running
[[08:00:38]] [INFO] Executing action 4/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[08:00:38]] [SUCCESS] Screenshot refreshed
[[08:00:38]] [INFO] Refreshing screenshot...
[[08:00:38]] [INFO] Xr6F8gdd8q=pass
[[08:00:35]] [INFO] Xr6F8gdd8q=running
[[08:00:35]] [INFO] Executing action 3/576: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[08:00:35]] [SUCCESS] Screenshot refreshed successfully
[[08:00:35]] [SUCCESS] Screenshot refreshed successfully
[[08:00:34]] [SUCCESS] Screenshot refreshed
[[08:00:34]] [INFO] Refreshing screenshot...
[[08:00:34]] [INFO] F4NGh9HrLw=pass
[[08:00:32]] [INFO] Collapsed all test cases
[[08:00:29]] [SUCCESS] Screenshot refreshed successfully
[[08:00:29]] [SUCCESS] Screenshot refreshed successfully
[[08:00:29]] [INFO] F4NGh9HrLw=running
[[08:00:29]] [INFO] Executing action 2/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[08:00:28]] [SUCCESS] Screenshot refreshed
[[08:00:28]] [INFO] Refreshing screenshot...
[[08:00:28]] [INFO] H9fy9qcFbZ=pass
[[08:00:23]] [INFO] H9fy9qcFbZ=running
[[08:00:23]] [INFO] Executing action 1/576: Restart app: env[appid]
[[08:00:23]] [INFO] ExecutionManager: Starting execution of 576 actions...
[[08:00:23]] [SUCCESS] Cleared 1 screenshots from database
[[08:00:23]] [INFO] Clearing screenshots from database before execution...
[[08:00:23]] [SUCCESS] All screenshots deleted successfully
[[08:00:23]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[08:00:23]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250701_080023/screenshots
[[08:00:23]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250701_080023
[[08:00:23]] [SUCCESS] Report directory initialized successfully
[[08:00:23]] [INFO] Initializing report directory and screenshots folder for test suite...
[[08:00:08]] [SUCCESS] All screenshots deleted successfully
[[08:00:08]] [INFO] All actions cleared
[[08:00:08]] [INFO] Cleaning up screenshots...
[[08:00:03]] [SUCCESS] Screenshot refreshed successfully
[[08:00:01]] [SUCCESS] Screenshot refreshed
[[08:00:01]] [INFO] Refreshing screenshot...
[[08:00:00]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[08:00:00]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[07:59:54]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[07:58:58]] [SUCCESS] Found 1 device(s)
[[07:58:58]] [INFO] Refreshing device list...
