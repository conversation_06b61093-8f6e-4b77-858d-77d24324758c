/* Test Case Modification Styles */

/* Search Results Table */
#searchResultsTable {
    font-size: 0.9rem;
}

#searchResultsTable th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

#searchResultsTable tbody tr:hover {
    background-color: #f8f9fa;
}

.matching-actions {
    max-width: 200px;
}

.matching-actions .badge {
    font-size: 0.7rem;
    margin-bottom: 2px;
}

/* Action Items */
.action-item {
    border-left: 4px solid #007bff;
    transition: all 0.2s ease;
}

.action-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-item .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.action-item .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Action Editor */
.action-editor {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 0.5rem;
}

.action-editor .form-label {
    font-weight: 500;
    font-size: 0.875rem;
    color: #495057;
}

.action-editor .form-control,
.action-editor .form-select {
    font-size: 0.875rem;
}

/* Search Filters */
.form-select[multiple] {
    min-height: 100px;
}

.form-text {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Cards */
.card-header {
    font-weight: 600;
}

.card-header small {
    font-weight: 400;
    opacity: 0.8;
}

/* Notifications */
#notification-container {
    max-width: 400px;
}

#notification-container .alert {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: none;
}

#notification-container .alert-success {
    background-color: #d1e7dd;
    color: #0a3622;
}

#notification-container .alert-danger {
    background-color: #f8d7da;
    color: #58151c;
}

#notification-container .alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

#notification-container .alert-info {
    background-color: #cff4fc;
    color: #055160;
}

/* Progress Indicator */
#progress-indicator .card {
    min-width: 250px;
    border: none;
}

#progress-indicator .spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .action-item .btn-group {
        flex-direction: column;
    }
    
    .action-item .btn-group .btn {
        margin-bottom: 2px;
        border-radius: 0.375rem !important;
    }
    
    .matching-actions {
        max-width: none;
    }
    
    #searchResultsTable {
        font-size: 0.8rem;
    }
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
}

.btn .bi-hourglass-split {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Action Type Badges */
.badge.bg-primary {
    background-color: #0d6efd !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

/* Form Enhancements */
.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Table Enhancements */
.table-hover tbody tr:hover td {
    background-color: rgba(13, 110, 253, 0.075);
}

/* Button Enhancements */
.btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-outline-success:hover {
    background-color: #198754;
    border-color: #198754;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Scrollbar Styling */
.overflow-auto::-webkit-scrollbar {
    width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Empty State */
.text-center.text-muted {
    padding: 2rem;
}

.text-center.text-muted .bi {
    opacity: 0.5;
}

/* Action Details */
.action-details {
    font-size: 0.875rem;
    line-height: 1.5;
}

.action-details strong {
    color: #495057;
}

/* Selection UI */
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Card Transitions */
.card {
    transition: all 0.2s ease;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Tab Content */
.tab-pane {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
