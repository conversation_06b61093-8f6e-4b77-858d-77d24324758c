/**
 * Test Case Modification Manager
 * Handles search, filter, and editing of test cases
 */

class TestCaseModificationManager {
    constructor() {
        this.searchResults = [];
        this.selectedTestCases = [];
        this.currentEditingTestCase = null;
        this.actionTypes = [];
        this.locatorTypes = [];
        this.jsonEditor = new JSONTestCaseEditor();

        this.initializeEventListeners();
        this.initializeKeyboardShortcuts();
        this.loadFilterOptions();
    }

    initializeEventListeners() {
        // Search functionality
        document.getElementById('searchTestCasesBtn')?.addEventListener('click', () => {
            this.searchTestCases();
        });

        document.getElementById('clearSearchBtn')?.addEventListener('click', () => {
            this.clearSearch();
        });

        document.getElementById('loadFiltersBtn')?.addEventListener('click', () => {
            this.loadFilterOptions();
        });

        // Selection functionality
        document.getElementById('selectAllResults')?.addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        document.getElementById('editSelectedBtn')?.addEventListener('click', () => {
            this.editSelectedTestCase();
        });



        // Add search on Enter key
        const searchInputs = [
            'locatorValueSearch', 'imageFileSearch', 'textValueSearch'
        ];

        searchInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.searchTestCases();
                    }
                });
            }
        });
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + S to save test case
            if ((e.ctrlKey || e.metaKey) && e.key === 's' && this.currentEditingTestCase) {
                e.preventDefault();
                this.saveTestCase();
            }

            // Escape to cancel edit
            if (e.key === 'Escape' && this.currentEditingTestCase) {
                this.cancelEdit();
            }

            // Ctrl/Cmd + F to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                const searchInput = document.getElementById('locatorValueSearch');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
    }

    async loadFilterOptions() {
        try {
            this.showLoadingState('loadFiltersBtn', true);

            // Load action types
            const actionTypesResponse = await fetch('/api/test_cases/action_types');
            const actionTypesData = await actionTypesResponse.json();
            
            if (actionTypesData.status === 'success') {
                this.actionTypes = actionTypesData.action_types;
                this.populateActionTypeFilter();
            }

            // Load locator types
            const locatorTypesResponse = await fetch('/api/test_cases/locator_types');
            const locatorTypesData = await locatorTypesResponse.json();
            
            if (locatorTypesData.status === 'success') {
                this.locatorTypes = locatorTypesData.locator_types;
                this.populateLocatorTypeFilter();
            }

            this.showNotification('Filter options loaded successfully', 'success');

        } catch (error) {
            console.error('Error loading filter options:', error);
            this.showNotification('Failed to load filter options', 'error');
        } finally {
            this.showLoadingState('loadFiltersBtn', false);
        }
    }

    populateActionTypeFilter() {
        const select = document.getElementById('actionTypeFilter');
        if (!select) return;

        // Clear existing options except the first one
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        // Add action types
        this.actionTypes.forEach(actionType => {
            const option = document.createElement('option');
            option.value = actionType;
            option.textContent = actionType;
            select.appendChild(option);
        });
    }

    populateLocatorTypeFilter() {
        const select = document.getElementById('locatorTypeFilter');
        if (!select) return;

        // Clear existing options except the first one
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        // Add locator types
        this.locatorTypes.forEach(locatorType => {
            const option = document.createElement('option');
            option.value = locatorType;
            option.textContent = locatorType;
            select.appendChild(option);
        });
    }

    async searchTestCases() {
        try {
            this.showLoadingState('searchTestCasesBtn', true);
            this.showProgressIndicator('Searching test cases...');

            // Collect search criteria
            const searchCriteria = this.getSearchCriteria();

            // Validate that at least one search criterion is provided
            const hasSearchCriteria = Object.values(searchCriteria).some(value =>
                Array.isArray(value) ? value.length > 0 : value.trim() !== ''
            );

            if (!hasSearchCriteria) {
                this.showNotification('Please enter at least one search criterion', 'warning');
                return;
            }

            const response = await fetch('/api/test_cases/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(searchCriteria)
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.searchResults = data.results;
                this.displaySearchResults();
                this.showSearchResultsCard();

                if (data.results.length === 0) {
                    this.showNotification('No test cases found matching your criteria', 'info');
                } else {
                    this.showNotification(`Found ${data.results.length} test case(s)`, 'success');
                }
            } else {
                throw new Error(data.error || 'Search failed');
            }

        } catch (error) {
            console.error('Search error:', error);
            this.showNotification(`Search failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('searchTestCasesBtn', false);
            this.hideProgressIndicator();
        }
    }

    getSearchCriteria() {
        const actionTypeSelect = document.getElementById('actionTypeFilter');
        const locatorTypeSelect = document.getElementById('locatorTypeFilter');

        return {
            action_types: Array.from(actionTypeSelect.selectedOptions)
                .map(option => option.value)
                .filter(value => value !== ''),
            locator_types: Array.from(locatorTypeSelect.selectedOptions)
                .map(option => option.value)
                .filter(value => value !== ''),
            locator_value: document.getElementById('locatorValueSearch')?.value || '',
            image_files: document.getElementById('imageFileSearch')?.value ?
                [document.getElementById('imageFileSearch').value] : [],
            text_values: document.getElementById('textValueSearch')?.value ?
                [document.getElementById('textValueSearch').value] : []
        };
    }

    displaySearchResults() {
        const tbody = document.getElementById('searchResultsBody');
        const countElement = document.getElementById('searchResultsCount');
        
        if (!tbody || !countElement) return;

        // Update count
        countElement.textContent = `${this.searchResults.length} test cases found`;

        // Clear existing results
        tbody.innerHTML = '';

        // Add results
        this.searchResults.forEach((result, index) => {
            const row = this.createSearchResultRow(result, index);
            tbody.appendChild(row);
        });

        // Reset selection
        this.selectedTestCases = [];
        this.updateSelectionUI();
    }

    createSearchResultRow(result, index) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input result-checkbox" 
                       data-filename="${result.filename}" data-index="${index}">
            </td>
            <td>
                <div class="fw-bold">${this.escapeHtml(result.name)}</div>
                <small class="text-muted">${this.escapeHtml(result.filename)}</small>
                ${result.description ? `<div class="text-muted small">${this.escapeHtml(result.description)}</div>` : ''}
            </td>
            <td>
                <span class="badge bg-primary">${result.action_count}</span>
            </td>
            <td>
                <small>${result.modified_date}</small>
            </td>
            <td>
                <div class="matching-actions">
                    ${result.matching_actions.slice(0, 3).map(action => 
                        `<span class="badge bg-secondary me-1">${action.type}</span>`
                    ).join('')}
                    ${result.matching_actions.length > 3 ? 
                        `<span class="badge bg-light text-dark">+${result.matching_actions.length - 3}</span>` : ''}
                </div>
            </td>
            <td>
                <button class="btn btn-sm btn-primary edit-single-btn" data-filename="${result.filename}">
                    <i class="bi bi-pencil"></i> Edit
                </button>
            </td>
        `;

        // Add event listeners
        const checkbox = row.querySelector('.result-checkbox');
        checkbox.addEventListener('change', () => this.updateSelection());

        const editBtn = row.querySelector('.edit-single-btn');
        editBtn.addEventListener('click', () => this.jsonEditor.openEditor(result.filename, this.getSearchCriteria()));

        return row;
    }

    showSearchResultsCard() {
        const card = document.getElementById('searchResultsCard');
        if (card) {
            card.style.display = 'block';
        }
    }

    clearSearch() {
        // Clear all search inputs
        document.getElementById('actionTypeFilter').selectedIndex = 0;
        document.getElementById('locatorTypeFilter').selectedIndex = 0;
        document.getElementById('locatorValueSearch').value = '';
        document.getElementById('imageFileSearch').value = '';
        document.getElementById('textValueSearch').value = '';
        document.getElementById('testCaseNameSearch').value = '';
        document.getElementById('descriptionSearch').value = '';

        // Hide search results
        const card = document.getElementById('searchResultsCard');
        if (card) {
            card.style.display = 'none';
        }

        // Clear results
        this.searchResults = [];
        this.selectedTestCases = [];
    }

    updateSelection() {
        const checkboxes = document.querySelectorAll('.result-checkbox:checked');
        this.selectedTestCases = Array.from(checkboxes).map(cb => cb.dataset.filename);
        this.updateSelectionUI();
    }

    updateSelectionUI() {
        const count = this.selectedTestCases.length;
        const countElement = document.getElementById('selectionCount');
        const editBtn = document.getElementById('editSelectedBtn');
        const bulkBtn = document.getElementById('bulkModifySelectedBtn');

        if (countElement) {
            countElement.textContent = `${count} selected`;
        }

        if (editBtn) {
            editBtn.disabled = count !== 1;
        }

        if (bulkBtn) {
            bulkBtn.disabled = count === 0;
        }
    }

    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.result-checkbox');
        checkboxes.forEach(cb => {
            cb.checked = checked;
        });
        this.updateSelection();
    }

    async editTestCase(filename) {
        try {
            this.showLoadingState('editSelectedBtn', true);

            const response = await fetch(`/api/test_cases/load/${filename}`);
            const data = await response.json();

            if (data.status === 'success') {
                this.currentEditingTestCase = {
                    filename: filename,
                    data: data.test_case
                };
                this.showTestCaseEditor();
                this.populateTestCaseEditor();
            } else {
                throw new Error(data.error || 'Failed to load test case');
            }

        } catch (error) {
            console.error('Error loading test case:', error);
            this.showNotification(`Failed to load test case: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('editSelectedBtn', false);
        }
    }

    editSelectedTestCase() {
        if (this.selectedTestCases.length === 1) {
            this.jsonEditor.openEditor(this.selectedTestCases[0], this.getSearchCriteria());
        }
    }

    showTestCaseEditor() {
        const card = document.getElementById('testCaseEditorCard');
        if (card) {
            card.style.display = 'block';
            card.scrollIntoView({ behavior: 'smooth' });
        }
    }

    populateTestCaseEditor() {
        if (!this.currentEditingTestCase) return;

        const testCase = this.currentEditingTestCase.data;

        // Update header
        document.getElementById('editorTestCaseName').textContent =
            `Editing: ${testCase.name || 'Unnamed Test Case'}`;

        // Populate form fields
        document.getElementById('editTestCaseName').value = testCase.name || '';
        document.getElementById('editTestCaseDescription').value = testCase.description || '';

        // Populate actions
        this.populateActionsContainer(testCase.actions || []);
    }

    populateActionsContainer(actions) {
        const container = document.getElementById('actionsContainer');
        if (!container) return;

        container.innerHTML = '';

        actions.forEach((action, index) => {
            const actionElement = this.createActionElement(action, index);
            container.appendChild(actionElement);
        });

        // Add empty state if no actions
        if (actions.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="bi bi-plus-circle fs-1"></i>
                    <p>No actions yet. Click "Add Action" to get started.</p>
                </div>
            `;
        }
    }

    createActionElement(action, index) {
        const actionDiv = document.createElement('div');
        actionDiv.className = 'action-item card mb-2';
        actionDiv.dataset.index = index;

        actionDiv.innerHTML = `
            <div class="card-header d-flex justify-content-between align-items-center py-2">
                <div class="d-flex align-items-center">
                    <span class="badge bg-primary me-2">${index + 1}</span>
                    <strong>${this.escapeHtml(action.type || 'Unknown')}</strong>
                    <small class="text-muted ms-2">${action.action_id || 'No ID'}</small>
                </div>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary move-up-btn" ${index === 0 ? 'disabled' : ''}>
                        <i class="bi bi-arrow-up"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary move-down-btn">
                        <i class="bi bi-arrow-down"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary edit-action-btn">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger delete-action-btn">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
            <div class="card-body py-2">
                <div class="action-details">
                    ${this.renderActionDetails(action)}
                </div>
                <div class="action-editor" style="display: none;">
                    ${this.renderActionEditor(action, index)}
                </div>
            </div>
        `;

        // Add event listeners
        this.addActionEventListeners(actionDiv, index);

        return actionDiv;
    }

    renderActionDetails(action) {
        let details = [];

        if (action.locator_type && action.locator_value) {
            details.push(`<strong>Locator:</strong> ${action.locator_type} = "${this.escapeHtml(action.locator_value)}"`);
        }

        if (action.text) {
            details.push(`<strong>Text:</strong> "${this.escapeHtml(action.text)}"`);
        }

        if (action.image) {
            details.push(`<strong>Image:</strong> ${this.escapeHtml(action.image)}`);
        }

        if (action.timeout) {
            details.push(`<strong>Timeout:</strong> ${action.timeout}s`);
        }

        if (action.coordinates) {
            details.push(`<strong>Coordinates:</strong> (${action.coordinates.x}, ${action.coordinates.y})`);
        }

        return details.length > 0 ? details.join('<br>') : '<em class="text-muted">No additional parameters</em>';
    }

    renderActionEditor(action, index) {
        return `
            <div class="row">
                <div class="col-md-4 mb-2">
                    <label class="form-label">Action Type</label>
                    <select class="form-select action-type-select" data-index="${index}">
                        ${this.renderActionTypeOptions(action.type)}
                    </select>
                </div>
                <div class="col-md-4 mb-2">
                    <label class="form-label">Locator Type</label>
                    <select class="form-select locator-type-select" data-index="${index}">
                        <option value="">None</option>
                        ${this.locatorTypes.map(type =>
                            `<option value="${type}" ${action.locator_type === type ? 'selected' : ''}>${type}</option>`
                        ).join('')}
                    </select>
                </div>
                <div class="col-md-4 mb-2">
                    <label class="form-label">Locator Value</label>
                    <input type="text" class="form-control locator-value-input"
                           value="${this.escapeHtml(action.locator_value || '')}" data-index="${index}">
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-2">
                    <label class="form-label">Text</label>
                    <input type="text" class="form-control text-input"
                           value="${this.escapeHtml(action.text || '')}" data-index="${index}">
                </div>
                <div class="col-md-4 mb-2">
                    <label class="form-label">Image</label>
                    <input type="text" class="form-control image-input"
                           value="${this.escapeHtml(action.image || '')}" data-index="${index}">
                </div>
                <div class="col-md-4 mb-2">
                    <label class="form-label">Timeout (seconds)</label>
                    <input type="number" class="form-control timeout-input"
                           value="${action.timeout || ''}" data-index="${index}" min="1" max="300">
                </div>
            </div>
            <div class="d-flex gap-2 mt-2">
                <button type="button" class="btn btn-sm btn-success save-action-btn" data-index="${index}">
                    <i class="bi bi-check"></i> Save
                </button>
                <button type="button" class="btn btn-sm btn-secondary cancel-action-edit-btn" data-index="${index}">
                    <i class="bi bi-x"></i> Cancel
                </button>
            </div>
        `;
    }

    renderActionTypeOptions(selectedType) {
        const commonActionTypes = [
            'tap', 'clickElement', 'inputText', 'textClear', 'waitTill', 'waitElement',
            'takeScreenshot', 'getValue', 'compareValue', 'checkIfExists', 'tapOnImage',
            'doubleTap', 'longPress', 'swipe', 'scroll', 'wait', 'info'
        ];

        // Combine common types with discovered types
        const allTypes = [...new Set([...commonActionTypes, ...this.actionTypes])];

        return allTypes.map(type =>
            `<option value="${type}" ${type === selectedType ? 'selected' : ''}>${type}</option>`
        ).join('');
    }

    addActionEventListeners(actionDiv, index) {
        // Move up/down buttons
        const moveUpBtn = actionDiv.querySelector('.move-up-btn');
        const moveDownBtn = actionDiv.querySelector('.move-down-btn');
        const editBtn = actionDiv.querySelector('.edit-action-btn');
        const deleteBtn = actionDiv.querySelector('.delete-action-btn');

        moveUpBtn?.addEventListener('click', () => this.moveAction(index, -1));
        moveDownBtn?.addEventListener('click', () => this.moveAction(index, 1));
        editBtn?.addEventListener('click', () => this.toggleActionEdit(index));
        deleteBtn?.addEventListener('click', () => this.deleteAction(index));

        // Save/cancel buttons in editor
        const saveBtn = actionDiv.querySelector('.save-action-btn');
        const cancelBtn = actionDiv.querySelector('.cancel-action-edit-btn');

        saveBtn?.addEventListener('click', () => this.saveActionEdit(index));
        cancelBtn?.addEventListener('click', () => this.cancelActionEdit(index));
    }

    addNewAction() {
        if (!this.currentEditingTestCase) return;

        const newAction = {
            action_id: this.generateActionId(),
            type: 'tap',
            locator_type: 'xpath',
            locator_value: '',
            timestamp: Date.now()
        };

        this.currentEditingTestCase.data.actions = this.currentEditingTestCase.data.actions || [];
        this.currentEditingTestCase.data.actions.push(newAction);

        this.populateActionsContainer(this.currentEditingTestCase.data.actions);

        // Scroll to the new action and start editing
        const container = document.getElementById('actionsContainer');
        if (container) {
            container.scrollTop = container.scrollHeight;
            setTimeout(() => {
                this.toggleActionEdit(this.currentEditingTestCase.data.actions.length - 1);
            }, 100);
        }
    }

    toggleActionEdit(index) {
        const actionDiv = document.querySelector(`[data-index="${index}"]`);
        if (!actionDiv) return;

        const detailsDiv = actionDiv.querySelector('.action-details');
        const editorDiv = actionDiv.querySelector('.action-editor');

        if (detailsDiv.style.display === 'none') {
            // Cancel edit mode
            detailsDiv.style.display = 'block';
            editorDiv.style.display = 'none';
        } else {
            // Enter edit mode
            detailsDiv.style.display = 'none';
            editorDiv.style.display = 'block';
        }
    }

    saveActionEdit(index) {
        if (!this.currentEditingTestCase) return;

        const actionDiv = document.querySelector(`[data-index="${index}"]`);
        if (!actionDiv) return;

        // Collect form data
        const actionData = {
            action_id: this.currentEditingTestCase.data.actions[index].action_id,
            type: actionDiv.querySelector('.action-type-select').value,
            locator_type: actionDiv.querySelector('.locator-type-select').value,
            locator_value: actionDiv.querySelector('.locator-value-input').value,
            text: actionDiv.querySelector('.text-input').value,
            image: actionDiv.querySelector('.image-input').value,
            timeout: parseInt(actionDiv.querySelector('.timeout-input').value) || undefined,
            timestamp: this.currentEditingTestCase.data.actions[index].timestamp || Date.now()
        };

        // Remove empty fields
        Object.keys(actionData).forEach(key => {
            if (actionData[key] === '' || actionData[key] === undefined) {
                delete actionData[key];
            }
        });

        // Update the action
        this.currentEditingTestCase.data.actions[index] = actionData;

        // Refresh the display
        this.populateActionsContainer(this.currentEditingTestCase.data.actions);

        this.showNotification('Action updated successfully', 'success');
    }

    cancelActionEdit(index) {
        this.toggleActionEdit(index);
    }

    deleteAction(index) {
        if (!this.currentEditingTestCase) return;

        const action = this.currentEditingTestCase.data.actions[index];
        const actionName = action.type || 'Unknown Action';

        this.showConfirmDialog(
            'Delete Action',
            `Are you sure you want to delete the "${actionName}" action? This cannot be undone.`,
            () => {
                this.currentEditingTestCase.data.actions.splice(index, 1);
                this.populateActionsContainer(this.currentEditingTestCase.data.actions);
                this.showNotification('Action deleted successfully', 'success');
            }
        );
    }

    moveAction(index, direction) {
        if (!this.currentEditingTestCase) return;

        const actions = this.currentEditingTestCase.data.actions;
        const newIndex = index + direction;

        if (newIndex < 0 || newIndex >= actions.length) return;

        // Swap actions
        [actions[index], actions[newIndex]] = [actions[newIndex], actions[index]];

        this.populateActionsContainer(actions);
        this.showNotification('Action moved successfully', 'success');
    }

    async validateTestCase() {
        if (!this.currentEditingTestCase) return;

        try {
            this.showLoadingState('validateTestCaseBtn', true);

            // Update test case data from form
            this.updateTestCaseFromForm();

            const response = await fetch('/api/test_cases/save_modified', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filename: this.currentEditingTestCase.filename,
                    test_case: this.currentEditingTestCase.data,
                    validate_only: true
                })
            });

            const data = await response.json();

            if (response.ok && data.status === 'success') {
                this.showNotification('Test case validation passed!', 'success');
            } else {
                const errors = data.validation_errors || [data.error || 'Validation failed'];
                this.showValidationErrors(errors);
            }

        } catch (error) {
            console.error('Validation error:', error);
            this.showNotification(`Validation failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('validateTestCaseBtn', false);
        }
    }

    async saveTestCase() {
        if (!this.currentEditingTestCase) return;

        try {
            this.showLoadingState('saveTestCaseBtn', true);

            // Update test case data from form
            this.updateTestCaseFromForm();

            const response = await fetch('/api/test_cases/save_modified', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filename: this.currentEditingTestCase.filename,
                    test_case: this.currentEditingTestCase.data
                })
            });

            const data = await response.json();

            if (response.ok && data.status === 'success') {
                this.showNotification('Test case saved successfully!', 'success');
                this.cancelEdit();

                // Refresh search results if visible
                if (this.searchResults.length > 0) {
                    this.searchTestCases();
                }
            } else {
                const errors = data.validation_errors || [data.error || 'Save failed'];
                this.showValidationErrors(errors);
            }

        } catch (error) {
            console.error('Save error:', error);
            this.showNotification(`Save failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('saveTestCaseBtn', false);
        }
    }

    updateTestCaseFromForm() {
        if (!this.currentEditingTestCase) return;

        this.currentEditingTestCase.data.name = document.getElementById('editTestCaseName').value;
        this.currentEditingTestCase.data.description = document.getElementById('editTestCaseDescription').value;
        this.currentEditingTestCase.data.updated = new Date().toISOString();
    }

    cancelEdit() {
        this.currentEditingTestCase = null;

        const card = document.getElementById('testCaseEditorCard');
        if (card) {
            card.style.display = 'none';
        }

        // Clear form
        document.getElementById('editTestCaseName').value = '';
        document.getElementById('editTestCaseDescription').value = '';
        document.getElementById('actionsContainer').innerHTML = '';
    }

    showValidationErrors(errors) {
        const errorHtml = errors.map(error => `<li>${this.escapeHtml(error)}</li>`).join('');
        this.showNotification(`
            <strong>Validation Errors:</strong>
            <ul class="mb-0 mt-2">${errorHtml}</ul>
        `, 'error');
    }

    generateActionId() {
        // Generate a 10-character alphanumeric ID
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 10; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    async createBackup() {
        if (!this.currentEditingTestCase) return;

        try {
            this.showLoadingState('createBackupBtn', true);

            const response = await fetch(`/api/test_cases/backup/${this.currentEditingTestCase.filename}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (response.ok && data.status === 'success') {
                this.showNotification('Backup created successfully!', 'success');
            } else {
                throw new Error(data.error || 'Backup failed');
            }

        } catch (error) {
            console.error('Backup error:', error);
            this.showNotification(`Backup failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('createBackupBtn', false);
        }
    }

    // Utility methods
    showLoadingState(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        if (loading) {
            button.disabled = true;
            const icon = button.querySelector('i');
            if (icon) {
                icon.className = 'bi bi-hourglass-split';
            }
        } else {
            button.disabled = false;
            const icon = button.querySelector('i');
            if (icon) {
                // Restore original icon based on button ID
                const iconMap = {
                    'searchTestCasesBtn': 'bi bi-search',
                    'loadFiltersBtn': 'bi bi-arrow-clockwise',
                    'editSelectedBtn': 'bi bi-pencil'
                };
                icon.className = iconMap[buttonId] || 'bi bi-check';
            }
        }
    }

    showNotification(message, type = 'info', duration = 5000) {
        // Create a notification container if it doesn't exist
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            document.body.appendChild(container);
        }

        // Create notification
        const alertClass = type === 'error' ? 'alert-danger' :
                          type === 'success' ? 'alert-success' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show mb-2`;
        notification.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="bi ${this.getNotificationIcon(type)} me-2 mt-1"></i>
                <div class="flex-grow-1">${message}</div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        container.appendChild(notification);

        // Auto-remove after specified duration
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 150);
                }
            }, duration);
        }
    }

    getNotificationIcon(type) {
        const iconMap = {
            'success': 'bi-check-circle-fill',
            'error': 'bi-exclamation-triangle-fill',
            'warning': 'bi-exclamation-circle-fill',
            'info': 'bi-info-circle-fill'
        };
        return iconMap[type] || iconMap['info'];
    }

    showConfirmDialog(title, message, onConfirm, onCancel = null) {
        const modalId = 'confirmDialog' + Date.now();
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${modalId}Label">
                                <i class="bi bi-question-circle text-warning me-2"></i>${title}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${message}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary confirm-btn">Confirm</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById(modalId));

        // Add event listeners
        const confirmBtn = document.querySelector(`#${modalId} .confirm-btn`);
        confirmBtn.addEventListener('click', () => {
            modal.hide();
            if (onConfirm) onConfirm();
        });

        document.getElementById(modalId).addEventListener('hidden.bs.modal', () => {
            document.getElementById(modalId).remove();
            if (onCancel) onCancel();
        });

        modal.show();
    }

    showProgressIndicator(message = 'Processing...') {
        // Remove existing progress indicator
        this.hideProgressIndicator();

        const progressHtml = `
            <div id="progress-indicator" class="position-fixed top-50 start-50 translate-middle" style="z-index: 10000;">
                <div class="card shadow">
                    <div class="card-body text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="fw-bold">${message}</div>
                    </div>
                </div>
            </div>
            <div id="progress-backdrop" class="position-fixed top-0 start-0 w-100 h-100 bg-dark bg-opacity-50" style="z-index: 9999;"></div>
        `;

        document.body.insertAdjacentHTML('beforeend', progressHtml);
    }

    hideProgressIndicator() {
        const indicator = document.getElementById('progress-indicator');
        const backdrop = document.getElementById('progress-backdrop');

        if (indicator) indicator.remove();
        if (backdrop) backdrop.remove();
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

/**
 * JSON Test Case Editor
 * Handles JSON editing functionality with syntax highlighting and validation
 */
class JSONTestCaseEditor {
    constructor() {
        this.currentTestCase = null;
        this.originalJson = null;
        this.searchCriteria = null;
        this.searchMatches = [];
        this.currentMatchIndex = -1;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Modal event listeners
        document.getElementById('validateJsonBtn')?.addEventListener('click', () => {
            this.validateJson();
        });

        document.getElementById('saveJsonBtn')?.addEventListener('click', () => {
            this.saveJson();
        });

        document.getElementById('revertJsonBtn')?.addEventListener('click', () => {
            this.revertJson();
        });

        // JSON search event listeners
        document.getElementById('jsonSearchBtn')?.addEventListener('click', () => {
            this.performSearch();
        });

        document.getElementById('jsonSearchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        document.getElementById('jsonSearchNextBtn')?.addEventListener('click', () => {
            this.navigateToNextMatch();
        });

        document.getElementById('jsonSearchPrevBtn')?.addEventListener('click', () => {
            this.navigateToPrevMatch();
        });

        document.getElementById('jsonSearchClearBtn')?.addEventListener('click', () => {
            this.clearSearch();
        });

        // JSON editor event listeners
        const jsonEditor = document.getElementById('jsonEditor');
        if (jsonEditor) {
            jsonEditor.addEventListener('input', () => {
                this.updateLineNumbers();
                this.clearValidationMessages();
            });

            jsonEditor.addEventListener('scroll', () => {
                this.syncLineNumbers();
            });
        }
    }

    async openEditor(filename, searchCriteria = null) {
        try {
            this.searchCriteria = searchCriteria;

            // Load test case data
            const response = await fetch(`/api/test_cases/load/${filename}`);
            const data = await response.json();

            if (data.status === 'success') {
                this.currentTestCase = {
                    filename: filename,
                    data: data.test_case
                };

                // Create backup before editing
                await this.createBackup();

                // Populate editor
                this.populateEditor();

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('jsonEditorModal'));
                modal.show();

                // Highlight search results if criteria provided
                if (searchCriteria) {
                    setTimeout(() => this.highlightSearchResults(), 500);
                }
            } else {
                throw new Error(data.error || 'Failed to load test case');
            }

        } catch (error) {
            console.error('Error opening JSON editor:', error);
            this.showNotification(`Failed to open JSON editor: ${error.message}`, 'error');
        }
    }

    populateEditor() {
        if (!this.currentTestCase) return;

        // Update modal title
        document.getElementById('jsonEditorTestCaseName').textContent =
            `Test Case: ${this.currentTestCase.data.name || 'Unnamed'}`;

        // Format and display JSON
        const jsonString = JSON.stringify(this.currentTestCase.data, null, 2);
        this.originalJson = jsonString;

        const jsonEditor = document.getElementById('jsonEditor');
        jsonEditor.value = jsonString;

        this.updateLineNumbers();
        this.clearValidationMessages();
    }

    updateLineNumbers() {
        const jsonEditor = document.getElementById('jsonEditor');
        const lineNumbers = document.getElementById('jsonEditorLineNumbers');

        if (!jsonEditor || !lineNumbers) return;

        const lines = jsonEditor.value.split('\n');
        const lineNumbersHtml = lines.map((_, index) =>
            `<div style="height: 1.5em; line-height: 1.5em;">${index + 1}</div>`
        ).join('');

        lineNumbers.innerHTML = lineNumbersHtml;
    }

    syncLineNumbers() {
        const jsonEditor = document.getElementById('jsonEditor');
        const lineNumbers = document.getElementById('jsonEditorLineNumbers');

        if (!jsonEditor || !lineNumbers) return;

        lineNumbers.scrollTop = jsonEditor.scrollTop;
    }

    async createBackup() {
        try {
            const response = await fetch('/api/test_cases/json_backup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filename: this.currentTestCase.filename,
                    json_data: this.currentTestCase.data
                })
            });

            const data = await response.json();
            if (data.status !== 'success') {
                console.warn('Failed to create backup:', data.error);
            }
        } catch (error) {
            console.warn('Failed to create backup:', error);
        }
    }

    async validateJson() {
        try {
            const jsonEditor = document.getElementById('jsonEditor');
            const jsonString = jsonEditor.value;

            // Parse JSON
            let parsedJson;
            try {
                parsedJson = JSON.parse(jsonString);
            } catch (parseError) {
                this.showValidationErrors(['Invalid JSON syntax: ' + parseError.message]);
                return false;
            }

            // Perform client-side validation first
            const clientValidation = this.validateTestCaseStructure(parsedJson);
            if (!clientValidation.valid) {
                this.showValidationErrors(clientValidation.errors);
                return false;
            }

            // Validate with backend
            const response = await fetch('/api/test_cases/validate_json', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    test_case: parsedJson,
                    filename: this.currentTestCase.filename
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.showValidationSuccess('JSON validation passed!');
                return true;
            } else {
                this.showValidationErrors(data.validation_errors || [data.error || 'Validation failed']);
                return false;
            }

        } catch (error) {
            this.showValidationErrors(['Validation error: ' + error.message]);
            return false;
        }
    }

    validateTestCaseStructure(testCase) {
        const errors = [];

        // Check required fields
        if (!testCase.name || typeof testCase.name !== 'string') {
            errors.push('Test case must have a valid name');
        }

        if (!testCase.actions || !Array.isArray(testCase.actions)) {
            errors.push('Test case must have an actions array');
        } else {
            // Validate each action
            testCase.actions.forEach((action, index) => {
                const actionErrors = this.validateAction(action, index);
                errors.push(...actionErrors);
            });
        }

        return {
            valid: errors.length === 0,
            errors: errors
        };
    }

    validateAction(action, index) {
        const errors = [];
        const actionPrefix = `Action ${index + 1}`;

        // Check required action fields
        if (!action.type || typeof action.type !== 'string') {
            errors.push(`${actionPrefix}: Action type is required`);
        } else {
            // Validate action type
            const validActionTypes = this.getValidActionTypes();
            if (!validActionTypes.includes(action.type)) {
                errors.push(`${actionPrefix}: Invalid action type '${action.type}'`);
            }
        }

        // Validate action ID
        if (!action.action_id || typeof action.action_id !== 'string') {
            errors.push(`${actionPrefix}: Action ID is required`);
        } else if (!/^[a-zA-Z0-9_]{10}$/.test(action.action_id) && !/^al_[a-zA-Z0-9]{7}$/.test(action.action_id)) {
            errors.push(`${actionPrefix}: Action ID must be 10 alphanumeric characters or 'al_' + 7 characters for addLog actions`);
        }

        // Validate locator-based actions
        if (this.isLocatorBasedAction(action.type)) {
            if (!action.locator_type) {
                errors.push(`${actionPrefix}: Locator type is required for ${action.type} actions`);
            } else {
                const validLocatorTypes = this.getValidLocatorTypes();
                if (!validLocatorTypes.includes(action.locator_type)) {
                    errors.push(`${actionPrefix}: Invalid locator type '${action.locator_type}'`);
                }
            }

            if (!action.locator_value) {
                errors.push(`${actionPrefix}: Locator value is required for ${action.type} actions`);
            }
        }

        // Validate specific action requirements
        if (action.type === 'inputText' && !action.text) {
            errors.push(`${actionPrefix}: Text is required for inputText actions`);
        }

        if (action.type === 'wait' && (!action.duration || isNaN(action.duration))) {
            errors.push(`${actionPrefix}: Valid duration is required for wait actions`);
        }

        return errors;
    }

    getValidActionTypes() {
        return [
            'tap', 'clickElement', 'inputText', 'clearText', 'swipe', 'scroll',
            'wait', 'takeScreenshot', 'restartApp', 'info', 'addLog',
            'iosFunctions', 'androidFunctions', 'multistep', 'repeatSteps',
            'cleanupSteps', 'tapOnElementUISelector'
        ];
    }

    getValidLocatorTypes() {
        return [
            'xpath', 'accessibility_id', 'id', 'class_name', 'name',
            'tag_name', 'css_selector', 'link_text', 'partial_link_text',
            'uiautomator', 'predicate', 'class_chain'
        ];
    }

    isLocatorBasedAction(actionType) {
        const locatorBasedActions = [
            'tap', 'clickElement', 'inputText', 'clearText', 'swipe',
            'scroll', 'tapOnElementUISelector'
        ];
        return locatorBasedActions.includes(actionType);
    }

    async saveJson() {
        try {
            // Validate first
            const isValid = await this.validateJson();
            if (!isValid) {
                return;
            }

            const jsonEditor = document.getElementById('jsonEditor');
            const parsedJson = JSON.parse(jsonEditor.value);

            // Generate action IDs for new actions
            this.generateActionIds(parsedJson);

            // Save to backend
            const response = await fetch('/api/test_cases/save_json', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filename: this.currentTestCase.filename,
                    test_case: parsedJson
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.showNotification('Test case saved successfully!', 'success');

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('jsonEditorModal'));
                modal.hide();

                // Refresh the search results if available
                if (window.testCaseModificationManager) {
                    window.testCaseModificationManager.searchTestCases();
                }
            } else {
                this.showValidationErrors(data.validation_errors || [data.error || 'Save failed']);
            }

        } catch (error) {
            this.showValidationErrors(['Save error: ' + error.message]);
        }
    }

    async revertJson() {
        if (this.originalJson) {
            const jsonEditor = document.getElementById('jsonEditor');
            jsonEditor.value = this.originalJson;
            this.updateLineNumbers();
            this.clearValidationMessages();
            this.showNotification('Reverted to original JSON', 'info');
        }
    }

    generateActionIds(testCase) {
        if (!testCase.actions || !Array.isArray(testCase.actions)) {
            return;
        }

        testCase.actions.forEach(action => {
            if (!action.action_id) {
                action.action_id = this.generateUniqueActionId(action.type);
            }
        });
    }

    generateUniqueActionId(actionType) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';

        // Add prefix for specific action types
        if (actionType === 'addLog') {
            result = 'al_';
        }

        // Generate remaining characters
        const remainingLength = 10 - result.length;
        for (let i = 0; i < remainingLength; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        return result;
    }

    performSearch() {
        const searchInput = document.getElementById('jsonSearchInput');
        const jsonEditor = document.getElementById('jsonEditor');
        const caseSensitive = document.getElementById('jsonSearchCaseSensitive').checked;

        if (!searchInput || !jsonEditor) return;

        const searchTerm = searchInput.value.trim();
        if (!searchTerm) {
            this.clearSearch();
            return;
        }

        const content = jsonEditor.value;
        const flags = caseSensitive ? 'g' : 'gi';
        const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), flags);

        this.searchMatches = [];
        let match;

        while ((match = regex.exec(content)) !== null) {
            this.searchMatches.push({
                index: match.index,
                length: match[0].length,
                line: this.getLineNumber(content, match.index)
            });
        }

        if (this.searchMatches.length > 0) {
            this.currentMatchIndex = 0;
            this.highlightMatches();
            this.navigateToMatch(0);
            this.updateSearchResults();

            // Enable navigation buttons
            document.getElementById('jsonSearchNextBtn').disabled = this.searchMatches.length <= 1;
            document.getElementById('jsonSearchPrevBtn').disabled = this.searchMatches.length <= 1;
        } else {
            this.showSearchResults('No matches found');
        }
    }

    navigateToNextMatch() {
        if (this.searchMatches.length === 0) return;

        this.currentMatchIndex = (this.currentMatchIndex + 1) % this.searchMatches.length;
        this.navigateToMatch(this.currentMatchIndex);
        this.updateSearchResults();
    }

    navigateToPrevMatch() {
        if (this.searchMatches.length === 0) return;

        this.currentMatchIndex = this.currentMatchIndex === 0 ?
            this.searchMatches.length - 1 : this.currentMatchIndex - 1;
        this.navigateToMatch(this.currentMatchIndex);
        this.updateSearchResults();
    }

    navigateToMatch(index) {
        const jsonEditor = document.getElementById('jsonEditor');
        if (!jsonEditor || !this.searchMatches[index]) return;

        const match = this.searchMatches[index];

        // Set cursor position and select the match
        jsonEditor.focus();
        jsonEditor.setSelectionRange(match.index, match.index + match.length);

        // Scroll to the match
        const lineHeight = 24; // Approximate line height
        const scrollTop = (match.line - 1) * lineHeight - (jsonEditor.clientHeight / 2);
        jsonEditor.scrollTop = Math.max(0, scrollTop);
    }

    highlightMatches() {
        // For basic textarea, we'll use selection highlighting
        // In a more advanced implementation, we could use a code editor library
        this.updateSearchResults();
    }

    clearSearch() {
        this.searchMatches = [];
        this.currentMatchIndex = -1;

        const searchInput = document.getElementById('jsonSearchInput');
        if (searchInput) {
            searchInput.value = '';
        }

        // Disable navigation buttons
        document.getElementById('jsonSearchNextBtn').disabled = true;
        document.getElementById('jsonSearchPrevBtn').disabled = true;

        this.showSearchResults('');
    }

    updateSearchResults() {
        if (this.searchMatches.length === 0) {
            this.showSearchResults('No matches');
        } else {
            this.showSearchResults(`${this.currentMatchIndex + 1} of ${this.searchMatches.length}`);
        }
    }

    showSearchResults(text) {
        const resultsElement = document.getElementById('jsonSearchResults');
        if (resultsElement) {
            resultsElement.textContent = text;
        }
    }

    getLineNumber(content, index) {
        return content.substring(0, index).split('\n').length;
    }

    highlightSearchResults() {
        // Enhanced search result highlighting based on current search criteria
        if (this.searchCriteria && this.searchMatches.length === 0) {
            // Auto-search based on criteria when editor opens
            this.autoSearchFromCriteria();
        }
    }

    autoSearchFromCriteria() {
        if (!this.searchCriteria) return;

        const searchTerms = [];

        // Extract search terms from criteria
        if (this.searchCriteria.locator_value) {
            searchTerms.push(this.searchCriteria.locator_value);
        }
        if (this.searchCriteria.image_files && this.searchCriteria.image_files.length > 0) {
            searchTerms.push(...this.searchCriteria.image_files);
        }
        if (this.searchCriteria.text_values && this.searchCriteria.text_values.length > 0) {
            searchTerms.push(...this.searchCriteria.text_values);
        }

        // Use the first search term for auto-search
        if (searchTerms.length > 0) {
            const searchInput = document.getElementById('jsonSearchInput');
            if (searchInput) {
                searchInput.value = searchTerms[0];
                setTimeout(() => this.performSearch(), 100);
            }
        }
    }

    showValidationSuccess(message) {
        const messagesDiv = document.getElementById('jsonValidationMessages');
        const listDiv = document.getElementById('jsonValidationList');

        if (messagesDiv && listDiv) {
            messagesDiv.className = 'alert alert-success';
            listDiv.innerHTML = `<li>${message}</li>`;
            messagesDiv.classList.remove('d-none');
        }
    }

    showValidationErrors(errors) {
        const messagesDiv = document.getElementById('jsonValidationMessages');
        const listDiv = document.getElementById('jsonValidationList');

        if (messagesDiv && listDiv) {
            messagesDiv.className = 'alert alert-danger';
            listDiv.innerHTML = errors.map(error => `<li>${error}</li>`).join('');
            messagesDiv.classList.remove('d-none');
        }
    }

    clearValidationMessages() {
        const messagesDiv = document.getElementById('jsonValidationMessages');
        if (messagesDiv) {
            messagesDiv.classList.add('d-none');
        }
    }

    showNotification(message, type = 'info') {
        // Use the existing notification system if available
        if (window.testCaseModificationManager && window.testCaseModificationManager.showNotification) {
            window.testCaseModificationManager.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.testCaseModificationManager = new TestCaseModificationManager();
});
