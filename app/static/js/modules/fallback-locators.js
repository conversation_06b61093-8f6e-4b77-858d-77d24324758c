/**
 * Fallback Locators Module
 * Handles the UI for adding and managing fallback locators
 */
class FallbackLocatorsManager {
    constructor(app) {
        this.app = app;
        this.fallbackLocatorCounter = 0;
        this.currentCoordinateLocatorId = null;

        // Initialize the module
        this.initEventListeners();
        console.log('FallbackLocatorsManager initialized');
    }

    /**
     * Initialize event listeners for fallback locator functionality
     */
    initEventListeners() {
        // Add tap fallback locator button
        const addTapFallbackBtn = document.getElementById('addTapFallbackLocator');
        if (addTapFallbackBtn) {
            addTapFallbackBtn.addEventListener('click', () => {
                this.addFallbackLocator('tap');
            });
        } else {
            console.debug('Add tap fallback locator button not found (this is normal if not on tap action form)');
        }

        // Add doubleTap fallback locator button
        const addDoubleTapFallbackBtn = document.getElementById('addDoubleTapFallbackLocator');
        if (addDoubleTapFallbackBtn) {
            addDoubleTapFallbackBtn.addEventListener('click', () => {
                this.addFallbackLocator('doubleTap');
            });
        } else {
            console.debug('Add doubleTap fallback locator button not found (this is normal if not on doubleTap action form)');
        }

        // Add event listeners for fallback tabs to ensure they're properly initialized
        const tapFallbackTab = document.getElementById('tap-fallback-tab');
        if (tapFallbackTab) {
            tapFallbackTab.addEventListener('shown.bs.tab', () => {
                console.log('Tap fallback tab shown');
                // Ensure the container is properly initialized
                this.ensureFallbackContainerInitialized('tap');
            });
        }

        const doubleTapFallbackTab = document.getElementById('doubletap-fallback-tab');
        if (doubleTapFallbackTab) {
            doubleTapFallbackTab.addEventListener('shown.bs.tab', () => {
                console.log('Double Tap fallback tab shown');
                // Ensure the container is properly initialized
                this.ensureFallbackContainerInitialized('doubleTap');
            });
        }

        // Initialize any other action types that need fallback locators
        // For example:
        // const addWaitTillFallbackBtn = document.getElementById('addWaitTillFallbackLocator');
        // if (addWaitTillFallbackBtn) {
        //     addWaitTillFallbackBtn.addEventListener('click', () => {
        //         this.addFallbackLocator('waitTill');
        //     });
        // }
    }

    /**
     * Ensure the fallback container is properly initialized
     * @param {string} actionType - The type of action (tap, doubleTap, etc.)
     */
    ensureFallbackContainerInitialized(actionType) {
        // Use the appropriate container based on action type
        let containerId = 'fallbackLocatorsContainer';

        // For doubleTap, use the doubleTap-specific container
        if (actionType === 'doubleTap') {
            containerId = 'doubleTapFallbackLocatorsContainer';
        }

        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Fallback container not found: ${containerId}`);
            return;
        }

        // If the container is empty, add the info alert
        if (container.children.length === 0) {
            const infoAlert = document.createElement('div');
            infoAlert.className = 'alert alert-info';
            infoAlert.innerHTML = `
                <i class="bi bi-info-circle-fill me-2"></i>
                Add fallback locators that will be tried in sequence if the primary locator fails.
            `;
            container.appendChild(infoAlert);
        }
    }

    /**
     * Add a new fallback locator to the specified action type
     * @param {string} actionType - The type of action (tap, waitTill, etc.)
     */
    addFallbackLocator(actionType) {
        // Use the appropriate container based on action type
        let containerId = 'fallbackLocatorsContainer';

        // For doubleTap, use the doubleTap-specific container
        if (actionType === 'doubleTap') {
            containerId = 'doubleTapFallbackLocatorsContainer';
        }

        const container = document.getElementById(containerId);

        if (!container) {
            console.error(`Container not found: ${containerId}`);
            return;
        }

        // Increment counter to ensure unique IDs
        this.fallbackLocatorCounter++;
        const locatorId = `${actionType}FallbackLocator_${this.fallbackLocatorCounter}`;

        // Create fallback locator element
        const fallbackLocator = document.createElement('div');
        fallbackLocator.className = 'fallback-locator mb-3 p-3 border rounded';
        fallbackLocator.id = locatorId;

        // Add locator content
        fallbackLocator.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Fallback Locator #${this.fallbackLocatorCounter}</h6>
                <button type="button" class="btn btn-sm btn-danger remove-fallback" data-locator-id="${locatorId}">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            <div class="form-group mb-2">
                <label>Locator Type</label>
                <select class="form-control fallback-locator-type" onchange="window.app.fallbackLocatorsManager.handleLocatorTypeChange(this)">
                    <option value="id">ID</option>
                    <option value="xpath">XPath</option>
                    <option value="accessibility_id">Accessibility ID</option>
                    <option value="text">Text</option>
                    <option value="text_on_screen">Text on Screen (OCR)</option>
                    <option value="image">Image</option>
                    <option value="coordinates">Coordinates</option>
                    <option value="ios_predicate">iOS Predicate</option>
                    <option value="ios_class_chain">iOS Class Chain</option>
                    <option value="android_uiautomator">Android UiAutomator</option>
                </select>
            </div>
            <div class="form-group locator-value-container">
                <label>Locator Value</label>
                <input type="text" class="form-control fallback-locator-value" placeholder="Enter locator value">
            </div>
            <div class="form-group image-selector-container d-none">
                <label>Select Image</label>
                <div class="d-flex">
                    <input type="text" class="form-control image-path" readonly placeholder="No image selected">
                    <button type="button" class="btn btn-primary ms-2 select-image-btn">
                        <i class="bi bi-image"></i> Browse
                    </button>
                </div>
                <div class="image-preview mt-2 d-none">
                    <img src="" class="img-thumbnail selected-image" style="max-height: 150px;">
                </div>
            </div>
            <div class="form-group coordinates-container d-none">
                <label>Coordinates</label>
                <div class="row">
                    <div class="col-6">
                        <div class="form-group">
                            <label>X Coordinate</label>
                            <input type="number" class="form-control coordinate-x" value="0">
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label>Y Coordinate</label>
                            <input type="number" class="form-control coordinate-y" value="0">
                        </div>
                    </div>
                </div>
                <button class="btn btn-outline-primary btn-sm mt-2 pick-coordinates-btn">
                    <i class="bi bi-cursor"></i> Pick from Screen
                </button>
            </div>
        `;

        // Add to container
        container.appendChild(fallbackLocator);

        // Add event listener for remove button
        fallbackLocator.querySelector('.remove-fallback').addEventListener('click', (e) => {
            const locatorId = e.currentTarget.getAttribute('data-locator-id');
            this.removeFallbackLocator(locatorId);
        });

        // Add event listener for image selection button
        const selectImageBtn = fallbackLocator.querySelector('.select-image-btn');
        if (selectImageBtn) {
            selectImageBtn.addEventListener('click', () => {
                this.openImageSelector(locatorId);
            });
        }

        // Add event listener for coordinate picker button
        const pickCoordinatesBtn = fallbackLocator.querySelector('.pick-coordinates-btn');
        if (pickCoordinatesBtn) {
            pickCoordinatesBtn.addEventListener('click', () => {
                this.pickCoordinatesFromScreen(locatorId);
            });
        }

        // Remove the info alert if it exists
        const infoAlert = container.querySelector('.alert-info');
        if (infoAlert) {
            infoAlert.remove();
        }
    }

    /**
     * Remove a fallback locator
     * @param {string} locatorId - The ID of the locator to remove
     */
    removeFallbackLocator(locatorId) {
        const locator = document.getElementById(locatorId);
        if (locator) {
            locator.remove();
        }
    }

    /**
     * Get all fallback locators for a specific action type
     * @param {string} actionType - The type of action (tap, waitTill, etc.)
     * @returns {Array} Array of locator objects with locator_type and locator_value
     */
    getFallbackLocators(actionType) {
        // Use the appropriate container based on action type
        let containerId = 'fallbackLocatorsContainer';

        // For doubleTap, use the doubleTap-specific container
        if (actionType === 'doubleTap') {
            containerId = 'doubleTapFallbackLocatorsContainer';
        }

        const container = document.getElementById(containerId);

        if (!container) {
            console.warn(`Fallback locators container not found: ${containerId}`);
            return [];
        }

        // Check if the fallback tab is active
        const fallbackTab = document.getElementById(`${actionType.toLowerCase()}-fallback-tab`);
        if (fallbackTab && !fallbackTab.classList.contains('active')) {
            // If the fallback tab is not active, activate it to ensure all elements are properly rendered
            // This is important for getting accurate values from the DOM
            console.log(`Activating fallback tab for ${actionType} to get locators`);
        }

        const fallbackLocators = [];
        const locatorElements = container.querySelectorAll('.fallback-locator');

        locatorElements.forEach(locator => {
            const locatorType = locator.querySelector('.fallback-locator-type').value;

            // Handle different locator types
            if (locatorType === 'image') {
                // For image locators, get the image path from the hidden input
                const imageData = locator.querySelector('.image-data')?.value;
                const imagePath = locator.querySelector('.image-path')?.value;

                if (locatorType && (imageData || imagePath)) {
                    fallbackLocators.push({
                        locator_type: locatorType,
                        locator_value: imageData || imagePath
                    });
                }
            } else if (locatorType === 'coordinates') {
                // For coordinates, get the x and y values
                const xCoord = locator.querySelector('.coordinate-x')?.value;
                const yCoord = locator.querySelector('.coordinate-y')?.value;

                if (locatorType && xCoord !== undefined && yCoord !== undefined) {
                    // Store coordinates as a JSON string to be parsed on the backend
                    fallbackLocators.push({
                        locator_type: locatorType,
                        locator_value: JSON.stringify({x: parseInt(xCoord), y: parseInt(yCoord)})
                    });
                }
            } else {
                // For other locator types, get the value from the input field
                const locatorValue = locator.querySelector('.fallback-locator-value')?.value;

                if (locatorType && locatorValue) {
                    fallbackLocators.push({
                        locator_type: locatorType,
                        locator_value: locatorValue
                    });
                }
            }
        });

        return fallbackLocators;
    }

    /**
     * Handle locator type change to show/hide appropriate input fields
     * @param {HTMLElement} selectElement - The select element that changed
     */
    handleLocatorTypeChange(selectElement) {
        if (!selectElement) return;

        const locatorContainer = selectElement.closest('.fallback-locator');
        if (!locatorContainer) return;

        const locatorType = selectElement.value;
        const valueContainer = locatorContainer.querySelector('.locator-value-container');
        const imageContainer = locatorContainer.querySelector('.image-selector-container');
        const coordinatesContainer = locatorContainer.querySelector('.coordinates-container');

        // Show/hide containers based on locator type
        if (locatorType === 'image') {
            valueContainer.classList.add('d-none');
            imageContainer.classList.remove('d-none');
            coordinatesContainer.classList.add('d-none');
        } else if (locatorType === 'coordinates') {
            valueContainer.classList.add('d-none');
            imageContainer.classList.add('d-none');
            coordinatesContainer.classList.remove('d-none');
        } else {
            valueContainer.classList.remove('d-none');
            imageContainer.classList.add('d-none');
            coordinatesContainer.classList.add('d-none');
        }

        // Update placeholder text based on locator type
        const valueInput = locatorContainer.querySelector('.fallback-locator-value');
        if (valueInput) {
            switch (locatorType) {
                case 'id':
                    valueInput.placeholder = 'Enter element ID';
                    break;
                case 'xpath':
                    valueInput.placeholder = 'Enter XPath expression';
                    break;
                case 'accessibility_id':
                    valueInput.placeholder = 'Enter accessibility identifier';
                    break;
                case 'text':
                    valueInput.placeholder = 'Enter text to find';
                    break;
                case 'text_on_screen':
                    valueInput.placeholder = 'Enter text to find on screen (OCR)';
                    break;
                case 'ios_predicate':
                    valueInput.placeholder = 'Enter iOS predicate string';
                    break;
                case 'ios_class_chain':
                    valueInput.placeholder = 'Enter iOS class chain';
                    break;
                case 'android_uiautomator':
                    valueInput.placeholder = 'Enter UiAutomator selector';
                    break;
                default:
                    valueInput.placeholder = 'Enter locator value';
            }
        }
    }

    /**
     * Open the image selector modal for a fallback locator
     * @param {string} locatorId - The ID of the locator
     */
    openImageSelector(locatorId) {
        const locator = document.getElementById(locatorId);
        if (!locator) return;

        // Create a file input element
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.style.display = 'none';
        document.body.appendChild(fileInput);

        // Handle file selection
        fileInput.addEventListener('change', (e) => {
            if (e.target.files && e.target.files[0]) {
                const file = e.target.files[0];
                const reader = new FileReader();

                reader.onload = (event) => {
                    // Update the image preview
                    const imagePath = locator.querySelector('.image-path');
                    const imagePreview = locator.querySelector('.image-preview');
                    const selectedImage = locator.querySelector('.selected-image');

                    if (imagePath && imagePreview && selectedImage) {
                        imagePath.value = file.name;
                        selectedImage.src = event.target.result;
                        imagePreview.classList.remove('d-none');

                        // Store the image data in a hidden input
                        let imageDataInput = locator.querySelector('.image-data');
                        if (!imageDataInput) {
                            imageDataInput = document.createElement('input');
                            imageDataInput.type = 'hidden';
                            imageDataInput.className = 'image-data';
                            locator.appendChild(imageDataInput);
                        }
                        imageDataInput.value = event.target.result;
                    }
                };

                reader.readAsDataURL(file);
            }

            // Remove the temporary file input
            document.body.removeChild(fileInput);
        });

        // Trigger the file input click
        fileInput.click();
    }

    /**
     * Load reference images from the server
     * @param {string} locatorId - The ID of the locator
     */
    loadReferenceImages(locatorId) {
        const locator = document.getElementById(locatorId);
        if (!locator) return;

        // Show loading indicator
        this.app.showLoading();

        // Fetch reference images from the server
        fetch('/api/reference_images')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.images) {
                    this.showImageSelectionModal(locatorId, data.images);
                } else {
                    this.app.showToast('Error', 'Failed to load reference images', 'error');
                }
            })
            .catch(error => {
                console.error('Error loading reference images:', error);
                this.app.showToast('Error', 'Failed to load reference images', 'error');
            })
            .finally(() => {
                this.app.hideLoading();
            });
    }

    /**
     * Show the image selection modal
     * @param {string} locatorId - The ID of the locator
     * @param {Array} images - Array of image objects with path and preview properties
     */
    showImageSelectionModal(locatorId, images) {
        // Create modal if it doesn't exist
        let modal = document.getElementById('imageSelectionModal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'imageSelectionModal';
            modal.className = 'modal fade';
            modal.setAttribute('tabindex', '-1');
            modal.setAttribute('aria-hidden', 'true');

            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Select Reference Image</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row" id="imageSelectionGrid"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Populate the image grid
        const imageGrid = document.getElementById('imageSelectionGrid');
        imageGrid.innerHTML = '';

        images.forEach(image => {
            const imageCol = document.createElement('div');
            imageCol.className = 'col-md-3 mb-3';

            imageCol.innerHTML = `
                <div class="card image-selection-card" data-image-path="${image.path}">
                    <img src="${image.preview}" class="card-img-top" alt="${image.path}">
                    <div class="card-body p-2">
                        <p class="card-text small text-truncate">${image.path}</p>
                    </div>
                </div>
            `;

            imageGrid.appendChild(imageCol);

            // Add click event to select the image
            imageCol.querySelector('.image-selection-card').addEventListener('click', () => {
                this.selectReferenceImage(locatorId, image.path, image.preview);
                const bsModal = bootstrap.Modal.getInstance(modal);
                bsModal.hide();
            });
        });

        // Show the modal
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }

    /**
     * Select a reference image for a fallback locator
     * @param {string} locatorId - The ID of the locator
     * @param {string} imagePath - The path to the image
     * @param {string} imagePreviewUrl - The URL for the image preview
     */
    selectReferenceImage(locatorId, imagePath, imagePreviewUrl) {
        const locator = document.getElementById(locatorId);
        if (!locator) return;

        // Update the image preview
        const imagePathInput = locator.querySelector('.image-path');
        const imagePreview = locator.querySelector('.image-preview');
        const selectedImage = locator.querySelector('.selected-image');

        if (imagePathInput && imagePreview && selectedImage) {
            imagePathInput.value = imagePath;
            selectedImage.src = imagePreviewUrl;
            imagePreview.classList.remove('d-none');

            // Store the image path in a hidden input
            let imageDataInput = locator.querySelector('.image-data');
            if (!imageDataInput) {
                imageDataInput = document.createElement('input');
                imageDataInput.type = 'hidden';
                imageDataInput.className = 'image-data';
                locator.appendChild(imageDataInput);
            }
            imageDataInput.value = imagePath;
        }
    }

    /**
     * Pick coordinates from the device screen for a fallback locator
     * @param {string} locatorId - The ID of the locator
     */
    pickCoordinatesFromScreen(locatorId) {
        const locator = document.getElementById(locatorId);
        if (!locator) return;

        // Check if device is connected
        if (!this.app.isDeviceConnected) {
            this.app.showToast('Error', 'Please connect to a device first', 'error');
            return;
        }

        // Show a message to the user
        this.app.logAction('info', 'Click on the device screen to select coordinates');
        this.app.showToast('Info', 'Click on the device screen to select coordinates', 'info');

        // Enable coordinate selection mode
        const deviceScreen = document.getElementById('deviceScreen');

        // Store the current locator ID to update when coordinates are selected
        this.currentCoordinateLocatorId = locatorId;

        // Add a one-time click event listener to the device screen
        const clickHandler = (e) => {
            // Get click coordinates relative to the image
            const rect = deviceScreen.getBoundingClientRect();
            const x = Math.round(e.clientX - rect.left);
            const y = Math.round(e.clientY - rect.top);

            // Calculate the coordinates relative to the original image size
            const originalWidth = deviceScreen.naturalWidth;
            const originalHeight = deviceScreen.naturalHeight;
            const displayWidth = deviceScreen.width;
            const displayHeight = deviceScreen.height;

            // Scale the coordinates to the original image size
            const scaleX = originalWidth / displayWidth;
            const scaleY = originalHeight / displayHeight;

            const originalX = Math.round(x * scaleX);
            const originalY = Math.round(y * scaleY);

            // Update the coordinate inputs in the locator
            const xInput = locator.querySelector('.coordinate-x');
            const yInput = locator.querySelector('.coordinate-y');

            if (xInput && yInput) {
                xInput.value = originalX;
                yInput.value = originalY;

                this.app.logAction('success', `Selected coordinates: (${originalX}, ${originalY})`);
            }

            // Remove the event listener
            deviceScreen.removeEventListener('click', clickHandler);

            // Clear the current locator ID
            this.currentCoordinateLocatorId = null;
        };

        // Add the click event listener
        deviceScreen.addEventListener('click', clickHandler);
    }

    /**
     * Load fallback locators from an action object
     * @param {string} actionType - The type of action (tap, waitTill, etc.)
     * @param {Object} action - The action object containing fallback locators
     */
    loadFallbackLocators(actionType, action) {
        // Use the appropriate container based on action type
        let containerId = 'fallbackLocatorsContainer';

        // For doubleTap, use the doubleTap-specific container
        if (actionType === 'doubleTap') {
            containerId = 'doubleTapFallbackLocatorsContainer';
        }

        // Clear existing fallback locators
        const container = document.getElementById(containerId);

        if (!container) {
            console.warn(`Fallback locators container not found: ${containerId}`);
            return;
        }

        // Activate the fallback tab if we have fallback locators to load
        if (action && action.fallback_locators && Array.isArray(action.fallback_locators) && action.fallback_locators.length > 0) {
            const fallbackTab = document.getElementById(`${actionType.toLowerCase()}-fallback-tab`);
            if (fallbackTab) {
                // We don't need to actually click the tab, just make sure the container is visible
                console.log(`Fallback tab found for ${actionType}, will load ${action.fallback_locators.length} fallback locators`);
            }
        }

        // Clear existing locators
        container.innerHTML = '';

        // If the action has fallback locators, add them to the UI
        if (action && action.fallback_locators && Array.isArray(action.fallback_locators)) {
            action.fallback_locators.forEach(fallback => {
                // Add a new fallback locator
                this.addFallbackLocator(actionType);

                // Get the last added locator
                const locators = container.querySelectorAll('.fallback-locator');
                const lastLocator = locators[locators.length - 1];

                if (lastLocator) {
                    // Set the values
                    const typeSelect = lastLocator.querySelector('.fallback-locator-type');

                    if (typeSelect) {
                        typeSelect.value = fallback.locator_type || 'id';

                        // Trigger the change event to update the UI
                        this.handleLocatorTypeChange(typeSelect);

                        if (fallback.locator_type === 'image') {
                            // Handle image locator
                            const imagePath = lastLocator.querySelector('.image-path');
                            const imagePreview = lastLocator.querySelector('.image-preview');
                            const selectedImage = lastLocator.querySelector('.selected-image');

                            if (imagePath && imagePreview && selectedImage && fallback.locator_value) {
                                imagePath.value = fallback.locator_value;

                                // Try to load the image preview
                                fetch(`/api/reference_image_preview?path=${encodeURIComponent(fallback.locator_value)}`)
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.status === 'success' && data.preview) {
                                            selectedImage.src = data.preview;
                                            imagePreview.classList.remove('d-none');
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error loading image preview:', error);
                                    });

                                // Store the image path in a hidden input
                                let imageDataInput = lastLocator.querySelector('.image-data');
                                if (!imageDataInput) {
                                    imageDataInput = document.createElement('input');
                                    imageDataInput.type = 'hidden';
                                    imageDataInput.className = 'image-data';
                                    lastLocator.appendChild(imageDataInput);
                                }
                                imageDataInput.value = fallback.locator_value;
                            }
                        } else if (fallback.locator_type === 'coordinates') {
                            // Handle coordinates locator
                            const xCoordInput = lastLocator.querySelector('.coordinate-x');
                            const yCoordInput = lastLocator.querySelector('.coordinate-y');

                            if (xCoordInput && yCoordInput && fallback.locator_value) {
                                try {
                                    // Parse the JSON string to get coordinates
                                    const coords = JSON.parse(fallback.locator_value);
                                    xCoordInput.value = coords.x || 0;
                                    yCoordInput.value = coords.y || 0;
                                } catch (e) {
                                    console.error('Error parsing coordinates:', e);
                                    // Fallback to default values if parsing fails
                                    xCoordInput.value = 0;
                                    yCoordInput.value = 0;
                                }
                            }
                        } else {
                            // Handle other locator types
                            const valueInput = lastLocator.querySelector('.fallback-locator-value');
                            if (valueInput) {
                                valueInput.value = fallback.locator_value || '';
                            }
                        }
                    }
                }
            });
        }
    }
}
