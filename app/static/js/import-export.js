/**
 * Import/Export functionality for test cases and test suites
 */

class ImportExportManager {
    constructor() {
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Test Cases Export
        document.getElementById('exportTestCasesBtn')?.addEventListener('click', () => {
            this.exportTestCases();
        });

        // Test Cases Import
        document.getElementById('importTestCasesBtn')?.addEventListener('click', () => {
            this.showImportTestCasesModal();
        });

        // Recording Export (same as test cases for now)
        document.getElementById('exportRecordingBtn')?.addEventListener('click', () => {
            this.exportTestCases();
        });

        // Recording Import (same as test cases for now)
        document.getElementById('importRecordingBtn')?.addEventListener('click', () => {
            this.showImportTestCasesModal();
        });

        document.getElementById('confirmImportTestCases')?.addEventListener('click', () => {
            this.importTestCases();
        });

        // Test Suites Export
        document.getElementById('exportTestSuitesBtn')?.addEventListener('click', () => {
            this.exportTestSuites();
        });

        // Test Suites Import
        document.getElementById('importTestSuitesBtn')?.addEventListener('click', () => {
            this.showImportTestSuitesModal();
        });

        document.getElementById('confirmImportTestSuites')?.addEventListener('click', () => {
            this.importTestSuites();
        });
    }

    async exportTestCases() {
        try {
            this.showLoadingState('exportTestCasesBtn', true);
            
            const response = await fetch('/api/test_cases/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                // Get the filename from the response headers or generate one
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = 'test_cases_export.zip';
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                // Create blob and download
                const blob = await response.blob();
                this.downloadBlob(blob, filename);
                
                this.showNotification('Test cases exported successfully!', 'success');
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Export failed');
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification(`Export failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('exportTestCasesBtn', false);
        }
    }

    async exportTestSuites() {
        try {
            this.showLoadingState('exportTestSuitesBtn', true);
            
            const response = await fetch('/api/test_suites/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                // Get the filename from the response headers or generate one
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = 'test_suites_export.zip';
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                // Create blob and download
                const blob = await response.blob();
                this.downloadBlob(blob, filename);
                
                this.showNotification('Test suites exported successfully!', 'success');
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Export failed');
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification(`Export failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('exportTestSuitesBtn', false);
        }
    }

    showImportTestCasesModal() {
        const modal = new bootstrap.Modal(document.getElementById('importTestCasesModal'));
        modal.show();
    }

    showImportTestSuitesModal() {
        const modal = new bootstrap.Modal(document.getElementById('importTestSuitesModal'));
        modal.show();
    }

    async importTestCases() {
        try {
            const fileInput = document.getElementById('testCasesFile');
            const conflictResolution = document.getElementById('testCasesConflictResolution').value;

            if (!fileInput.files || fileInput.files.length === 0) {
                this.showNotification('Please select a file to import', 'error');
                return;
            }

            const file = fileInput.files[0];
            if (!file.name.toLowerCase().endsWith('.zip')) {
                this.showNotification('Please select a ZIP file', 'error');
                return;
            }

            this.showLoadingState('confirmImportTestCases', true);

            const formData = new FormData();
            formData.append('file', file);
            formData.append('conflict_resolution', conflictResolution);

            const response = await fetch('/api/test_cases/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok && result.success !== false) {
                this.showImportResults('Test Cases', result);
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('importTestCasesModal'));
                modal.hide();
                
                // Refresh test cases list if available
                if (typeof loadTestCases === 'function') {
                    loadTestCases();
                }
            } else {
                throw new Error(result.error || 'Import failed');
            }
        } catch (error) {
            console.error('Import error:', error);
            this.showNotification(`Import failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('confirmImportTestCases', false);
        }
    }

    async importTestSuites() {
        try {
            const fileInput = document.getElementById('testSuitesFile');
            const conflictResolution = document.getElementById('testSuitesConflictResolution').value;

            if (!fileInput.files || fileInput.files.length === 0) {
                this.showNotification('Please select a file to import', 'error');
                return;
            }

            const file = fileInput.files[0];
            if (!file.name.toLowerCase().endsWith('.zip')) {
                this.showNotification('Please select a ZIP file', 'error');
                return;
            }

            this.showLoadingState('confirmImportTestSuites', true);

            const formData = new FormData();
            formData.append('file', file);
            formData.append('conflict_resolution', conflictResolution);

            const response = await fetch('/api/test_suites/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok && result.success !== false) {
                this.showImportResults('Test Suites', result);
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('importTestSuitesModal'));
                modal.hide();
                
                // Refresh test suites list if available
                if (typeof loadTestSuites === 'function') {
                    loadTestSuites();
                }
            } else {
                throw new Error(result.error || 'Import failed');
            }
        } catch (error) {
            console.error('Import error:', error);
            this.showNotification(`Import failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('confirmImportTestSuites', false);
        }
    }

    showImportResults(type, results) {
        let message = `${type} Import Results:\n`;
        message += `• Imported: ${results.imported || 0}\n`;
        message += `• Skipped: ${results.skipped || 0}\n`;
        message += `• Errors: ${results.errors || 0}`;
        
        if (results.conflicts && results.conflicts.length > 0) {
            message += `\n• Conflicts: ${results.conflicts.length}`;
        }
        
        if (results.error_details && results.error_details.length > 0) {
            message += `\n\nErrors:\n${results.error_details.join('\n')}`;
        }

        this.showNotification(message, results.errors > 0 ? 'warning' : 'success');
    }

    downloadBlob(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    showLoadingState(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        if (loading) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
        } else {
            button.disabled = false;
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    }

    showNotification(message, type = 'info') {
        // Use existing notification system if available, otherwise use alert
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            alert(message);
        }
    }
}

/**
 * Bulk Modification Manager for test cases
 */
class BulkModificationManager {
    constructor() {
        this.initializeEventListeners();
        this.previewData = null;
        this.ruleCounter = 0;
    }

    initializeEventListeners() {
        // Preview modifications
        document.getElementById('previewModificationsBtn')?.addEventListener('click', () => {
            this.previewModifications();
        });

        // Apply modifications
        document.getElementById('applyModificationsBtn')?.addEventListener('click', () => {
            this.applyModifications();
        });

        // Clear rules
        document.getElementById('clearRulesBtn')?.addEventListener('click', () => {
            this.clearRules();
        });

        // Validate rules
        document.getElementById('validateRulesBtn')?.addEventListener('click', () => {
            this.validateRules();
        });

        // Add rule pair
        document.getElementById('addRulePairBtn')?.addEventListener('click', () => {
            this.addRulePair();
        });

        // Backup and revert functionality
        document.getElementById('createBackupAllBtn')?.addEventListener('click', () => {
            this.createBackupAllTestCases();
        });

        document.getElementById('revertFromBackupBtn')?.addEventListener('click', () => {
            this.showRevertModal();
        });

        // Initialize with one rule pair
        this.initializeRulePairs();
    }

    showLoadingState(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        if (loading) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
        } else {
            button.disabled = false;
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    }

    showNotification(message, type = 'info') {
        // Use existing notification system if available, otherwise use alert
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            alert(message);
        }
    }

    initializeRulePairs() {
        // Check if we're using the new rule pairs interface
        const rulePairsContainer = document.getElementById('rulePairsContainer');
        if (rulePairsContainer) {
            // Add initial rule pair if container is empty
            if (rulePairsContainer.children.length === 0) {
                this.addRulePair();
            }
        }
    }

    addRulePair() {
        const container = document.getElementById('rulePairsContainer');
        if (!container) return;

        this.ruleCounter++;
        const ruleId = `rule-${this.ruleCounter}`;

        const rulePairHtml = `
            <div class="rule-pair card mb-3" id="${ruleId}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Rule ${this.ruleCounter}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-rule-btn" onclick="bulkModificationManager.removeRulePair('${ruleId}')">
                        <i class="bi bi-trash"></i> Remove
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Before (Trigger Condition):</label>
                            <textarea class="form-control rule-before" rows="3" placeholder="Enter the action that should trigger this rule...&#10;&#10;Example:&#10;Tap on element with xpath: //XCUIElementTypeButton[contains(@name,&quot;Tab 4 of 5&quot;)]"></textarea>
                            <div class="validation-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">After (Result/Modification):</label>
                            <textarea class="form-control rule-after" rows="3" placeholder="Enter what should happen after the trigger...&#10;&#10;Example:&#10;Tap on element with xpath: //XCUIElementTypeButton[contains(@name,&quot;Tab 4 of 5&quot;)]&#10;Tap if locator exists: xpath=&quot;//XCUIElementTypeButton[@name=&quot;Checkout&quot;]&quot;"></textarea>
                            <div class="validation-feedback"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', rulePairHtml);
    }

    removeRulePair(ruleId) {
        const ruleElement = document.getElementById(ruleId);
        if (ruleElement) {
            ruleElement.remove();
        }

        // Ensure at least one rule pair exists
        const container = document.getElementById('rulePairsContainer');
        if (container && container.children.length === 0) {
            this.addRulePair();
        }
    }

    validateRules() {
        const rulePairs = this.collectRulePairs();
        let isValid = true;
        let validationResults = [];

        rulePairs.forEach((pair, index) => {
            const result = this.validateRulePair(pair, index + 1);
            validationResults.push(result);
            if (!result.isValid) {
                isValid = false;
            }
        });

        this.displayValidationResults(validationResults);

        if (isValid) {
            this.showNotification('All rules are valid!', 'success');
        } else {
            this.showNotification('Some rules have validation errors. Please fix them before proceeding.', 'error');
        }

        return isValid;
    }

    collectRulePairs() {
        const pairs = [];
        const ruleElements = document.querySelectorAll('.rule-pair');

        ruleElements.forEach(element => {
            const beforeText = element.querySelector('.rule-before').value.trim();
            const afterText = element.querySelector('.rule-after').value.trim();

            if (beforeText || afterText) {
                pairs.push({
                    element: element,
                    before: beforeText,
                    after: afterText
                });
            }
        });

        return pairs;
    }

    validateRulePair(pair, ruleNumber) {
        const errors = [];

        // Check if both before and after are provided
        if (!pair.before) {
            errors.push('Before condition is required');
        }

        if (!pair.after) {
            errors.push('After modification is required');
        }

        // Validate XPath syntax if present
        const xpathRegex = /xpath[:\s]*[=]?\s*["']([^"']+)["']/gi;

        // Check before condition
        if (pair.before) {
            const beforeXPaths = [...pair.before.matchAll(xpathRegex)];
            beforeXPaths.forEach(match => {
                if (!this.isValidXPath(match[1])) {
                    errors.push(`Invalid XPath in before condition: ${match[1]}`);
                }
            });
        }

        // Check after modification
        if (pair.after) {
            const afterXPaths = [...pair.after.matchAll(xpathRegex)];
            afterXPaths.forEach(match => {
                if (!this.isValidXPath(match[1])) {
                    errors.push(`Invalid XPath in after modification: ${match[1]}`);
                }
            });
        }

        return {
            ruleNumber: ruleNumber,
            isValid: errors.length === 0,
            errors: errors,
            element: pair.element
        };
    }

    isValidXPath(xpath) {
        try {
            // Basic XPath validation
            if (!xpath || xpath.trim() === '') return false;

            // Check for basic XPath structure
            if (!xpath.startsWith('//') && !xpath.startsWith('/') && !xpath.startsWith('.')) {
                return false;
            }

            // Check for balanced brackets and quotes
            const openBrackets = (xpath.match(/\[/g) || []).length;
            const closeBrackets = (xpath.match(/\]/g) || []).length;
            if (openBrackets !== closeBrackets) return false;

            const singleQuotes = (xpath.match(/'/g) || []).length;
            const doubleQuotes = (xpath.match(/"/g) || []).length;
            if (singleQuotes % 2 !== 0 || doubleQuotes % 2 !== 0) return false;

            return true;
        } catch (e) {
            return false;
        }
    }

    displayValidationResults(results) {
        results.forEach(result => {
            const beforeFeedback = result.element.querySelector('.rule-before').nextElementSibling;
            const afterFeedback = result.element.querySelector('.rule-after').nextElementSibling;

            // Clear previous validation states
            result.element.querySelector('.rule-before').classList.remove('is-valid', 'is-invalid');
            result.element.querySelector('.rule-after').classList.remove('is-valid', 'is-invalid');
            beforeFeedback.textContent = '';
            afterFeedback.textContent = '';

            if (result.isValid) {
                result.element.querySelector('.rule-before').classList.add('is-valid');
                result.element.querySelector('.rule-after').classList.add('is-valid');
                beforeFeedback.className = 'validation-feedback valid-feedback';
                afterFeedback.className = 'validation-feedback valid-feedback';
                beforeFeedback.textContent = 'Valid';
                afterFeedback.textContent = 'Valid';
            } else {
                result.element.querySelector('.rule-before').classList.add('is-invalid');
                result.element.querySelector('.rule-after').classList.add('is-invalid');
                beforeFeedback.className = 'validation-feedback invalid-feedback';
                afterFeedback.className = 'validation-feedback invalid-feedback';
                beforeFeedback.textContent = result.errors.join(', ');
            }
        });
    }

    convertRulePairsToApiFormat(rulePairs) {
        const rules = [];

        rulePairs.forEach(pair => {
            if (pair.before && pair.after) {
                try {
                    // Parse the before condition to extract trigger information
                    const beforeLines = pair.before.split('\n').filter(line => line.trim());
                    const afterLines = pair.after.split('\n').filter(line => line.trim());

                    if (beforeLines.length === 0 || afterLines.length === 0) {
                        return; // Skip invalid pairs
                    }

                    // Extract the trigger condition from the "before" text
                    const triggerLine = beforeLines[0];

                    // Find what needs to be added from the "after" text
                    // The first line in "after" should match the "before" (existing step)
                    // Additional lines are what should be added
                    const newSteps = afterLines.slice(1); // Skip the first line which should match "before"

                    if (newSteps.length > 0) {
                        // For each new step, create a rule
                        newSteps.forEach(newStep => {
                            // Parse the new step to extract action type and parameters
                            const rule = this.parseStepToRule(newStep, triggerLine);
                            if (rule) {
                                rules.push(rule);
                            }
                        });
                    }
                } catch (error) {
                    console.warn('Error converting rule pair:', error, pair);
                }
            }
        });

        return rules;
    }

    parseStepToRule(stepText, triggerText) {
        try {
            // Extract action type and parameters from step text
            // Example: "Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]""

            // Common patterns for different action types
            const patterns = [
                {
                    // Tap if locator exists: xpath="..."
                    regex: /^Tap if locator exists:\s*xpath\s*=\s*["']([^"']+)["']/i,
                    actionType: 'tapIfLocatorExists',
                    parameterName: 'locator_value',
                    extraParams: { locator_type: 'xpath' }
                },
                {
                    // Tap on element with xpath: ...
                    regex: /^Tap on element with xpath:\s*(.+)/i,
                    actionType: 'tap',
                    parameterName: 'locator_value',
                    extraParams: { locator_type: 'xpath' }
                },
                {
                    // Take Screenshot: name
                    regex: /^Take Screenshot:\s*(.+)/i,
                    actionType: 'takeScreenshot',
                    parameterName: 'name'
                },
                {
                    // Wait for element: xpath="..."
                    regex: /^Wait for element:\s*xpath\s*=\s*["']([^"']+)["']/i,
                    actionType: 'waitTill',
                    parameterName: 'locator_value',
                    extraParams: { locator_type: 'xpath' }
                }
            ];

            for (const pattern of patterns) {
                const match = stepText.match(pattern.regex);
                if (match) {
                    const parameterValue = match[1];

                    // Build parameters string
                    let parametersStr = `${pattern.parameterName} '${parameterValue}'`;
                    if (pattern.extraParams) {
                        for (const [key, value] of Object.entries(pattern.extraParams)) {
                            parametersStr += ` ${key} '${value}'`;
                        }
                    }

                    // Create the rule based on the pattern
                    return `Add step '${pattern.actionType}' with ${parametersStr} after step that contains '${this.extractTriggerCondition(triggerText)}'`;
                }
            }

            // Fallback: generic rule format
            return `Add step "${stepText}" after step that contains "${this.extractTriggerCondition(triggerText)}"`;

        } catch (error) {
            console.warn('Error parsing step to rule:', error, stepText);
            return null;
        }
    }

    extractTriggerCondition(triggerText) {
        try {
            // Extract key identifying information from the trigger text
            // Example: "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]"

            // Look for xpath patterns and extract the xpath value
            const xpathMatch = triggerText.match(/xpath:\s*(.+)/i);
            if (xpathMatch) {
                const xpathValue = xpathMatch[1].trim();
                // Return condition that matches the backend's step matching logic
                return `contains "locator_value":"${xpathValue}"`;
            }

            // Look for action type patterns
            const actionMatch = triggerText.match(/^([^:]+):/);
            if (actionMatch) {
                const actionType = actionMatch[1].trim().toLowerCase();
                // Map frontend action names to backend action types
                const actionMapping = {
                    'tap on element': 'tap',
                    'wait for element': 'waitTill',
                    'input text': 'textClear',
                    'take screenshot': 'takeScreenshot'
                };
                const mappedAction = actionMapping[actionType] || actionType;
                return `contains "type":"${mappedAction}"`;
            }

            // Fallback: use the whole text but limit length
            return triggerText.length > 50 ? triggerText.substring(0, 50) + '...' : triggerText;

        } catch (error) {
            console.warn('Error extracting trigger condition:', error, triggerText);
            return triggerText;
        }
    }

    async previewModifications() {
        try {
            let rules = [];

            // Check if we're using the new rule pairs interface
            const rulePairsContainer = document.getElementById('rulePairsContainer');
            if (rulePairsContainer) {
                // Validate rules first
                if (!this.validateRules()) {
                    return;
                }

                // Collect and convert rule pairs
                const rulePairs = this.collectRulePairs();
                if (rulePairs.length === 0) {
                    this.showNotification('Please add at least one rule pair', 'error');
                    return;
                }

                rules = this.convertRulePairsToApiFormat(rulePairs);
            } else {
                // Fallback to old textarea method
                const rulesText = document.getElementById('modificationRules').value.trim();
                if (!rulesText) {
                    this.showNotification('Please enter modification rules', 'error');
                    return;
                }
                rules = rulesText.split('\n').filter(rule => rule.trim() !== '');
            }

            this.showLoadingState('previewModificationsBtn', true);

            const response = await fetch('/api/test_cases/bulk_modify/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ rules })
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                this.previewData = result.preview;
                this.displayPreviewResults(result.preview);

                // Enable apply button if there are modifications
                const applyBtn = document.getElementById('applyModificationsBtn');
                if (applyBtn) {
                    applyBtn.disabled = result.preview.affected_test_cases === 0;
                }
            } else {
                throw new Error(result.error || 'Preview failed');
            }
        } catch (error) {
            console.error('Preview error:', error);
            this.showNotification(`Preview failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('previewModificationsBtn', false);
        }
    }

    async applyModifications() {
        try {
            if (!this.previewData || this.previewData.affected_test_cases === 0) {
                this.showNotification('No modifications to apply. Please preview first.', 'error');
                return;
            }

            // Show the enhanced modification modal
            this.showModificationModal();

        } catch (error) {
            console.error('Apply error:', error);
            this.showNotification(`Apply failed: ${error.message}`, 'error');
        }
    }

    showModificationModal() {
        // Create the modal if it doesn't exist
        if (!document.getElementById('bulkModificationModal')) {
            this.createModificationModal();
        }

        // Initialize modal data
        this.currentModificationIndex = 0;
        this.modificationFiles = this.previewData.modifications || [];

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('bulkModificationModal'));
        modal.show();

        // Display the first modification
        this.displayCurrentModification();
    }

    createModificationModal() {
        const modalHtml = `
            <div class="modal fade" id="bulkModificationModal" tabindex="-1" aria-labelledby="bulkModificationModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="bulkModificationModalLabel">Bulk Modification Review</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Before Changes</h6>
                                    <div id="beforeChanges" class="border p-3" style="height: 400px; overflow-y: auto; background-color: #f8f9fa;">
                                        <!-- Before content will be displayed here -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>After Changes</h6>
                                    <div id="afterChanges" class="border p-3" style="height: 400px; overflow-y: auto; background-color: #e8f5e8;">
                                        <!-- After content will be displayed here -->
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span id="modificationProgress">File 1 of 1</span>
                                        <span class="ms-3 text-muted" id="currentFileName">filename.json</span>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-secondary" id="prevModificationBtn" disabled>
                                            <i class="bi bi-arrow-left"></i> Previous
                                        </button>
                                        <button type="button" class="btn btn-secondary ms-2" id="nextModificationBtn">
                                            Next <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-success" id="createBackupBtn">
                                <i class="bi bi-shield-check"></i> Create Backup
                            </button>
                            <button type="button" class="btn btn-primary" id="applyChangesBtn">
                                <i class="bi bi-check-circle"></i> Apply Changes
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Add event listeners
        document.getElementById('prevModificationBtn').addEventListener('click', () => this.showPreviousModification());
        document.getElementById('nextModificationBtn').addEventListener('click', () => this.showNextModification());
        document.getElementById('createBackupBtn').addEventListener('click', () => this.createBackupFiles());
        document.getElementById('applyChangesBtn').addEventListener('click', () => this.applyChangesToFiles());
    }

    displayCurrentModification() {
        if (!this.modificationFiles || this.modificationFiles.length === 0) {
            return;
        }

        const currentFile = this.modificationFiles[this.currentModificationIndex];

        // Update progress and filename
        document.getElementById('modificationProgress').textContent =
            `File ${this.currentModificationIndex + 1} of ${this.modificationFiles.length}`;
        document.getElementById('currentFileName').textContent = currentFile.test_case;

        // Load and display the test case content
        this.loadTestCaseForComparison(currentFile);

        // Update navigation buttons
        document.getElementById('prevModificationBtn').disabled = this.currentModificationIndex === 0;
        document.getElementById('nextModificationBtn').disabled = this.currentModificationIndex === this.modificationFiles.length - 1;
    }

    async loadTestCaseForComparison(fileData) {
        try {
            // Load the original test case
            const response = await fetch(`/api/test_cases/load/${fileData.test_case}`);
            const originalData = await response.json();

            // Create a copy and apply the modifications for preview
            const modifiedData = JSON.parse(JSON.stringify(originalData));

            // Apply the changes (this is a simplified version - you might want to enhance this)
            fileData.changes.forEach(change => {
                // Apply each change to the modified data
                // This is a basic implementation - you can enhance it based on your needs
                console.log('Applying change:', change);
            });

            // Display the before and after
            document.getElementById('beforeChanges').innerHTML = this.formatTestCaseForDisplay(originalData);
            document.getElementById('afterChanges').innerHTML = this.formatTestCaseForDisplay(modifiedData);

        } catch (error) {
            console.error('Error loading test case for comparison:', error);
            document.getElementById('beforeChanges').innerHTML = '<div class="text-danger">Error loading original test case</div>';
            document.getElementById('afterChanges').innerHTML = '<div class="text-danger">Error generating modified version</div>';
        }
    }

    formatTestCaseForDisplay(testCaseData) {
        // Format the test case data for display in the modal
        const formatted = JSON.stringify(testCaseData, null, 2);
        return `<pre><code>${this.escapeHtml(formatted)}</code></pre>`;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    clearRules() {
        // Check if we're using the new rule pairs interface
        const rulePairsContainer = document.getElementById('rulePairsContainer');
        if (rulePairsContainer) {
            // Clear all rule pairs and add one empty pair
            rulePairsContainer.innerHTML = '';
            this.ruleCounter = 0;
            this.addRulePair();
        } else {
            // Fallback to old textarea method
            const modificationRules = document.getElementById('modificationRules');
            if (modificationRules) {
                modificationRules.value = '';
            }
        }

        const previewResults = document.getElementById('previewResults');
        if (previewResults) {
            previewResults.innerHTML = `
                <div class="text-muted text-center">
                    <i class="bi bi-eye-slash"></i>
                    <p>Click "Preview Changes" to see what modifications will be made</p>
                </div>
            `;
        }

        const modificationResults = document.getElementById('modificationResults');
        if (modificationResults) {
            modificationResults.classList.add('d-none');
        }

        const applyBtn = document.getElementById('applyModificationsBtn');
        if (applyBtn) {
            applyBtn.disabled = true;
        }

        this.previewData = null;
    }

    showPreviousModification() {
        if (this.currentModificationIndex > 0) {
            this.currentModificationIndex--;
            this.displayCurrentModification();
        }
    }

    showNextModification() {
        if (this.currentModificationIndex < this.modificationFiles.length - 1) {
            this.currentModificationIndex++;
            this.displayCurrentModification();
        }
    }

    async createBackupFiles() {
        try {
            this.showLoadingState('createBackupBtn', true);

            const response = await fetch('/api/test_cases/bulk_modify/backup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    files: this.modificationFiles.map(f => f.test_case)
                })
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                this.showNotification(`Created ${result.backup_count} backup files`, 'success');
            } else {
                throw new Error(result.error || 'Backup creation failed');
            }

        } catch (error) {
            console.error('Backup error:', error);
            this.showNotification(`Backup failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('createBackupBtn', false);
        }
    }

    async applyChangesToFiles() {
        try {
            // Confirm with user
            const confirmMessage = `This will modify ${this.modificationFiles.length} test case(s). Are you sure you want to continue?`;
            if (!confirm(confirmMessage)) {
                return;
            }

            this.showLoadingState('applyChangesBtn', true);

            let rules = [];

            // Check if we're using the new rule pairs interface
            const rulePairsContainer = document.getElementById('rulePairsContainer');
            if (rulePairsContainer) {
                // Collect and convert rule pairs
                const rulePairs = this.collectRulePairs();
                rules = this.convertRulePairsToApiFormat(rulePairs);
            } else {
                // Fallback to old textarea method
                const rulesText = document.getElementById('modificationRules').value.trim();
                rules = rulesText.split('\n').filter(rule => rule.trim() !== '');
            }

            const createBackup = document.getElementById('createBackup').checked;

            const response = await fetch('/api/test_cases/bulk_modify/apply', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rules,
                    create_backup: createBackup
                })
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                this.showNotification('Modifications applied successfully!', 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('bulkModificationModal'));
                modal.hide();

                // Clear preview data and disable apply button
                this.previewData = null;
                document.getElementById('applyModificationsBtn').disabled = true;

                // Refresh test cases list if available
                if (typeof loadTestCases === 'function') {
                    loadTestCases();
                }
            } else {
                throw new Error(result.error || 'Modification failed');
            }

        } catch (error) {
            console.error('Apply error:', error);
            this.showNotification(`Apply failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('applyChangesBtn', false);
        }
    }

    async createBackupAllTestCases() {
        try {
            // Get list of all test case files
            const response = await fetch('/api/recording/list');
            const result = await response.json();

            if (!response.ok || result.status !== 'success') {
                throw new Error('Failed to get test cases list');
            }

            const testCaseFiles = result.test_cases.map(tc => tc.filename);

            if (testCaseFiles.length === 0) {
                this.showNotification('No test cases found to backup', 'warning');
                return;
            }

            // Confirm with user
            const confirmMessage = `This will create backup files for ${testCaseFiles.length} test case(s). Continue?`;
            if (!confirm(confirmMessage)) {
                return;
            }

            this.showLoadingState('createBackupAllBtn', true);

            const backupResponse = await fetch('/api/test_cases/bulk_modify/backup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    files: testCaseFiles
                })
            });

            const backupResult = await backupResponse.json();

            if (backupResponse.ok && backupResult.status === 'success') {
                this.showNotification(`Successfully created ${backupResult.backup_count} backup files`, 'success');

                if (backupResult.errors && backupResult.errors.length > 0) {
                    console.warn('Backup errors:', backupResult.errors);
                }
            } else {
                throw new Error(backupResult.error || 'Backup creation failed');
            }

        } catch (error) {
            console.error('Backup error:', error);
            this.showNotification(`Backup failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('createBackupAllBtn', false);
        }
    }

    showRevertModal() {
        // Create revert modal if it doesn't exist
        if (!document.getElementById('revertModal')) {
            this.createRevertModal();
        }

        // Load test cases for selection
        this.loadTestCasesForRevert();

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('revertModal'));
        modal.show();
    }

    createRevertModal() {
        const modalHtml = `
            <div class="modal fade" id="revertModal" tabindex="-1" aria-labelledby="revertModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="revertModalLabel">Revert Test Cases from Backup</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>Warning:</strong> This will overwrite the current test case files with their backup versions. This action cannot be undone.
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Select test cases to revert:</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllTestCases">
                                    <label class="form-check-label" for="selectAllTestCases">
                                        <strong>Select All</strong>
                                    </label>
                                </div>
                                <hr>
                                <div id="testCasesList" style="max-height: 300px; overflow-y: auto;">
                                    <!-- Test cases will be loaded here -->
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-danger" id="confirmRevertBtn">
                                <i class="bi bi-arrow-counterclockwise"></i> Revert Selected
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Add event listeners
        document.getElementById('selectAllTestCases').addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('#testCasesList input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = e.target.checked);
        });

        document.getElementById('confirmRevertBtn').addEventListener('click', () => {
            this.revertSelectedTestCases();
        });
    }

    async loadTestCasesForRevert() {
        try {
            // Get list of all test case files
            const response = await fetch('/api/recording/list');
            const result = await response.json();

            if (!response.ok || result.status !== 'success') {
                throw new Error('Failed to get test cases list');
            }

            const testCasesList = document.getElementById('testCasesList');
            testCasesList.innerHTML = '';

            if (result.test_cases.length === 0) {
                testCasesList.innerHTML = '<div class="text-muted">No test cases found</div>';
                return;
            }

            result.test_cases.forEach(testCase => {
                const checkboxHtml = `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="${testCase.filename}" id="tc_${testCase.filename}">
                        <label class="form-check-label" for="tc_${testCase.filename}">
                            ${testCase.name || testCase.filename}
                            <small class="text-muted d-block">${testCase.filename}</small>
                        </label>
                    </div>
                `;
                testCasesList.insertAdjacentHTML('beforeend', checkboxHtml);
            });

        } catch (error) {
            console.error('Error loading test cases:', error);
            document.getElementById('testCasesList').innerHTML =
                '<div class="text-danger">Error loading test cases</div>';
        }
    }

    async revertSelectedTestCases() {
        try {
            const selectedCheckboxes = document.querySelectorAll('#testCasesList input[type="checkbox"]:checked');
            const selectedFiles = Array.from(selectedCheckboxes).map(cb => cb.value);

            if (selectedFiles.length === 0) {
                this.showNotification('Please select at least one test case to revert', 'warning');
                return;
            }

            // Final confirmation
            const confirmMessage = `This will revert ${selectedFiles.length} test case(s) from their backup files. This action cannot be undone. Continue?`;
            if (!confirm(confirmMessage)) {
                return;
            }

            this.showLoadingState('confirmRevertBtn', true);

            const response = await fetch('/api/test_cases/bulk_modify/revert', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    files: selectedFiles
                })
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                this.showNotification(`Successfully reverted ${result.reverted_count} test case(s)`, 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('revertModal'));
                modal.hide();

                // Refresh test cases list if available
                if (typeof loadTestCases === 'function') {
                    loadTestCases();
                }

                if (result.errors && result.errors.length > 0) {
                    console.warn('Revert errors:', result.errors);
                    this.showNotification(`Some files had errors: ${result.errors.join(', ')}`, 'warning');
                }
            } else {
                throw new Error(result.error || 'Revert failed');
            }

        } catch (error) {
            console.error('Revert error:', error);
            this.showNotification(`Revert failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('confirmRevertBtn', false);
        }
    }

    displayPreviewResults(preview) {
        const container = document.getElementById('previewResults');

        if (preview.errors && preview.errors.length > 0) {
            let html = '<div class="alert alert-danger"><h6>Errors:</h6><ul class="mb-0">';
            preview.errors.forEach(error => {
                html += `<li>${this.escapeHtml(error)}</li>`;
            });
            html += '</ul></div>';
            container.innerHTML = html;
            return;
        }

        if (preview.affected_test_cases === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No test cases would be affected by these rules.
                    <br><small>Total test cases scanned: ${preview.total_test_cases}</small>
                </div>
            `;
            return;
        }

        let html = `
            <div class="alert alert-success">
                <h6><i class="bi bi-check-circle"></i> Preview Results:</h6>
                <ul class="mb-0">
                    <li>Total test cases: ${preview.total_test_cases}</li>
                    <li>Test cases to be modified: ${preview.affected_test_cases}</li>
                </ul>
            </div>
        `;

        if (preview.modifications && preview.modifications.length > 0) {
            html += '<div class="mt-3"><h6>Modifications by Test Case:</h6>';
            preview.modifications.forEach(mod => {
                html += `
                    <div class="card mb-2">
                        <div class="card-header py-2">
                            <strong>${this.escapeHtml(mod.test_case_name)}</strong>
                            <small class="text-muted">(${this.escapeHtml(mod.test_case)})</small>
                        </div>
                        <div class="card-body py-2">
                            <ul class="mb-0">
                `;
                mod.changes.forEach(change => {
                    html += `<li><small>${this.escapeHtml(change.description)}</small></li>`;
                });
                html += '</ul></div></div>';
            });
            html += '</div>';
        }

        container.innerHTML = html;
    }

    displayModificationResults(results) {
        const container = document.getElementById('resultsContent');
        const resultsDiv = document.getElementById('modificationResults');

        let html = `
            <div class="alert alert-${results.errors > 0 ? 'warning' : 'success'}">
                <h6><i class="bi bi-check-circle"></i> Modification Results:</h6>
                <ul class="mb-0">
                    <li>Total test cases processed: ${results.total_test_cases}</li>
                    <li>Test cases modified: ${results.modified_test_cases}</li>
                    ${results.backup_files.length > 0 ? `<li>Backup files created: ${results.backup_files.length}</li>` : ''}
                    ${results.errors > 0 ? `<li class="text-danger">Errors: ${results.errors}</li>` : ''}
                </ul>
            </div>
        `;

        if (results.errors && results.errors.length > 0) {
            html += '<div class="alert alert-danger mt-2"><h6>Errors:</h6><ul class="mb-0">';
            results.errors.forEach(error => {
                html += `<li><small>${this.escapeHtml(error)}</small></li>`;
            });
            html += '</ul></div>';
        }

        container.innerHTML = html;
        resultsDiv.classList.remove('d-none');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }



    showNotification(message, type = 'info') {
        // Use existing notification system if available, otherwise use alert
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.importExportManager = new ImportExportManager();
    window.bulkModificationManager = new BulkModificationManager();
});
