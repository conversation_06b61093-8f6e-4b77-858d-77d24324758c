{"name": "Sample Image Recognition Test", "description": "Test using image recognition for element interaction", "actions": [{"action_id": "img123abc9", "type": "Tap on Image", "image": "env[login_button_image]", "timestamp": 1704067400000}, {"action_id": "img456def2", "type": "Input Text", "locator_type": "id", "locator_value": "username_field", "text": "env[test_username]", "timestamp": 1704067410000}, {"action_id": "img789ghi5", "type": "Tap on Image", "image": "env[submit_button_image]", "timestamp": 1704067420000}, {"action_id": "img012jkl8", "type": "Wait for Element", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Logout\")]", "timeout": 15, "timestamp": 1704067430000}]}