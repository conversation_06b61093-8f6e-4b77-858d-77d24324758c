#!/usr/bin/env python3
"""
Simple test for bulk modification
"""

import json
import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.import_export_manager import ImportExportManager
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_simple():
    """Simple test"""
    logger.info("=== Simple Test ===")
    
    # Initialize the manager
    manager = ImportExportManager('app/test_cases', 'app/test_suites')
    
    # Test a simple rule
    test_rules = [
        "Add step 'tap' with locator_value '//XCUIElementTypeButton[@name=\"Checkout\"]' locator_type 'xpath' after step that contains 'Tab 4 of 5'"
    ]
    
    logger.info(f"Testing rule: {test_rules[0]}")
    parsed = manager._parse_modification_rule(test_rules[0])
    logger.info(f"Parsed result: {parsed}")
    print(f"Rule: {test_rules[0]}")
    print(f"Parsed: {json.dumps(parsed, indent=2)}")

if __name__ == "__main__":
    print("Simple Bulk Modification Test")
    print("=" * 40)
    
    try:
        test_simple()
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
