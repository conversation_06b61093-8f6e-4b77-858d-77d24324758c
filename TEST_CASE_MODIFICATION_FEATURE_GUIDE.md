# Test Case Modification Feature - User Guide

## Overview

The Test Case Modification feature provides a comprehensive interface for searching, filtering, and editing test cases in the Mobile App Automation Tool. This feature replaces the basic bulk modification functionality with a powerful, user-friendly system for managing test cases.

## Key Features

### 🔍 Advanced Search & Filter System
- **Action Type Filtering**: Filter test cases by specific action types (tap, input, wait, etc.)
- **Locator Type Filtering**: Search by locator types (xpath, id, accessibility_id, etc.)
- **Text-based Search**: Search in locator values, test case names, descriptions, image files, and text values
- **Multi-criteria Search**: Combine multiple filters for precise results
- **Real-time Results**: Instant search results with detailed matching information

### ✏️ Individual Test Case Editor
- **Visual Editor**: User-friendly interface for editing test case properties and actions
- **Action Management**: Add, edit, delete, and reorder test case actions
- **Real-time Validation**: Validate test cases before saving
- **Action ID Generation**: Automatic generation of unique action IDs for new steps
- **Backup Support**: Create backups before making changes

### 🛡️ Data Validation & Persistence
- **Comprehensive Validation**: Validate all test case data before saving
- **File & Database Sync**: Maintain consistency between JSON files and database
- **Error Handling**: Clear error messages and validation feedback
- **Rollback Support**: Backup and restore functionality

### 🎯 Enhanced User Experience
- **Keyboard Shortcuts**: Ctrl/Cmd+S to save, Escape to cancel, Ctrl/Cmd+F to search
- **Progress Indicators**: Loading states and progress feedback
- **Notifications**: Success/error messages with detailed information
- **Responsive Design**: Works on different screen sizes

## How to Use

### 1. Accessing the Feature

1. Open the Mobile App Automation Tool
2. Navigate to the **"Search & Edit Tests"** tab
3. The interface will load with search filters and options

### 2. Searching for Test Cases

#### Basic Search
1. Enter search terms in any of the filter fields:
   - **Test Case Name**: Search by test case names
   - **Description**: Search in test case descriptions
   - **Locator Value**: Search in element locators
   - **Image Files**: Search for image-based actions
   - **Text Values**: Search in text input values

2. Click **"Search Test Cases"** or press Enter in any search field

#### Advanced Filtering
1. Click **"Load Filter Options"** to populate dropdown filters
2. Select multiple options from:
   - **Action Type**: Choose specific action types
   - **Locator Type**: Filter by locator methods
3. Combine text search with dropdown filters for precise results

### 3. Viewing Search Results

- Results are displayed in a table with:
  - Test case name and description
  - Number of actions
  - Last modified date
  - Matching actions preview
- Use checkboxes to select multiple test cases
- Click **"Edit"** button to edit individual test cases

### 4. Editing Test Cases

#### Opening the Editor
1. Select a test case from search results
2. Click the **"Edit"** button
3. The test case editor will open below the search results

#### Editing Test Case Properties
- **Name**: Update the test case name
- **Description**: Modify the description

#### Managing Actions
- **Add Action**: Click **"Add Action"** to create new steps
- **Edit Action**: Click the edit button on any action
- **Delete Action**: Click the delete button (with confirmation)
- **Reorder Actions**: Use up/down arrows to change action sequence

#### Action Editor Features
- **Action Type**: Select from available action types
- **Locator Settings**: Configure locator type and value
- **Parameters**: Set text, image, timeout, and other parameters
- **Validation**: Real-time validation of required fields

### 5. Saving Changes

1. Click **"Validate"** to check for errors before saving
2. Click **"Save Test Case"** to persist changes
3. Use **"Create Backup"** to backup before major changes
4. Click **"Cancel"** to discard changes

## Keyboard Shortcuts

- **Ctrl/Cmd + S**: Save current test case
- **Escape**: Cancel editing and close editor
- **Ctrl/Cmd + F**: Focus on test case name search field
- **Enter**: Execute search when in any search field

## API Endpoints

The feature provides several new API endpoints:

### Search Test Cases
```
POST /api/test_cases/search
```
Search test cases with multiple filter criteria.

### Get Action Types
```
GET /api/test_cases/action_types
```
Retrieve all available action types from existing test cases.

### Get Locator Types
```
GET /api/test_cases/locator_types
```
Retrieve all available locator types from existing test cases.

### Load Test Case
```
GET /api/test_cases/load/<filename>
```
Load a specific test case for editing.

### Save Modified Test Case
```
POST /api/test_cases/save_modified
```
Save a modified test case with validation.

### Create Backup
```
POST /api/test_cases/backup/<filename>
```
Create a backup of a specific test case.

## Technical Implementation

### Backend Components
- **Enhanced TestCaseManager**: Added search, validation, and backup methods
- **New API Routes**: RESTful endpoints for all modification operations
- **Validation System**: Comprehensive test case and action validation
- **Action ID Management**: Automatic generation of unique identifiers

### Frontend Components
- **TestCaseModificationManager**: JavaScript class managing the entire interface
- **Search Interface**: Advanced filtering and search functionality
- **Test Case Editor**: Visual editor for test case properties and actions
- **Notification System**: User feedback and progress indicators

### Database Integration
- **Consistent Data**: Maintains sync between files and database
- **Validation**: Ensures data integrity before persistence
- **Backup Tracking**: Logs backup operations and modifications

## Best Practices

1. **Always Validate**: Use the validation feature before saving changes
2. **Create Backups**: Backup important test cases before major modifications
3. **Use Descriptive Names**: Give test cases and actions clear, descriptive names
4. **Test Changes**: Run modified test cases to ensure they work correctly
5. **Regular Cleanup**: Remove unused or obsolete test cases periodically

## Troubleshooting

### Common Issues

**Search returns no results**
- Check that at least one search criterion is provided
- Verify test cases exist in the test_cases directory
- Try broader search terms

**Validation errors when saving**
- Review error messages for specific issues
- Ensure required fields are filled
- Check action parameters match action type requirements

**Editor not loading**
- Refresh the page and try again
- Check browser console for JavaScript errors
- Ensure all required files are loaded

**Backup creation fails**
- Verify write permissions to test_cases directory
- Check available disk space
- Review server logs for detailed error information

## Platform Support

This feature is available on both:
- **iOS Version** (port 8080)
- **Android Version** (port 8081)

Both versions share the same functionality and user interface.

## Future Enhancements

Planned improvements include:
- **Bulk Operations**: Apply changes to multiple selected test cases
- **Import/Export**: Enhanced import/export with modification support
- **Version Control**: Track changes and revision history
- **Templates**: Create test case templates for common patterns
- **Advanced Search**: Regular expression and complex query support
