# Core dependencies
setuptools>=65.5.1
wheel>=0.38.0
flask==2.2.3
Werkzeug==2.2.3  # Keep this version for compatibility with Flask 2.2.3
numpy>=1.24.2
Pillow>=10.0.0
requests>=2.28.0

# OpenCV - Choose one of these based on your needs
opencv-python>=4.8.0  # Full OpenCV with GUI support
# opencv-python-headless>=********  # Uncomment if you need headless version

# Socket communication for real-time updates
flask-socketio==5.3.3
python-engineio==4.4.1
python-socketio==5.8.0

# OCR (Optical Character Recognition) for text detection in images
pytesseract>=0.3.10
# easyocr>=1.7.0  # Uncomment if you need more advanced OCR

# Android device support
pure-python-adb==0.3.0.dev0
adbutils>=2.8.0
uiautomator2>=3.2.9

# Image processing libraries
scikit-image>=0.20.0
scipy>=1.15.0

# iOS device support (macOS only)
facebook-wda>=1.5.0
tidevice>=0.12.0

# Appium support - core libraries for mobile automation
Appium-Python-Client>=5.0.0
webdriver-manager>=3.8.6
selenium>=4.10.0

# Web server and async support
cython==3.0.9  # Required for gevent
gevent==23.9.1
gevent-websocket==0.10.1

# Optional but recommended utilities
PyYAML>=6.0.0  # For configuration files
tqdm>=4.66.0  # For progress bars

# Airtest support (optional, for additional automation capabilities)
# airtest>=1.2.10  # Uncomment if you need Airtest features

# Healenium self-healing support
docker>=6.1.0  # For Docker container management
psycopg2-binary>=2.9.0  # PostgreSQL adapter for Python