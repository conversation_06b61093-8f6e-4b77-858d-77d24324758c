{"name": "App Settings AU", "created": "2025-07-05 20:31:14", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "XEbZHdi0GT", "executionTime": "2206ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "veukWo4573", "executionTime": "2410ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "rJ86z4njuR", "executionTime": "1131ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "x6vffndoRV", "expanded": false, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps_count": 6, "timestamp": *************, "type": "multiStep", "loading_in_progress": false, "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "3170ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": true, "function_name": "text", "text": "env[uname]", "timestamp": 1749384094911, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "qPv5C4K0a2", "enter": true, "executionTime": "4199ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1745666288726, "type": "iosFunctions"}], "steps_loaded": true, "display_depth": 0}, {"action_id": "mIKA85kXaW", "executionTime": "32ms", "package_id": "com.apple.Preferences", "timestamp": 1749444829578, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "1207ms", "package_id": "com.apple.Preferences", "timestamp": 1749444810598, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "3310ms", "text_to_find": "Wi-Fi", "timeout": 30, "timestamp": 1749444898707, "type": "tapOnText"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5016ms", "time": 5, "timestamp": 1749445273934, "type": "wait"}, {"action_id": "jUCAk6GJc4", "executionTime": "1034ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "method": "locator", "timeout": 10, "timestamp": 1749445081254, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5016ms", "time": 5, "timestamp": 1750843578808, "type": "wait"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "3220ms", "package_id": "env[appid]", "timestamp": 1749445437019, "type": "restartApp"}, {"action_id": "cokvFXhj4c", "executionTime": "235ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445125565, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "724ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445177888, "type": "tap"}, {"action_id": "eSr9EFlJek", "executionTime": "207ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445183545, "type": "exists"}, {"action_id": "6xgrAWyfZ4", "executionTime": "652ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445193141, "type": "tap"}, {"action_id": "WoymrHdtrO", "executionTime": "214ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445211742, "type": "exists"}, {"action_id": "UpUSVInizv", "executionTime": "681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445217156, "type": "tap"}, {"action_id": "seQcUKjkSU", "executionTime": "176ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445234048, "type": "exists"}, {"action_id": "LfyQctrEJn", "executionTime": "125ms", "package_id": "com.apple.Preferences", "timestamp": 1749445249154, "type": "launchApp"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5016ms", "time": 5, "timestamp": 1750843560629, "type": "wait"}, {"action_id": "GRwHMVK4sA", "executionTime": "762ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "method": "locator", "timeout": 10, "timestamp": 1749445257372, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5016ms", "time": 5, "timestamp": 1750838420209, "type": "wait"}, {"action_id": "hCCEvRtj1A", "executionTime": "3213ms", "package_id": "env[appid]", "timestamp": 1749445309230, "type": "restartApp"}, {"action_id": "UpUSVInizv", "executionTime": "681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445687300, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749445784426, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "LcYLwUffqj", "text_to_find": "out", "timeout": 30, "timestamp": 1749445809311, "type": "tapOnText"}, {"action_id": "xVuuejtCFA", "package_id": "com.apple.mobilesafari", "timestamp": 1749445877653, "type": "restartApp"}, {"action_id": "0Q0fm6OTij", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]", "method": "locator", "timeout": 10, "timestamp": 1749*********, "type": "tap"}, {"action_id": "rYJcLPh8Aq", "enter": true, "function_name": "text", "text": "kmart au", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "fTdGMJ3NH3", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "0QtNHB5WEK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "UpUSVInizv", "executionTime": "681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"type": "swipe", "timestamp": *************, "x": 0, "y": 0, "method": "coordinates", "start_x": 50, "start_y": 50, "end_x": 50, "end_y": 30, "vector_start": [0.5, 0.5], "vector_end": [0.5, 0.3], "duration": 300, "count": 1, "interval": 0.5, "direction": "up", "action_id": "SnfhJXwr1e"}, {"action_id": "gkkQzTCmma", "text_to_find": "Catalogue", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "Xqj9EIVE7g", "condition": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]", "timeout": 20}, "condition_type": "exists", "executionTime": "7212ms", "then_action": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]", "timeout": 10, "type": "clickElement"}, "timestamp": 1746144409595, "type": "ifElseSteps"}, {"action_id": "igReeDqips", "image_filename": "env[catalogue-menu-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749471352255, "type": "tap"}, {"action_id": "Pd7cReoJM6", "text_to_find": "List", "timeout": 30, "timestamp": 1749472320276, "type": "tapOnText"}, {"action_id": "JcAR0JctQ6", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "method": "locator", "timeout": 10, "timestamp": 1749472290567, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1751711463086, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Cmvm82hiAa", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1749472379198, "type": "tap"}, {"action_id": "UpUSVInizv", "executionTime": "681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749470221523, "type": "tap"}, {"action_id": "gkkQzTCmma", "text_to_find": "Catalogue", "timeout": 30, "timestamp": 1749472424498, "type": "tapOnText"}, {"action_id": "Xqj9EIVE7g", "condition": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]", "timeout": 20}, "condition_type": "exists", "executionTime": "7212ms", "then_action": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]", "timeout": 10, "type": "clickElement"}, "timestamp": 1749472435030, "type": "ifElseSteps"}, {"action_id": "igReeDqips", "image_filename": "env[catalogue-menu-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749472450590, "type": "tap"}, {"action_id": "YHaMIjULRf", "text_to_find": "List", "timeout": 30, "timestamp": 1749472769571, "type": "tapOnText"}, {"action_id": "Qy0Y0uJchm", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "method": "locator", "timeout": 10, "timestamp": 1749472775719, "type": "tap"}, {"action_id": "Iab9zCfpqO", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1749472786795, "type": "tap"}, {"action_id": "UpUSVInizv", "executionTime": "681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749472811046, "type": "tap"}, {"action_id": "DbM0d0m6rU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Increase quantity\"]", "method": "locator", "timeout": 10, "timestamp": 1749473005084, "type": "tap"}, {"action_id": "IW6uAwdtiW", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Decrease quantity\"]", "method": "locator", "timeout": 10, "timestamp": 1749473072817, "type": "tap"}, {"action_id": "K0c1gL9UK1", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1749472862396, "type": "tap"}, {"action_id": "3NOS1fbxZs", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749473253040, "type": "tap"}, {"action_id": "3KNqlNy6Bj", "executionTime": "724ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749473131022, "type": "tap"}, {"action_id": "OKiI82VdnE", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "start_x": 50, "start_y": 70, "timestamp": 1749474098726, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "L59V5hqMX9", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Home & Living\"]", "method": "locator", "timeout": 10, "timestamp": 1749889216570, "type": "tap"}, {"action_id": "n57KEWjTea", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "method": "locator", "timeout": 10, "timestamp": 1749474131811, "type": "tap"}, {"action_id": "2hGhWulI52", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,\"$\")])[1]", "method": "locator", "timeout": 10, "timestamp": 1749474207391, "type": "tap"}, {"action_id": "Cr1z26u7Va", "condition": {"locator_type": "accessibility_id", "locator_value": "Add to bag", "timeout": 15}, "condition_type": "exists", "then_action": {"type": "tap", "x": 0, "y": 0}, "timestamp": 1750975431548, "type": "ifElseSteps"}, {"action_id": "c4T3INQkzn", "enabled": true, "package_id": "env[appid]", "timestamp": 1749889997693, "type": "restartApp"}, {"action_id": "UpUSVInizv", "enabled": true, "executionTime": "681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749474449613, "type": "tap"}, {"action_id": "0SHxVJkq0l", "condition": {"locator_type": "id", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "timeout": 20}, "condition_type": "exists", "then_action": {"type": "tap", "x": 0, "y": 0}, "timestamp": 1750985090332, "type": "ifElseSteps"}, {"action_id": "Qb1AArnpCH", "duration": 5, "time": 5, "timestamp": 1750975463814, "type": "wait"}, {"action_id": "kPdSiomhwu", "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps_count": 0, "timestamp": 1750975487799, "type": "cleanupSteps", "loading_in_progress": false, "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "au.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "steps_loaded": true, "display_depth": 0}], "labels": [], "updated": "2025-07-05 20:31:14"}